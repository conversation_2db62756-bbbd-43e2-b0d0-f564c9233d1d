{"version": 3, "file": "Index-DBfSJWIQ.js", "sources": ["../../../../js/file/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as FilePreview } from \"./shared/FilePreview.svelte\";\n\texport { default as BaseFileUpload } from \"./shared/FileUpload.svelte\";\n\texport { default as BaseFile } from \"./shared/File.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport File from \"./shared/File.svelte\";\n\timport FileUpload from \"./shared/FileUpload.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData | FileData[];\n\n\texport let interactive: boolean;\n\texport let root: string;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let height: number | undefined = undefined;\n\n\texport let _selectable = false;\n\texport let loading_status: LoadingStatus;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t\tclear_status: LoadingStatus;\n\t\tdelete: FileData;\n\t}>;\n\texport let file_count: \"single\" | \"multiple\" | \"directory\";\n\texport let file_types: string[] = [\"file\"];\n\n\tlet old_value = value;\n\t$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\n\t\tgradio.dispatch(\"change\");\n\t\told_value = value;\n\t}\n\n\tlet dragging = false;\n\tlet pending_upload = false;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value ? \"solid\" : \"dashed\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tstatus={pending_upload\n\t\t\t? \"generating\"\n\t\t\t: loading_status?.status || \"complete\"}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t{#if !interactive}\n\t\t<File\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\tselectable={_selectable}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{height}\n\t\t\ti18n={gradio.i18n}\n\t\t/>\n\t{:else}\n\t\t<FileUpload\n\t\t\tupload={gradio.client.upload}\n\t\t\tstream_handler={gradio.client.stream}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{value}\n\t\t\t{file_count}\n\t\t\t{file_types}\n\t\t\tselectable={_selectable}\n\t\t\t{root}\n\t\t\t{height}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\ton:change={({ detail }) => {\n\t\t\t\tvalue = detail;\n\t\t\t}}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t\ton:delete={({ detail }) => {\n\t\t\t\tgradio.dispatch(\"delete\", detail);\n\t\t\t}}\n\t\t\ti18n={gradio.i18n}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"file\" />\n\t\t</FileUpload>\n\t{/if}\n</Block>\n"], "names": ["ctx", "dirty", "fileupload_changes", "file_changes", "uploadtext_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "interactive", "root", "label", "show_label", "height", "_selectable", "loading_status", "container", "scale", "min_width", "gradio", "file_count", "file_types", "old_value", "dragging", "clear_status_handler", "select_handler", "detail", "$$invalidate", "select_handler_1"], "mappings": "klCA0FWA,EAAM,EAAA,EAAC,OAAO,sBACNA,EAAM,EAAA,EAAC,OAAO,0FAMlBA,EAAW,EAAA,wBAGR,cAAAA,MAAO,cAgBhB,KAAAA,MAAO,uRA1BLA,EAAM,EAAA,EAAC,OAAO,mCACNA,EAAM,EAAA,EAAC,OAAO,+JAMlBA,EAAW,EAAA,8CAGRC,EAAA,QAAAC,EAAA,cAAAF,MAAO,eAgBhBC,EAAA,QAAAC,EAAA,KAAAF,MAAO,kMAnCDA,EAAW,EAAA,oDAKjB,KAAAA,MAAO,mHALDA,EAAW,EAAA,+FAKjBC,EAAA,QAAAE,EAAA,KAAAH,MAAO,8IAgCK,KAAAA,MAAO,mFAAPC,EAAA,QAAAG,EAAA,KAAAJ,MAAO,yIAhDd,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,GACV,OAELA,EAAc,CAAA,GAAE,QAAU,yJAGxBA,EAAW,CAAA,IAAA,mKARJ,WAAAA,MAAO,YACbC,EAAA,OAAA,CAAA,KAAAD,MAAO,IAAI,SACbA,EAAc,CAAA,CAAA,QACV,OAELA,EAAc,CAAA,GAAE,QAAU,4VAhBrBA,EAAK,CAAA,EAAG,QAAU,qBACdA,EAAQ,EAAA,EAAG,QAAU,eACzB,6FAMO,qJARPA,EAAK,CAAA,EAAG,QAAU,mCACdA,EAAQ,EAAA,EAAG,QAAU,+RAzCvB,GAAA,CAAA,QAAAK,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAmC,EAAAH,GAEnC,YAAAI,CAAoB,EAAAJ,GACpB,KAAAK,CAAY,EAAAL,GACZ,MAAAM,CAAa,EAAAN,GACb,WAAAO,CAAmB,EAAAP,EACnB,CAAA,OAAAQ,EAA6B,MAAS,EAAAR,EAEtC,CAAA,YAAAS,EAAc,EAAK,EAAAT,GACnB,eAAAU,CAA6B,EAAAV,EAC7B,CAAA,UAAAW,EAAY,EAAI,EAAAX,EAChB,CAAA,MAAAY,EAAuB,IAAI,EAAAZ,EAC3B,CAAA,UAAAa,EAAgC,MAAS,EAAAb,GACzC,OAAAc,CAQT,EAAAd,GACS,WAAAe,CAA+C,EAAAf,EAC/C,CAAA,WAAAgB,GAAwB,MAAM,CAAA,EAAAhB,EAErCiB,EAAYd,EAMZe,EAAW,GAuBS,MAAAC,EAAA,IAAAL,EAAO,SAAS,eAAgBJ,CAAc,EAItDU,EAAA,CAAA,CAAA,OAAAC,KAAaP,EAAO,SAAS,SAAUO,CAAM,MAqB7C,OAAAA,KAAM,CACnBC,EAAA,EAAAnB,EAAQkB,CAAM,OAEH,OAAAA,CAAM,IAAAC,EAAA,GAAQJ,EAAWG,CAAM,QAC3BP,EAAO,SAAS,OAAO,EACzBS,EAAA,CAAA,CAAA,OAAAF,KAAaP,EAAO,SAAS,SAAUO,CAAM,QAC1CP,EAAO,SAAS,QAAQ,MAC5B,OAAAO,KAAM,CAClBC,EAAA,EAAAZ,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BI,EAAO,SAAS,QAASO,CAAM,OAElB,OAAAA,KAAM,CACnBP,EAAO,SAAS,SAAUO,CAAM,8oBAlE5B,KAAK,UAAUJ,CAAS,IAAM,KAAK,UAAUd,CAAK,IACxDW,EAAO,SAAS,QAAQ,EACxBQ,EAAA,GAAAL,EAAYd,CAAK"}