{"version": 3, "file": "ImageUploader-Dh2I3hM7.js", "sources": ["../../../../js/icons/src/Camera.svelte", "../../../../js/icons/src/Circle.svelte", "../../../../js/icons/src/Square.svelte", "../../../../js/image/shared/WebcamPermissions.svelte", "../../../../js/image/shared/stream_utils.ts", "../../../../js/image/shared/Webcam.svelte", "../../../../js/image/shared/ClearImage.svelte", "../../../../js/image/shared/ImageUploader.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-camera\"\n>\n\t<path\n\t\td=\"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\"\n\t/>\n\t<circle cx=\"12\" cy=\"13\" r=\"4\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-circle\"\n>\n\t<circle cx=\"12\" cy=\"12\" r=\"10\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-square\"\n>\n\t<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { Webcam } from \"@gradio/icons\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tclick: undefined;\n\t}>();\n</script>\n\n<button style:height=\"100%\" on:click={() => dispatch(\"click\")}>\n\t<div class=\"wrap\">\n\t\t<span class=\"icon-wrap\">\n\t\t\t<Webcam />\n\t\t</span>\n\t\t{\"Click to Access Webcam\"}\n\t</div>\n</button>\n\n<style>\n\tbutton {\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t}\n\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmin-height: var(--size-60);\n\t\tcolor: var(--block-label-text-color);\n\t\theight: 100%;\n\t\tpadding-top: var(--size-3);\n\t}\n\n\t.icon-wrap {\n\t\twidth: 30px;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n</style>\n", "export function get_devices(): Promise<MediaDeviceInfo[]> {\n\treturn navigator.mediaDevices.enumerateDevices();\n}\n\nexport function handle_error(error: string): void {\n\tthrow new Error(error);\n}\n\nexport function set_local_stream(\n\tlocal_stream: MediaStream | null,\n\tvideo_source: HTMLVideoElement\n): void {\n\tvideo_source.srcObject = local_stream;\n\tvideo_source.muted = true;\n\tvideo_source.play();\n}\n\nexport async function get_video_stream(\n\tinclude_audio: boolean,\n\tvideo_source: HTMLVideoElement,\n\tdevice_id?: string\n): Promise<MediaStream> {\n\tconst size = {\n\t\twidth: { ideal: 1920 },\n\t\theight: { ideal: 1440 }\n\t};\n\n\tconst constraints = {\n\t\tvideo: device_id ? { deviceId: { exact: device_id }, ...size } : size,\n\t\taudio: include_audio\n\t};\n\n\treturn navigator.mediaDevices\n\t\t.getUserMedia(constraints)\n\t\t.then((local_stream: MediaStream) => {\n\t\t\tset_local_stream(local_stream, video_source);\n\t\t\treturn local_stream;\n\t\t});\n}\n\nexport function set_available_devices(\n\tdevices: MediaDeviceInfo[]\n): MediaDeviceInfo[] {\n\tconst cameras = devices.filter(\n\t\t(device: MediaDeviceInfo) => device.kind === \"videoinput\"\n\t);\n\n\treturn cameras;\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { Camera, Circle, Square, DropdownArrow } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { type FileData, type Client, prepare_files } from \"@gradio/client\";\n\timport WebcamPermissions from \"./WebcamPermissions.svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport {\n\t\tget_devices,\n\t\tget_video_stream,\n\t\tset_available_devices\n\t} from \"./stream_utils\";\n\n\tlet video_source: HTMLVideoElement;\n\tlet available_video_devices: MediaDeviceInfo[] = [];\n\tlet selected_device: MediaDeviceInfo | null = null;\n\n\tlet canvas: HTMLCanvasElement;\n\texport let streaming = false;\n\texport let pending = false;\n\texport let root = \"\";\n\n\texport let mode: \"image\" | \"video\" = \"image\";\n\texport let mirror_webcam: boolean;\n\texport let include_audio: boolean;\n\texport let i18n: I18nFormatter;\n\texport let upload: Client[\"upload\"];\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstream: undefined;\n\t\tcapture: FileData | Blob | null;\n\t\terror: string;\n\t\tstart_recording: undefined;\n\t\tstop_recording: undefined;\n\t}>();\n\n\tonMount(() => (canvas = document.createElement(\"canvas\")));\n\n\tconst handle_device_change = async (event: InputEvent): Promise<void> => {\n\t\tconst target = event.target as HTMLInputElement;\n\t\tconst device_id = target.value;\n\n\t\tawait get_video_stream(include_audio, video_source, device_id).then(\n\t\t\tasync (local_stream) => {\n\t\t\t\tstream = local_stream;\n\t\t\t\tselected_device =\n\t\t\t\t\tavailable_video_devices.find(\n\t\t\t\t\t\t(device) => device.deviceId === device_id\n\t\t\t\t\t) || null;\n\t\t\t\toptions_open = false;\n\t\t\t}\n\t\t);\n\t};\n\n\tasync function access_webcam(): Promise<void> {\n\t\ttry {\n\t\t\tget_video_stream(include_audio, video_source)\n\t\t\t\t.then(async (local_stream) => {\n\t\t\t\t\twebcam_accessed = true;\n\t\t\t\t\tavailable_video_devices = await get_devices();\n\t\t\t\t\tstream = local_stream;\n\t\t\t\t})\n\t\t\t\t.then(() => set_available_devices(available_video_devices))\n\t\t\t\t.then((devices) => {\n\t\t\t\t\tavailable_video_devices = devices;\n\n\t\t\t\t\tconst used_devices = stream\n\t\t\t\t\t\t.getTracks()\n\t\t\t\t\t\t.map((track) => track.getSettings()?.deviceId)[0];\n\n\t\t\t\t\tselected_device = used_devices\n\t\t\t\t\t\t? devices.find((device) => device.deviceId === used_devices) ||\n\t\t\t\t\t\t\tavailable_video_devices[0]\n\t\t\t\t\t\t: available_video_devices[0];\n\t\t\t\t});\n\n\t\t\tif (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n\t\t\t\tdispatch(\"error\", i18n(\"image.no_webcam_support\"));\n\t\t\t}\n\t\t} catch (err) {\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", i18n(\"image.allow_webcam_access\"));\n\t\t\t} else {\n\t\t\t\tthrow err;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction take_picture(): void {\n\t\tvar context = canvas.getContext(\"2d\")!;\n\t\tif (\n\t\t\t(!streaming || (streaming && recording)) &&\n\t\t\tvideo_source.videoWidth &&\n\t\t\tvideo_source.videoHeight\n\t\t) {\n\t\t\tcanvas.width = video_source.videoWidth;\n\t\t\tcanvas.height = video_source.videoHeight;\n\t\t\tcontext.drawImage(\n\t\t\t\tvideo_source,\n\t\t\t\t0,\n\t\t\t\t0,\n\t\t\t\tvideo_source.videoWidth,\n\t\t\t\tvideo_source.videoHeight\n\t\t\t);\n\n\t\t\tif (mirror_webcam) {\n\t\t\t\tcontext.scale(-1, 1);\n\t\t\t\tcontext.drawImage(video_source, -video_source.videoWidth, 0);\n\t\t\t}\n\n\t\t\tcanvas.toBlob(\n\t\t\t\t(blob) => {\n\t\t\t\t\tdispatch(streaming ? \"stream\" : \"capture\", blob);\n\t\t\t\t},\n\t\t\t\t\"image/png\",\n\t\t\t\t0.8\n\t\t\t);\n\t\t}\n\t}\n\n\tlet recording = false;\n\tlet recorded_blobs: BlobPart[] = [];\n\tlet stream: MediaStream;\n\tlet mimeType: string;\n\tlet media_recorder: MediaRecorder;\n\n\tfunction take_recording(): void {\n\t\tif (recording) {\n\t\t\tmedia_recorder.stop();\n\t\t\tlet video_blob = new Blob(recorded_blobs, { type: mimeType });\n\t\t\tlet ReaderObj = new FileReader();\n\t\t\tReaderObj.onload = async function (e): Promise<void> {\n\t\t\t\tif (e.target) {\n\t\t\t\t\tlet _video_blob = new File(\n\t\t\t\t\t\t[video_blob],\n\t\t\t\t\t\t\"sample.\" + mimeType.substring(6)\n\t\t\t\t\t);\n\t\t\t\t\tconst val = await prepare_files([_video_blob]);\n\t\t\t\t\tlet value = (\n\t\t\t\t\t\t(await upload(val, root))?.filter(Boolean) as FileData[]\n\t\t\t\t\t)[0];\n\t\t\t\t\tdispatch(\"capture\", value);\n\t\t\t\t\tdispatch(\"stop_recording\");\n\t\t\t\t}\n\t\t\t};\n\t\t\tReaderObj.readAsDataURL(video_blob);\n\t\t} else {\n\t\t\tdispatch(\"start_recording\");\n\t\t\trecorded_blobs = [];\n\t\t\tlet validMimeTypes = [\"video/webm\", \"video/mp4\"];\n\t\t\tfor (let validMimeType of validMimeTypes) {\n\t\t\t\tif (MediaRecorder.isTypeSupported(validMimeType)) {\n\t\t\t\t\tmimeType = validMimeType;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (mimeType === null) {\n\t\t\t\tconsole.error(\"No supported MediaRecorder mimeType\");\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tmedia_recorder = new MediaRecorder(stream, {\n\t\t\t\tmimeType: mimeType\n\t\t\t});\n\t\t\tmedia_recorder.addEventListener(\"dataavailable\", function (e) {\n\t\t\t\trecorded_blobs.push(e.data);\n\t\t\t});\n\t\t\tmedia_recorder.start(200);\n\t\t}\n\t\trecording = !recording;\n\t}\n\n\tlet webcam_accessed = false;\n\n\tfunction record_video_or_photo(): void {\n\t\tif (mode === \"image\" && streaming) {\n\t\t\trecording = !recording;\n\t\t}\n\t\tif (mode === \"image\") {\n\t\t\ttake_picture();\n\t\t} else {\n\t\t\ttake_recording();\n\t\t}\n\t\tif (!recording && stream) {\n\t\t\tstream.getTracks().forEach((track) => track.stop());\n\t\t\tvideo_source.srcObject = null;\n\t\t\twebcam_accessed = false;\n\t\t}\n\t}\n\n\tif (streaming && mode === \"image\") {\n\t\twindow.setInterval(() => {\n\t\t\tif (video_source && !pending) {\n\t\t\t\ttake_picture();\n\t\t\t}\n\t\t}, 500);\n\t}\n\n\tlet options_open = false;\n\n\texport function click_outside(node: Node, cb: any): any {\n\t\tconst handle_click = (event: MouseEvent): void => {\n\t\t\tif (\n\t\t\t\tnode &&\n\t\t\t\t!node.contains(event.target as Node) &&\n\t\t\t\t!event.defaultPrevented\n\t\t\t) {\n\t\t\t\tcb(event);\n\t\t\t}\n\t\t};\n\n\t\tdocument.addEventListener(\"click\", handle_click, true);\n\n\t\treturn {\n\t\t\tdestroy() {\n\t\t\t\tdocument.removeEventListener(\"click\", handle_click, true);\n\t\t\t}\n\t\t};\n\t}\n\n\tfunction handle_click_outside(event: MouseEvent): void {\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t\toptions_open = false;\n\t}\n</script>\n\n<div class=\"wrap\">\n\t<!-- svelte-ignore a11y-media-has-caption -->\n\t<!-- need to suppress for video streaming https://github.com/sveltejs/svelte/issues/5967 -->\n\t<video\n\t\tbind:this={video_source}\n\t\tclass:flip={mirror_webcam}\n\t\tclass:hide={!webcam_accessed}\n\t/>\n\t{#if !webcam_accessed}\n\t\t<div\n\t\t\tin:fade={{ delay: 100, duration: 200 }}\n\t\t\ttitle=\"grant webcam access\"\n\t\t\tstyle=\"height: 100%\"\n\t\t>\n\t\t\t<WebcamPermissions on:click={async () => access_webcam()} />\n\t\t</div>\n\t{:else}\n\t\t<div class=\"button-wrap\">\n\t\t\t<button\n\t\t\t\ton:click={record_video_or_photo}\n\t\t\t\taria-label={mode === \"image\" ? \"capture photo\" : \"start recording\"}\n\t\t\t>\n\t\t\t\t{#if mode === \"video\" || streaming}\n\t\t\t\t\t{#if recording}\n\t\t\t\t\t\t<div class=\"icon red\" title=\"stop recording\">\n\t\t\t\t\t\t\t<Square />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<div class=\"icon red\" title=\"start recording\">\n\t\t\t\t\t\t\t<Circle />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{:else}\n\t\t\t\t\t<div class=\"icon\" title=\"capture photo\">\n\t\t\t\t\t\t<Camera />\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t\t{#if !recording}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"icon\"\n\t\t\t\t\ton:click={() => (options_open = true)}\n\t\t\t\t\taria-label=\"select input source\"\n\t\t\t\t>\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\t\t\t{/if}\n\t\t</div>\n\t\t{#if options_open && selected_device}\n\t\t\t<select\n\t\t\t\tclass=\"select-wrap\"\n\t\t\t\taria-label=\"select source\"\n\t\t\t\tuse:click_outside={handle_click_outside}\n\t\t\t\ton:change={handle_device_change}\n\t\t\t>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"inset-icon\"\n\t\t\t\t\ton:click|stopPropagation={() => (options_open = false)}\n\t\t\t\t>\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\t\t\t\t{#if available_video_devices.length === 0}\n\t\t\t\t\t<option value=\"\">{i18n(\"common.no_devices\")}</option>\n\t\t\t\t{:else}\n\t\t\t\t\t{#each available_video_devices as device}\n\t\t\t\t\t\t<option\n\t\t\t\t\t\t\tvalue={device.deviceId}\n\t\t\t\t\t\t\tselected={selected_device.deviceId === device.deviceId}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{device.label}\n\t\t\t\t\t\t</option>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</select>\n\t\t{/if}\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\tvideo {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: cover;\n\t}\n\n\t.button-wrap {\n\t\tposition: absolute;\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-1-5);\n\t\tdisplay: flex;\n\t\tbottom: var(--size-2);\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--radius-xl);\n\t\tline-height: var(--size-3);\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\tbottom: var(--size-4);\n\t\t}\n\t}\n\n\t@media (--screen-xl) {\n\t\tbutton {\n\t\t\tbottom: var(--size-8);\n\t\t}\n\t}\n\n\t.icon {\n\t\topacity: 0.8;\n\t\twidth: 18px;\n\t\theight: 18px;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.red {\n\t\tfill: red;\n\t\tstroke: red;\n\t}\n\n\t.flip {\n\t\ttransform: scaleX(-1);\n\t}\n\n\t.select-wrap {\n\t\t-webkit-appearance: none;\n\t\t-moz-appearance: none;\n\t\tappearance: none;\n\t\tcolor: var(--button-secondary-text-color);\n\t\tbackground-color: transparent;\n\t\twidth: 95%;\n\t\tfont-size: var(--text-md);\n\t\tposition: absolute;\n\t\tbottom: var(--size-2);\n\t\tbackground-color: var(--block-background-fill);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--radius-xl);\n\t\tz-index: var(--layer-top);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t\tline-height: var(--size-4);\n\t\twhite-space: nowrap;\n\t\ttext-overflow: ellipsis;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\tmax-width: var(--size-52);\n\t}\n\n\t.select-wrap > option {\n\t\tpadding: 0.25rem 0.5rem;\n\t\tborder-bottom: 1px solid var(--border-color-accent);\n\t\tpadding-right: var(--size-8);\n\t\ttext-overflow: ellipsis;\n\t\toverflow: hidden;\n\t}\n\n\t.select-wrap > option:hover {\n\t\tbackground-color: var(--color-accent);\n\t}\n\n\t.select-wrap > option:last-child {\n\t\tborder: none;\n\t}\n\n\t.inset-icon {\n\t\tposition: absolute;\n\t\ttop: 5px;\n\t\tright: -6.5px;\n\t\twidth: var(--size-10);\n\t\theight: var(--size-5);\n\t\topacity: 0.8;\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Clear } from \"@gradio/icons\";\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<div>\n\t<IconButton\n\t\tIcon={Clear}\n\t\tlabel=\"Remove Image\"\n\t\ton:click={(event) => {\n\t\t\tdispatch(\"remove_image\");\n\t\t\tevent.stopPropagation();\n\t\t}}\n\t/>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Image as ImageIcon } from \"@gradio/icons\";\n\timport type { SelectData, I18nFormatter } from \"@gradio/utils\";\n\timport { get_coordinates_of_clicked_image } from \"./utils\";\n\timport Webcam from \"./Webcam.svelte\";\n\n\timport { Upload } from \"@gradio/upload\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport ClearImage from \"./ClearImage.svelte\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\timport Image from \"./Image.svelte\";\n\n\texport let value: null | FileData;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\n\ttype source_type = \"upload\" | \"webcam\" | \"clipboard\" | \"microphone\" | null;\n\n\texport let sources: source_type[] = [\"upload\", \"clipboard\", \"webcam\"];\n\texport let streaming = false;\n\texport let pending = false;\n\texport let mirror_webcam: boolean;\n\texport let selectable = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet upload_input: Upload;\n\tlet uploading = false;\n\texport let active_source: source_type = null;\n\n\tfunction handle_upload({ detail }: CustomEvent<FileData>): void {\n\t\tvalue = detail;\n\t\tdispatch(\"upload\");\n\t}\n\n\tfunction handle_clear(): void {\n\t\tvalue = null;\n\t\tdispatch(\"clear\");\n\t\tdispatch(\"change\", null);\n\t}\n\n\tasync function handle_save(img_blob: Blob | any): Promise<void> {\n\t\tpending = true;\n\t\tconst f = await upload_input.load_files([\n\t\t\tnew File([img_blob], `webcam.png`)\n\t\t]);\n\n\t\tvalue = f?.[0] || null;\n\n\t\tawait tick();\n\n\t\tdispatch(streaming ? \"stream\" : \"change\");\n\t\tpending = false;\n\t}\n\n\t$: active_streaming = streaming && active_source === \"webcam\";\n\t$: if (uploading && !active_streaming) value = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange?: never;\n\t\tstream?: never;\n\t\tclear?: never;\n\t\tdrag: boolean;\n\t\tupload?: never;\n\t\tselect: SelectData;\n\t}>();\n\n\texport let dragging = false;\n\n\t$: dispatch(\"drag\", dragging);\n\n\tfunction handle_click(evt: MouseEvent): void {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t}\n\n\t$: if (!active_source && sources) {\n\t\tactive_source = sources[0];\n\t}\n\n\tasync function handle_select_source(\n\t\tsource: (typeof sources)[number]\n\t): Promise<void> {\n\t\tswitch (source) {\n\t\t\tcase \"clipboard\":\n\t\t\t\tupload_input.paste_clipboard();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n</script>\n\n<BlockLabel {show_label} Icon={ImageIcon} label={label || \"Image\"} />\n\n<div data-testid=\"image\" class=\"image-container\">\n\t{#if value?.url && !active_streaming}\n\t\t<ClearImage\n\t\t\ton:remove_image={() => {\n\t\t\t\tvalue = null;\n\t\t\t\tdispatch(\"clear\");\n\t\t\t}}\n\t\t/>\n\t{/if}\n\t<div\n\t\tclass=\"upload-container\"\n\t\tclass:reduced-height={sources.length > 1}\n\t\tstyle:width={value ? \"auto\" : \"100%\"}\n\t>\n\t\t<Upload\n\t\t\thidden={value !== null || active_source === \"webcam\"}\n\t\t\tbind:this={upload_input}\n\t\t\tbind:uploading\n\t\t\tbind:dragging\n\t\t\tfiletype={active_source === \"clipboard\" ? \"clipboard\" : \"image/*\"}\n\t\t\ton:load={handle_upload}\n\t\t\ton:error\n\t\t\t{root}\n\t\t\t{max_file_size}\n\t\t\tdisable_click={!sources.includes(\"upload\") || value !== null}\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t>\n\t\t\t{#if value === null}\n\t\t\t\t<slot />\n\t\t\t{/if}\n\t\t</Upload>\n\t\t{#if active_source === \"webcam\" && (streaming || (!streaming && !value))}\n\t\t\t<Webcam\n\t\t\t\t{root}\n\t\t\t\ton:capture={(e) => handle_save(e.detail)}\n\t\t\t\ton:stream={(e) => handle_save(e.detail)}\n\t\t\t\ton:error\n\t\t\t\ton:drag\n\t\t\t\ton:upload={(e) => handle_save(e.detail)}\n\t\t\t\t{mirror_webcam}\n\t\t\t\t{streaming}\n\t\t\t\tmode=\"image\"\n\t\t\t\tinclude_audio={false}\n\t\t\t\t{i18n}\n\t\t\t\t{upload}\n\t\t\t/>\n\t\t{:else if value !== null && !streaming}\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t<div class:selectable class=\"image-frame\" on:click={handle_click}>\n\t\t\t\t<Image src={value.url} alt={value.alt_text} />\n\t\t\t</div>\n\t\t{/if}\n\t</div>\n\t{#if sources.length > 1 || sources.includes(\"clipboard\")}\n\t\t<SelectSource\n\t\t\t{sources}\n\t\t\tbind:active_source\n\t\t\t{handle_clear}\n\t\t\thandle_select={handle_select_source}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.image-frame :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: scale-down;\n\t}\n\n\t.image-frame {\n\t\tobject-fit: cover;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.upload-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\theight: 100%;\n\t\tflex-shrink: 1;\n\t\tmax-height: 100%;\n\t}\n\n\t.reduced-height {\n\t\theight: calc(100% - var(--size-10));\n\t}\n\n\t.image-container {\n\t\tdisplay: flex;\n\t\theight: 100%;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmax-height: 100%;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "circle", "rect", "createEventDispatcher", "button", "div", "span", "dispatch", "get_devices", "set_local_stream", "local_stream", "video_source", "get_video_stream", "include_audio", "device_id", "size", "constraints", "set_available_devices", "devices", "device", "onMount", "ctx", "create_if_block_3", "if_block2", "create_if_block_1", "attr", "button_aria_label_value", "current", "dirty", "div_intro", "create_in_transition", "fade", "create_if_block_2", "select", "i", "t_value", "option", "set_data", "t", "t0_value", "option_value_value", "option_selected_value", "t0", "video", "click_outside", "node", "cb", "handle_click", "event", "available_video_devices", "selected_device", "canvas", "streaming", "$$props", "pending", "root", "mode", "mirror_webcam", "i18n", "upload", "handle_device_change", "stream", "$$invalidate", "options_open", "access_webcam", "webcam_accessed", "used_devices", "track", "err", "take_picture", "context", "recording", "blob", "recorded_blobs", "mimeType", "media_recorder", "take_recording", "video_blob", "ReaderObj", "e", "_video_blob", "val", "prepare_files", "value", "validMimeTypes", "validMimeType", "record_video_or_photo", "handle_click_outside", "$$value", "click_handler_1", "click_handler_2", "Clear", "tick", "if_block", "image_changes", "ImageIcon", "create_if_block_4", "div1", "div0", "blocklabel_changes", "upload_1_changes", "label", "show_label", "sources", "selectable", "max_file_size", "stream_handler", "upload_input", "uploading", "active_source", "handle_upload", "detail", "handle_clear", "handle_save", "img_blob", "f", "dragging", "evt", "coordinates", "get_coordinates_of_clicked_image", "handle_select_source", "source", "capture_handler", "stream_handler_1", "upload_handler", "active_streaming"], "mappings": "0oCAAAA,GAgBKC,EAAAC,EAAAC,CAAA,EAJJC,GAECF,EAAAG,CAAA,EACDD,GAA+BF,EAAAI,CAAA,qlBCfhCN,GAWKC,EAAAC,EAAAC,CAAA,EADJC,GAAgCF,EAAAI,CAAA,koBCVjCN,GAWKC,EAAAC,EAAAC,CAAA,EADJC,GAAwDF,EAAAK,CAAA,6XCR/C,CAAAC,sBAAAA,WAAqC,0DAY5C,uQALHR,GAOQC,EAAAQ,EAAAN,CAAA,EANPC,GAKKK,EAAAC,CAAA,EAJJN,GAEMM,EAAAC,CAAA,0LATD,MAAAC,EAAWJ,cAK0B,IAAAI,EAAS,OAAO,sECTrD,SAASC,IAA0C,CAClD,OAAA,UAAU,aAAa,kBAC/B,CAMgB,SAAAC,GACfC,EACAC,EACO,CACPA,EAAa,UAAYD,EACzBC,EAAa,MAAQ,GACrBA,EAAa,KAAK,CACnB,CAEsB,eAAAC,GACrBC,EACAF,EACAG,EACuB,CACvB,MAAMC,EAAO,CACZ,MAAO,CAAE,MAAO,IAAK,EACrB,OAAQ,CAAE,MAAO,IAAK,CAAA,EAGjBC,EAAc,CACnB,MAAOF,EAAY,CAAE,SAAU,CAAE,MAAOA,CAAU,EAAG,GAAGC,CAAA,EAASA,EACjE,MAAOF,CAAA,EAGR,OAAO,UAAU,aACf,aAAaG,CAAW,EACxB,KAAMN,IACND,GAAiBC,EAAcC,CAAY,EACpCD,EACP,CACH,CAEO,SAASO,GACfC,EACoB,CAKb,OAJSA,EAAQ,OACtBC,GAA4BA,EAAO,OAAS,YAAA,CAI/C,miBC/C+B,QAAAC,IAAS,OAAgB,yKAuP/CC,EAAI,CAAA,IAAK,SAAWA,EAAS,CAAA,EAAA,iCAgB7BA,EAAS,CAAA,GAAAC,GAAAD,CAAA,EAUXE,EAAAF,OAAgBA,EAAe,CAAA,GAAAG,GAAAH,CAAA,mFA5BtBI,EAAArB,EAAA,aAAAsB,EAAAL,OAAS,QAAU,gBAAkB,iBAAiB,kFAHpE1B,EA8BKC,EAAAS,EAAAP,CAAA,EA7BJC,EAmBQM,EAAAD,CAAA,6FAlBGiB,EAAqB,EAAA,CAAA,sJACnB,CAAAM,GAAAC,EAAA,CAAA,EAAA,GAAAF,KAAAA,EAAAL,OAAS,QAAU,gBAAkB,yCAkB5CA,EAAS,CAAA,wGAUXA,OAAgBA,EAAe,CAAA,iZAvCpC1B,EAMKC,EAAAS,EAAAP,CAAA,oEALO+B,EAAAC,GAAAzB,EAAA0B,GAAA,CAAA,MAAO,IAAK,SAAU,GAAG,CAAA,8NAuBlCpC,EAEKC,EAAAS,EAAAP,CAAA,qLAZAuB,EAAS,CAAA,EAAA,kbAKb1B,EAEKC,EAAAS,EAAAP,CAAA,oQANLH,EAEKC,EAAAS,EAAAP,CAAA,iRAaPH,EAMQC,EAAAQ,EAAAN,CAAA,4NAgBHuB,EAAuB,CAAA,EAAC,SAAW,EAACW,0NAZ1CrC,EAwBQC,EAAAqC,EAAAnC,CAAA,EAlBPC,EAKQkC,EAAA7B,CAAA,uFARWiB,EAAoB,EAAA,CAAA,CAAA,gBAC5BA,EAAoB,EAAA,CAAA,kNAWvBA,EAAuB,CAAA,CAAA,uBAA5B,OAAIa,GAAA,qKAACb,EAAuB,CAAA,CAAA,oBAA5B,OAAIa,GAAA,EAAA,2HAAJ,qDAFgBC,EAAAd,KAAK,mBAAmB,EAAA,yGAA1C1B,EAAoDC,EAAAwC,EAAAtC,CAAA,iBAAlC8B,EAAA,CAAA,EAAA,GAAAO,KAAAA,EAAAd,KAAK,mBAAmB,EAAA,KAAAgB,GAAAC,EAAAH,CAAA,uCAOvCI,EAAAlB,MAAO,MAAK,mDAHNe,EAAA,QAAAI,EAAAnB,MAAO,yBACJe,EAAA,SAAAK,EAAApB,EAAgB,CAAA,EAAA,WAAaA,MAAO,+CAF/C1B,EAKQC,EAAAwC,EAAAtC,CAAA,wBADN8B,EAAA,CAAA,EAAA,IAAAW,KAAAA,EAAAlB,MAAO,MAAK,KAAAgB,GAAAK,EAAAH,CAAA,EAHNX,EAAA,CAAA,EAAA,IAAAY,KAAAA,EAAAnB,MAAO,yCACJO,EAAA,CAAA,EAAA,KAAAa,KAAAA,EAAApB,EAAgB,CAAA,EAAA,WAAaA,MAAO,qHA3D9CA,EAAe,CAAA,IAAA,mHAHRA,EAAa,CAAA,CAAA,eACZA,EAAe,CAAA,CAAA,6CAN9B1B,EA4EKC,EAAAS,EAAAP,CAAA,EAzEJC,EAICM,EAAAsC,CAAA,wEAFYtB,EAAa,CAAA,CAAA,+BACZA,EAAe,CAAA,CAAA,0NAjCbuB,GAAcC,EAAYC,EAAO,CAC1C,MAAAC,EAAgBC,GAAiB,CAErCH,GAAI,CACHA,EAAK,SAASG,EAAM,MAAc,GAAA,CAClCA,EAAM,kBAEPF,EAAGE,CAAK,GAIV,gBAAS,iBAAiB,QAASD,EAAc,EAAI,GAGpD,SAAO,CACN,SAAS,oBAAoB,QAASA,EAAc,EAAI,2BAzMvDpC,EACAsC,EAAuB,CAAA,EACvBC,EAA0C,KAE1CC,EACO,CAAA,UAAAC,EAAY,EAAK,EAAAC,EACjB,CAAA,QAAAC,EAAU,EAAK,EAAAD,EACf,CAAA,KAAAE,EAAO,EAAE,EAAAF,EAET,CAAA,KAAAG,EAA0B,OAAO,EAAAH,GACjC,cAAAI,CAAsB,EAAAJ,GACtB,cAAAxC,CAAsB,EAAAwC,GACtB,KAAAK,CAAmB,EAAAL,GACnB,OAAAM,CAAwB,EAAAN,EAE7B,MAAA9C,EAAWJ,KAQjBiB,OAAe+B,EAAS,SAAS,cAAc,QAAQ,CAAA,EAEjD,MAAAS,QAA8BZ,GAAiB,OAE9ClC,EADSkC,EAAM,OACI,YAEnBpC,GAAiBC,EAAeF,EAAcG,CAAS,EAAE,KAAI,MAC3DJ,GAAY,CAClBmD,EAASnD,EACToD,EAAA,EAAAZ,EACCD,EAAwB,KACtB9B,GAAWA,EAAO,WAAaL,CAAS,GACrC,IAAI,EACVgD,EAAA,GAAAC,EAAe,EAAK,oBAKRC,GAAa,KAE1BpD,GAAiBC,EAAeF,CAAY,EAC1C,WAAYD,GAAY,CACxBoD,EAAA,EAAAG,EAAkB,EAAI,EACtBH,EAAA,EAAAb,QAAgCzC,GAAW,CAAA,EAC3CqD,EAASnD,CAET,CAAA,EAAA,SAAWO,GAAsBgC,CAAuB,CACxD,EAAA,KAAM/B,GAAO,CACb4C,EAAA,EAAAb,EAA0B/B,CAAO,EAE3B,MAAAgD,EAAeL,EACnB,UAAS,EACT,IAAKM,GAAUA,EAAM,YAAe,GAAA,QAAQ,EAAE,CAAC,EAEjDL,EAAA,EAAAZ,EAAkBgB,GACfhD,EAAQ,KAAMC,GAAWA,EAAO,WAAa+C,CAAY,GAC1DjB,EAAwB,CAAC,CACC,KAGzB,CAAA,UAAU,cAAY,CAAK,UAAU,aAAa,eACtD1C,EAAS,QAASmD,EAAK,yBAAyB,CAAA,QAEzCU,EAAG,CACP,GAAAA,aAAe,cAAgBA,EAAI,MAAQ,kBAC9C7D,EAAS,QAASmD,EAAK,2BAA2B,CAAA,aAE5CU,YAKAC,GAAY,CAChB,IAAAC,EAAUnB,EAAO,WAAW,IAAI,IAEjCC,GAAcA,GAAamB,IAC7B5D,EAAa,YACbA,EAAa,cAEbwC,EAAO,MAAQxC,EAAa,WAC5BwC,EAAO,OAASxC,EAAa,YAC7B2D,EAAQ,UACP3D,EACA,EACA,EACAA,EAAa,WACbA,EAAa,WAAW,EAGrB8C,IACHa,EAAQ,MAAO,GAAG,CAAC,EACnBA,EAAQ,UAAU3D,GAAeA,EAAa,WAAY,CAAC,GAG5DwC,EAAO,OACLqB,GAAI,CACJjE,EAAS6C,EAAY,SAAW,UAAWoB,CAAI,GAEhD,YACA,KAKC,IAAAD,EAAY,GACZE,EAAc,CAAA,EACdZ,EACAa,EACAC,WAEKC,GAAc,IAClBL,EAAS,CACZI,EAAe,KAAI,EACf,IAAAE,MAAiB,KAAKJ,EAAkB,CAAA,KAAMC,CAAQ,CAAA,EACtDI,MAAgB,WACpBA,EAAU,OAAM,eAAmBC,EAAC,CAC/B,GAAAA,EAAE,OAAM,KACPC,EAAW,IAAO,KAAI,CACxBH,CAAU,EACX,UAAYH,EAAS,UAAU,CAAC,CAAA,QAE3BO,EAAG,MAASC,GAAa,CAAEF,CAAW,CAAA,MACxCG,IAAK,MACDxB,EAAOsB,EAAK1B,CAAI,IAAI,OAAO,OAAO,EACxC,CAAC,EACHhD,EAAS,UAAW4E,EAAK,EACzB5E,EAAS,gBAAgB,IAG3BuE,EAAU,cAAcD,CAAU,OAElCtE,EAAS,iBAAiB,EAC1BkE,EAAc,CAAA,MACVW,EAAc,CAAI,aAAc,WAAW,EACtC,QAAAC,KAAiBD,KACrB,cAAc,gBAAgBC,CAAa,EAAA,CAC9CX,EAAWW,QAIT,GAAAX,IAAa,KAAI,CACpB,QAAQ,MAAM,qCAAqC,SAGpDC,EAAqB,IAAA,cAAcd,EACxB,CAAA,SAAAa,CAAA,CAAA,EAEXC,EAAe,iBAAiB,yBAA2BI,EAAC,CAC3DN,EAAe,KAAKM,EAAE,IAAI,IAE3BJ,EAAe,MAAM,GAAG,EAEzBb,EAAA,EAAAS,GAAaA,CAAS,EAGnB,IAAAN,EAAkB,YAEbqB,GAAqB,CACzB9B,IAAS,SAAWJ,GACvBU,EAAA,EAAAS,GAAaA,CAAS,EAEnBf,IAAS,QACZa,IAEAO,IAEI,CAAAL,GAAaV,IACjBA,EAAO,UAAS,EAAG,QAASM,GAAUA,EAAM,KAAI,CAAA,MAChDxD,EAAa,UAAY,KAAIA,CAAA,EAC7BmD,EAAA,EAAAG,EAAkB,EAAK,GAIrBb,GAAaI,IAAS,SACzB,OAAO,iBACF7C,IAAiB2C,GACpBe,KAEC,KAGA,IAAAN,EAAe,GAsBV,SAAAwB,GAAqBvC,EAAiB,CAC9CA,EAAM,eAAc,EACpBA,EAAM,gBAAe,EACrBc,EAAA,GAAAC,EAAe,EAAK,6CAQTpD,EAAY6E,6BAUmBxB,IA2BtByB,GAAA,IAAA3B,EAAA,GAAAC,EAAe,EAAI,EAgBH2B,GAAA,IAAA5B,EAAA,GAAAC,EAAe,EAAK,g6CC1RhD,uBAAA5D,EAAA,SAAqC,iFASvCwF,GAAK,MAAA,cAAA,iGAFbhG,GASKC,EAAAS,EAAAP,CAAA,kIAZE,MAAAS,EAAWJ,cAOL6C,GAAK,CACfzC,EAAS,cAAc,EACvByC,EAAM,gBAAe,2jBCbO,KAAA4C,IAAM,OAAgB,qfAiI7CC,EAAAxE,OAAU,MAAIC,GAAAD,CAAA,wEAAdA,OAAU,sOAuBF,IAAAA,KAAM,IAAU,IAAAA,KAAM,yHADnC1B,GAEKC,EAAAS,EAAAP,CAAA,sCAF+CuB,EAAY,EAAA,CAAA,2BACnDO,EAAA,CAAA,EAAA,IAAAkE,EAAA,IAAAzE,KAAM,KAAUO,EAAA,CAAA,EAAA,IAAAkE,EAAA,IAAAzE,KAAM,kRARnB,mhBAiBDA,EAAoB,EAAA,8XALhCA,EAAO,CAAA,EAAC,OAAS,GAAKA,EAAO,CAAA,EAAC,SAAS,WAAW,0CAzDzB0E,GAAkB,MAAA1E,MAAS,iBAGpDA,EAAK,CAAA,GAAE,KAAG,CAAKA,EAAgB,EAAA,GAAA2E,GAAA3E,CAAA,uDAc1B,OAAAA,EAAU,CAAA,IAAA,MAAQA,OAAkB,SAIlC,SAAAA,OAAkB,YAAc,YAAc,wCAKxC,cAAA,CAAAA,KAAQ,SAAS,QAAQ,GAAKA,OAAU,+PAJ/CA,EAAa,EAAA,CAAA,4DAYlB,OAAAA,OAAkB,WAAaA,EAAe,CAAA,GAAA,CAAAA,OAAcA,EAAK,CAAA,GAAA,EAe5DA,EAAK,CAAA,IAAK,MAAI,CAAKA,EAAS,CAAA,EAAA,kPApChBA,EAAO,CAAA,EAAC,OAAS,CAAC,eAC3BA,EAAK,CAAA,EAAG,OAAS,MAAM,wGAZtC1B,GA+DKC,EAAAqG,EAAAnG,CAAA,yBAtDJC,GA6CKkG,EAAAC,CAAA,qHAxD2CtE,EAAA,CAAA,EAAA,IAAAuE,EAAA,MAAA9E,MAAS,mBAGpDA,EAAK,CAAA,GAAE,KAAG,CAAKA,EAAgB,EAAA,kHAc1BO,EAAA,CAAA,EAAA,IAAAwE,EAAA,OAAA/E,EAAU,CAAA,IAAA,MAAQA,OAAkB,UAIlCO,EAAA,CAAA,EAAA,IAAAwE,EAAA,SAAA/E,OAAkB,YAAc,YAAc,sEAKxCO,EAAA,CAAA,EAAA,KAAAwE,EAAA,cAAA,CAAA/E,KAAQ,SAAS,QAAQ,GAAKA,OAAU,iaAbnCA,EAAO,CAAA,EAAC,OAAS,CAAC,uBAC3BA,EAAK,CAAA,EAAG,OAAS,MAAM,cA2ChCA,EAAO,CAAA,EAAC,OAAS,GAAKA,EAAO,CAAA,EAAC,SAAS,WAAW,oXA/I5C,MAAA8D,CAAsB,EAAA9B,EACtB,CAAA,MAAAgD,EAA4B,MAAS,EAAAhD,GACrC,WAAAiD,CAAmB,EAAAjD,EAInB,CAAA,QAAAkD,EAA0B,CAAA,SAAU,YAAa,QAAQ,CAAA,EAAAlD,EACzD,CAAA,UAAAD,EAAY,EAAK,EAAAC,EACjB,CAAA,QAAAC,EAAU,EAAK,EAAAD,GACf,cAAAI,CAAsB,EAAAJ,EACtB,CAAA,WAAAmD,EAAa,EAAK,EAAAnD,GAClB,KAAAE,CAAY,EAAAF,GACZ,KAAAK,CAAmB,EAAAL,EACnB,CAAA,cAAAoD,EAA+B,IAAI,EAAApD,GACnC,OAAAM,CAAwB,EAAAN,GACxB,eAAAqD,CAAgC,EAAArD,EAEvCsD,EACAC,EAAY,GACL,CAAA,cAAAC,EAA6B,IAAI,EAAAxD,EAEnC,SAAAyD,GAAgB,OAAAC,GAAM,CAC9BjD,EAAA,EAAAqB,EAAQ4B,CAAM,EACdxG,EAAS,QAAQ,WAGTyG,GAAY,CACpBlD,EAAA,EAAAqB,EAAQ,IAAI,EACZ5E,EAAS,OAAO,EAChBA,EAAS,SAAU,IAAI,EAGT,eAAA0G,EAAYC,EAAoB,CAC9CpD,EAAA,GAAAR,EAAU,EAAI,EACR,MAAA6D,SAAUR,EAAa,WACxB,CAAA,IAAA,MAAMO,CAAQ,EAAA,YAAA,CAAA,CAAA,EAGnBpD,EAAA,EAAAqB,EAAQgC,KAAI,CAAC,GAAK,IAAI,QAEhBvB,GAAI,EAEVrF,EAAS6C,EAAY,SAAW,QAAQ,EACxCU,EAAA,GAAAR,EAAU,EAAK,EAMV,MAAA/C,EAAWJ,KASN,GAAA,CAAA,SAAAiH,EAAW,EAAK,EAAA/D,EAIlB,SAAAN,EAAasE,EAAe,KAChCC,GAAcC,GAAiCF,CAAG,EAClDC,IACH/G,EAAS,SAAY,CAAA,MAAO+G,GAAa,MAAO,IAAI,CAAA,EAQvC,eAAAE,GACdC,EAAgC,QAExBA,EAAM,KACR,YACJd,EAAa,gBAAe,uBAc5B7C,EAAA,EAAAqB,EAAQ,IAAI,EACZ5E,EAAS,OAAO,8CAWNoG,EAAYnB,iGAmBT,MAAAkC,EAAA3C,GAAMkC,EAAYlC,EAAE,MAAM,EAC3B4C,EAAA5C,GAAMkC,EAAYlC,EAAE,MAAM,kEAG1B,MAAA6C,GAAA7C,GAAMkC,EAAYlC,EAAE,MAAM,8oBA1DxC,CAAO8B,GAAiBN,OACxBM,EAAgBN,EAAQ,CAAC,CAAA,yBAxBvBsB,EAAmBzE,GAAayD,IAAkB,QAAQ,uBACtDD,GAAS,CAAKiB,GAAkB/D,EAAA,EAAAqB,EAAQ,IAAI,mBAahD5E,EAAS,OAAQ6G,CAAQ"}