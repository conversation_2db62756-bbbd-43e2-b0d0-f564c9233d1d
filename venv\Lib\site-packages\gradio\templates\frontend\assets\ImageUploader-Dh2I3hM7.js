import{I as xe,C as et}from"./Index-DB1XLvMK.js";import{B as tt}from"./BlockLabel-BlSr62f_.js";import{I as nt}from"./Image-Bsh8Umrh.js";import{W as lt,S as it}from"./SelectSource-C3FIO9My.js";import{g as rt}from"./utils-Gtzs_Zla.js";import{D as Qe}from"./DropdownArrow-B_jYsAai.js";import{b as ot}from"./index-BQPjLIsY.js";import{f as at}from"./Button-BIUaXfcG.js";import{U as st}from"./Upload-1-QDTAlg.js";/* empty css                                                   */import{I as ct}from"./Image-CJc3fwmN.js";const{SvelteComponent:ut,append:Ue,attr:U,detach:_t,init:ft,insert:dt,noop:$e,safe_not_equal:mt,svg_element:Ie}=window.__gradio__svelte__internal;function gt(i){let e,t,n;return{c(){e=Ie("svg"),t=Ie("path"),n=Ie("circle"),U(t,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),U(n,"cx","12"),U(n,"cy","13"),U(n,"r","4"),U(e,"xmlns","http://www.w3.org/2000/svg"),U(e,"width","100%"),U(e,"height","100%"),U(e,"viewBox","0 0 24 24"),U(e,"fill","none"),U(e,"stroke","currentColor"),U(e,"stroke-width","1.5"),U(e,"stroke-linecap","round"),U(e,"stroke-linejoin","round"),U(e,"class","feather feather-camera")},m(l,r){dt(l,e,r),Ue(e,t),Ue(e,n)},p:$e,i:$e,o:$e,d(l){l&&_t(e)}}}class ht extends ut{constructor(e){super(),ft(this,e,null,gt,mt,{})}}const{SvelteComponent:pt,append:bt,attr:O,detach:vt,init:wt,insert:kt,noop:ye,safe_not_equal:$t,svg_element:je}=window.__gradio__svelte__internal;function It(i){let e,t;return{c(){e=je("svg"),t=je("circle"),O(t,"cx","12"),O(t,"cy","12"),O(t,"r","10"),O(e,"xmlns","http://www.w3.org/2000/svg"),O(e,"width","100%"),O(e,"height","100%"),O(e,"viewBox","0 0 24 24"),O(e,"stroke-width","1.5"),O(e,"stroke-linecap","round"),O(e,"stroke-linejoin","round"),O(e,"class","feather feather-circle")},m(n,l){kt(n,e,l),bt(e,t)},p:ye,i:ye,o:ye,d(n){n&&vt(e)}}}class yt extends pt{constructor(e){super(),wt(this,e,null,It,$t,{})}}const{SvelteComponent:Ct,append:qt,attr:j,detach:St,init:Dt,insert:Et,noop:Ce,safe_not_equal:Wt,svg_element:Ne}=window.__gradio__svelte__internal;function zt(i){let e,t;return{c(){e=Ne("svg"),t=Ne("rect"),j(t,"x","3"),j(t,"y","3"),j(t,"width","18"),j(t,"height","18"),j(t,"rx","2"),j(t,"ry","2"),j(e,"xmlns","http://www.w3.org/2000/svg"),j(e,"width","100%"),j(e,"height","100%"),j(e,"viewBox","0 0 24 24"),j(e,"stroke-width","1.5"),j(e,"stroke-linecap","round"),j(e,"stroke-linejoin","round"),j(e,"class","feather feather-square")},m(n,l){Et(n,e,l),qt(e,t)},p:Ce,i:Ce,o:Ce,d(n){n&&St(e)}}}class Mt extends Ct{constructor(e){super(),Dt(this,e,null,zt,Wt,{})}}const{SvelteComponent:Bt,append:ie,attr:qe,create_component:Rt,destroy_component:Tt,detach:Ut,element:Se,init:jt,insert:Nt,listen:Lt,mount_component:At,noop:Ht,safe_not_equal:Ot,set_style:Pt,space:Ft,text:Vt,transition_in:Gt,transition_out:Jt}=window.__gradio__svelte__internal,{createEventDispatcher:Kt}=window.__gradio__svelte__internal;function Qt(i){let e,t,n,l,r,o="Click to Access Webcam",f,c,g,a;return l=new lt({}),{c(){e=Se("button"),t=Se("div"),n=Se("span"),Rt(l.$$.fragment),r=Ft(),f=Vt(o),qe(n,"class","icon-wrap svelte-qbrfs"),qe(t,"class","wrap svelte-qbrfs"),qe(e,"class","svelte-qbrfs"),Pt(e,"height","100%")},m(_,w){Nt(_,e,w),ie(e,t),ie(t,n),At(l,n,null),ie(t,r),ie(t,f),c=!0,g||(a=Lt(e,"click",i[1]),g=!0)},p:Ht,i(_){c||(Gt(l.$$.fragment,_),c=!0)},o(_){Jt(l.$$.fragment,_),c=!1},d(_){_&&Ut(e),Tt(l),g=!1,a()}}}function Xt(i){const e=Kt();return[e,()=>e("click")]}class Yt extends Bt{constructor(e){super(),jt(this,e,Xt,Qt,Ot,{})}}function Zt(){return navigator.mediaDevices.enumerateDevices()}function xt(i,e){e.srcObject=i,e.muted=!0,e.play()}async function Le(i,e,t){const n={width:{ideal:1920},height:{ideal:1440}},l={video:t?{deviceId:{exact:t},...n}:n,audio:i};return navigator.mediaDevices.getUserMedia(l).then(r=>(xt(r,e),r))}function en(i){return i.filter(t=>t.kind==="videoinput")}const{SvelteComponent:tn,action_destroyer:nn,add_render_callback:ln,append:F,attr:y,binding_callbacks:rn,check_outros:ee,create_component:Q,create_in_transition:on,destroy_component:X,destroy_each:an,detach:N,element:A,empty:Be,ensure_array_like:Ae,flush:V,group_outros:te,init:sn,insert:L,listen:_e,mount_component:Y,noop:Re,run_all:cn,safe_not_equal:un,set_data:Xe,set_input_value:Ee,set_style:_n,space:ne,stop_propagation:fn,text:Ye,toggle_class:re,transition_in:S,transition_out:z}=window.__gradio__svelte__internal,{createEventDispatcher:dn,onMount:mn}=window.__gradio__svelte__internal;function He(i,e,t){const n=i.slice();return n[31]=e[t],n}function gn(i){let e,t,n,l,r,o,f,c,g,a,_;const w=[bn,pn],I=[];function M(p,k){return p[1]==="video"||p[0]?0:1}n=M(i),l=I[n]=w[n](i);let d=!i[8]&&Oe(i),v=i[10]&&i[7]&&Pe(i);return{c(){e=A("div"),t=A("button"),l.c(),o=ne(),d&&d.c(),f=ne(),v&&v.c(),c=Be(),y(t,"aria-label",r=i[1]==="image"?"capture photo":"start recording"),y(t,"class","svelte-1aa1mud"),y(e,"class","button-wrap svelte-1aa1mud")},m(p,k){L(p,e,k),F(e,t),I[n].m(t,null),F(e,o),d&&d.m(e,null),L(p,f,k),v&&v.m(p,k),L(p,c,k),g=!0,a||(_=_e(t,"click",i[13]),a=!0)},p(p,k){let $=n;n=M(p),n===$?I[n].p(p,k):(te(),z(I[$],1,1,()=>{I[$]=null}),ee(),l=I[n],l?l.p(p,k):(l=I[n]=w[n](p),l.c()),S(l,1),l.m(t,null)),(!g||k[0]&2&&r!==(r=p[1]==="image"?"capture photo":"start recording"))&&y(t,"aria-label",r),p[8]?d&&(te(),z(d,1,1,()=>{d=null}),ee()):d?(d.p(p,k),k[0]&256&&S(d,1)):(d=Oe(p),d.c(),S(d,1),d.m(e,null)),p[10]&&p[7]?v?(v.p(p,k),k[0]&1152&&S(v,1)):(v=Pe(p),v.c(),S(v,1),v.m(c.parentNode,c)):v&&(te(),z(v,1,1,()=>{v=null}),ee())},i(p){g||(S(l),S(d),S(v),g=!0)},o(p){z(l),z(d),z(v),g=!1},d(p){p&&(N(e),N(f),N(c)),I[n].d(),d&&d.d(),v&&v.d(p),a=!1,_()}}}function hn(i){let e,t,n,l;return t=new Yt({}),t.$on("click",i[20]),{c(){e=A("div"),Q(t.$$.fragment),y(e,"title","grant webcam access"),_n(e,"height","100%")},m(r,o){L(r,e,o),Y(t,e,null),l=!0},p:Re,i(r){l||(S(t.$$.fragment,r),r&&(n||ln(()=>{n=on(e,at,{delay:100,duration:200}),n.start()})),l=!0)},o(r){z(t.$$.fragment,r),l=!1},d(r){r&&N(e),X(t)}}}function pn(i){let e,t,n;return t=new ht({}),{c(){e=A("div"),Q(t.$$.fragment),y(e,"class","icon svelte-1aa1mud"),y(e,"title","capture photo")},m(l,r){L(l,e,r),Y(t,e,null),n=!0},p:Re,i(l){n||(S(t.$$.fragment,l),n=!0)},o(l){z(t.$$.fragment,l),n=!1},d(l){l&&N(e),X(t)}}}function bn(i){let e,t,n,l;const r=[wn,vn],o=[];function f(c,g){return c[8]?0:1}return e=f(i),t=o[e]=r[e](i),{c(){t.c(),n=Be()},m(c,g){o[e].m(c,g),L(c,n,g),l=!0},p(c,g){let a=e;e=f(c),e!==a&&(te(),z(o[a],1,1,()=>{o[a]=null}),ee(),t=o[e],t||(t=o[e]=r[e](c),t.c()),S(t,1),t.m(n.parentNode,n))},i(c){l||(S(t),l=!0)},o(c){z(t),l=!1},d(c){c&&N(n),o[e].d(c)}}}function vn(i){let e,t,n;return t=new yt({}),{c(){e=A("div"),Q(t.$$.fragment),y(e,"class","icon red svelte-1aa1mud"),y(e,"title","start recording")},m(l,r){L(l,e,r),Y(t,e,null),n=!0},i(l){n||(S(t.$$.fragment,l),n=!0)},o(l){z(t.$$.fragment,l),n=!1},d(l){l&&N(e),X(t)}}}function wn(i){let e,t,n;return t=new Mt({}),{c(){e=A("div"),Q(t.$$.fragment),y(e,"class","icon red svelte-1aa1mud"),y(e,"title","stop recording")},m(l,r){L(l,e,r),Y(t,e,null),n=!0},i(l){n||(S(t.$$.fragment,l),n=!0)},o(l){z(t.$$.fragment,l),n=!1},d(l){l&&N(e),X(t)}}}function Oe(i){let e,t,n,l,r;return t=new Qe({}),{c(){e=A("button"),Q(t.$$.fragment),y(e,"class","icon svelte-1aa1mud"),y(e,"aria-label","select input source")},m(o,f){L(o,e,f),Y(t,e,null),n=!0,l||(r=_e(e,"click",i[21]),l=!0)},p:Re,i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){z(t.$$.fragment,o),n=!1},d(o){o&&N(e),X(t),l=!1,r()}}}function Pe(i){let e,t,n,l,r,o,f;n=new Qe({});function c(_,w){return _[6].length===0?$n:kn}let g=c(i),a=g(i);return{c(){e=A("select"),t=A("button"),Q(n.$$.fragment),l=ne(),a.c(),y(t,"class","inset-icon svelte-1aa1mud"),y(e,"class","select-wrap svelte-1aa1mud"),y(e,"aria-label","select source")},m(_,w){L(_,e,w),F(e,t),Y(n,t,null),F(t,l),a.m(e,null),r=!0,o||(f=[_e(t,"click",fn(i[22])),nn(Te.call(null,e,i[14])),_e(e,"change",i[11])],o=!0)},p(_,w){g===(g=c(_))&&a?a.p(_,w):(a.d(1),a=g(_),a&&(a.c(),a.m(e,null)))},i(_){r||(S(n.$$.fragment,_),r=!0)},o(_){z(n.$$.fragment,_),r=!1},d(_){_&&N(e),X(n),a.d(),o=!1,cn(f)}}}function kn(i){let e,t=Ae(i[6]),n=[];for(let l=0;l<t.length;l+=1)n[l]=Fe(He(i,t,l));return{c(){for(let l=0;l<n.length;l+=1)n[l].c();e=Be()},m(l,r){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(l,r);L(l,e,r)},p(l,r){if(r[0]&192){t=Ae(l[6]);let o;for(o=0;o<t.length;o+=1){const f=He(l,t,o);n[o]?n[o].p(f,r):(n[o]=Fe(f),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(l){l&&N(e),an(n,l)}}}function $n(i){let e,t=i[3]("common.no_devices")+"",n;return{c(){e=A("option"),n=Ye(t),e.__value="",Ee(e,e.__value),y(e,"class","svelte-1aa1mud")},m(l,r){L(l,e,r),F(e,n)},p(l,r){r[0]&8&&t!==(t=l[3]("common.no_devices")+"")&&Xe(n,t)},d(l){l&&N(e)}}}function Fe(i){let e,t=i[31].label+"",n,l,r,o;return{c(){e=A("option"),n=Ye(t),l=ne(),e.__value=r=i[31].deviceId,Ee(e,e.__value),e.selected=o=i[7].deviceId===i[31].deviceId,y(e,"class","svelte-1aa1mud")},m(f,c){L(f,e,c),F(e,n),F(e,l)},p(f,c){c[0]&64&&t!==(t=f[31].label+"")&&Xe(n,t),c[0]&64&&r!==(r=f[31].deviceId)&&(e.__value=r,Ee(e,e.__value)),c[0]&192&&o!==(o=f[7].deviceId===f[31].deviceId)&&(e.selected=o)},d(f){f&&N(e)}}}function In(i){let e,t,n,l,r,o;const f=[hn,gn],c=[];function g(a,_){return a[9]?1:0}return l=g(i),r=c[l]=f[l](i),{c(){e=A("div"),t=A("video"),n=ne(),r.c(),y(t,"class","svelte-1aa1mud"),re(t,"flip",i[2]),re(t,"hide",!i[9]),y(e,"class","wrap svelte-1aa1mud")},m(a,_){L(a,e,_),F(e,t),i[19](t),F(e,n),c[l].m(e,null),o=!0},p(a,_){(!o||_[0]&4)&&re(t,"flip",a[2]),(!o||_[0]&512)&&re(t,"hide",!a[9]);let w=l;l=g(a),l===w?c[l].p(a,_):(te(),z(c[w],1,1,()=>{c[w]=null}),ee(),r=c[l],r?r.p(a,_):(r=c[l]=f[l](a),r.c()),S(r,1),r.m(e,null))},i(a){o||(S(r),o=!0)},o(a){z(r),o=!1},d(a){a&&N(e),i[19](null),c[l].d()}}}function Te(i,e){const t=n=>{i&&!i.contains(n.target)&&!n.defaultPrevented&&e(n)};return document.addEventListener("click",t,!0),{destroy(){document.removeEventListener("click",t,!0)}}}function yn(i,e,t){let n,l=[],r=null,o,{streaming:f=!1}=e,{pending:c=!1}=e,{root:g=""}=e,{mode:a="image"}=e,{mirror_webcam:_}=e,{include_audio:w}=e,{i18n:I}=e,{upload:M}=e;const d=dn();mn(()=>o=document.createElement("canvas"));const v=async m=>{const P=m.target.value;await Le(w,n,P).then(async Z=>{E=Z,t(7,r=l.find(x=>x.deviceId===P)||null),t(10,H=!1)})};async function p(){try{Le(w,n).then(async m=>{t(9,W=!0),t(6,l=await Zt()),E=m}).then(()=>en(l)).then(m=>{t(6,l=m);const B=E.getTracks().map(P=>P.getSettings()?.deviceId)[0];t(7,r=B&&m.find(P=>P.deviceId===B)||l[0])}),(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&d("error",I("image.no_webcam_support"))}catch(m){if(m instanceof DOMException&&m.name=="NotAllowedError")d("error",I("image.allow_webcam_access"));else throw m}}function k(){var m=o.getContext("2d");(!f||f&&$)&&n.videoWidth&&n.videoHeight&&(o.width=n.videoWidth,o.height=n.videoHeight,m.drawImage(n,0,0,n.videoWidth,n.videoHeight),_&&(m.scale(-1,1),m.drawImage(n,-n.videoWidth,0)),o.toBlob(B=>{d(f?"stream":"capture",B)},"image/png",.8))}let $=!1,D=[],E,b,u;function h(){if($){u.stop();let m=new Blob(D,{type:b}),B=new FileReader;B.onload=async function(P){if(P.target){let Z=new File([m],"sample."+b.substring(6));const x=await ot([Z]);let ke=(await M(x,g))?.filter(Boolean)[0];d("capture",ke),d("stop_recording")}},B.readAsDataURL(m)}else{d("start_recording"),D=[];let m=["video/webm","video/mp4"];for(let B of m)if(MediaRecorder.isTypeSupported(B)){b=B;break}if(b===null){console.error("No supported MediaRecorder mimeType");return}u=new MediaRecorder(E,{mimeType:b}),u.addEventListener("dataavailable",function(B){D.push(B.data)}),u.start(200)}t(8,$=!$)}let W=!1;function C(){a==="image"&&f&&t(8,$=!$),a==="image"?k():h(),!$&&E&&(E.getTracks().forEach(m=>m.stop()),t(5,n.srcObject=null,n),t(9,W=!1))}f&&a==="image"&&window.setInterval(()=>{n&&!c&&k()},500);let H=!1;function he(m){m.preventDefault(),m.stopPropagation(),t(10,H=!1)}function pe(m){rn[m?"unshift":"push"](()=>{n=m,t(5,n)})}const be=async()=>p(),ve=()=>t(10,H=!0),we=()=>t(10,H=!1);return i.$$set=m=>{"streaming"in m&&t(0,f=m.streaming),"pending"in m&&t(15,c=m.pending),"root"in m&&t(16,g=m.root),"mode"in m&&t(1,a=m.mode),"mirror_webcam"in m&&t(2,_=m.mirror_webcam),"include_audio"in m&&t(17,w=m.include_audio),"i18n"in m&&t(3,I=m.i18n),"upload"in m&&t(18,M=m.upload)},[f,a,_,I,Te,n,l,r,$,W,H,v,p,C,he,c,g,w,M,pe,be,ve,we]}class Cn extends tn{constructor(e){super(),sn(this,e,yn,In,un,{streaming:0,pending:15,root:16,mode:1,mirror_webcam:2,include_audio:17,i18n:3,upload:18,click_outside:4},null,[-1,-1])}get streaming(){return this.$$.ctx[0]}set streaming(e){this.$$set({streaming:e}),V()}get pending(){return this.$$.ctx[15]}set pending(e){this.$$set({pending:e}),V()}get root(){return this.$$.ctx[16]}set root(e){this.$$set({root:e}),V()}get mode(){return this.$$.ctx[1]}set mode(e){this.$$set({mode:e}),V()}get mirror_webcam(){return this.$$.ctx[2]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),V()}get include_audio(){return this.$$.ctx[17]}set include_audio(e){this.$$set({include_audio:e}),V()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),V()}get upload(){return this.$$.ctx[18]}set upload(e){this.$$set({upload:e}),V()}get click_outside(){return Te}}const qn=Cn,{SvelteComponent:Sn,attr:Dn,create_component:En,destroy_component:Wn,detach:zn,element:Mn,init:Bn,insert:Rn,mount_component:Tn,noop:Un,safe_not_equal:jn,transition_in:Nn,transition_out:Ln}=window.__gradio__svelte__internal,{createEventDispatcher:An}=window.__gradio__svelte__internal;function Hn(i){let e,t,n;return t=new xe({props:{Icon:et,label:"Remove Image"}}),t.$on("click",i[1]),{c(){e=Mn("div"),En(t.$$.fragment),Dn(e,"class","svelte-s6ybro")},m(l,r){Rn(l,e,r),Tn(t,e,null),n=!0},p:Un,i(l){n||(Nn(t.$$.fragment,l),n=!0)},o(l){Ln(t.$$.fragment,l),n=!1},d(l){l&&zn(e),Wn(t)}}}function On(i){const e=An();return[e,n=>{e("remove_image"),n.stopPropagation()}]}class Pn extends Sn{constructor(e){super(),Bn(this,e,On,Hn,jn,{})}}const{SvelteComponent:Fn,add_flush_callback:We,append:oe,attr:se,bind:ze,binding_callbacks:fe,bubble:De,check_outros:ce,create_component:G,create_slot:Vn,destroy_component:J,detach:de,element:Me,empty:Gn,flush:R,get_all_dirty_from_scope:Jn,get_slot_changes:Kn,group_outros:ue,init:Qn,insert:me,listen:Xn,mount_component:K,noop:Yn,safe_not_equal:Zn,set_style:Ve,space:ae,toggle_class:ge,transition_in:q,transition_out:T,update_slot_base:xn}=window.__gradio__svelte__internal,{createEventDispatcher:el,tick:tl}=window.__gradio__svelte__internal;function Ge(i){let e,t;return e=new Pn({}),e.$on("remove_image",i[25]),{c(){G(e.$$.fragment)},m(n,l){K(e,n,l),t=!0},p:Yn,i(n){t||(q(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Je(i){let e;const t=i[24].default,n=Vn(t,i,i[36],null);return{c(){n&&n.c()},m(l,r){n&&n.m(l,r),e=!0},p(l,r){n&&n.p&&(!e||r[1]&32)&&xn(n,t,l,l[36],e?Kn(t,l[36],r,null):Jn(l[36]),null)},i(l){e||(q(n,l),e=!0)},o(l){T(n,l),e=!1},d(l){n&&n.d(l)}}}function nl(i){let e,t,n=i[2]===null&&Je(i);return{c(){n&&n.c(),e=Gn()},m(l,r){n&&n.m(l,r),me(l,e,r),t=!0},p(l,r){l[2]===null?n?(n.p(l,r),r[0]&4&&q(n,1)):(n=Je(l),n.c(),q(n,1),n.m(e.parentNode,e)):n&&(ue(),T(n,1,1,()=>{n=null}),ce())},i(l){t||(q(n),t=!0)},o(l){T(n),t=!1},d(l){l&&de(e),n&&n.d(l)}}}function ll(i){let e,t,n,l,r;return t=new ct({props:{src:i[2].url,alt:i[2].alt_text}}),{c(){e=Me("div"),G(t.$$.fragment),se(e,"class","image-frame svelte-1ti4ehe"),ge(e,"selectable",i[8])},m(o,f){me(o,e,f),K(t,e,null),n=!0,l||(r=Xn(e,"click",i[21]),l=!0)},p(o,f){const c={};f[0]&4&&(c.src=o[2].url),f[0]&4&&(c.alt=o[2].alt_text),t.$set(c),(!n||f[0]&256)&&ge(e,"selectable",o[8])},i(o){n||(q(t.$$.fragment,o),n=!0)},o(o){T(t.$$.fragment,o),n=!1},d(o){o&&de(e),J(t),l=!1,r()}}}function il(i){let e,t;return e=new qn({props:{root:i[9],mirror_webcam:i[7],streaming:i[6],mode:"image",include_audio:!1,i18n:i[10],upload:i[12]}}),e.$on("capture",i[30]),e.$on("stream",i[31]),e.$on("error",i[32]),e.$on("drag",i[33]),e.$on("upload",i[34]),{c(){G(e.$$.fragment)},m(n,l){K(e,n,l),t=!0},p(n,l){const r={};l[0]&512&&(r.root=n[9]),l[0]&128&&(r.mirror_webcam=n[7]),l[0]&64&&(r.streaming=n[6]),l[0]&1024&&(r.i18n=n[10]),l[0]&4096&&(r.upload=n[12]),e.$set(r)},i(n){t||(q(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Ke(i){let e,t,n;function l(o){i[35](o)}let r={sources:i[5],handle_clear:i[18],handle_select:i[22]};return i[0]!==void 0&&(r.active_source=i[0]),e=new it({props:r}),fe.push(()=>ze(e,"active_source",l)),{c(){G(e.$$.fragment)},m(o,f){K(e,o,f),n=!0},p(o,f){const c={};f[0]&32&&(c.sources=o[5]),!t&&f[0]&1&&(t=!0,c.active_source=o[0],We(()=>t=!1)),e.$set(c)},i(o){n||(q(e.$$.fragment,o),n=!0)},o(o){T(e.$$.fragment,o),n=!1},d(o){J(e,o)}}}function rl(i){let e,t,n,l,r,o,f,c,g,a,_,w,I=i[5].length>1||i[5].includes("clipboard"),M;e=new tt({props:{show_label:i[4],Icon:nt,label:i[3]||"Image"}});let d=i[2]?.url&&!i[15]&&Ge(i);function v(u){i[27](u)}function p(u){i[28](u)}let k={hidden:i[2]!==null||i[0]==="webcam",filetype:i[0]==="clipboard"?"clipboard":"image/*",root:i[9],max_file_size:i[11],disable_click:!i[5].includes("upload")||i[2]!==null,upload:i[12],stream_handler:i[13],$$slots:{default:[nl]},$$scope:{ctx:i}};i[14]!==void 0&&(k.uploading=i[14]),i[1]!==void 0&&(k.dragging=i[1]),o=new st({props:k}),i[26](o),fe.push(()=>ze(o,"uploading",v)),fe.push(()=>ze(o,"dragging",p)),o.$on("load",i[17]),o.$on("error",i[29]);const $=[il,ll],D=[];function E(u,h){return u[0]==="webcam"&&(u[6]||!u[6]&&!u[2])?0:u[2]!==null&&!u[6]?1:-1}~(a=E(i))&&(_=D[a]=$[a](i));let b=I&&Ke(i);return{c(){G(e.$$.fragment),t=ae(),n=Me("div"),d&&d.c(),l=ae(),r=Me("div"),G(o.$$.fragment),g=ae(),_&&_.c(),w=ae(),b&&b.c(),se(r,"class","upload-container svelte-1ti4ehe"),ge(r,"reduced-height",i[5].length>1),Ve(r,"width",i[2]?"auto":"100%"),se(n,"data-testid","image"),se(n,"class","image-container svelte-1ti4ehe")},m(u,h){K(e,u,h),me(u,t,h),me(u,n,h),d&&d.m(n,null),oe(n,l),oe(n,r),K(o,r,null),oe(r,g),~a&&D[a].m(r,null),oe(n,w),b&&b.m(n,null),M=!0},p(u,h){const W={};h[0]&16&&(W.show_label=u[4]),h[0]&8&&(W.label=u[3]||"Image"),e.$set(W),u[2]?.url&&!u[15]?d?(d.p(u,h),h[0]&32772&&q(d,1)):(d=Ge(u),d.c(),q(d,1),d.m(n,l)):d&&(ue(),T(d,1,1,()=>{d=null}),ce());const C={};h[0]&5&&(C.hidden=u[2]!==null||u[0]==="webcam"),h[0]&1&&(C.filetype=u[0]==="clipboard"?"clipboard":"image/*"),h[0]&512&&(C.root=u[9]),h[0]&2048&&(C.max_file_size=u[11]),h[0]&36&&(C.disable_click=!u[5].includes("upload")||u[2]!==null),h[0]&4096&&(C.upload=u[12]),h[0]&8192&&(C.stream_handler=u[13]),h[0]&4|h[1]&32&&(C.$$scope={dirty:h,ctx:u}),!f&&h[0]&16384&&(f=!0,C.uploading=u[14],We(()=>f=!1)),!c&&h[0]&2&&(c=!0,C.dragging=u[1],We(()=>c=!1)),o.$set(C);let H=a;a=E(u),a===H?~a&&D[a].p(u,h):(_&&(ue(),T(D[H],1,1,()=>{D[H]=null}),ce()),~a?(_=D[a],_?_.p(u,h):(_=D[a]=$[a](u),_.c()),q(_,1),_.m(r,null)):_=null),(!M||h[0]&32)&&ge(r,"reduced-height",u[5].length>1),h[0]&4&&Ve(r,"width",u[2]?"auto":"100%"),h[0]&32&&(I=u[5].length>1||u[5].includes("clipboard")),I?b?(b.p(u,h),h[0]&32&&q(b,1)):(b=Ke(u),b.c(),q(b,1),b.m(n,null)):b&&(ue(),T(b,1,1,()=>{b=null}),ce())},i(u){M||(q(e.$$.fragment,u),q(d),q(o.$$.fragment,u),q(_),q(b),M=!0)},o(u){T(e.$$.fragment,u),T(d),T(o.$$.fragment,u),T(_),T(b),M=!1},d(u){u&&(de(t),de(n)),J(e,u),d&&d.d(),i[26](null),J(o),~a&&D[a].d(),b&&b.d()}}}function ol(i,e,t){let n,{$$slots:l={},$$scope:r}=e,{value:o}=e,{label:f=void 0}=e,{show_label:c}=e,{sources:g=["upload","clipboard","webcam"]}=e,{streaming:a=!1}=e,{pending:_=!1}=e,{mirror_webcam:w}=e,{selectable:I=!1}=e,{root:M}=e,{i18n:d}=e,{max_file_size:v=null}=e,{upload:p}=e,{stream_handler:k}=e,$,D=!1,{active_source:E=null}=e;function b({detail:s}){t(2,o=s),W("upload")}function u(){t(2,o=null),W("clear"),W("change",null)}async function h(s){t(23,_=!0);const le=await $.load_files([new File([s],"webcam.png")]);t(2,o=le?.[0]||null),await tl(),W(a?"stream":"change"),t(23,_=!1)}const W=el();let{dragging:C=!1}=e;function H(s){let le=rt(s);le&&W("select",{index:le,value:null})}async function he(s){switch(s){case"clipboard":$.paste_clipboard();break}}const pe=()=>{t(2,o=null),W("clear")};function be(s){fe[s?"unshift":"push"](()=>{$=s,t(16,$)})}function ve(s){D=s,t(14,D)}function we(s){C=s,t(1,C)}function m(s){De.call(this,i,s)}const B=s=>h(s.detail),P=s=>h(s.detail);function Z(s){De.call(this,i,s)}function x(s){De.call(this,i,s)}const ke=s=>h(s.detail);function Ze(s){E=s,t(0,E),t(5,g)}return i.$$set=s=>{"value"in s&&t(2,o=s.value),"label"in s&&t(3,f=s.label),"show_label"in s&&t(4,c=s.show_label),"sources"in s&&t(5,g=s.sources),"streaming"in s&&t(6,a=s.streaming),"pending"in s&&t(23,_=s.pending),"mirror_webcam"in s&&t(7,w=s.mirror_webcam),"selectable"in s&&t(8,I=s.selectable),"root"in s&&t(9,M=s.root),"i18n"in s&&t(10,d=s.i18n),"max_file_size"in s&&t(11,v=s.max_file_size),"upload"in s&&t(12,p=s.upload),"stream_handler"in s&&t(13,k=s.stream_handler),"active_source"in s&&t(0,E=s.active_source),"dragging"in s&&t(1,C=s.dragging),"$$scope"in s&&t(36,r=s.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&33&&!E&&g&&t(0,E=g[0]),i.$$.dirty[0]&65&&t(15,n=a&&E==="webcam"),i.$$.dirty[0]&49152&&D&&!n&&t(2,o=null),i.$$.dirty[0]&2&&W("drag",C)},[E,C,o,f,c,g,a,w,I,M,d,v,p,k,D,n,$,b,u,h,W,H,he,_,l,pe,be,ve,we,m,B,P,Z,x,ke,Ze,r]}class al extends Fn{constructor(e){super(),Qn(this,e,ol,rl,Zn,{value:2,label:3,show_label:4,sources:5,streaming:6,pending:23,mirror_webcam:7,selectable:8,root:9,i18n:10,max_file_size:11,upload:12,stream_handler:13,active_source:0,dragging:1},null,[-1,-1])}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),R()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),R()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),R()}get sources(){return this.$$.ctx[5]}set sources(e){this.$$set({sources:e}),R()}get streaming(){return this.$$.ctx[6]}set streaming(e){this.$$set({streaming:e}),R()}get pending(){return this.$$.ctx[23]}set pending(e){this.$$set({pending:e}),R()}get mirror_webcam(){return this.$$.ctx[7]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),R()}get selectable(){return this.$$.ctx[8]}set selectable(e){this.$$set({selectable:e}),R()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),R()}get i18n(){return this.$$.ctx[10]}set i18n(e){this.$$set({i18n:e}),R()}get max_file_size(){return this.$$.ctx[11]}set max_file_size(e){this.$$set({max_file_size:e}),R()}get upload(){return this.$$.ctx[12]}set upload(e){this.$$set({upload:e}),R()}get stream_handler(){return this.$$.ctx[13]}set stream_handler(e){this.$$set({stream_handler:e}),R()}get active_source(){return this.$$.ctx[0]}set active_source(e){this.$$set({active_source:e}),R()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),R()}}const vl=al;export{vl as I,qn as W};
//# sourceMappingURL=ImageUploader-Dh2I3hM7.js.map
