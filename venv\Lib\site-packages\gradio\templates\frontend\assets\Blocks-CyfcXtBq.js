const __vite__fileDeps=["./Index-DesKBBmJ.js","./Button-BIUaXfcG.js","./Index-DB1XLvMK.js","./index-BQPjLIsY.js","./index-BOW2xVAS.css","./Index-BEyjvDG_.css","./Button-CTZL5Nos.css","./Index-CuoXAbPt.js","./Index-CptIZeFZ.css","./Index-C5NYahSl.css","./Index-BYBDLc2n.js","./BlockLabel-BlSr62f_.js","./Empty-BgF7sXBn.js","./Minimize-X5PPawdt.js","./Image-Bsh8Umrh.js","./file-url-SIRImsEF.js","./Index-DLaE6XNB.css","./StaticAudio-Dw97rWIw.js","./ShareButton-DRfMJDgB.js","./Download-DVtk-Jv3.js","./Music-CDm0RGMk.js","./Trim-UKwaW4UI.js","./Undo-CpmTQw3B.js","./DownloadLink-CHpWw1Ex.js","./StaticAudio-CCdGW_0q.css","./Example-uQ8MuYg6.js","./Example-D7K5RtQ2.css","./index-BmHQU65L.js","./Upload-1-QDTAlg.js","./ModifyUpload-C849vsWf.css","./ModifyUpload-CEEIIKhx.js","./SelectSource-C3FIO9My.js","./Upload-Cp8Go_XF.js","./UploadText-CCg0GCB-.js","./index-h4xPCdhH.css","./Index-DlGXsSlG.js","./Index-DRonTktv.js","./Index-A4-C6YLr.js","./index-CnqicUFC.js","./Image-CJc3fwmN.js","./Image-B8dFOee4.css","./Index.svelte_svelte_type_style_lang-OwOFPfLe.js","./Check-CZUQOzJl.js","./Copy-B6RcHnoK.js","./Example.svelte_svelte_type_style_lang-DXGdiQV3.js","./prism-python-BTLCWl-V.js","./Example-FbnSQP03.css","./Index-Xv8ZQETB.css","./Index-D9RVvV4H.css","./ImagePreview-DlqNSi_R.css","./Example-CZ-iEz1g.js","./Index-BUc4SVJJ.js","./Info-CrBVEpWV.js","./Index-BTCbxfal.css","./Example-CRm1Pmw_.js","./Index-DmOfYZye.js","./BlockTitle-CXNngU7y.js","./Index-D8o7u_T6.css","./Example-Wp-_4AVX.js","./Example-oomIF0ca.css","./Index-CKgowMni.js","./Code-DGNrTu_I.js","./Index-DZruPJqq.css","./Example-BaLyJYAe.js","./Example-Bw8Q_3wB.css","./Index-C6W5a0bI.js","./Index-B6tpubC5.css","./index-CK7CWdzV.js","./Example-BT2jlY4j.js","./Example-CX34aPix.css","./Index-B7z1eIWS.js","./dsv-DB8NKgIY.js","./Index-BhDD_Y03.css","./Index-BEKHNubq.js","./Example-C7XUkkid.js","./Example-Cj3ii62O.css","./Index-D3f6Hf9S.css","./Textbox-D8IAzrZj.css","./Index-zN1wv2uI.js","./Example-BBLMS951.js","./Index-DjIb8LtA.css","./Index-CEDCRqJB.js","./Index-tcNSQSor.css","./Example-CUwox43B.js","./Index-Vm7MXWuW.js","./DropdownArrow-B_jYsAai.js","./Index-BAQumg2K.css","./Example-DrmWnoSo.js","./Example-DpWs9cEC.css","./Index-DBfSJWIQ.js","./FileUpload-DIyAvrHT.js","./File-BQ_9P3Ye.js","./FileUpload-2TE7T7kD.css","./Example-CIFMxn5c.js","./Example-DfhEULNF.css","./Index-2uWJiLiv.js","./Index-YPTlsVjp.css","./Index-DDCF2BFd.js","./Index-B0JJ6p9c.css","./Gallery-XI6EERbz.js","./Gallery-D-mOALVO.css","./Index-DjtuMFOG.js","./Index-CS2xVf8J.js","./Index-C-7D3Y3j.css","./Index-C68UQGaZ.js","./color-FyDVBI5Z.js","./Index-Dwy3Ni24.css","./Index-QJJ51qFF.js","./Index-BicZ6NTU.css","./Example-C2a4WxRl.js","./Example-CSw4pLi5.css","./ImagePreview-BpvYwBpC.js","./utils-Gtzs_Zla.js","./Example-Ig2aUhUe.js","./Example-DikqVAPo.css","./Index-qbjg5-4V.js","./ImageUploader-Dh2I3hM7.js","./ImageUploader-r0sXxVKa.css","./Example-DD8yppZ9.js","./Example-6rv12T44.css","./Index-Ds0AjFoZ.js","./__vite-browser-external-D7Ct-6yo.js","./Index-BUolJ3qq.css","./Index-j4wxG1wD.js","./Index-CkUqDkVC.css","./Index-mT8gSmoJ.js","./LineChart-CKh1Fdep.js","./Index-BM4_tjMk.css","./Example-CSVraJnq.js","./Index-BK-zC9ir.js","./Example-BQyGztrG.js","./Index-CMg_fjf1.js","./Index-BEzrmV9X.css","./Example-DxaKKsjH.js","./Video-BnSeZxqU.js","./Video-DJw86Ppo.css","./Example-CCTTJ5R1.css","./Index-Bk0whK9w.js","./Video-fsmLZWjA.js","./Index-DzvE-ly6.css","./Index-BRMkropX.js","./vega-embed.module-B-qtr4Vf.js","./Index-DvmFt2zS.css","./Example-CqL1e7EB.js","./Index--WpWguRY.js","./Index-BUublr_b.css","./Example-C9__vDgN.js","./Index-CvmM5KQ3.js","./Index-Ox7h1H2c.css","./Plot-Ce8WWIhc.js","./Index-ALXcDGxx.js","./Example-BoMLuz1A.js","./Index-DphV2Gww.js","./Index-BSqct-uf.css","./Index-Tmzj9QOr.js","./Index-C43SQM_O.css","./Example-DxdiEFS_.js","./Index-D9JcTZE7.js","./Index-CJeiS5ET.css","./index-Dc9jIDHc.js","./Index-BA3fpEJb.js","./Tabs-C0yBUI0Z.js","./Tabs-DX5TEub2.css","./Index-thdqX2vf.css","./Index-BfLZDIPR.js","./Index-CSD8SlVL.js","./Textbox-BxQ_qaNA.js","./Index-BOi8ACPk.js","./Index-DYDmCduo.css","./VideoPreview-NCcOumWs.js","./VideoPreview-Dvitrirr.css","./Example-DUQ4sHSf.js","./Example-B5CSTz0f.css","./index-DJvrwMOP.js","./index-CFBZQE_H.css","./Example-BrizabXh.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{e as As,f as Ls,$ as Ro,_ as T,w as kn,s as Ts}from"./index-BQPjLIsY.js";import{c as qs,f as zl,a as In,B as rn}from"./Button-BIUaXfcG.js";import{p as Fl,L as So}from"./Index-DB1XLvMK.js";const{SvelteComponent:Ps,append:Os,attr:Ie,detach:Is,init:Ds,insert:Rs,noop:Wn,safe_not_equal:Ss,svg_element:Ul}=window.__gradio__svelte__internal;function Vs(l){let e,t;return{c(){e=Ul("svg"),t=Ul("path"),Ie(t,"stroke-linecap","round"),Ie(t,"stroke-linejoin","round"),Ie(t,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),Ie(e,"fill","none"),Ie(e,"stroke","currentColor"),Ie(e,"viewBox","0 0 24 24"),Ie(e,"width","100%"),Ie(e,"height","100%"),Ie(e,"xmlns","http://www.w3.org/2000/svg"),Ie(e,"aria-hidden","true"),Ie(e,"stroke-width","2"),Ie(e,"stroke-linecap","round"),Ie(e,"stroke-linejoin","round")},m(n,i){Rs(n,e,i),Os(e,t)},p:Wn,i:Wn,o:Wn,d(n){n&&Is(e)}}}let Ms=class extends Ps{constructor(e){super(),Ds(this,e,null,Vs,Ss,{})}};const{SvelteComponent:Ns,append:zs,attr:De,detach:Fs,init:Us,insert:Bs,noop:Qn,safe_not_equal:Hs,svg_element:Bl}=window.__gradio__svelte__internal;function Gs(l){let e,t;return{c(){e=Bl("svg"),t=Bl("path"),De(t,"stroke-linecap","round"),De(t,"stroke-linejoin","round"),De(t,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),De(e,"fill","none"),De(e,"stroke","currentColor"),De(e,"viewBox","0 0 24 24"),De(e,"width","100%"),De(e,"height","100%"),De(e,"xmlns","http://www.w3.org/2000/svg"),De(e,"aria-hidden","true"),De(e,"stroke-width","2"),De(e,"stroke-linecap","round"),De(e,"stroke-linejoin","round")},m(n,i){Bs(n,e,i),zs(e,t)},p:Qn,i:Qn,o:Qn,d(n){n&&Fs(e)}}}class Zs extends Ns{constructor(e){super(),Us(this,e,null,Gs,Hs,{})}}const{SvelteComponent:Ws,append:Qs,attr:Re,detach:Js,init:Xs,insert:Ys,noop:Jn,safe_not_equal:Ks,svg_element:Hl}=window.__gradio__svelte__internal;function xs(l){let e,t;return{c(){e=Hl("svg"),t=Hl("path"),Re(t,"stroke-linecap","round"),Re(t,"stroke-linejoin","round"),Re(t,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),Re(e,"fill","none"),Re(e,"stroke","currentColor"),Re(e,"stroke-width","2"),Re(e,"viewBox","0 0 24 24"),Re(e,"width","100%"),Re(e,"height","100%"),Re(e,"xmlns","http://www.w3.org/2000/svg"),Re(e,"aria-hidden","true"),Re(e,"stroke-linecap","round"),Re(e,"stroke-linejoin","round")},m(n,i){Ys(n,e,i),Qs(e,t)},p:Jn,i:Jn,o:Jn,d(n){n&&Js(e)}}}class er extends Ws{constructor(e){super(),Xs(this,e,null,xs,Ks,{})}}class Xn extends Error{constructor(e){super(e),this.name="ShareError"}}async function jf(l,e){if(window.__gradio_space__==null)throw new Xn("Must be on Spaces to share.");let t,n,i;{let _;if(typeof l=="object"&&l.url)_=l.url;else if(typeof l=="string")_=l;else throw new Error("Invalid data format for URL type");const r=await fetch(_);t=await r.blob(),n=r.headers.get("content-type")||"",i=r.headers.get("content-disposition")||""}const o=new File([t],i,{type:n}),s=await fetch("https://huggingface.co/uploads",{method:"POST",body:o,headers:{"Content-Type":o.type,"X-Requested-With":"XMLHttpRequest"}});if(!s.ok){if(s.headers.get("content-type")?.includes("application/json")){const _=await s.json();throw new Xn(`Upload failed: ${_.error}`)}throw new Xn("Upload failed.")}return await s.text()}function Cf(l){l.addEventListener("click",e);async function e(t){const n=t.composedPath(),[i]=n.filter(o=>o?.tagName==="BUTTON"&&o.classList.contains("copy_code_button"));if(i){let o=function(r){r.style.opacity="1",setTimeout(()=>{r.style.opacity="0"},2e3)};t.stopImmediatePropagation();const s=i.parentElement.innerText.trim(),a=Array.from(i.children)[1];await tr(s)&&o(a)}}return{destroy(){l.removeEventListener("click",e)}}}async function tr(l){let e=!1;if("clipboard"in navigator)await navigator.clipboard.writeText(l),e=!0;else{const t=document.createElement("textarea");t.value=l,t.style.position="absolute",t.style.left="-999999px",document.body.prepend(t),t.select();try{document.execCommand("copy"),e=!0}catch(n){console.error(n),e=!1}finally{t.remove()}}return e}const Af=l=>{const e=Math.floor(l/3600),t=Math.floor(l%3600/60),n=Math.round(l)%60,i=`${t<10?"0":""}${t}`,o=`${n<10?"0":""}${n}`;return e>0?`${e}:${i}:${o}`:`${t}:${o}`};class nr{constructor(e,t,n,i,o,s,a,_=u=>u,r,c){this.load_component=lr.bind(this),this.#e=e,this.theme=n,this.version=i,this.#t=t,this.max_file_size=a,this.i18n=_,this.root=o,this.autoscroll=s,this.client=r,this._load_component=c}#e;#t;dispatch(e,t){const n=new CustomEvent("gradio",{bubbles:!0,detail:{data:t,id:this.#e,event:e}});this.#t.dispatchEvent(n)}}function lr(l,e="component"){return this._load_component({name:l,api_url:this.client.config?.root,variant:e})}function ir(l,{from:e,to:t},n={}){const i=getComputedStyle(l),o=i.transform==="none"?"":i.transform,[s,a]=i.transformOrigin.split(" ").map(parseFloat),_=e.left+e.width*s/t.width-(t.left+s),r=e.top+e.height*a/t.height-(t.top+a),{delay:c=0,duration:u=d=>Math.sqrt(d)*120,easing:f=qs}=n;return{delay:c,duration:As(u)?u(Math.sqrt(_*_+r*r)):u,easing:f,css:(d,g)=>{const m=g*_,v=g*r,p=d+g*e.width/t.width,j=d+g*e.height/t.height;return`transform: ${o} translate(${m}px, ${v}px) scale(${p}, ${j});`}}}const{SvelteComponent:or,add_render_callback:sr,append:Fe,attr:ie,bubble:Gl,check_outros:rr,create_component:bl,create_in_transition:ar,create_out_transition:_r,destroy_component:kl,detach:cr,element:ct,flush:Yt,group_outros:ur,init:fr,insert:pr,listen:Yn,mount_component:wl,run_all:mr,safe_not_equal:dr,set_data:hr,space:un,stop_propagation:Zl,text:gr,toggle_class:Wl,transition_in:ln,transition_out:on}=window.__gradio__svelte__internal,{createEventDispatcher:vr,onMount:$r}=window.__gradio__svelte__internal;function br(l){let e,t;return e=new Ms({}),{c(){bl(e.$$.fragment)},m(n,i){wl(e,n,i),t=!0},i(n){t||(ln(e.$$.fragment,n),t=!0)},o(n){on(e.$$.fragment,n),t=!1},d(n){kl(e,n)}}}function kr(l){let e,t;return e=new Zs({}),{c(){bl(e.$$.fragment)},m(n,i){wl(e,n,i),t=!0},i(n){t||(ln(e.$$.fragment,n),t=!0)},o(n){on(e.$$.fragment,n),t=!1},d(n){kl(e,n)}}}function wr(l){let e,t;return e=new er({}),{c(){bl(e.$$.fragment)},m(n,i){wl(e,n,i),t=!0},i(n){t||(ln(e.$$.fragment,n),t=!0)},o(n){on(e.$$.fragment,n),t=!1},d(n){kl(e,n)}}}function yr(l){let e,t,n,i,o,s,a,_,r,c,u,f,d,g,m,v,p,j,w,b,C,h,k,L,y,$,E,P;const D=[wr,kr,br],U=[];function O(V,M){return V[1]==="warning"?0:V[1]==="info"?1:V[1]==="error"?2:-1}return~(n=O(l))&&(i=U[n]=D[n](l)),{c(){e=ct("div"),t=ct("div"),i&&i.c(),s=un(),a=ct("div"),_=ct("div"),r=gr(l[1]),u=un(),f=ct("div"),m=un(),v=ct("button"),p=ct("span"),p.textContent="×",w=un(),b=ct("div"),ie(t,"class",o="toast-icon "+l[1]+" svelte-re9qbj"),ie(_,"class",c="toast-title "+l[1]+" svelte-re9qbj"),ie(f,"class",d="toast-text "+l[1]+" svelte-re9qbj"),ie(a,"class",g="toast-details "+l[1]+" svelte-re9qbj"),ie(p,"aria-hidden","true"),ie(v,"class",j="toast-close "+l[1]+" svelte-re9qbj"),ie(v,"type","button"),ie(v,"aria-label","Close"),ie(v,"data-testid","toast-close"),ie(b,"class",C="timer "+l[1]+" svelte-re9qbj"),ie(b,"style",h=`animation-duration: ${l[2]};`),ie(e,"class",k="toast-body "+l[1]+" svelte-re9qbj"),ie(e,"role","alert"),ie(e,"data-testid","toast-body"),Wl(e,"hidden",!l[3])},m(V,M){pr(V,e,M),Fe(e,t),~n&&U[n].m(t,null),Fe(e,s),Fe(e,a),Fe(a,_),Fe(_,r),Fe(a,u),Fe(a,f),f.innerHTML=l[0],Fe(e,m),Fe(e,v),Fe(v,p),Fe(e,w),Fe(e,b),$=!0,E||(P=[Yn(v,"click",l[4]),Yn(e,"click",Zl(l[8])),Yn(e,"keydown",Zl(l[9]))],E=!0)},p(V,[M]){let R=n;n=O(V),n!==R&&(i&&(ur(),on(U[R],1,1,()=>{U[R]=null}),rr()),~n?(i=U[n],i||(i=U[n]=D[n](V),i.c()),ln(i,1),i.m(t,null)):i=null),(!$||M&2&&o!==(o="toast-icon "+V[1]+" svelte-re9qbj"))&&ie(t,"class",o),(!$||M&2)&&hr(r,V[1]),(!$||M&2&&c!==(c="toast-title "+V[1]+" svelte-re9qbj"))&&ie(_,"class",c),(!$||M&1)&&(f.innerHTML=V[0]),(!$||M&2&&d!==(d="toast-text "+V[1]+" svelte-re9qbj"))&&ie(f,"class",d),(!$||M&2&&g!==(g="toast-details "+V[1]+" svelte-re9qbj"))&&ie(a,"class",g),(!$||M&2&&j!==(j="toast-close "+V[1]+" svelte-re9qbj"))&&ie(v,"class",j),(!$||M&2&&C!==(C="timer "+V[1]+" svelte-re9qbj"))&&ie(b,"class",C),(!$||M&4&&h!==(h=`animation-duration: ${V[2]};`))&&ie(b,"style",h),(!$||M&2&&k!==(k="toast-body "+V[1]+" svelte-re9qbj"))&&ie(e,"class",k),(!$||M&10)&&Wl(e,"hidden",!V[3])},i(V){$||(ln(i),V&&sr(()=>{$&&(y&&y.end(1),L=ar(e,zl,{duration:200,delay:100}),L.start())}),$=!0)},o(V){on(i),L&&L.invalidate(),V&&(y=_r(e,zl,{duration:200})),$=!1},d(V){V&&cr(e),~n&&U[n].d(),V&&y&&y.end(),E=!1,mr(P)}}}function Er(l,e,t){let n,i,{message:o=""}=e,{type:s}=e,{id:a}=e,{duration:_=10}=e,{visible:r=!0}=e;const c=m=>{try{return!!m&&new URL(m,location.href).origin!==location.origin}catch{return!1}};Fl.addHook("afterSanitizeAttributes",function(m){"target"in m&&c(m.getAttribute("href"))&&(m.setAttribute("target","_blank"),m.setAttribute("rel","noopener noreferrer"))});const u=vr();function f(){u("close",a)}$r(()=>{_!==null&&setTimeout(()=>{f()},_*1e3)});function d(m){Gl.call(this,l,m)}function g(m){Gl.call(this,l,m)}return l.$$set=m=>{"message"in m&&t(0,o=m.message),"type"in m&&t(1,s=m.type),"id"in m&&t(6,a=m.id),"duration"in m&&t(5,_=m.duration),"visible"in m&&t(7,r=m.visible)},l.$$.update=()=>{l.$$.dirty&1&&t(0,o=Fl.sanitize(o)),l.$$.dirty&128&&t(3,n=r),l.$$.dirty&32&&t(5,_=_||null),l.$$.dirty&32&&t(2,i=`${_||0}s`)},[o,s,i,n,f,_,a,r,d,g]}class jr extends or{constructor(e){super(),fr(this,e,Er,yr,dr,{message:0,type:1,id:6,duration:5,visible:7})}get message(){return this.$$.ctx[0]}set message(e){this.$$set({message:e}),Yt()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),Yt()}get id(){return this.$$.ctx[6]}set id(e){this.$$set({id:e}),Yt()}get duration(){return this.$$.ctx[5]}set duration(e){this.$$set({duration:e}),Yt()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),Yt()}}const{SvelteComponent:Cr,append:Ar,attr:Lr,bubble:Tr,check_outros:qr,create_animation:Pr,create_component:Or,destroy_component:Ir,detach:Vo,element:Mo,ensure_array_like:Ql,fix_and_outro_and_destroy_block:Dr,fix_position:Rr,flush:Sr,group_outros:Vr,init:Mr,insert:No,mount_component:Nr,noop:zr,safe_not_equal:Fr,set_style:Ur,space:Br,transition_in:zo,transition_out:Fo,update_keyed_each:Hr}=window.__gradio__svelte__internal;function Jl(l,e,t){const n=l.slice();return n[2]=e[t].type,n[3]=e[t].message,n[4]=e[t].id,n[5]=e[t].duration,n[6]=e[t].visible,n}function Xl(l,e){let t,n,i,o,s=zr,a;return n=new jr({props:{type:e[2],message:e[3],duration:e[5],visible:e[6],id:e[4]}}),n.$on("close",e[1]),{key:l,first:null,c(){t=Mo("div"),Or(n.$$.fragment),i=Br(),Ur(t,"width","100%"),this.first=t},m(_,r){No(_,t,r),Nr(n,t,null),Ar(t,i),a=!0},p(_,r){e=_;const c={};r&1&&(c.type=e[2]),r&1&&(c.message=e[3]),r&1&&(c.duration=e[5]),r&1&&(c.visible=e[6]),r&1&&(c.id=e[4]),n.$set(c)},r(){o=t.getBoundingClientRect()},f(){Rr(t),s()},a(){s(),s=Pr(t,o,ir,{duration:300})},i(_){a||(zo(n.$$.fragment,_),a=!0)},o(_){Fo(n.$$.fragment,_),a=!1},d(_){_&&Vo(t),Ir(n)}}}function Gr(l){let e,t=[],n=new Map,i,o=Ql(l[0]);const s=a=>a[4];for(let a=0;a<o.length;a+=1){let _=Jl(l,o,a),r=s(_);n.set(r,t[a]=Xl(r,_))}return{c(){e=Mo("div");for(let a=0;a<t.length;a+=1)t[a].c();Lr(e,"class","toast-wrap svelte-pu0yf1")},m(a,_){No(a,e,_);for(let r=0;r<t.length;r+=1)t[r]&&t[r].m(e,null);i=!0},p(a,[_]){if(_&1){o=Ql(a[0]),Vr();for(let r=0;r<t.length;r+=1)t[r].r();t=Hr(t,_,s,1,a,o,n,e,Dr,Xl,null,Jl);for(let r=0;r<t.length;r+=1)t[r].a();qr()}},i(a){if(!i){for(let _=0;_<o.length;_+=1)zo(t[_]);i=!0}},o(a){for(let _=0;_<t.length;_+=1)Fo(t[_]);i=!1},d(a){a&&Vo(e);for(let _=0;_<t.length;_+=1)t[_].d()}}}function Zr(l){l.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function Wr(l,e,t){let{messages:n=[]}=e;function i(o){Tr.call(this,l,o)}return l.$$set=o=>{"messages"in o&&t(0,n=o.messages)},l.$$.update=()=>{l.$$.dirty&1&&Zr(n)},[n,i]}class Qr extends Cr{constructor(e){super(),Mr(this,e,Wr,Gr,Fr,{messages:0})}get messages(){return this.$$.ctx[0]}set messages(e){this.$$set({messages:e}),Sr()}}const{SvelteComponent:Jr,append:Kn,attr:it,detach:Xr,init:Yr,insert:Kr,noop:xn,safe_not_equal:xr,set_style:Kt,svg_element:fn}=window.__gradio__svelte__internal;function ea(l){let e,t,n,i;return{c(){e=fn("svg"),t=fn("g"),n=fn("path"),i=fn("path"),it(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),it(i,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),it(e,"width","100%"),it(e,"height","100%"),it(e,"viewBox","0 0 5 5"),it(e,"version","1.1"),it(e,"xmlns","http://www.w3.org/2000/svg"),it(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),it(e,"xml:space","preserve"),Kt(e,"fill","currentColor"),Kt(e,"fill-rule","evenodd"),Kt(e,"clip-rule","evenodd"),Kt(e,"stroke-linejoin","round"),Kt(e,"stroke-miterlimit","2")},m(o,s){Kr(o,e,s),Kn(e,t),Kn(t,n),Kn(t,i)},p:xn,i:xn,o:xn,d(o){o&&Xr(e)}}}class Uo extends Jr{constructor(e){super(),Yr(this,e,null,ea,xr,{})}}const{SvelteComponent:ta,append:ut,attr:pn,create_component:na,destroy_component:la,detach:el,element:St,flush:ia,init:oa,insert:tl,listen:sa,mount_component:ra,safe_not_equal:aa,set_data:_a,space:nl,text:Yl,transition_in:ca,transition_out:ua}=window.__gradio__svelte__internal,{createEventDispatcher:fa}=window.__gradio__svelte__internal;function pa(l){let e,t,n,i,o,s,a,_,r,c,u,f,d,g,m;return f=new Uo({}),{c(){e=St("div"),t=St("h1"),t.textContent="API Docs",n=nl(),i=St("p"),o=Yl(`No API Routes found for
		`),s=St("code"),a=Yl(l[0]),_=nl(),r=St("p"),r.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,c=nl(),u=St("button"),na(f.$$.fragment),pn(s,"class","svelte-e1ha0f"),pn(i,"class","attention svelte-e1ha0f"),pn(e,"class","wrap prose svelte-e1ha0f"),pn(u,"class","svelte-e1ha0f")},m(v,p){tl(v,e,p),ut(e,t),ut(e,n),ut(e,i),ut(i,o),ut(i,s),ut(s,a),ut(e,_),ut(e,r),tl(v,c,p),tl(v,u,p),ra(f,u,null),d=!0,g||(m=sa(u,"click",l[2]),g=!0)},p(v,[p]){(!d||p&1)&&_a(a,v[0])},i(v){d||(ca(f.$$.fragment,v),d=!0)},o(v){ua(f.$$.fragment,v),d=!1},d(v){v&&(el(e),el(c),el(u)),la(f),g=!1,m()}}}function ma(l,e,t){const n=fa();let{root:i}=e;const o=()=>n("close");return l.$$set=s=>{"root"in s&&t(0,i=s.root)},[i,n,o]}class da extends ta{constructor(e){super(),oa(this,e,ma,pa,aa,{root:0})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),ia()}}const Bo="data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e",{SvelteComponent:ha,append:qe,attr:Xe,create_component:Kl,destroy_component:xl,detach:qt,element:Ye,flush:ei,init:ga,insert:Pt,listen:va,mount_component:ti,noop:$a,safe_not_equal:ba,set_data:ni,space:en,src_url_equal:ka,text:tn,transition_in:li,transition_out:ii}=window.__gradio__svelte__internal,{createEventDispatcher:wa}=window.__gradio__svelte__internal;function ya(l){let e,t,n;return{c(){e=Ye("div"),t=en(),n=Ye("p"),n.textContent="API Recorder",Xe(e,"class","loading-dot self-baseline svelte-1i1gjw2"),Xe(n,"class","self-baseline btn-text svelte-1i1gjw2")},m(i,o){Pt(i,e,o),Pt(i,t,o),Pt(i,n,o)},p:$a,d(i){i&&(qt(e),qt(t),qt(n))}}}function oi(l){let e;return{c(){e=tn("s")},m(t,n){Pt(t,e,n)},d(t){t&&qt(e)}}}function Ea(l){let e,t,n,i,o,s,a,_,r,c,u,f,d,g,m,v,p,j,w,b,C,h,k;u=new In({props:{size:"sm",variant:"secondary",elem_id:"start-api-recorder",$$slots:{default:[ya]},$$scope:{ctx:l}}}),u.$on("click",l[3]);let L=l[1]>1&&oi();return b=new Uo({}),{c(){e=Ye("h2"),t=Ye("img"),i=en(),o=Ye("div"),s=tn(`API documentation
		`),a=Ye("div"),_=tn(l[0]),r=en(),c=Ye("span"),Kl(u.$$.fragment),f=en(),d=Ye("p"),g=Ye("span"),m=tn(l[1]),v=tn(" API endpoint"),L&&L.c(),p=Ye("br"),j=en(),w=Ye("button"),Kl(b.$$.fragment),ka(t.src,n=Bo)||Xe(t,"src",n),Xe(t,"alt",""),Xe(t,"class","svelte-1i1gjw2"),Xe(a,"class","url svelte-1i1gjw2"),Xe(o,"class","title svelte-1i1gjw2"),Xe(g,"class","url svelte-1i1gjw2"),Xe(c,"class","counts svelte-1i1gjw2"),Xe(e,"class","svelte-1i1gjw2"),Xe(w,"class","svelte-1i1gjw2")},m(y,$){Pt(y,e,$),qe(e,t),qe(e,i),qe(e,o),qe(o,s),qe(o,a),qe(a,_),qe(e,r),qe(e,c),ti(u,c,null),qe(c,f),qe(c,d),qe(d,g),qe(g,m),qe(d,v),L&&L.m(d,null),qe(d,p),Pt(y,j,$),Pt(y,w,$),ti(b,w,null),C=!0,h||(k=va(w,"click",l[4]),h=!0)},p(y,[$]){(!C||$&1)&&ni(_,y[0]);const E={};$&32&&(E.$$scope={dirty:$,ctx:y}),u.$set(E),(!C||$&2)&&ni(m,y[1]),y[1]>1?L||(L=oi(),L.c(),L.m(d,p)):L&&(L.d(1),L=null)},i(y){C||(li(u.$$.fragment,y),li(b.$$.fragment,y),C=!0)},o(y){ii(u.$$.fragment,y),ii(b.$$.fragment,y),C=!1},d(y){y&&(qt(e),qt(j),qt(w)),xl(u),L&&L.d(),xl(b),h=!1,k()}}}function ja(l,e,t){let{root:n}=e,{api_count:i}=e;const o=wa(),s=()=>o("close",{api_recorder_visible:!0}),a=()=>o("close");return l.$$set=_=>{"root"in _&&t(0,n=_.root),"api_count"in _&&t(1,i=_.api_count)},[n,i,o,s,a]}class Ca extends ha{constructor(e){super(),ga(this,e,ja,Ea,ba,{root:0,api_count:1})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),ei()}get api_count(){return this.$$.ctx[1]}set api_count(e){this.$$set({api_count:e}),ei()}}function He(l,e,t=null){return e===void 0?t==="py"?"None":null:l===null&&t==="py"?"None":e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"||t==="bash"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):e.startsWith("Literal['")?'"'+l+'"':t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:(t==="bash"&&(l=_l(l)),t==="py"&&(l=cl(l)),Aa(l))}function Ho(l){if(typeof l=="object"&&l!==null&&l.hasOwnProperty("url")&&l.hasOwnProperty("meta")&&typeof l.meta=="object"&&l.meta!==null&&l.meta._type==="gradio.FileData")return!0;if(typeof l=="object"&&l!==null){for(let e in l)if(typeof l[e]=="object"&&Ho(l[e]))return!0}return!1}function _l(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?{path:l.url}:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=_l(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=_l(l[e])}),l)}function cl(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?`handle_file('${l.url}')`:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=cl(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=cl(l[e])}),l)}function Aa(l){let e=JSON.stringify(l,(i,o)=>o===null?"UNQUOTEDNone":typeof o=="string"&&o.startsWith("handle_file(")&&o.endsWith(")")?`UNQUOTED${o}`:o);const t=/"UNQUOTEDhandle_file\(([^)]*)\)"/g;e=e.replace(t,(i,o)=>`handle_file(${o})`);const n=/"UNQUOTEDNone"/g;return e.replace(n,"None")}const{SvelteComponent:La,append:pe,attr:dt,check_outros:Ta,create_component:qa,destroy_component:Pa,destroy_each:Oa,detach:be,element:Me,empty:Go,ensure_array_like:si,flush:mn,group_outros:Ia,init:Da,insert:ke,mount_component:Ra,noop:Sa,safe_not_equal:Va,set_data:Ot,set_style:jt,space:Ct,text:Ae,toggle_class:ri,transition_in:wn,transition_out:ul}=window.__gradio__svelte__internal;function ai(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].python_type,n[6]=e[t].component,n[7]=e[t].parameter_name,n[8]=e[t].parameter_has_default,n[9]=e[t].parameter_default,n[11]=t,n}function _i(l){let e;return{c(){e=Ae("s")},m(t,n){ke(t,e,n)},d(t){t&&be(e)}}}function Ma(l){let e=(l[2][l[11]].type||"any")+"",t;return{c(){t=Ae(e)},m(n,i){ke(n,t,i)},p(n,i){i&4&&e!==(e=(n[2][n[11]].type||"any")+"")&&Ot(t,e)},d(n){n&&be(t)}}}function Na(l){let e=l[5].type+"",t,n,i=l[8]&&l[9]===null&&ci();return{c(){t=Ae(e),i&&i.c(),n=Go()},m(o,s){ke(o,t,s),i&&i.m(o,s),ke(o,n,s)},p(o,s){s&2&&e!==(e=o[5].type+"")&&Ot(t,e),o[8]&&o[9]===null?i||(i=ci(),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(o){o&&(be(t),be(n)),i&&i.d(o)}}}function ci(l){let e;return{c(){e=Ae(` |
							None`)},m(t,n){ke(t,e,n)},d(t){t&&be(e)}}}function za(l){let e,t,n=He(l[9],l[5].type,"py")+"",i;return{c(){e=Me("span"),e.textContent="Default: ",t=Me("span"),i=Ae(n),dt(t,"class","code svelte-1yt946s"),jt(t,"font-size","var(--text-sm)")},m(o,s){ke(o,e,s),ke(o,t,s),pe(t,i)},p(o,s){s&2&&n!==(n=He(o[9],o[5].type,"py")+"")&&Ot(i,n)},d(o){o&&(be(e),be(t))}}}function Fa(l){let e;return{c(){e=Me("span"),e.textContent="Required",jt(e,"font-weight","bold")},m(t,n){ke(t,e,n)},p:Sa,d(t){t&&be(e)}}}function ui(l){let e,t,n,i,o,s=(l[3]!=="bash"&&l[7]?l[7]:"["+l[11]+"]")+"",a,_,r,c,u,f,d,g=l[4]+"",m,v,p=l[6]+"",j,w,b;function C(E,P){return E[3]==="python"?Na:Ma}let h=C(l),k=h(l);function L(E,P){return!E[8]||E[3]=="bash"?Fa:za}let y=L(l),$=y(l);return{c(){e=Me("hr"),t=Ct(),n=Me("div"),i=Me("p"),o=Me("span"),a=Ae(s),_=Ct(),r=Me("span"),k.c(),c=Ct(),$.c(),u=Ct(),f=Me("p"),d=Ae('The input value that is provided in the "'),m=Ae(g),v=Ae('" '),j=Ae(p),w=Ae(`
				component.`),b=Ct(),dt(e,"class","hr svelte-1yt946s"),dt(o,"class","code svelte-1yt946s"),jt(o,"margin-right","10px"),dt(r,"class","code highlight svelte-1yt946s"),jt(r,"margin-right","10px"),jt(i,"white-space","nowrap"),jt(i,"overflow-x","auto"),dt(f,"class","desc svelte-1yt946s"),jt(n,"margin","10px")},m(E,P){ke(E,e,P),ke(E,t,P),ke(E,n,P),pe(n,i),pe(i,o),pe(o,a),pe(i,_),pe(i,r),k.m(r,null),pe(i,c),$.m(i,null),pe(n,u),pe(n,f),pe(f,d),pe(f,m),pe(f,v),pe(f,j),pe(f,w),pe(n,b)},p(E,P){P&10&&s!==(s=(E[3]!=="bash"&&E[7]?E[7]:"["+E[11]+"]")+"")&&Ot(a,s),h===(h=C(E))&&k?k.p(E,P):(k.d(1),k=h(E),k&&(k.c(),k.m(r,null))),y===(y=L(E))&&$?$.p(E,P):($.d(1),$=y(E),$&&($.c(),$.m(i,null))),P&2&&g!==(g=E[4]+"")&&Ot(m,g),P&2&&p!==(p=E[6]+"")&&Ot(j,p)},d(E){E&&(be(e),be(t),be(n)),k.d(),$.d()}}}function fi(l){let e,t,n;return t=new So({props:{margin:!1}}),{c(){e=Me("div"),qa(t.$$.fragment),dt(e,"class","load-wrap")},m(i,o){ke(i,e,o),Ra(t,e,null),n=!0},i(i){n||(wn(t.$$.fragment,i),n=!0)},o(i){ul(t.$$.fragment,i),n=!1},d(i){i&&be(e),Pa(t)}}}function Ua(l){let e,t,n,i=l[1].length+"",o,s,a,_,r,c,u,f,d=l[1].length!=1&&_i(),g=si(l[1]),m=[];for(let p=0;p<g.length;p+=1)m[p]=ui(ai(l,g,p));let v=l[0]&&fi();return{c(){e=Me("h4"),t=Me("div"),t.innerHTML='<div class="toggle-dot svelte-1yt946s"></div>',n=Ae(`
	Accepts `),o=Ae(i),s=Ae(" parameter"),d&&d.c(),a=Ae(":"),_=Ct(),r=Me("div");for(let p=0;p<m.length;p+=1)m[p].c();c=Ct(),v&&v.c(),u=Go(),dt(t,"class","toggle-icon svelte-1yt946s"),dt(e,"class","svelte-1yt946s"),ri(r,"hide",l[0])},m(p,j){ke(p,e,j),pe(e,t),pe(e,n),pe(e,o),pe(e,s),d&&d.m(e,null),pe(e,a),ke(p,_,j),ke(p,r,j);for(let w=0;w<m.length;w+=1)m[w]&&m[w].m(r,null);ke(p,c,j),v&&v.m(p,j),ke(p,u,j),f=!0},p(p,[j]){if((!f||j&2)&&i!==(i=p[1].length+"")&&Ot(o,i),p[1].length!=1?d||(d=_i(),d.c(),d.m(e,a)):d&&(d.d(1),d=null),j&14){g=si(p[1]);let w;for(w=0;w<g.length;w+=1){const b=ai(p,g,w);m[w]?m[w].p(b,j):(m[w]=ui(b),m[w].c(),m[w].m(r,null))}for(;w<m.length;w+=1)m[w].d(1);m.length=g.length}(!f||j&1)&&ri(r,"hide",p[0]),p[0]?v?j&1&&wn(v,1):(v=fi(),v.c(),wn(v,1),v.m(u.parentNode,u)):v&&(Ia(),ul(v,1,1,()=>{v=null}),Ta())},i(p){f||(wn(v),f=!0)},o(p){ul(v),f=!1},d(p){p&&(be(e),be(_),be(r),be(c),be(u)),d&&d.d(),Oa(m,p),v&&v.d(p)}}}function Ba(l,e,t){let{is_running:n}=e,{endpoint_returns:i}=e,{js_returns:o}=e,{current_language:s}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,i=a.endpoint_returns),"js_returns"in a&&t(2,o=a.js_returns),"current_language"in a&&t(3,s=a.current_language)},[n,i,o,s]}class Ha extends La{constructor(e){super(),Da(this,e,Ba,Ua,Va,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),mn()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),mn()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),mn()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),mn()}}const{SvelteComponent:Ga,create_component:Za,destroy_component:Wa,detach:Qa,flush:Ja,init:Xa,insert:Ya,mount_component:Ka,safe_not_equal:xa,set_data:e_,text:t_,transition_in:n_,transition_out:l_}=window.__gradio__svelte__internal;function i_(l){let e;return{c(){e=t_(l[0])},m(t,n){Ya(t,e,n)},p(t,n){n&1&&e_(e,t[0])},d(t){t&&Qa(e)}}}function o_(l){let e,t;return e=new In({props:{size:"sm",$$slots:{default:[i_]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){Za(e.$$.fragment)},m(n,i){Ka(e,n,i),t=!0},p(n,[i]){const o={};i&9&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(n_(e.$$.fragment,n),t=!0)},o(n){l_(e.$$.fragment,n),t=!1},d(n){Wa(e,n)}}}function s_(l,e,t){let{code:n}=e,i="copy";function o(){navigator.clipboard.writeText(n),t(0,i="copied!"),setTimeout(()=>{t(0,i="copy")},1500)}return l.$$set=s=>{"code"in s&&t(2,n=s.code)},[i,o,n]}class st extends Ga{constructor(e){super(),Xa(this,e,s_,o_,xa,{code:2})}get code(){return this.$$.ctx[2]}set code(e){this.$$set({code:e}),Ja()}}const{SvelteComponent:r_,append:yl,attr:Dt,check_outros:a_,create_component:Dn,destroy_component:Rn,detach:tt,element:nt,flush:__,group_outros:c_,init:u_,insert:lt,mount_component:Sn,noop:El,safe_not_equal:f_,space:jl,transition_in:Ht,transition_out:Gt}=window.__gradio__svelte__internal;function p_(l){let e,t,n,i,o,s;return t=new st({props:{code:di}}),{c(){e=nt("div"),Dn(t.$$.fragment),n=jl(),i=nt("div"),o=nt("pre"),o.textContent=`$ ${di}`,Dt(e,"class","copy svelte-hq8ezf"),Dt(o,"class","svelte-hq8ezf")},m(a,_){lt(a,e,_),Sn(t,e,null),lt(a,n,_),lt(a,i,_),yl(i,o),s=!0},p:El,i(a){s||(Ht(t.$$.fragment,a),s=!0)},o(a){Gt(t.$$.fragment,a),s=!1},d(a){a&&(tt(e),tt(n),tt(i)),Rn(t)}}}function m_(l){let e,t,n,i,o,s;return t=new st({props:{code:mi}}),{c(){e=nt("div"),Dn(t.$$.fragment),n=jl(),i=nt("div"),o=nt("pre"),o.textContent=`$ ${mi}`,Dt(e,"class","copy svelte-hq8ezf"),Dt(o,"class","svelte-hq8ezf")},m(a,_){lt(a,e,_),Sn(t,e,null),lt(a,n,_),lt(a,i,_),yl(i,o),s=!0},p:El,i(a){s||(Ht(t.$$.fragment,a),s=!0)},o(a){Gt(t.$$.fragment,a),s=!1},d(a){a&&(tt(e),tt(n),tt(i)),Rn(t)}}}function d_(l){let e,t,n,i,o,s;return t=new st({props:{code:pi}}),{c(){e=nt("div"),Dn(t.$$.fragment),n=jl(),i=nt("div"),o=nt("pre"),o.textContent=`$ ${pi}`,Dt(e,"class","copy svelte-hq8ezf"),Dt(o,"class","svelte-hq8ezf")},m(a,_){lt(a,e,_),Sn(t,e,null),lt(a,n,_),lt(a,i,_),yl(i,o),s=!0},p:El,i(a){s||(Ht(t.$$.fragment,a),s=!0)},o(a){Gt(t.$$.fragment,a),s=!1},d(a){a&&(tt(e),tt(n),tt(i)),Rn(t)}}}function h_(l){let e,t,n,i;const o=[d_,m_,p_],s=[];function a(_,r){return _[0]==="python"?0:_[0]==="javascript"?1:_[0]==="bash"?2:-1}return~(t=a(l))&&(n=s[t]=o[t](l)),{c(){e=nt("code"),n&&n.c(),Dt(e,"class","svelte-hq8ezf")},m(_,r){lt(_,e,r),~t&&s[t].m(e,null),i=!0},p(_,r){let c=t;t=a(_),t===c?~t&&s[t].p(_,r):(n&&(c_(),Gt(s[c],1,1,()=>{s[c]=null}),a_()),~t?(n=s[t],n?n.p(_,r):(n=s[t]=o[t](_),n.c()),Ht(n,1),n.m(e,null)):n=null)},i(_){i||(Ht(n),i=!0)},o(_){Gt(n),i=!1},d(_){_&&tt(e),~t&&s[t].d()}}}function g_(l){let e,t;return e=new rn({props:{border_mode:"contrast",$$slots:{default:[h_]},$$scope:{ctx:l}}}),{c(){Dn(e.$$.fragment)},m(n,i){Sn(e,n,i),t=!0},p(n,[i]){const o={};i&3&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(Ht(e.$$.fragment,n),t=!0)},o(n){Gt(e.$$.fragment,n),t=!1},d(n){Rn(e,n)}}}let pi="pip install gradio_client",mi="npm i -D @gradio/client",di="curl --version";function v_(l,e,t){let{current_language:n}=e;return l.$$set=i=>{"current_language"in i&&t(0,n=i.current_language)},[n]}class $_ extends r_{constructor(e){super(),u_(this,e,v_,g_,f_,{current_language:0})}get current_language(){return this.$$.ctx[0]}set current_language(e){this.$$set({current_language:e}),__()}}const{SvelteComponent:b_,append:Ut,attr:En,detach:Cl,element:jn,empty:k_,flush:ll,init:w_,insert:Al,noop:hi,safe_not_equal:y_,set_data:Zo,text:Cn}=window.__gradio__svelte__internal;function E_(l){let e,t,n,i;return{c(){e=jn("h3"),t=Cn(`fn_index:
		`),n=jn("span"),i=Cn(l[1]),En(n,"class","post svelte-41kcm6"),En(e,"class","svelte-41kcm6")},m(o,s){Al(o,e,s),Ut(e,t),Ut(e,n),Ut(n,i)},p(o,s){s&2&&Zo(i,o[1])},d(o){o&&Cl(e)}}}function j_(l){let e,t,n,i="/"+l[0],o;return{c(){e=jn("h3"),t=Cn(`api_name:
		`),n=jn("span"),o=Cn(i),En(n,"class","post svelte-41kcm6"),En(e,"class","svelte-41kcm6")},m(s,a){Al(s,e,a),Ut(e,t),Ut(e,n),Ut(n,o)},p(s,a){a&1&&i!==(i="/"+s[0])&&Zo(o,i)},d(s){s&&Cl(e)}}}function C_(l){let e;function t(o,s){return o[2]?j_:E_}let n=t(l),i=n(l);return{c(){i.c(),e=k_()},m(o,s){i.m(o,s),Al(o,e,s)},p(o,[s]){n===(n=t(o))&&i?i.p(o,s):(i.d(1),i=n(o),i&&(i.c(),i.m(e.parentNode,e)))},i:hi,o:hi,d(o){o&&Cl(e),i.d(o)}}}function A_(l,e,t){let{api_name:n=null}=e,{fn_index:i=null}=e,{named:o}=e;return l.$$set=s=>{"api_name"in s&&t(0,n=s.api_name),"fn_index"in s&&t(1,i=s.fn_index),"named"in s&&t(2,o=s.named)},[n,i,o]}class Wo extends b_{constructor(e){super(),w_(this,e,A_,C_,y_,{api_name:0,fn_index:1,named:2})}get api_name(){return this.$$.ctx[0]}set api_name(e){this.$$set({api_name:e}),ll()}get fn_index(){return this.$$.ctx[1]}set fn_index(e){this.$$set({fn_index:e}),ll()}get named(){return this.$$.ctx[2]}set named(e){this.$$set({named:e}),ll()}}const{SvelteComponent:L_,append:I,attr:ae,binding_callbacks:il,check_outros:gi,create_component:bt,destroy_component:kt,destroy_each:An,detach:H,element:te,empty:Qo,ensure_array_like:ht,flush:ft,group_outros:vi,init:T_,insert:G,mount_component:wt,noop:q_,safe_not_equal:P_,set_data:ge,space:Vn,text:q,transition_in:Ge,transition_out:Ze}=window.__gradio__svelte__internal;function $i(l,e,t){const n=l.slice();return n[25]=e[t].label,n[20]=e[t].parameter_name,n[26]=e[t].type,n[18]=e[t].python_type,n[27]=e[t].component,n[19]=e[t].example_input,n[28]=e[t].serializer,n[24]=t,n}function bi(l,e,t){const n=l.slice();return n[25]=e[t].label,n[20]=e[t].parameter_name,n[26]=e[t].type,n[18]=e[t].python_type,n[27]=e[t].component,n[19]=e[t].example_input,n[28]=e[t].serializer,n[24]=t,n}function ki(l,e,t){const n=l.slice();return n[27]=e[t].component,n[19]=e[t].example_input,n[24]=t,n}function wi(l,e,t){const n=l.slice();return n[18]=e[t].python_type,n[19]=e[t].example_input,n[20]=e[t].parameter_name,n[21]=e[t].parameter_has_default,n[22]=e[t].parameter_default,n[24]=t,n}function O_(l){let e,t;return e=new Wo({props:{named:l[5],fn_index:l[1]}}),{c(){bt(e.$$.fragment)},m(n,i){wt(e,n,i),t=!0},p(n,i){const o={};i[0]&32&&(o.named=n[5]),i[0]&2&&(o.fn_index=n[1]),e.$set(o)},i(n){t||(Ge(e.$$.fragment,n),t=!0)},o(n){Ze(e.$$.fragment,n),t=!1},d(n){kt(e,n)}}}function I_(l){let e,t;return e=new Wo({props:{named:l[5],api_name:l[0].api_name}}),{c(){bt(e.$$.fragment)},m(n,i){wt(e,n,i),t=!0},p(n,i){const o={};i[0]&32&&(o.named=n[5]),i[0]&1&&(o.api_name=n[0].api_name),e.$set(o)},i(n){t||(Ge(e.$$.fragment,n),t=!0)},o(n){Ze(e.$$.fragment,n),t=!1},d(n){kt(e,n)}}}function D_(l){let e,t;return e=new rn({props:{$$slots:{default:[V_]},$$scope:{ctx:l}}}),{c(){bt(e.$$.fragment)},m(n,i){wt(e,n,i),t=!0},p(n,i){const o={};i[0]&1045|i[1]&2&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(Ge(e.$$.fragment,n),t=!0)},o(n){Ze(e.$$.fragment,n),t=!1},d(n){kt(e,n)}}}function R_(l){let e,t;return e=new rn({props:{$$slots:{default:[U_]},$$scope:{ctx:l}}}),{c(){bt(e.$$.fragment)},m(n,i){wt(e,n,i),t=!0},p(n,i){const o={};i[0]&639|i[1]&2&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(Ge(e.$$.fragment,n),t=!0)},o(n){Ze(e.$$.fragment,n),t=!1},d(n){kt(e,n)}}}function S_(l){let e,t;return e=new rn({props:{$$slots:{default:[H_]},$$scope:{ctx:l}}}),{c(){bt(e.$$.fragment)},m(n,i){wt(e,n,i),t=!0},p(n,i){const o={};i[0]&349|i[1]&2&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(Ge(e.$$.fragment,n),t=!0)},o(n){Ze(e.$$.fragment,n),t=!1},d(n){kt(e,n)}}}function yi(l){let e;return{c(){e=q(",")},m(t,n){G(t,e,n)},d(t){t&&H(e)}}}function Ei(l){let e,t=He(l[19],l[18].type,"bash")+"",n,i,o=l[24]<l[4].length-1&&yi();return{c(){e=q(`
							`),n=q(t),o&&o.c(),i=Qo()},m(s,a){G(s,e,a),G(s,n,a),o&&o.m(s,a),G(s,i,a)},p(s,a){a[0]&16&&t!==(t=He(s[19],s[18].type,"bash")+"")&&ge(n,t),s[24]<s[4].length-1?o||(o=yi(),o.c(),o.m(i.parentNode,i)):o&&(o.d(1),o=null)},d(s){s&&(H(e),H(n),H(i)),o&&o.d(s)}}}function V_(l){let e,t,n,i,o,s,a,_,r,c=l[0].api_name+"",u,f,d="{",g,m,v,p="}",j,w,b="{",C,h,k="}",L,y,$,E,P=l[0].api_name+"",D,U,O;n=new st({props:{code:l[10]?.innerText}});let V=ht(l[4]),M=[];for(let R=0;R<V.length;R+=1)M[R]=Ei($i(l,V,R));return{c(){e=te("code"),t=te("div"),bt(n.$$.fragment),i=Vn(),o=te("div"),s=te("pre"),a=q("curl -X POST "),_=q(l[2]),r=q("call/"),u=q(c),f=q(` -s -H "Content-Type: application/json" -d '`),g=q(d),m=q(`
  "data": [`);for(let R=0;R<M.length;R+=1)M[R].c();v=q(`
]`),j=q(p),w=q(`' \\
  | awk -F'"' '`),C=q(b),h=q(" print $4"),L=q(k),y=q(`'  \\
  | read EVENT_ID; curl -N `),$=q(l[2]),E=q("call/"),D=q(P),U=q("/$EVENT_ID"),ae(t,"class","copy svelte-114qcyq"),ae(s,"class","svelte-114qcyq"),ae(e,"class","svelte-114qcyq")},m(R,se){G(R,e,se),I(e,t),wt(n,t,null),I(e,i),I(e,o),I(o,s),I(s,a),I(s,_),I(s,r),I(s,u),I(s,f),I(s,g),I(s,m);for(let de=0;de<M.length;de+=1)M[de]&&M[de].m(s,null);I(s,v),I(s,j),I(s,w),I(s,C),I(s,h),I(s,L),I(s,y),I(s,$),I(s,E),I(s,D),I(s,U),l[16](o),O=!0},p(R,se){const de={};if(se[0]&1024&&(de.code=R[10]?.innerText),n.$set(de),(!O||se[0]&4)&&ge(_,R[2]),(!O||se[0]&1)&&c!==(c=R[0].api_name+"")&&ge(u,c),se[0]&16){V=ht(R[4]);let Y;for(Y=0;Y<V.length;Y+=1){const Qe=$i(R,V,Y);M[Y]?M[Y].p(Qe,se):(M[Y]=Ei(Qe),M[Y].c(),M[Y].m(s,v))}for(;Y<M.length;Y+=1)M[Y].d(1);M.length=V.length}(!O||se[0]&4)&&ge($,R[2]),(!O||se[0]&1)&&P!==(P=R[0].api_name+"")&&ge(D,P)},i(R){O||(Ge(n.$$.fragment,R),O=!0)},o(R){Ze(n.$$.fragment,R),O=!1},d(R){R&&H(e),kt(n),An(M,R),l[16](null)}}}function ji(l){let e,t,n,i=l[19].url+"",o,s,a=l[27]+"",_,r,c,u;return{c(){e=q(`
const response_`),t=q(l[24]),n=q(' = await fetch("'),o=q(i),s=q(`");
const example`),_=q(a),r=q(" = await response_"),c=q(l[24]),u=q(`.blob();
						`)},m(f,d){G(f,e,d),G(f,t,d),G(f,n,d),G(f,o,d),G(f,s,d),G(f,_,d),G(f,r,d),G(f,c,d),G(f,u,d)},p:q_,d(f){f&&(H(e),H(t),H(n),H(o),H(s),H(_),H(r),H(c),H(u))}}}function Ci(l){let e,t,n;return{c(){e=q(', {auth: ["'),t=q(l[6]),n=q('", **password**]}')},m(i,o){G(i,e,o),G(i,t,o),G(i,n,o)},p(i,o){o[0]&64&&ge(t,i[6])},d(i){i&&(H(e),H(t),H(n))}}}function M_(l){let e;return{c(){e=q(l[1])},m(t,n){G(t,e,n)},p(t,n){n[0]&2&&ge(e,t[1])},d(t){t&&H(e)}}}function N_(l){let e,t,n=l[0].api_name+"",i,o;return{c(){e=te("span"),t=q('"/'),i=q(n),o=q('"'),ae(e,"class","api-name svelte-114qcyq")},m(s,a){G(s,e,a),I(e,t),I(e,i),I(e,o)},p(s,a){a[0]&1&&n!==(n=s[0].api_name+"")&&ge(i,n)},d(s){s&&H(e)}}}function z_(l){let e,t,n=l[20]+"",i,o,s=He(l[19],l[18].type,"js")+"",a,_;return{c(){e=q(`		
		`),t=te("span"),i=q(n),o=q(": "),a=q(s),_=q(", "),ae(t,"class","example-inputs")},m(r,c){G(r,e,c),G(r,t,c),I(t,i),I(t,o),I(t,a),G(r,_,c)},p(r,c){c[0]&16&&n!==(n=r[20]+"")&&ge(i,n),c[0]&16&&s!==(s=He(r[19],r[18].type,"js")+"")&&ge(a,s)},d(r){r&&(H(e),H(t),H(_))}}}function F_(l){let e,t,n=l[20]+"",i,o,s=l[27]+"",a,_,r;return{c(){e=q(`
				`),t=te("span"),i=q(n),o=q(": example"),a=q(s),_=q(", "),r=te("span"),r.innerHTML="",ae(t,"class","example-inputs"),ae(r,"class","desc svelte-114qcyq")},m(c,u){G(c,e,u),G(c,t,u),I(t,i),I(t,o),I(t,a),G(c,_,u),G(c,r,u)},p(c,u){u[0]&16&&n!==(n=c[20]+"")&&ge(i,n),u[0]&16&&s!==(s=c[27]+"")&&ge(a,s)},d(c){c&&(H(e),H(t),H(_),H(r))}}}function Ai(l){let e,t;function n(s,a){return a[0]&16&&(e=null),e==null&&(e=!!s[12].includes(s[27])),e?F_:z_}let i=n(l,[-1,-1]),o=i(l);return{c(){o.c(),t=Qo()},m(s,a){o.m(s,a),G(s,t,a)},p(s,a){i===(i=n(s,a))&&o?o.p(s,a):(o.d(1),o=i(s),o&&(o.c(),o.m(t.parentNode,t)))},d(s){s&&H(t),o.d(s)}}}function U_(l){let e,t,n,i,o,s,a,_,r,c,u=(l[3]||l[2])+"",f,d,g,m,v,p;n=new st({props:{code:l[9]?.innerText}});let j=ht(l[13]),w=[];for(let $=0;$<j.length;$+=1)w[$]=ji(ki(l,j,$));let b=l[6]!==null&&Ci(l);function C($,E){return $[5]?N_:M_}let h=C(l),k=h(l),L=ht(l[4]),y=[];for(let $=0;$<L.length;$+=1)y[$]=Ai(bi(l,L,$));return{c(){e=te("code"),t=te("div"),bt(n.$$.fragment),i=Vn(),o=te("div"),s=te("pre"),a=q(`import { Client } from "@gradio/client";
`);for(let $=0;$<w.length;$+=1)w[$].c();_=q(`
const client = await Client.connect(`),r=te("span"),c=q('"'),f=q(u),d=q('"'),b&&b.c(),g=q(`);
const result = await client.predict(`),k.c(),m=q(", { ");for(let $=0;$<y.length;$+=1)y[$].c();v=q(`
});

console.log(result.data);
`),ae(t,"class","copy svelte-114qcyq"),ae(r,"class","token string svelte-114qcyq"),ae(s,"class","svelte-114qcyq"),ae(e,"class","svelte-114qcyq")},m($,E){G($,e,E),I(e,t),wt(n,t,null),I(e,i),I(e,o),I(o,s),I(s,a);for(let P=0;P<w.length;P+=1)w[P]&&w[P].m(s,null);I(s,_),I(s,r),I(r,c),I(r,f),I(r,d),b&&b.m(s,null),I(s,g),k.m(s,null),I(s,m);for(let P=0;P<y.length;P+=1)y[P]&&y[P].m(s,null);I(s,v),l[15](o),p=!0},p($,E){const P={};if(E[0]&512&&(P.code=$[9]?.innerText),n.$set(P),E[0]&8192){j=ht($[13]);let D;for(D=0;D<j.length;D+=1){const U=ki($,j,D);w[D]?w[D].p(U,E):(w[D]=ji(U),w[D].c(),w[D].m(s,_))}for(;D<w.length;D+=1)w[D].d(1);w.length=j.length}if((!p||E[0]&12)&&u!==(u=($[3]||$[2])+"")&&ge(f,u),$[6]!==null?b?b.p($,E):(b=Ci($),b.c(),b.m(s,g)):b&&(b.d(1),b=null),h===(h=C($))&&k?k.p($,E):(k.d(1),k=h($),k&&(k.c(),k.m(s,m))),E[0]&4112){L=ht($[4]);let D;for(D=0;D<L.length;D+=1){const U=bi($,L,D);y[D]?y[D].p(U,E):(y[D]=Ai(U),y[D].c(),y[D].m(s,v))}for(;D<y.length;D+=1)y[D].d(1);y.length=L.length}},i($){p||(Ge(n.$$.fragment,$),p=!0)},o($){Ze(n.$$.fragment,$),p=!1},d($){$&&H(e),kt(n),An(w,$),b&&b.d(),k.d(),An(y,$),l[15](null)}}}function B_(l){let e;return{c(){e=q(", handle_file")},m(t,n){G(t,e,n)},d(t){t&&H(e)}}}function Li(l){let e,t,n;return{c(){e=q(', auth=("'),t=q(l[6]),n=q('", **password**)')},m(i,o){G(i,e,o),G(i,t,o),G(i,n,o)},p(i,o){o[0]&64&&ge(t,i[6])},d(i){i&&(H(e),H(t),H(n))}}}function Ti(l){let e,t=l[20]?l[20]+"=":"",n,i,o=He(l[21]?l[22]:l[19],l[18].type,"py")+"",s,a;return{c(){e=q(`
		`),n=q(t),i=te("span"),s=q(o),a=q(",")},m(_,r){G(_,e,r),G(_,n,r),G(_,i,r),I(i,s),G(_,a,r)},p(_,r){r[0]&16&&t!==(t=_[20]?_[20]+"=":"")&&ge(n,t),r[0]&16&&o!==(o=He(_[21]?_[22]:_[19],_[18].type,"py")+"")&&ge(s,o)},d(_){_&&(H(e),H(n),H(i),H(a))}}}function H_(l){let e,t,n,i,o,s,a,_,r,c,u,f,d,g=(l[3]||l[2])+"",m,v,p,j,w,b,C,h,k=l[0].api_name+"",L,y,$,E,P,D;n=new st({props:{code:l[8]?.innerText}});let U=l[11]&&B_(),O=l[6]!==null&&Li(l),V=ht(l[4]),M=[];for(let R=0;R<V.length;R+=1)M[R]=Ti(wi(l,V,R));return{c(){e=te("code"),t=te("div"),bt(n.$$.fragment),i=Vn(),o=te("div"),s=te("pre"),a=te("span"),a.textContent="from",_=q(" gradio_client "),r=te("span"),r.textContent="import",c=q(" Client"),U&&U.c(),u=q(`

client = Client(`),f=te("span"),d=q('"'),m=q(g),v=q('"'),O&&O.c(),p=q(`)
result = client.`),j=te("span"),j.textContent="predict",w=q("(");for(let R=0;R<M.length;R+=1)M[R].c();b=q(`
		api_name=`),C=te("span"),h=q('"/'),L=q(k),y=q('"'),$=q(`
)
`),E=te("span"),E.textContent="print",P=q("(result)"),ae(t,"class","copy svelte-114qcyq"),ae(a,"class","highlight"),ae(r,"class","highlight"),ae(f,"class","token string svelte-114qcyq"),ae(j,"class","highlight"),ae(C,"class","api-name svelte-114qcyq"),ae(E,"class","highlight"),ae(s,"class","svelte-114qcyq"),ae(e,"class","svelte-114qcyq")},m(R,se){G(R,e,se),I(e,t),wt(n,t,null),I(e,i),I(e,o),I(o,s),I(s,a),I(s,_),I(s,r),I(s,c),U&&U.m(s,null),I(s,u),I(s,f),I(f,d),I(f,m),I(f,v),O&&O.m(s,null),I(s,p),I(s,j),I(s,w);for(let de=0;de<M.length;de+=1)M[de]&&M[de].m(s,null);I(s,b),I(s,C),I(C,h),I(C,L),I(C,y),I(s,$),I(s,E),I(s,P),l[14](o),D=!0},p(R,se){const de={};if(se[0]&256&&(de.code=R[8]?.innerText),n.$set(de),(!D||se[0]&12)&&g!==(g=(R[3]||R[2])+"")&&ge(m,g),R[6]!==null?O?O.p(R,se):(O=Li(R),O.c(),O.m(s,p)):O&&(O.d(1),O=null),se[0]&16){V=ht(R[4]);let Y;for(Y=0;Y<V.length;Y+=1){const Qe=wi(R,V,Y);M[Y]?M[Y].p(Qe,se):(M[Y]=Ti(Qe),M[Y].c(),M[Y].m(s,b))}for(;Y<M.length;Y+=1)M[Y].d(1);M.length=V.length}(!D||se[0]&1)&&k!==(k=R[0].api_name+"")&&ge(L,k)},i(R){D||(Ge(n.$$.fragment,R),D=!0)},o(R){Ze(n.$$.fragment,R),D=!1},d(R){R&&H(e),kt(n),U&&U.d(),O&&O.d(),An(M,R),l[14](null)}}}function G_(l){let e,t,n,i,o,s,a;const _=[I_,O_],r=[];function c(g,m){return g[5]?0:1}t=c(l),n=r[t]=_[t](l);const u=[S_,R_,D_],f=[];function d(g,m){return g[7]==="python"?0:g[7]==="javascript"?1:g[7]==="bash"?2:-1}return~(o=d(l))&&(s=f[o]=u[o](l)),{c(){e=te("div"),n.c(),i=Vn(),s&&s.c(),ae(e,"class","container svelte-114qcyq")},m(g,m){G(g,e,m),r[t].m(e,null),I(e,i),~o&&f[o].m(e,null),a=!0},p(g,m){let v=t;t=c(g),t===v?r[t].p(g,m):(vi(),Ze(r[v],1,1,()=>{r[v]=null}),gi(),n=r[t],n?n.p(g,m):(n=r[t]=_[t](g),n.c()),Ge(n,1),n.m(e,i));let p=o;o=d(g),o===p?~o&&f[o].p(g,m):(s&&(vi(),Ze(f[p],1,1,()=>{f[p]=null}),gi()),~o?(s=f[o],s?s.p(g,m):(s=f[o]=u[o](g),s.c()),Ge(s,1),s.m(e,null)):s=null)},i(g){a||(Ge(n),Ge(s),a=!0)},o(g){Ze(n),Ze(s),a=!1},d(g){g&&H(e),r[t].d(),~o&&f[o].d()}}}function Z_(l,e,t){let{dependency:n}=e,{dependency_index:i}=e,{root:o}=e,{space_id:s}=e,{endpoint_parameters:a}=e,{named:_}=e,{username:r}=e,{current_language:c}=e,u,f,d,g=a.some(b=>Ho(b.example_input)),m=["Audio","File","Image","Video"],v=a.filter(b=>m.includes(b.component));function p(b){il[b?"unshift":"push"](()=>{u=b,t(8,u)})}function j(b){il[b?"unshift":"push"](()=>{f=b,t(9,f)})}function w(b){il[b?"unshift":"push"](()=>{d=b,t(10,d)})}return l.$$set=b=>{"dependency"in b&&t(0,n=b.dependency),"dependency_index"in b&&t(1,i=b.dependency_index),"root"in b&&t(2,o=b.root),"space_id"in b&&t(3,s=b.space_id),"endpoint_parameters"in b&&t(4,a=b.endpoint_parameters),"named"in b&&t(5,_=b.named),"username"in b&&t(6,r=b.username),"current_language"in b&&t(7,c=b.current_language)},[n,i,o,s,a,_,r,c,u,f,d,g,m,v,p,j,w]}class W_ extends L_{constructor(e){super(),T_(this,e,Z_,G_,P_,{dependency:0,dependency_index:1,root:2,space_id:3,endpoint_parameters:4,named:5,username:6,current_language:7},null,[-1,-1])}get dependency(){return this.$$.ctx[0]}set dependency(e){this.$$set({dependency:e}),ft()}get dependency_index(){return this.$$.ctx[1]}set dependency_index(e){this.$$set({dependency_index:e}),ft()}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),ft()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),ft()}get endpoint_parameters(){return this.$$.ctx[4]}set endpoint_parameters(e){this.$$set({endpoint_parameters:e}),ft()}get named(){return this.$$.ctx[5]}set named(e){this.$$set({named:e}),ft()}get username(){return this.$$.ctx[6]}set username(e){this.$$set({username:e}),ft()}get current_language(){return this.$$.ctx[7]}set current_language(e){this.$$set({current_language:e}),ft()}}const{SvelteComponent:Q_,append:S,attr:$e,binding_callbacks:ol,check_outros:J_,create_component:Mn,destroy_component:Nn,destroy_each:Ll,detach:ce,element:_e,empty:X_,ensure_array_like:Zt,flush:Vt,group_outros:Y_,init:K_,insert:ue,mount_component:zn,safe_not_equal:x_,set_data:Ne,space:Fn,text:F,transition_in:Wt,transition_out:Qt}=window.__gradio__svelte__internal,{onMount:ec,tick:tc}=window.__gradio__svelte__internal;function qi(l,e,t){const n=l.slice();return n[19]=e[t].call,n[20]=e[t].api_name,n}function Pi(l,e,t){const n=l.slice();return n[19]=e[t].call,n[20]=e[t].api_name,n}function Oi(l,e,t){const n=l.slice();return n[19]=e[t].call,n[20]=e[t].api_name,n}function nc(l){let e,t,n,i,o,s;n=new st({props:{code:l[6]?.innerText}});let a=Zt(l[9]),_=[];for(let r=0;r<a.length;r+=1)_[r]=Ii(qi(l,a,r));return{c(){e=_e("code"),t=_e("div"),Mn(n.$$.fragment),i=Fn(),o=_e("div");for(let r=0;r<_.length;r+=1)_[r].c();$e(t,"class","copy svelte-j71ub0"),$e(e,"class","svelte-j71ub0")},m(r,c){ue(r,e,c),S(e,t),zn(n,t,null),S(e,i),S(e,o);for(let u=0;u<_.length;u+=1)_[u]&&_[u].m(o,null);l[15](o),s=!0},p(r,c){const u={};if(c&64&&(u.code=r[6]?.innerText),n.$set(u),c&513){a=Zt(r[9]);let f;for(f=0;f<a.length;f+=1){const d=qi(r,a,f);_[f]?_[f].p(d,c):(_[f]=Ii(d),_[f].c(),_[f].m(o,null))}for(;f<_.length;f+=1)_[f].d(1);_.length=a.length}},i(r){s||(Wt(n.$$.fragment,r),s=!0)},o(r){Qt(n.$$.fragment,r),s=!1},d(r){r&&ce(e),Nn(n),Ll(_,r),l[15](null)}}}function lc(l){let e,t,n,i,o,s,a,_,r,c,u,f,d;n=new st({props:{code:l[5]?.innerText}});let g=l[2]!==null&&Di(l),m=Zt(l[8]),v=[];for(let p=0;p<m.length;p+=1)v[p]=Si(Pi(l,m,p));return{c(){e=_e("code"),t=_e("div"),Mn(n.$$.fragment),i=Fn(),o=_e("div"),s=_e("pre"),a=F(`import { Client } from "@gradio/client";

const app = await Client.connect(`),_=_e("span"),r=F('"'),c=F(l[0]),u=F('"'),g&&g.c(),f=F(`);
					`);for(let p=0;p<v.length;p+=1)v[p].c();$e(t,"class","copy svelte-j71ub0"),$e(_,"class","token string svelte-j71ub0"),$e(s,"class","svelte-j71ub0"),$e(e,"class","svelte-j71ub0")},m(p,j){ue(p,e,j),S(e,t),zn(n,t,null),S(e,i),S(e,o),S(o,s),S(s,a),S(s,_),S(_,r),S(_,c),S(_,u),g&&g.m(s,null),S(s,f);for(let w=0;w<v.length;w+=1)v[w]&&v[w].m(s,null);l[14](o),d=!0},p(p,j){const w={};if(j&32&&(w.code=p[5]?.innerText),n.$set(w),(!d||j&1)&&Ne(c,p[0]),p[2]!==null?g?g.p(p,j):(g=Di(p),g.c(),g.m(s,f)):g&&(g.d(1),g=null),j&256){m=Zt(p[8]);let b;for(b=0;b<m.length;b+=1){const C=Pi(p,m,b);v[b]?v[b].p(C,j):(v[b]=Si(C),v[b].c(),v[b].m(s,null))}for(;b<v.length;b+=1)v[b].d(1);v.length=m.length}},i(p){d||(Wt(n.$$.fragment,p),d=!0)},o(p){Qt(n.$$.fragment,p),d=!1},d(p){p&&ce(e),Nn(n),g&&g.d(),Ll(v,p),l[14](null)}}}function ic(l){let e,t,n,i,o,s,a,_,r,c,u,f,d,g,m,v;n=new st({props:{code:l[4]}});let p=l[2]!==null&&Vi(l),j=Zt(l[7]),w=[];for(let b=0;b<j.length;b+=1)w[b]=Mi(Oi(l,j,b));return{c(){e=_e("code"),t=_e("div"),Mn(n.$$.fragment),i=Fn(),o=_e("div"),s=_e("pre"),a=_e("span"),a.textContent="from",_=F(" gradio_client "),r=_e("span"),r.textContent="import",c=F(` Client, file

client = Client(`),u=_e("span"),f=F('"'),d=F(l[0]),g=F('"'),p&&p.c(),m=F(`)
`);for(let b=0;b<w.length;b+=1)w[b].c();$e(t,"class","copy svelte-j71ub0"),$e(a,"class","highlight"),$e(r,"class","highlight"),$e(u,"class","token string svelte-j71ub0"),$e(s,"class","svelte-j71ub0"),$e(e,"class","svelte-j71ub0")},m(b,C){ue(b,e,C),S(e,t),zn(n,t,null),S(e,i),S(e,o),S(o,s),S(s,a),S(s,_),S(s,r),S(s,c),S(s,u),S(u,f),S(u,d),S(u,g),p&&p.m(s,null),S(s,m);for(let h=0;h<w.length;h+=1)w[h]&&w[h].m(s,null);l[13](o),v=!0},p(b,C){const h={};if(C&16&&(h.code=b[4]),n.$set(h),(!v||C&1)&&Ne(d,b[0]),b[2]!==null?p?p.p(b,C):(p=Vi(b),p.c(),p.m(s,m)):p&&(p.d(1),p=null),C&128){j=Zt(b[7]);let k;for(k=0;k<j.length;k+=1){const L=Oi(b,j,k);w[k]?w[k].p(L,C):(w[k]=Mi(L),w[k].c(),w[k].m(s,null))}for(;k<w.length;k+=1)w[k].d(1);w.length=j.length}},i(b){v||(Wt(n.$$.fragment,b),v=!0)},o(b){Qt(n.$$.fragment,b),v=!1},d(b){b&&ce(e),Nn(n),p&&p.d(),Ll(w,b),l[13](null)}}}function Ii(l){let e,t,n,i,o=l[20]+"",s,a,_="{",r,c,u=l[19]+"",f,d,g="}",m,v,p="{",j,w,b="}",C,h,k,L,y=l[20]+"",$,E,P,D;return{c(){e=_e("pre"),t=F("curl -X POST "),n=F(l[0]),i=F("call/"),s=F(o),a=F(` -s -H "Content-Type: application/json" -d '`),r=F(_),c=F(` 
	"data": [`),f=F(u),d=F("]"),m=F(g),v=F(`' \\
  | awk -F'"' '`),j=F(p),w=F(" print $4"),C=F(b),h=F(`' \\
  | read EVENT_ID; curl -N `),k=F(l[0]),L=F("call/"),$=F(y),E=F("/$EVENT_ID"),P=Fn(),D=_e("br"),$e(e,"class","svelte-j71ub0")},m(U,O){ue(U,e,O),S(e,t),S(e,n),S(e,i),S(e,s),S(e,a),S(e,r),S(e,c),S(e,f),S(e,d),S(e,m),S(e,v),S(e,j),S(e,w),S(e,C),S(e,h),S(e,k),S(e,L),S(e,$),S(e,E),ue(U,P,O),ue(U,D,O)},p(U,O){O&1&&Ne(n,U[0]),O&512&&o!==(o=U[20]+"")&&Ne(s,o),O&512&&u!==(u=U[19]+"")&&Ne(f,u),O&1&&Ne(k,U[0]),O&512&&y!==(y=U[20]+"")&&Ne($,y)},d(U){U&&(ce(e),ce(P),ce(D))}}}function Di(l){let e,t,n;return{c(){e=F(', {auth: ["'),t=F(l[2]),n=F('", **password**]}')},m(i,o){ue(i,e,o),ue(i,t,o),ue(i,n,o)},p(i,o){o&4&&Ne(t,i[2])},d(i){i&&(ce(e),ce(t),ce(n))}}}function Ri(l){let e;return{c(){e=F(`,
							`)},m(t,n){ue(t,e,n)},d(t){t&&ce(e)}}}function Si(l){let e,t,n,i=l[20]+"",o,s,a=l[19]+"",_,r,c=l[19]&&Ri();return{c(){e=F(`
await client.predict(`),t=_e("span"),n=F(`
  "/`),o=F(i),s=F('"'),c&&c.c(),_=F(a),r=F(`);
						`),$e(t,"class","api-name svelte-j71ub0")},m(u,f){ue(u,e,f),ue(u,t,f),S(t,n),S(t,o),S(t,s),c&&c.m(u,f),ue(u,_,f),ue(u,r,f)},p(u,f){f&256&&i!==(i=u[20]+"")&&Ne(o,i),u[19]?c||(c=Ri(),c.c(),c.m(_.parentNode,_)):c&&(c.d(1),c=null),f&256&&a!==(a=u[19]+"")&&Ne(_,a)},d(u){u&&(ce(e),ce(t),ce(_),ce(r)),c&&c.d(u)}}}function Vi(l){let e,t,n;return{c(){e=F(', auth=("'),t=F(l[2]),n=F('", **password**)')},m(i,o){ue(i,e,o),ue(i,t,o),ue(i,n,o)},p(i,o){o&4&&Ne(t,i[2])},d(i){i&&(ce(e),ce(t),ce(n))}}}function Mi(l){let e,t,n,i=l[19]+"",o,s,a,_,r=l[20]+"",c,u,f;return{c(){e=F(`
client.`),t=_e("span"),n=F(`predict(
`),o=F(i),s=F("  api_name="),a=_e("span"),_=F('"/'),c=F(r),u=F('"'),f=F(`
)
`),$e(a,"class","api-name svelte-j71ub0"),$e(t,"class","highlight")},m(d,g){ue(d,e,g),ue(d,t,g),S(t,n),S(t,o),S(t,s),S(t,a),S(a,_),S(a,c),S(a,u),S(t,f)},p(d,g){g&128&&i!==(i=d[19]+"")&&Ne(o,i),g&128&&r!==(r=d[20]+"")&&Ne(c,r)},d(d){d&&(ce(e),ce(t))}}}function oc(l){let e,t,n,i;const o=[ic,lc,nc],s=[];function a(_,r){return _[1]==="python"?0:_[1]==="javascript"?1:_[1]==="bash"?2:-1}return~(e=a(l))&&(t=s[e]=o[e](l)),{c(){t&&t.c(),n=X_()},m(_,r){~e&&s[e].m(_,r),ue(_,n,r),i=!0},p(_,r){let c=e;e=a(_),e===c?~e&&s[e].p(_,r):(t&&(Y_(),Qt(s[c],1,1,()=>{s[c]=null}),J_()),~e?(t=s[e],t?t.p(_,r):(t=s[e]=o[e](_),t.c()),Wt(t,1),t.m(n.parentNode,n)):t=null)},i(_){i||(Wt(t),i=!0)},o(_){Qt(t),i=!1},d(_){_&&ce(n),~e&&s[e].d(_)}}}function sc(l){let e,t,n;return t=new rn({props:{border_mode:"focus",$$slots:{default:[oc]},$$scope:{ctx:l}}}),{c(){e=_e("div"),Mn(t.$$.fragment),$e(e,"class","container svelte-j71ub0")},m(i,o){ue(i,e,o),zn(t,e,null),n=!0},p(i,[o]){const s={};o&134218751&&(s.$$scope={dirty:o,ctx:i}),t.$set(s)},i(i){n||(Wt(t.$$.fragment,i),n=!0)},o(i){Qt(t.$$.fragment,i),n=!1},d(i){i&&ce(e),Nn(t)}}}function rc(l,e,t){let{dependencies:n}=e,{short_root:i}=e,{root:o}=e,{current_language:s}=e,{username:a}=e,_,r,c,u,{api_calls:f=[]}=e;async function d(){return await(await fetch(o+"info/?all_endpoints=true")).json()}let g,m=[],v=[],p=[];function j(h,k){const L=`/${n[h.fn_index].api_name}`,$=h.data.filter(E=>typeof E<"u").map((E,P)=>{if(g[L]){const D=g[L].parameters[P];if(!D)return;const U=D.parameter_name,O=D.python_type.type;if(k==="py")return`  ${U}=${He(E,O,"py")}`;if(k==="js")return`    ${U}: ${He(E,O,"js")}`;if(k==="bash")return`    ${He(E,O,"bash")}`}return`  ${He(E,void 0,k)}`}).filter(E=>typeof E<"u").join(`,
`);if($){if(k==="py")return`${$},
`;if(k==="js")return`{
${$},
}`;if(k==="bash")return`
${$}
`}return k==="py"?"":`
`}ec(async()=>{g=(await d()).named_endpoints;let k=f.map(E=>j(E,"py")),L=f.map(E=>j(E,"js")),y=f.map(E=>j(E,"bash")),$=f.map(E=>n[E.fn_index].api_name||"");t(7,m=k.map((E,P)=>({call:E,api_name:$[P]}))),t(8,v=L.map((E,P)=>({call:E,api_name:$[P]}))),t(9,p=y.map((E,P)=>({call:E,api_name:$[P]}))),await tc(),t(4,r=_.innerText)});function w(h){ol[h?"unshift":"push"](()=>{_=h,t(3,_)})}function b(h){ol[h?"unshift":"push"](()=>{c=h,t(5,c)})}function C(h){ol[h?"unshift":"push"](()=>{u=h,t(6,u)})}return l.$$set=h=>{"dependencies"in h&&t(10,n=h.dependencies),"short_root"in h&&t(0,i=h.short_root),"root"in h&&t(11,o=h.root),"current_language"in h&&t(1,s=h.current_language),"username"in h&&t(2,a=h.username),"api_calls"in h&&t(12,f=h.api_calls)},[i,s,a,_,r,c,u,m,v,p,n,o,f,w,b,C]}class ac extends Q_{constructor(e){super(),K_(this,e,rc,sc,x_,{dependencies:10,short_root:0,root:11,current_language:1,username:2,api_calls:12})}get dependencies(){return this.$$.ctx[10]}set dependencies(e){this.$$set({dependencies:e}),Vt()}get short_root(){return this.$$.ctx[0]}set short_root(e){this.$$set({short_root:e}),Vt()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),Vt()}get current_language(){return this.$$.ctx[1]}set current_language(e){this.$$set({current_language:e}),Vt()}get username(){return this.$$.ctx[2]}set username(e){this.$$set({username:e}),Vt()}get api_calls(){return this.$$.ctx[12]}set api_calls(e){this.$$set({api_calls:e}),Vt()}}const _c="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e",cc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e",uc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e",{SvelteComponent:fc,append:Se,attr:It,check_outros:pc,create_component:mc,destroy_component:dc,destroy_each:hc,detach:we,element:xe,empty:gc,ensure_array_like:Ni,flush:dn,group_outros:vc,init:$c,insert:ye,mount_component:bc,noop:kc,safe_not_equal:wc,set_data:Jt,set_style:yc,space:Nt,text:ze,toggle_class:zi,transition_in:yn,transition_out:fl}=window.__gradio__svelte__internal;function Fi(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function Ec(l){let e;return{c(){e=ze("1 element")},m(t,n){ye(t,e,n)},p:kc,d(t){t&&we(e)}}}function jc(l){let e=l[3]=="python"?"tuple":"list",t,n,i=l[1].length+"",o,s;return{c(){t=ze(e),n=ze(" of "),o=ze(i),s=ze(`
		elements`)},m(a,_){ye(a,t,_),ye(a,n,_),ye(a,o,_),ye(a,s,_)},p(a,_){_&8&&e!==(e=a[3]=="python"?"tuple":"list")&&Jt(t,e),_&2&&i!==(i=a[1].length+"")&&Jt(o,i)},d(a){a&&(we(t),we(n),we(o),we(s))}}}function Ui(l){let e;return{c(){e=xe("span"),e.textContent=`[${l[10]}]`,It(e,"class","code svelte-16h224k")},m(t,n){ye(t,e,n)},d(t){t&&we(e)}}}function Cc(l){let e=l[2][l[10]].type+"",t;return{c(){t=ze(e)},m(n,i){ye(n,t,i)},p(n,i){i&4&&e!==(e=n[2][n[10]].type+"")&&Jt(t,e)},d(n){n&&we(t)}}}function Ac(l){let e=l[6].type+"",t;return{c(){t=ze(e)},m(n,i){ye(n,t,i)},p(n,i){i&2&&e!==(e=n[6].type+"")&&Jt(t,e)},d(n){n&&we(t)}}}function Bi(l){let e,t,n,i,o,s,a,_,r,c=l[4]+"",u,f,d=l[7]+"",g,m,v,p=l[1].length>1&&Ui(l);function j(C,h){return C[3]==="python"?Ac:Cc}let w=j(l),b=w(l);return{c(){e=xe("hr"),t=Nt(),n=xe("div"),i=xe("p"),p&&p.c(),o=Nt(),s=xe("span"),b.c(),a=Nt(),_=xe("p"),r=ze('The output value that appears in the "'),u=ze(c),f=ze('" '),g=ze(d),m=ze(`
				component.`),v=Nt(),It(e,"class","hr svelte-16h224k"),It(s,"class","code highlight svelte-16h224k"),It(_,"class","desc svelte-16h224k"),yc(n,"margin","10px")},m(C,h){ye(C,e,h),ye(C,t,h),ye(C,n,h),Se(n,i),p&&p.m(i,null),Se(i,o),Se(i,s),b.m(s,null),Se(n,a),Se(n,_),Se(_,r),Se(_,u),Se(_,f),Se(_,g),Se(_,m),Se(n,v)},p(C,h){C[1].length>1?p||(p=Ui(C),p.c(),p.m(i,o)):p&&(p.d(1),p=null),w===(w=j(C))&&b?b.p(C,h):(b.d(1),b=w(C),b&&(b.c(),b.m(s,null))),h&2&&c!==(c=C[4]+"")&&Jt(u,c),h&2&&d!==(d=C[7]+"")&&Jt(g,d)},d(C){C&&(we(e),we(t),we(n)),p&&p.d(),b.d()}}}function Hi(l){let e,t,n;return t=new So({props:{margin:!1}}),{c(){e=xe("div"),mc(t.$$.fragment),It(e,"class","load-wrap")},m(i,o){ye(i,e,o),bc(t,e,null),n=!0},i(i){n||(yn(t.$$.fragment,i),n=!0)},o(i){fl(t.$$.fragment,i),n=!1},d(i){i&&we(e),dc(t)}}}function Lc(l){let e,t,n,i,o,s,a,_;function r(m,v){return m[1].length>1?jc:Ec}let c=r(l),u=c(l),f=Ni(l[1]),d=[];for(let m=0;m<f.length;m+=1)d[m]=Bi(Fi(l,f,m));let g=l[0]&&Hi();return{c(){e=xe("h4"),t=xe("div"),t.innerHTML='<div class="toggle-dot toggle-right svelte-16h224k"></div>',n=ze(`
	Returns `),u.c(),i=Nt(),o=xe("div");for(let m=0;m<d.length;m+=1)d[m].c();s=Nt(),g&&g.c(),a=gc(),It(t,"class","toggle-icon svelte-16h224k"),It(e,"class","svelte-16h224k"),zi(o,"hide",l[0])},m(m,v){ye(m,e,v),Se(e,t),Se(e,n),u.m(e,null),ye(m,i,v),ye(m,o,v);for(let p=0;p<d.length;p+=1)d[p]&&d[p].m(o,null);ye(m,s,v),g&&g.m(m,v),ye(m,a,v),_=!0},p(m,[v]){if(c===(c=r(m))&&u?u.p(m,v):(u.d(1),u=c(m),u&&(u.c(),u.m(e,null))),v&14){f=Ni(m[1]);let p;for(p=0;p<f.length;p+=1){const j=Fi(m,f,p);d[p]?d[p].p(j,v):(d[p]=Bi(j),d[p].c(),d[p].m(o,null))}for(;p<d.length;p+=1)d[p].d(1);d.length=f.length}(!_||v&1)&&zi(o,"hide",m[0]),m[0]?g?v&1&&yn(g,1):(g=Hi(),g.c(),yn(g,1),g.m(a.parentNode,a)):g&&(vc(),fl(g,1,1,()=>{g=null}),pc())},i(m){_||(yn(g),_=!0)},o(m){fl(g),_=!1},d(m){m&&(we(e),we(i),we(o),we(s),we(a)),u.d(),hc(d,m),g&&g.d(m)}}}function Tc(l,e,t){let{is_running:n}=e,{endpoint_returns:i}=e,{js_returns:o}=e,{current_language:s}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,i=a.endpoint_returns),"js_returns"in a&&t(2,o=a.js_returns),"current_language"in a&&t(3,s=a.current_language)},[n,i,o,s]}class qc extends fc{constructor(e){super(),$c(this,e,Tc,Lc,wc,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),dn()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),dn()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),dn()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),dn()}}const{SvelteComponent:Pc,append:Q,attr:Z,bubble:Gi,check_outros:Ln,create_component:gt,destroy_component:vt,destroy_each:Zi,detach:N,element:X,empty:Tl,ensure_array_like:hn,flush:yt,group_outros:Tn,init:Oc,insert:z,listen:Ic,mount_component:$t,noop:Jo,safe_not_equal:Dc,set_data:pl,set_style:Et,space:Ce,src_url_equal:Rc,text:J,transition_in:me,transition_out:Ee}=window.__gradio__svelte__internal,{onMount:Sc,createEventDispatcher:Vc}=window.__gradio__svelte__internal;function Wi(l,e,t){const n=l.slice();return n[19]=e[t],n[21]=t,n}function Qi(l,e,t){const n=l.slice();return n[22]=e[t][0],n[23]=e[t][1],n}function Ji(l){let e,t,n,i;const o=[Nc,Mc],s=[];function a(_,r){return _[8]?0:1}return e=a(l),t=s[e]=o[e](l),{c(){t.c(),n=Tl()},m(_,r){s[e].m(_,r),z(_,n,r),i=!0},p(_,r){t.p(_,r)},i(_){i||(me(t),i=!0)},o(_){Ee(t),i=!1},d(_){_&&N(n),s[e].d(_)}}}function Mc(l){let e,t;return e=new da({props:{root:l[0]}}),e.$on("close",l[16]),{c(){gt(e.$$.fragment)},m(n,i){$t(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.root=n[0]),e.$set(o)},i(n){t||(me(e.$$.fragment,n),t=!0)},o(n){Ee(e.$$.fragment,n),t=!1},d(n){vt(e,n)}}}function Nc(l){let e,t,n,i,o,s,a,_,r,c,u,f,d;t=new Ca({props:{root:l[2]||l[0],api_count:l[8]}}),t.$on("close",l[13]);let g=hn(l[9]),m=[];for(let h=0;h<g.length;h+=1)m[h]=Xi(Qi(l,g,h));const v=[Fc,zc],p=[];function j(h,k){return h[4].length?0:1}c=j(l),u=p[c]=v[c](l);let w=hn(l[1]),b=[];for(let h=0;h<w.length;h+=1)b[h]=to(Wi(l,w,h));const C=h=>Ee(b[h],1,1,()=>{b[h]=null});return{c(){e=X("div"),gt(t.$$.fragment),n=Ce(),i=X("div"),o=X("div"),o.innerHTML=`<p style="font-size: var(--text-lg);">Choose a language to see the code snippets for interacting with the
					API.</p>`,s=Ce(),a=X("div"),_=X("div");for(let h=0;h<m.length;h+=1)m[h].c();r=Ce(),u.c(),f=Ce();for(let h=0;h<b.length;h+=1)b[h].c();Z(e,"class","banner-wrap svelte-v3jjme"),Z(o,"class","client-doc svelte-v3jjme"),Z(_,"class","snippets svelte-v3jjme"),Z(a,"class","endpoint svelte-v3jjme"),Z(i,"class","docs-wrap svelte-v3jjme")},m(h,k){z(h,e,k),$t(t,e,null),z(h,n,k),z(h,i,k),Q(i,o),Q(i,s),Q(i,a),Q(a,_);for(let L=0;L<m.length;L+=1)m[L]&&m[L].m(_,null);Q(a,r),p[c].m(a,null),Q(a,f);for(let L=0;L<b.length;L+=1)b[L]&&b[L].m(a,null);d=!0},p(h,k){const L={};if(k&5&&(L.root=h[2]||h[0]),t.$set(L),k&544){g=hn(h[9]);let $;for($=0;$<g.length;$+=1){const E=Qi(h,g,$);m[$]?m[$].p(E,k):(m[$]=Xi(E),m[$].c(),m[$].m(_,null))}for(;$<m.length;$+=1)m[$].d(1);m.length=g.length}let y=c;if(c=j(h),c===y?p[c].p(h,k):(Tn(),Ee(p[y],1,1,()=>{p[y]=null}),Ln(),u=p[c],u?u.p(h,k):(u=p[c]=v[c](h),u.c()),me(u,1),u.m(a,f)),k&239){w=hn(h[1]);let $;for($=0;$<w.length;$+=1){const E=Wi(h,w,$);b[$]?(b[$].p(E,k),me(b[$],1)):(b[$]=to(E),b[$].c(),me(b[$],1),b[$].m(a,null))}for(Tn(),$=w.length;$<b.length;$+=1)C($);Ln()}},i(h){if(!d){me(t.$$.fragment,h),me(u);for(let k=0;k<w.length;k+=1)me(b[k]);d=!0}},o(h){Ee(t.$$.fragment,h),Ee(u),b=b.filter(Boolean);for(let k=0;k<b.length;k+=1)Ee(b[k]);d=!1},d(h){h&&(N(e),N(n),N(i)),vt(t),Zi(m,h),p[c].d(),Zi(b,h)}}}function Xi(l){let e,t,n,i,o=l[22]+"",s,a,_,r,c;function u(){return l[14](l[22])}return{c(){e=X("li"),t=X("img"),i=Ce(),s=J(o),a=Ce(),Rc(t.src,n=l[23])||Z(t,"src",n),Z(t,"alt",""),Z(t,"class","svelte-v3jjme"),Z(e,"class",_="snippet "+(l[5]===l[22]?"current-lang":"inactive-lang")+" svelte-v3jjme")},m(f,d){z(f,e,d),Q(e,t),Q(e,i),Q(e,s),Q(e,a),r||(c=Ic(e,"click",u),r=!0)},p(f,d){l=f,d&32&&_!==(_="snippet "+(l[5]===l[22]?"current-lang":"inactive-lang")+" svelte-v3jjme")&&Z(e,"class",_)},d(f){f&&N(e),r=!1,c()}}}function zc(l){let e,t,n,i,o,s,a,_,r,c;function u(v,p){return v[5]=="python"||v[5]=="javascript"?Bc:Uc}let f=u(l),d=f(l);n=new $_({props:{current_language:l[5]}});let g=l[2]&&Yi(l);_=new In({props:{size:"sm",variant:"secondary",$$slots:{default:[Hc]},$$scope:{ctx:l}}}),_.$on("click",l[15]);let m=l[5]=="bash"&&Ki(l);return{c(){e=X("p"),d.c(),t=Ce(),gt(n.$$.fragment),i=Ce(),o=X("p"),s=J(`2. Find the API endpoint below corresponding to your desired
						function in the app. Copy the code snippet, replacing the
						placeholder values with your own input data.
						`),g&&g.c(),a=J(`

						Or use the
						`),gt(_.$$.fragment),r=J(`
						to automatically generate your API requests.
						`),m&&m.c(),Z(e,"class","padded svelte-v3jjme"),Z(o,"class","padded svelte-v3jjme")},m(v,p){z(v,e,p),d.m(e,null),z(v,t,p),$t(n,v,p),z(v,i,p),z(v,o,p),Q(o,s),g&&g.m(o,null),Q(o,a),$t(_,o,null),Q(o,r),m&&m.m(o,null),c=!0},p(v,p){f===(f=u(v))&&d?d.p(v,p):(d.d(1),d=f(v),d&&(d.c(),d.m(e,null)));const j={};p&32&&(j.current_language=v[5]),n.$set(j),v[2]?g?g.p(v,p):(g=Yi(v),g.c(),g.m(o,a)):g&&(g.d(1),g=null);const w={};p&67108864&&(w.$$scope={dirty:p,ctx:v}),_.$set(w),v[5]=="bash"?m?m.p(v,p):(m=Ki(v),m.c(),m.m(o,null)):m&&(m.d(1),m=null)},i(v){c||(me(n.$$.fragment,v),me(_.$$.fragment,v),c=!0)},o(v){Ee(n.$$.fragment,v),Ee(_.$$.fragment,v),c=!1},d(v){v&&(N(e),N(t),N(i),N(o)),d.d(),vt(n,v),g&&g.d(),vt(_),m&&m.d()}}}function Fc(l){let e,t,n,i,o,s=l[4].length+"",a,_,r,c,u,f,d,g,m,v,p,j,w,b;return m=new ac({props:{current_language:l[5],api_calls:l[4],dependencies:l[1],root:l[0],short_root:l[2]||l[0],username:l[3]}}),{c(){e=X("div"),t=X("p"),n=J("🪄 Recorded API Calls "),i=X("span"),o=J("["),a=J(s),_=J("]"),r=Ce(),c=X("p"),u=J(`Here is the code snippet to replay the most recently recorded API
							calls using the `),f=J(l[5]),d=J(`
							client.`),g=Ce(),gt(m.$$.fragment),v=Ce(),p=X("p"),p.textContent=`Note: Some API calls only affect the UI, so when using the
							clients, the desired result may be achieved with only a subset of
							the recorded calls.`,j=Ce(),w=X("p"),w.textContent="API Documentation",Z(i,"class","api-count svelte-v3jjme"),Z(t,"id","num-recorded-api-calls"),Et(t,"font-size","var(--text-lg)"),Et(t,"font-weight","bold"),Et(t,"margin","10px 0px"),Et(w,"font-size","var(--text-lg)"),Et(w,"font-weight","bold"),Et(w,"margin","30px 0px 10px")},m(C,h){z(C,e,h),Q(e,t),Q(t,n),Q(t,i),Q(i,o),Q(i,a),Q(i,_),Q(e,r),Q(e,c),Q(c,u),Q(c,f),Q(c,d),Q(e,g),$t(m,e,null),Q(e,v),Q(e,p),z(C,j,h),z(C,w,h),b=!0},p(C,h){(!b||h&16)&&s!==(s=C[4].length+"")&&pl(a,s),(!b||h&32)&&pl(f,C[5]);const k={};h&32&&(k.current_language=C[5]),h&16&&(k.api_calls=C[4]),h&2&&(k.dependencies=C[1]),h&1&&(k.root=C[0]),h&5&&(k.short_root=C[2]||C[0]),h&8&&(k.username=C[3]),m.$set(k)},i(C){b||(me(m.$$.fragment,C),b=!0)},o(C){Ee(m.$$.fragment,C),b=!1},d(C){C&&(N(e),N(j),N(w)),vt(m)}}}function Uc(l){let e;return{c(){e=J("1. Confirm that you have cURL installed on your system.")},m(t,n){z(t,e,n)},p:Jo,d(t){t&&N(e)}}}function Bc(l){let e,t,n,i,o,s,a,_;return{c(){e=J(`1. Install the
							`),t=X("span"),n=J(l[5]),i=J(`
							client (`),o=X("a"),s=J("docs"),_=J(") if you don't already have it installed."),Et(t,"text-transform","capitalize"),Z(o,"href",a=l[5]=="python"?Pn:qn),Z(o,"target","_blank"),Z(o,"class","svelte-v3jjme")},m(r,c){z(r,e,c),z(r,t,c),Q(t,n),z(r,i,c),z(r,o,c),Q(o,s),z(r,_,c)},p(r,c){c&32&&pl(n,r[5]),c&32&&a!==(a=r[5]=="python"?Pn:qn)&&Z(o,"href",a)},d(r){r&&(N(e),N(t),N(i),N(o),N(_))}}}function Yi(l){let e,t,n,i,o;return{c(){e=J(`If this is a private Space, you may need to pass your
							Hugging Face token as well (`),t=X("a"),n=J("read more"),o=J(")."),Z(t,"href",i=l[5]=="python"?Pn+gn:l[5]=="javascript"?qn+gn:ml),Z(t,"class","underline svelte-v3jjme"),Z(t,"target","_blank")},m(s,a){z(s,e,a),z(s,t,a),Q(t,n),z(s,o,a)},p(s,a){a&32&&i!==(i=s[5]=="python"?Pn+gn:s[5]=="javascript"?qn+gn:ml)&&Z(t,"href",i)},d(s){s&&(N(e),N(t),N(o))}}}function Hc(l){let e,t,n;return{c(){e=X("div"),t=Ce(),n=X("p"),n.textContent="API Recorder",Z(e,"class","loading-dot svelte-v3jjme"),Z(n,"class","self-baseline svelte-v3jjme")},m(i,o){z(i,e,o),z(i,t,o),z(i,n,o)},p:Jo,d(i){i&&(N(e),N(t),N(n))}}}function Ki(l){let e,t,n,i,o,s,a,_,r,c,u,f,d,g,m,v,p,j,w,b,C,h,k,L,y=l[3]!==null&&xi();return{c(){e=X("br"),t=J(" "),n=X("br"),i=J(`Making a
							prediction and getting a result requires
							`),o=X("strong"),o.textContent="2 requests",s=J(`: a
							`),a=X("code"),a.textContent="POST",_=J(`
							and a `),r=X("code"),r.textContent="GET",c=J(" request. The "),u=X("code"),u.textContent="POST",f=J(` request
							returns an `),d=X("code"),d.textContent="EVENT_ID",g=J(`, which is used in the second
							`),m=X("code"),m.textContent="GET",v=J(` request to fetch the results. In these snippets,
							we've used `),p=X("code"),p.textContent="awk",j=J(" and "),w=X("code"),w.textContent="read",b=J(` to parse the
							results, combining these two requests into one command for ease of
							use. `),y&&y.c(),C=J(` See
							`),h=X("a"),k=J("curl docs"),L=J("."),Z(a,"class","svelte-v3jjme"),Z(r,"class","svelte-v3jjme"),Z(u,"class","svelte-v3jjme"),Z(d,"class","svelte-v3jjme"),Z(m,"class","svelte-v3jjme"),Z(p,"class","svelte-v3jjme"),Z(w,"class","svelte-v3jjme"),Z(h,"href",ml),Z(h,"target","_blank"),Z(h,"class","svelte-v3jjme")},m($,E){z($,e,E),z($,t,E),z($,n,E),z($,i,E),z($,o,E),z($,s,E),z($,a,E),z($,_,E),z($,r,E),z($,c,E),z($,u,E),z($,f,E),z($,d,E),z($,g,E),z($,m,E),z($,v,E),z($,p,E),z($,j,E),z($,w,E),z($,b,E),y&&y.m($,E),z($,C,E),z($,h,E),Q(h,k),z($,L,E)},p($,E){$[3]!==null?y||(y=xi(),y.c(),y.m(C.parentNode,C)):y&&(y.d(1),y=null)},d($){$&&(N(e),N(t),N(n),N(i),N(o),N(s),N(a),N(_),N(r),N(c),N(u),N(f),N(d),N(g),N(m),N(v),N(p),N(j),N(w),N(b),N(C),N(h),N(L)),y&&y.d($)}}}function xi(l){let e;return{c(){e=J(`Note: connecting to an authenticated app requires an additional
								request.`)},m(t,n){z(t,e,n)},d(t){t&&N(e)}}}function eo(l){let e,t,n,i,o,s,a,_;return t=new W_({props:{named:!0,endpoint_parameters:l[6].named_endpoints["/"+l[19].api_name].parameters,dependency:l[19],dependency_index:l[21],current_language:l[5],root:l[0],space_id:l[2],username:l[3]}}),i=new Ha({props:{endpoint_returns:l[6].named_endpoints["/"+l[19].api_name].parameters,js_returns:l[7].named_endpoints["/"+l[19].api_name].parameters,is_running:no,current_language:l[5]}}),s=new qc({props:{endpoint_returns:l[6].named_endpoints["/"+l[19].api_name].returns,js_returns:l[7].named_endpoints["/"+l[19].api_name].returns,is_running:no,current_language:l[5]}}),{c(){e=X("div"),gt(t.$$.fragment),n=Ce(),gt(i.$$.fragment),o=Ce(),gt(s.$$.fragment),a=Ce(),Z(e,"class","endpoint-container svelte-v3jjme")},m(r,c){z(r,e,c),$t(t,e,null),Q(e,n),$t(i,e,null),Q(e,o),$t(s,e,null),Q(e,a),_=!0},p(r,c){const u={};c&66&&(u.endpoint_parameters=r[6].named_endpoints["/"+r[19].api_name].parameters),c&2&&(u.dependency=r[19]),c&32&&(u.current_language=r[5]),c&1&&(u.root=r[0]),c&4&&(u.space_id=r[2]),c&8&&(u.username=r[3]),t.$set(u);const f={};c&66&&(f.endpoint_returns=r[6].named_endpoints["/"+r[19].api_name].parameters),c&130&&(f.js_returns=r[7].named_endpoints["/"+r[19].api_name].parameters),c&32&&(f.current_language=r[5]),i.$set(f);const d={};c&66&&(d.endpoint_returns=r[6].named_endpoints["/"+r[19].api_name].returns),c&130&&(d.js_returns=r[7].named_endpoints["/"+r[19].api_name].returns),c&32&&(d.current_language=r[5]),s.$set(d)},i(r){_||(me(t.$$.fragment,r),me(i.$$.fragment,r),me(s.$$.fragment,r),_=!0)},o(r){Ee(t.$$.fragment,r),Ee(i.$$.fragment,r),Ee(s.$$.fragment,r),_=!1},d(r){r&&N(e),vt(t),vt(i),vt(s)}}}function to(l){let e,t,n=l[19].show_api&&l[6].named_endpoints["/"+l[19].api_name]&&eo(l);return{c(){n&&n.c(),e=Tl()},m(i,o){n&&n.m(i,o),z(i,e,o),t=!0},p(i,o){i[19].show_api&&i[6].named_endpoints["/"+i[19].api_name]?n?(n.p(i,o),o&66&&me(n,1)):(n=eo(i),n.c(),me(n,1),n.m(e.parentNode,e)):n&&(Tn(),Ee(n,1,1,()=>{n=null}),Ln())},i(i){t||(me(n),t=!0)},o(i){Ee(n),t=!1},d(i){i&&N(e),n&&n.d(i)}}}function Gc(l){let e,t,n=l[6]&&Ji(l);return{c(){n&&n.c(),e=Tl()},m(i,o){n&&n.m(i,o),z(i,e,o),t=!0},p(i,[o]){i[6]?n?(n.p(i,o),o&64&&me(n,1)):(n=Ji(i),n.c(),me(n,1),n.m(e.parentNode,e)):n&&(Tn(),Ee(n,1,1,()=>{n=null}),Ln())},i(i){t||(me(n),t=!0)},o(i){Ee(n),t=!1},d(i){i&&N(e),n&&n.d(i)}}}const qn="https://www.gradio.app/guides/getting-started-with-the-js-client",Pn="https://www.gradio.app/guides/getting-started-with-the-python-client",ml="https://www.gradio.app/guides/querying-gradio-apps-with-curl",gn="#connecting-to-a-hugging-face-space";let no=!1;function Zc(l,e,t){let{dependencies:n}=e,{root:i}=e,{app:o}=e,{space_id:s}=e,{root_node:a}=e,{username:_}=e,r=n.filter(h=>h.show_api).length;i===""&&(i=location.protocol+"//"+location.host+location.pathname),i.endsWith("/")||(i+="/");let{api_calls:c=[]}=e,u="python";const f=[["python",_c],["javascript",cc],["bash",uc]];async function d(){return await(await fetch(i+"info")).json()}async function g(){return await o.view_api()}let m,v;d().then(h=>{t(6,m=h)}),g().then(h=>{t(7,v=h)});const p=Vc();Sc(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function j(h){Gi.call(this,l,h)}const w=h=>t(5,u=h),b=()=>p("close",{api_recorder_visible:!0});function C(h){Gi.call(this,l,h)}return l.$$set=h=>{"dependencies"in h&&t(1,n=h.dependencies),"root"in h&&t(0,i=h.root),"app"in h&&t(11,o=h.app),"space_id"in h&&t(2,s=h.space_id),"root_node"in h&&t(12,a=h.root_node),"username"in h&&t(3,_=h.username),"api_calls"in h&&t(4,c=h.api_calls)},[i,n,s,_,c,u,m,v,r,f,p,o,a,j,w,b,C]}class Wc extends Pc{constructor(e){super(),Oc(this,e,Zc,Gc,Dc,{dependencies:1,root:0,app:11,space_id:2,root_node:12,username:3,api_calls:4})}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),yt()}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),yt()}get app(){return this.$$.ctx[11]}set app(e){this.$$set({app:e}),yt()}get space_id(){return this.$$.ctx[2]}set space_id(e){this.$$set({space_id:e}),yt()}get root_node(){return this.$$.ctx[12]}set root_node(e){this.$$set({root_node:e}),yt()}get username(){return this.$$.ctx[3]}set username(e){this.$$set({username:e}),yt()}get api_calls(){return this.$$.ctx[4]}set api_calls(e){this.$$set({api_calls:e}),yt()}}const{SvelteComponent:Qc,append:At,attr:zt,create_component:Jc,destroy_component:Xc,detach:Lt,element:Ft,flush:lo,init:Yc,insert:Tt,mount_component:Kc,safe_not_equal:xc,set_data:Xo,space:sl,text:nn,transition_in:eu,transition_out:tu}=window.__gradio__svelte__internal;function io(l){let e,t,n=l[1][l[0][l[0].length-1].fn_index].api_name+"",i;return{c(){e=Ft("span"),t=nn("/"),i=nn(n),zt(e,"class","api-name svelte-sy28j6")},m(o,s){Tt(o,e,s),At(e,t),At(e,i)},p(o,s){s&3&&n!==(n=o[1][o[0][o[0].length-1].fn_index].api_name+"")&&Xo(i,n)},d(o){o&&Lt(e)}}}function nu(l){let e,t,n,i,o,s,a,_=l[0].length+"",r,c,u,f=l[0].length>0&&io(l);return{c(){e=Ft("div"),t=sl(),n=Ft("p"),n.textContent="Recording API Calls:",i=sl(),o=Ft("p"),s=Ft("span"),a=nn("["),r=nn(_),c=nn("]"),u=sl(),f&&f.c(),zt(e,"class","loading-dot self-baseline svelte-sy28j6"),zt(n,"class","self-baseline svelte-sy28j6"),zt(s,"class","api-count svelte-sy28j6"),zt(o,"class","self-baseline api-section svelte-sy28j6")},m(d,g){Tt(d,e,g),Tt(d,t,g),Tt(d,n,g),Tt(d,i,g),Tt(d,o,g),At(o,s),At(s,a),At(s,r),At(s,c),At(o,u),f&&f.m(o,null)},p(d,g){g&1&&_!==(_=d[0].length+"")&&Xo(r,_),d[0].length>0?f?f.p(d,g):(f=io(d),f.c(),f.m(o,null)):f&&(f.d(1),f=null)},d(d){d&&(Lt(e),Lt(t),Lt(n),Lt(i),Lt(o)),f&&f.d()}}}function lu(l){let e,t,n;return t=new In({props:{size:"sm",variant:"secondary",$$slots:{default:[nu]},$$scope:{ctx:l}}}),{c(){e=Ft("div"),Jc(t.$$.fragment),zt(e,"id","api-recorder")},m(i,o){Tt(i,e,o),Kc(t,e,null),n=!0},p(i,[o]){const s={};o&7&&(s.$$scope={dirty:o,ctx:i}),t.$set(s)},i(i){n||(eu(t.$$.fragment,i),n=!0)},o(i){tu(t.$$.fragment,i),n=!1},d(i){i&&Lt(e),Xc(t)}}}function iu(l,e,t){let{api_calls:n=[]}=e,{dependencies:i}=e;return l.$$set=o=>{"api_calls"in o&&t(0,n=o.api_calls),"dependencies"in o&&t(1,i=o.dependencies)},[n,i]}class ou extends Qc{constructor(e){super(),Yc(this,e,iu,lu,xc,{api_calls:0,dependencies:1})}get api_calls(){return this.$$.ctx[0]}set api_calls(e){this.$$set({api_calls:e}),lo()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),lo()}}const su=Ls(Ro),{SvelteComponent:ru,add_flush_callback:au,assign:On,bind:oo,binding_callbacks:dl,bubble:_u,check_outros:cu,compute_rest_props:so,construct_svelte_component:ro,create_component:ao,create_slot:uu,destroy_component:_o,detach:fu,empty:pu,exclude_internal_props:mu,flush:Ke,get_all_dirty_from_scope:du,get_slot_changes:hu,get_spread_object:co,get_spread_update:uo,group_outros:gu,init:vu,insert:$u,mount_component:fo,not_equal:bu,transition_in:hl,transition_out:gl,update_slot_base:ku}=window.__gradio__svelte__internal,{bind:wu,binding_callbacks:yu}=window.__gradio__svelte__internal;function Eu(l){let e;const t=l[12].default,n=uu(t,l,l[16],null);return{c(){n&&n.c()},m(i,o){n&&n.m(i,o),e=!0},p(i,o){n&&n.p&&(!e||o&65536)&&ku(n,t,i,i[16],e?hu(t,i[16],o,null):du(i[16]),null)},i(i){e||(hl(n,i),e=!0)},o(i){gl(n,i),e=!1},d(i){n&&n.d(i)}}}function ju(l){let e,t,n,i;const o=[{elem_id:l[6]},{elem_classes:l[7]},{target:l[3]},l[9],{theme_mode:l[4]},{root:l[2]},{gradio:l[5]}];function s(r){l[14](r)}var a=l[8];function _(r,c){let u={$$slots:{default:[Eu]},$$scope:{ctx:r}};for(let f=0;f<o.length;f+=1)u=On(u,o[f]);return c!==void 0&&c&764&&(u=On(u,uo(o,[c&64&&{elem_id:r[6]},c&128&&{elem_classes:r[7]},c&8&&{target:r[3]},c&512&&co(r[9]),c&16&&{theme_mode:r[4]},c&4&&{root:r[2]},c&32&&{gradio:r[5]}]))),r[1]!==void 0&&(u.value=r[1]),{props:u}}return a&&(e=ro(a,_(l)),l[13](e),dl.push(()=>oo(e,"value",s)),e.$on("prop_change",l[15])),{c(){e&&ao(e.$$.fragment),n=pu()},m(r,c){e&&fo(e,r,c),$u(r,n,c),i=!0},p(r,[c]){if(a!==(a=r[8])){if(e){gu();const u=e;gl(u.$$.fragment,1,0,()=>{_o(u,1)}),cu()}a?(e=ro(a,_(r,c)),r[13](e),dl.push(()=>oo(e,"value",s)),e.$on("prop_change",r[15]),ao(e.$$.fragment),hl(e.$$.fragment,1),fo(e,n.parentNode,n)):e=null}else if(a){const u=c&764?uo(o,[c&64&&{elem_id:r[6]},c&128&&{elem_classes:r[7]},c&8&&{target:r[3]},c&512&&co(r[9]),c&16&&{theme_mode:r[4]},c&4&&{root:r[2]},c&32&&{gradio:r[5]}]):{};c&65536&&(u.$$scope={dirty:c,ctx:r}),!t&&c&2&&(t=!0,u.value=r[1],au(()=>t=!1)),e.$set(u)}},i(r){i||(e&&hl(e.$$.fragment,r),i=!0)},o(r){e&&gl(e.$$.fragment,r),i=!1},d(r){r&&fu(n),l[13](null),e&&_o(e,r)}}}function Cu(l,e,t){const n=["root","component","target","theme_mode","instance","value","gradio","elem_id","elem_classes","_id"];let i=so(e,n),{$$slots:o={},$$scope:s}=e,{root:a}=e,{component:_}=e,{target:r}=e,{theme_mode:c}=e,{instance:u}=e,{value:f}=e,{gradio:d}=e,{elem_id:g}=e,{elem_classes:m}=e,{_id:v}=e;const p=(k,L,y)=>new CustomEvent("prop_change",{detail:{id:k,prop:L,value:y}});function j(k){return new Proxy(k,{construct(y,$){const E=new y(...$),P=Object.keys(E.$$.props);function D(U){return function(O){const V=p(v,U,O);r.dispatchEvent(V)}}return P.forEach(U=>{yu.push(()=>wu(E,U,D(U)))}),E}})}const w=j(_);function b(k){dl[k?"unshift":"push"](()=>{u=k,t(0,u)})}function C(k){f=k,t(1,f)}function h(k){_u.call(this,l,k)}return l.$$set=k=>{e=On(On({},e),mu(k)),t(9,i=so(e,n)),"root"in k&&t(2,a=k.root),"component"in k&&t(10,_=k.component),"target"in k&&t(3,r=k.target),"theme_mode"in k&&t(4,c=k.theme_mode),"instance"in k&&t(0,u=k.instance),"value"in k&&t(1,f=k.value),"gradio"in k&&t(5,d=k.gradio),"elem_id"in k&&t(6,g=k.elem_id),"elem_classes"in k&&t(7,m=k.elem_classes),"_id"in k&&t(11,v=k._id),"$$scope"in k&&t(16,s=k.$$scope)},[u,f,a,r,c,d,g,m,w,i,_,v,o,b,C,h,s]}class Au extends ru{constructor(e){super(),vu(this,e,Cu,ju,bu,{root:2,component:10,target:3,theme_mode:4,instance:0,value:1,gradio:5,elem_id:6,elem_classes:7,_id:11})}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),Ke()}get component(){return this.$$.ctx[10]}set component(e){this.$$set({component:e}),Ke()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),Ke()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),Ke()}get instance(){return this.$$.ctx[0]}set instance(e){this.$$set({instance:e}),Ke()}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),Ke()}get gradio(){return this.$$.ctx[5]}set gradio(e){this.$$set({gradio:e}),Ke()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),Ke()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),Ke()}get _id(){return this.$$.ctx[11]}set _id(e){this.$$set({_id:e}),Ke()}}const Lu={accordion:{component:()=>T(()=>import("./Index-DesKBBmJ.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]),import.meta.url)},annotatedimage:{component:()=>T(()=>import("./Index-BYBDLc2n.js"),__vite__mapDeps([10,1,2,3,4,5,6,11,12,13,14,15,16]),import.meta.url)},audio:{base:()=>T(()=>import("./StaticAudio-Dw97rWIw.js").then(l=>l.b),__vite__mapDeps([17,2,3,4,5,11,12,18,19,20,21,22,15,23,24]),import.meta.url),example:()=>T(()=>import("./Example-uQ8MuYg6.js"),__vite__mapDeps([25,26]),import.meta.url),component:()=>T(()=>import("./index-BmHQU65L.js"),__vite__mapDeps([27,17,2,3,4,5,11,12,18,19,20,21,22,15,23,24,28,29,30,31,32,1,6,33,25,26,34]),import.meta.url)},box:{component:()=>T(()=>import("./Index-DlGXsSlG.js"),__vite__mapDeps([35,1,2,3,4,5,6]),import.meta.url)},button:{component:()=>T(()=>import("./Index-DRonTktv.js"),__vite__mapDeps([36,3,4,1,2,5,6]),import.meta.url)},chatbot:{component:()=>T(()=>import("./Index-A4-C6YLr.js"),__vite__mapDeps([37,2,3,4,5,38,18,39,15,40,41,42,43,44,45,46,1,6,47,23,11,48,49]),import.meta.url)},checkbox:{example:()=>T(()=>import("./Example-CZ-iEz1g.js"),__vite__mapDeps([50,26]),import.meta.url),component:()=>T(()=>import("./Index-BUc4SVJJ.js"),__vite__mapDeps([51,1,2,3,4,5,6,52,53]),import.meta.url)},checkboxgroup:{example:()=>T(()=>import("./Example-CRm1Pmw_.js"),__vite__mapDeps([54,26]),import.meta.url),component:()=>T(()=>import("./Index-DmOfYZye.js"),__vite__mapDeps([55,1,2,3,4,5,6,56,52,57]),import.meta.url)},code:{example:()=>T(()=>import("./Example-Wp-_4AVX.js"),__vite__mapDeps([58,59]),import.meta.url),component:()=>T(()=>import("./Index-CKgowMni.js").then(l=>l.F),__vite__mapDeps([60,3,4,42,43,2,5,1,6,19,23,15,61,11,12,58,59,62]),import.meta.url)},colorpicker:{example:()=>T(()=>import("./Example-BaLyJYAe.js"),__vite__mapDeps([63,64]),import.meta.url),component:()=>T(()=>import("./Index-C6W5a0bI.js"),__vite__mapDeps([65,2,3,4,5,56,52,1,6,63,64,66]),import.meta.url)},column:{component:()=>T(()=>import("./Index-CuoXAbPt.js"),__vite__mapDeps([7,2,3,4,5,8]),import.meta.url)},core:{component:()=>T(()=>import("./index-CK7CWdzV.js"),__vite__mapDeps([67,2,3,4,5]),import.meta.url)},dataframe:{example:()=>T(()=>import("./Example-BT2jlY4j.js"),__vite__mapDeps([68,69]),import.meta.url),component:()=>T(()=>import("./Index-B7z1eIWS.js"),__vite__mapDeps([70,1,2,3,4,5,6,38,28,29,41,42,43,44,45,46,47,71,68,69,72]),import.meta.url)},dataset:{component:()=>T(()=>import("./Index-BEKHNubq.js"),__vite__mapDeps([73,1,2,3,4,5,6,74,75,76,77]),import.meta.url)},datetime:{example:()=>T(()=>import("./Example-BBLMS951.js"),[],import.meta.url),component:()=>T(()=>import("./Index-zN1wv2uI.js"),__vite__mapDeps([78,1,2,3,4,5,6,56,52,79,80]),import.meta.url)},downloadbutton:{component:()=>T(()=>import("./Index-CEDCRqJB.js"),__vite__mapDeps([81,3,4,1,2,5,6,82]),import.meta.url)},dropdown:{example:()=>T(()=>import("./Example-CUwox43B.js"),__vite__mapDeps([83,26]),import.meta.url),component:()=>T(()=>import("./Index-Vm7MXWuW.js"),__vite__mapDeps([84,3,4,2,5,56,52,85,1,6,83,26,86]),import.meta.url)},file:{example:()=>T(()=>import("./Example-DrmWnoSo.js"),__vite__mapDeps([87,88]),import.meta.url),component:()=>T(()=>import("./Index-DBfSJWIQ.js"),__vite__mapDeps([89,90,2,3,4,5,11,12,91,28,29,30,19,22,23,15,92,1,6,33,32,87,88]),import.meta.url)},fileexplorer:{example:()=>T(()=>import("./Example-CIFMxn5c.js"),__vite__mapDeps([93,94]),import.meta.url),component:()=>T(()=>import("./Index-2uWJiLiv.js"),__vite__mapDeps([95,2,3,4,5,91,1,6,11,96]),import.meta.url)},form:{component:()=>T(()=>import("./Index-DDCF2BFd.js"),__vite__mapDeps([97,98]),import.meta.url)},gallery:{base:()=>T(()=>import("./Gallery-XI6EERbz.js"),__vite__mapDeps([99,2,3,4,5,11,12,18,19,13,14,30,22,23,15,29,39,40,1,6,100,49]),import.meta.url),component:()=>T(()=>import("./Index-DjtuMFOG.js"),__vite__mapDeps([101,1,2,3,4,5,6,33,32,99,11,12,18,19,13,14,30,22,23,15,29,39,40,100,49,90,91,28,92,88]),import.meta.url)},group:{component:()=>T(()=>import("./Index-CS2xVf8J.js"),__vite__mapDeps([102,103]),import.meta.url)},highlightedtext:{component:()=>T(()=>import("./Index-C68UQGaZ.js"),__vite__mapDeps([104,105,2,3,4,5,1,6,11,12,106]),import.meta.url)},html:{base:()=>T(()=>import("./Index-QJJ51qFF.js"),__vite__mapDeps([107,2,3,4,5,61,1,6,11,108]),import.meta.url),example:()=>T(()=>import("./Example-C2a4WxRl.js"),__vite__mapDeps([109,110]),import.meta.url),component:()=>T(()=>import("./Index-QJJ51qFF.js"),__vite__mapDeps([107,2,3,4,5,61,1,6,11,108]),import.meta.url)},image:{base:()=>T(()=>import("./ImagePreview-BpvYwBpC.js"),__vite__mapDeps([111,2,3,4,5,11,12,18,19,13,14,112,39,15,40,23,1,6,49]),import.meta.url),example:()=>T(()=>import("./Example-Ig2aUhUe.js"),__vite__mapDeps([113,39,15,2,3,4,5,40,114]),import.meta.url),component:()=>T(()=>import("./Index-qbjg5-4V.js"),__vite__mapDeps([115,111,2,3,4,5,11,12,18,19,13,14,112,39,15,40,23,1,6,49,116,31,32,85,28,29,117,33,113,114]),import.meta.url)},imageeditor:{example:()=>T(()=>import("./Example-DD8yppZ9.js"),__vite__mapDeps([118,2,3,4,5,39,15,40,116,11,14,31,32,112,85,1,6,28,29,117,119,49,114]),import.meta.url),component:()=>T(()=>import("./Index-Ds0AjFoZ.js"),__vite__mapDeps([120,111,2,3,4,5,11,12,18,19,13,14,112,39,15,40,23,1,6,49,116,31,32,85,28,29,117,121,42,22,122,114]),import.meta.url)},json:{component:()=>T(()=>import("./Index-j4wxG1wD.js"),__vite__mapDeps([123,42,43,2,3,4,5,12,1,6,11,124]),import.meta.url)},label:{component:()=>T(()=>import("./Index-mT8gSmoJ.js"),__vite__mapDeps([125,2,3,4,5,126,1,6,11,12,127]),import.meta.url)},markdown:{example:()=>T(()=>import("./Example-CSVraJnq.js"),__vite__mapDeps([128,44,3,4,45,46]),import.meta.url),component:()=>T(()=>import("./Index-BK-zC9ir.js"),__vite__mapDeps([129,41,2,3,4,5,42,43,44,45,46,1,6,47,128]),import.meta.url)},model3d:{example:()=>T(()=>import("./Example-BQyGztrG.js"),__vite__mapDeps([130,26]),import.meta.url),component:()=>T(()=>import("./Index-CMg_fjf1.js"),__vite__mapDeps([131,3,4,2,5,11,19,91,22,28,29,30,23,15,1,6,12,33,32,130,26,132]),import.meta.url)},multimodaltextbox:{example:()=>T(()=>import("./Example-DxaKKsjH.js"),__vite__mapDeps([133,39,15,2,3,4,5,40,134,135,136,49]),import.meta.url),component:()=>T(()=>import("./Index-Bk0whK9w.js"),__vite__mapDeps([137,2,3,4,5,56,52,91,20,138,28,29,39,15,40,1,6,133,134,135,136,49,139]),import.meta.url)},nativeplot:{example:()=>T(()=>import("./Example-Creifpe8.js"),[],import.meta.url),component:()=>T(()=>import("./Index-BRMkropX.js"),__vite__mapDeps([140,1,2,3,4,5,6,56,52,12,126,141,71,142]),import.meta.url)},number:{example:()=>T(()=>import("./Example-CqL1e7EB.js"),__vite__mapDeps([143,26]),import.meta.url),component:()=>T(()=>import("./Index--WpWguRY.js"),__vite__mapDeps([144,1,2,3,4,5,6,56,52,145]),import.meta.url)},paramviewer:{example:()=>T(()=>import("./Example-C9__vDgN.js"),__vite__mapDeps([146,26]),import.meta.url),component:()=>T(()=>import("./Index-CvmM5KQ3.js"),__vite__mapDeps([147,45,3,4,148]),import.meta.url)},plot:{base:()=>T(()=>import("./Plot-Ce8WWIhc.js").then(l=>l.b),__vite__mapDeps([149,3,4,2,5,12]),import.meta.url),component:()=>T(()=>import("./Index-ALXcDGxx.js"),__vite__mapDeps([150,149,3,4,2,5,12,1,6,11]),import.meta.url)},radio:{example:()=>T(()=>import("./Example-BoMLuz1A.js"),__vite__mapDeps([151,26]),import.meta.url),component:()=>T(()=>import("./Index-DphV2Gww.js"),__vite__mapDeps([152,1,2,3,4,5,6,56,52,151,26,153]),import.meta.url)},row:{component:()=>T(()=>import("./Index-Tmzj9QOr.js"),__vite__mapDeps([154,2,3,4,5,155]),import.meta.url)},slider:{example:()=>T(()=>import("./Example-DxdiEFS_.js"),__vite__mapDeps([156,26]),import.meta.url),component:()=>T(()=>import("./Index-D9JcTZE7.js"),__vite__mapDeps([157,1,2,3,4,5,6,56,52,158]),import.meta.url)},state:{component:()=>T(()=>import("./Index-uRgjJb4U.js"),[],import.meta.url)},statustracker:{component:()=>T(()=>import("./index-Dc9jIDHc.js"),__vite__mapDeps([159,2,3,4,5,1,6]),import.meta.url)},tabitem:{component:()=>T(()=>import("./Index-BA3fpEJb.js"),__vite__mapDeps([160,161,3,4,162,7,2,5,8,163]),import.meta.url)},tabs:{component:()=>T(()=>import("./Index-BfLZDIPR.js"),__vite__mapDeps([164,161,3,4,162]),import.meta.url)},textbox:{example:()=>T(()=>import("./Example-C7XUkkid.js"),__vite__mapDeps([74,75]),import.meta.url),component:()=>T(()=>import("./Index-CSD8SlVL.js"),__vite__mapDeps([165,166,2,3,4,5,56,52,42,43,1,6,77,74,75]),import.meta.url)},timer:{component:()=>T(()=>import("./Index-BMLc4VxK.js"),[],import.meta.url)},uploadbutton:{component:()=>T(()=>import("./Index-BOi8ACPk.js"),__vite__mapDeps([167,3,4,1,2,5,6,168]),import.meta.url)},video:{base:()=>T(()=>import("./VideoPreview-NCcOumWs.js").then(l=>l.a),__vite__mapDeps([169,2,3,4,5,11,12,18,19,138,23,15,21,22,134,135,170]),import.meta.url),example:()=>T(()=>import("./Example-DUQ4sHSf.js"),__vite__mapDeps([171,134,15,2,3,4,5,135,172]),import.meta.url),component:()=>T(()=>import("./index-DJvrwMOP.js"),__vite__mapDeps([173,28,3,4,29,30,2,5,19,22,23,15,11,138,31,32,39,40,116,14,112,85,1,6,117,134,135,169,12,18,21,170,171,172,33,174,49,114]),import.meta.url)}},pt={};function vl({api_url:l,name:e,id:t,variant:n}){const i=window.__GRADIO__CC__,o={...Lu,...i||{}};let s=t||e;if(pt[`${s}-${n}`])return{component:pt[`${s}-${n}`],name:e};try{if(!o?.[s]?.[n]&&!o?.[e]?.[n])throw new Error;return pt[`${s}-${n}`]=(o?.[s]?.[n]||o?.[e]?.[n])(),{name:e,component:pt[`${s}-${n}`]}}catch{if(!s)throw new Error(`Component not found: ${e}`);try{return pt[`${s}-${n}`]=qu(l,s,n),{name:e,component:pt[`${s}-${n}`]}}catch(_){if(n==="example")return pt[`${s}-${n}`]=T(()=>import("./Example-BrizabXh.js"),__vite__mapDeps([175,26]),import.meta.url),{name:e,component:pt[`${s}-${n}`]};throw console.error(`failed to load: ${e}`),console.error(_),_}}}function Tu(l){return new Promise((e,t)=>{const n=document.createElement("link");n.rel="stylesheet",n.href=l,document.head.appendChild(n),n.onload=()=>e(),n.onerror=()=>t()})}function qu(l,e,t){return Promise.all([Tu(`${l}/custom_component/${e}/${t}/style.css`),T(()=>import(`${l}/custom_component/${e}/${t}/index.js`),[],import.meta.url)]).then(([n,i])=>i)}const{SvelteComponent:Pu,add_flush_callback:po,assign:Ou,bind:mo,binding_callbacks:ho,bubble:go,check_outros:Yo,create_component:Ko,destroy_component:xo,detach:ql,empty:Pl,ensure_array_like:vo,flush:ot,get_spread_object:Iu,get_spread_update:Du,group_outros:es,init:Ru,insert:Ol,mount_component:ts,outro_and_destroy_block:Su,safe_not_equal:Vu,transition_in:Bt,transition_out:sn,update_keyed_each:Mu}=window.__gradio__svelte__internal,{onMount:Nu,createEventDispatcher:zu,setContext:Fu}=window.__gradio__svelte__internal;function $o(l,e,t){const n=l.slice();return n[16]=e[t],n}function bo(l){let e=[],t=new Map,n,i,o=vo(l[0].children);const s=a=>a[16].id;for(let a=0;a<o.length;a+=1){let _=$o(l,o,a),r=s(_);t.set(r,e[a]=ko(r,_))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=Pl()},m(a,_){for(let r=0;r<e.length;r+=1)e[r]&&e[r].m(a,_);Ol(a,n,_),i=!0},p(a,_){_&63&&(o=vo(a[0].children),es(),e=Mu(e,_,s,1,a,o,t,n.parentNode,Su,ko,n,$o),Yo())},i(a){if(!i){for(let _=0;_<o.length;_+=1)Bt(e[_]);i=!0}},o(a){for(let _=0;_<e.length;_+=1)sn(e[_]);i=!1},d(a){a&&ql(n);for(let _=0;_<e.length;_+=1)e[_].d(a)}}}function ko(l,e){let t,n,i;return n=new ns({props:{node:e[16],component:e[16].component,target:e[2],id:e[16].id,root:e[1],theme_mode:e[3],max_file_size:e[4],client:e[5]}}),n.$on("destroy",e[10]),n.$on("mount",e[11]),{key:l,first:null,c(){t=Pl(),Ko(n.$$.fragment),this.first=t},m(o,s){Ol(o,t,s),ts(n,o,s),i=!0},p(o,s){e=o;const a={};s&1&&(a.node=e[16]),s&1&&(a.component=e[16].component),s&4&&(a.target=e[2]),s&1&&(a.id=e[16].id),s&2&&(a.root=e[1]),s&8&&(a.theme_mode=e[3]),s&16&&(a.max_file_size=e[4]),s&32&&(a.client=e[5]),n.$set(a)},i(o){i||(Bt(n.$$.fragment,o),i=!0)},o(o){sn(n.$$.fragment,o),i=!1},d(o){o&&ql(t),xo(n,o)}}}function Uu(l){let e,t,n=l[0].children&&l[0].children.length&&bo(l);return{c(){n&&n.c(),e=Pl()},m(i,o){n&&n.m(i,o),Ol(i,e,o),t=!0},p(i,o){i[0].children&&i[0].children.length?n?(n.p(i,o),o&1&&Bt(n,1)):(n=bo(i),n.c(),Bt(n,1),n.m(e.parentNode,e)):n&&(es(),sn(n,1,1,()=>{n=null}),Yo())},i(i){t||(Bt(n),t=!0)},o(i){sn(n),t=!1},d(i){i&&ql(e),n&&n.d(i)}}}function Bu(l){let e,t,n,i;const o=[{_id:l[0].id},{component:l[0].component},{elem_id:"elem_id"in l[0].props&&l[0].props.elem_id||`component-${l[0].id}`},{elem_classes:"elem_classes"in l[0].props&&l[0].props.elem_classes||[]},{target:l[2]},l[0].props,{theme_mode:l[3]},{root:l[1]},{gradio:l[6]}];function s(r){l[12](r)}function a(r){l[13](r)}let _={$$slots:{default:[Uu]},$$scope:{ctx:l}};for(let r=0;r<o.length;r+=1)_=Ou(_,o[r]);return l[0].instance!==void 0&&(_.instance=l[0].instance),l[0].props.value!==void 0&&(_.value=l[0].props.value),e=new Au({props:_}),ho.push(()=>mo(e,"instance",s)),ho.push(()=>mo(e,"value",a)),{c(){Ko(e.$$.fragment)},m(r,c){ts(e,r,c),i=!0},p(r,[c]){const u=c&79?Du(o,[c&1&&{_id:r[0].id},c&1&&{component:r[0].component},c&1&&{elem_id:"elem_id"in r[0].props&&r[0].props.elem_id||`component-${r[0].id}`},c&1&&{elem_classes:"elem_classes"in r[0].props&&r[0].props.elem_classes||[]},c&4&&{target:r[2]},c&1&&Iu(r[0].props),c&8&&{theme_mode:r[3]},c&2&&{root:r[1]},c&64&&{gradio:r[6]}]):{};c&524351&&(u.$$scope={dirty:c,ctx:r}),!t&&c&1&&(t=!0,u.instance=r[0].instance,po(()=>t=!1)),!n&&c&1&&(n=!0,u.value=r[0].props.value,po(()=>n=!1)),e.$set(u)},i(r){i||(Bt(e.$$.fragment,r),i=!0)},o(r){sn(e.$$.fragment,r),i=!1},d(r){xo(e,r)}}}function Hu(l,e,t){let n,{root:i}=e,{node:o}=e,{parent:s=null}=e,{target:a}=e,{theme_mode:_}=e,{version:r}=e,{autoscroll:c}=e,{max_file_size:u}=e,{client:f}=e;const d=zu();let g=[];Nu(()=>{d("mount",o.id);for(const w of g)d("mount",w.id);return()=>{d("destroy",o.id);for(const w of g)d("mount",w.id)}}),Fu("BLOCK_KEY",s);function m(w){go.call(this,l,w)}function v(w){go.call(this,l,w)}function p(w){l.$$.not_equal(o.instance,w)&&(o.instance=w,t(0,o),t(15,g))}function j(w){l.$$.not_equal(o.props.value,w)&&(o.props.value=w,t(0,o),t(15,g))}return l.$$set=w=>{"root"in w&&t(1,i=w.root),"node"in w&&t(0,o=w.node),"parent"in w&&t(7,s=w.parent),"target"in w&&t(2,a=w.target),"theme_mode"in w&&t(3,_=w.theme_mode),"version"in w&&t(8,r=w.version),"autoscroll"in w&&t(9,c=w.autoscroll),"max_file_size"in w&&t(4,u=w.max_file_size),"client"in w&&t(5,f=w.client)},l.$$.update=()=>{l.$$.dirty&1&&t(0,o.children=o.children&&o.children.filter(w=>{const b=o.type!=="statustracker";return b||g.push(w),b}),o),l.$$.dirty&1&&o.type==="form"&&(o.children?.every(w=>!w.props.visible)?t(0,o.props.visible=!1,o):t(0,o.props.visible=!0,o)),l.$$.dirty&831&&t(6,n=new nr(o.id,a,_,r,i,c,u,su,f,vl))},[o,i,a,_,u,f,n,s,r,c,m,v,p,j]}class ns extends Pu{constructor(e){super(),Ru(this,e,Hu,Bu,Vu,{root:1,node:0,parent:7,target:2,theme_mode:3,version:8,autoscroll:9,max_file_size:4,client:5})}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),ot()}get node(){return this.$$.ctx[0]}set node(e){this.$$set({node:e}),ot()}get parent(){return this.$$.ctx[7]}set parent(e){this.$$set({parent:e}),ot()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),ot()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),ot()}get version(){return this.$$.ctx[8]}set version(e){this.$$set({version:e}),ot()}get autoscroll(){return this.$$.ctx[9]}set autoscroll(e){this.$$set({autoscroll:e}),ot()}get max_file_size(){return this.$$.ctx[4]}set max_file_size(e){this.$$set({max_file_size:e}),ot()}get client(){return this.$$.ctx[5]}set client(e){this.$$set({client:e}),ot()}}const{SvelteComponent:Gu,create_component:Zu,destroy_component:Wu,flush:mt,init:Qu,mount_component:Ju,safe_not_equal:Xu,transition_in:Yu,transition_out:Ku}=window.__gradio__svelte__internal,{onMount:xu,createEventDispatcher:ef}=window.__gradio__svelte__internal;function tf(l){let e,t;return e=new ns({props:{node:l[0],root:l[1],target:l[2],theme_mode:l[3],version:l[4],autoscroll:l[5],max_file_size:l[6],client:l[7]}}),{c(){Zu(e.$$.fragment)},m(n,i){Ju(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.node=n[0]),i&2&&(o.root=n[1]),i&4&&(o.target=n[2]),i&8&&(o.theme_mode=n[3]),i&16&&(o.version=n[4]),i&32&&(o.autoscroll=n[5]),i&64&&(o.max_file_size=n[6]),i&128&&(o.client=n[7]),e.$set(o)},i(n){t||(Yu(e.$$.fragment,n),t=!0)},o(n){Ku(e.$$.fragment,n),t=!1},d(n){Wu(e,n)}}}function nf(l,e,t){let{rootNode:n}=e,{root:i}=e,{target:o}=e,{theme_mode:s}=e,{version:a}=e,{autoscroll:_}=e,{max_file_size:r=null}=e,{client:c}=e;const u=ef();return xu(()=>{u("mount")}),l.$$set=f=>{"rootNode"in f&&t(0,n=f.rootNode),"root"in f&&t(1,i=f.root),"target"in f&&t(2,o=f.target),"theme_mode"in f&&t(3,s=f.theme_mode),"version"in f&&t(4,a=f.version),"autoscroll"in f&&t(5,_=f.autoscroll),"max_file_size"in f&&t(6,r=f.max_file_size),"client"in f&&t(7,c=f.client)},[n,i,o,s,a,_,r,c]}class lf extends Gu{constructor(e){super(),Qu(this,e,nf,tf,Xu,{rootNode:0,root:1,target:2,theme_mode:3,version:4,autoscroll:5,max_file_size:6,client:7})}get rootNode(){return this.$$.ctx[0]}set rootNode(e){this.$$set({rootNode:e}),mt()}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),mt()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),mt()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),mt()}get version(){return this.$$.ctx[4]}set version(e){this.$$set({version:e}),mt()}get autoscroll(){return this.$$.ctx[5]}set autoscroll(e){this.$$set({autoscroll:e}),mt()}get max_file_size(){return this.$$.ctx[6]}set max_file_size(e){this.$$set({max_file_size:e}),mt()}get client(){return this.$$.ctx[7]}set client(e){this.$$set({client:e}),mt()}}const of="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e";function sf(){const l=kn({}),e={},t={},n=new Map,i=new Map,o=new Map,s={};function a({fn_index:r,status:c,queue:u=!0,size:f,position:d=null,eta:g=null,message:m=null,progress:v}){const p=t[r],j=e[r],w=s[r],b=p.map(C=>{let h;const k=n.get(C)||0;if(w==="pending"&&c!=="pending"){let L=k-1;n.set(C,L<0?0:L),h=L>0?"pending":c}else w==="pending"&&c==="pending"?h="pending":w!=="pending"&&c==="pending"?(h="pending",n.set(C,k+1)):h=c;return{id:C,queue_position:d,queue_size:f,eta:g,status:h,message:m,progress:v}});j.forEach(C=>{const h=i.get(C)||0;if(w==="pending"&&c!=="pending"){let k=h-1;i.set(C,k<0?0:k),o.set(C,c)}else w!=="pending"&&c==="pending"?(i.set(C,h+1),o.set(C,c)):o.delete(C)}),l.update(C=>(b.forEach(({id:h,queue_position:k,queue_size:L,eta:y,status:$,message:E,progress:P})=>{C[h]={queue:u,queue_size:L,queue_position:k,eta:y,message:E,progress:P,status:$,fn_index:r}}),C)),s[r]=c}function _(r,c,u){e[r]=c,t[r]=u}return{update:a,register:_,subscribe:l.subscribe,get_status_for_fn(r){return s[r]},get_inputs_to_update(){return o}}}let Mt=[];function rf(){let l,e=kn({}),t={},n,i,o,s,a=sf();const _=kn();let r=[],c,u={},f;function d({app:h,components:k,layout:L,dependencies:y,root:$,options:E}){c=h,j(r),r=k,n=new Set,i=new Set,Mt=[],o=new Map,l=new Map,s={},f={id:L.id,type:"column",props:{interactive:!1,scale:E.fill_height?1:null},has_modes:!1,instance:null,component:null,component_class_id:"",key:null},k.push(f),y.forEach(P=>{a.register(P.id,P.inputs,P.outputs),P.frontend_fn=wo(P.js,!!P.backend_fn,P.inputs.length,P.outputs.length),yo(P.targets,P.id,t),Eo(P,n,i)}),e.set(t),o=jo(k,$),s=k.reduce((P,D)=>(P[D.id]=D,P),{}),m(L,$).then(()=>{_.set(f)})}function g({render_id:h,components:k,layout:L,root:y,dependencies:$}){jo(k,y).forEach((O,V)=>{o.set(V,O)}),t={},$.forEach(O=>{a.register(O.id,O.inputs,O.outputs),O.frontend_fn=wo(O.js,!!O.backend_fn,O.inputs.length,O.outputs.length),yo(O.targets,O.id,t),Eo(O,n,i)}),e.set(t);let P=s[L.id],D=[];const U=O=>{D.push(O),O.children&&O.children.forEach(V=>{U(V)})};U(P),j(D),Object.entries(s).forEach(([O,V])=>{let M=Number(O);V.rendered_in===h&&(delete s[M],l.has(M)&&l.delete(M))}),k.forEach(O=>{s[O.id]=O,l.set(O.id,O)}),P.parent&&(P.parent.children[P.parent.children.indexOf(P)]=s[L.id]),m(L,y,P.parent).then(()=>{_.set(f)})}async function m(h,k,L){const y=s[h.id];return y.component=(await o.get(y.component_class_id||y.type))?.default,y.parent=L,y.type==="dataset"&&(y.props.component_map=is(y.type,y.component_class_id,k,r,y.props.components).example_components),t[y.id]&&(y.props.attached_events=Object.keys(t[y.id])),y.props.interactive=_f(y.id,y.props.interactive,y.props.value,n,i),y.props.server=cf(y.id,y.props.server_fns,c),y.key!=null&&u[y.key]!==void 0&&(y.props.value=u[y.key]),l.set(y.id,y),h.children&&(y.children=await Promise.all(h.children.map($=>m($,k,y)))),y}let v=!1,p=kn(!1);function j(h){h.forEach(k=>{k.key!=null&&(u[k.key]=k.props.value)})}function w(){_.update(h=>{for(let k=0;k<Mt.length;k++)for(let L=0;L<Mt[k].length;L++){const y=Mt[k][L];if(!y)continue;const $=s[y.id];if(!$)continue;let E;y.value instanceof Map?E=new Map(y.value):y.value instanceof Set?E=new Set(y.value):Array.isArray(y.value)?E=[...y.value]:y.value===null?E=null:typeof y.value=="object"?E={...y.value}:E=y.value,$.props[y.prop]=E}return h}),Mt=[],v=!1,p.set(!1)}function b(h){h&&(Mt.push(h),v||(v=!0,p.set(!0),requestAnimationFrame(w)))}function C(h){const k=l.get(h);return k?k.instance.get_value?k.instance.get_value():k.props.value:null}return{layout:_,targets:e,update_value:b,get_data:C,loading_status:a,scheduled_updates:p,create_layout:(...h)=>requestAnimationFrame(()=>d(...h)),rerender_layout:g}}const ls=Object.getPrototypeOf(async function(){}).constructor;function wo(l,e,t,n){if(!l)return null;const i=e?t===1:n===1;try{return new ls("__fn_args",`  let result = await (${l})(...__fn_args);
  if (typeof result === "undefined") return [];
  return (${i} && !Array.isArray(result)) ? [result] : result;`)}catch(o){return console.error("Could not parse custom js method."),console.error(o),null}}function yo(l,e,t){return l.forEach(([n,i])=>{t[n]||(t[n]={}),t[n]?.[i]&&!t[n]?.[i].includes(e)?t[n][i].push(e):t[n][i]=[e]}),t}function Eo(l,e,t){return l.inputs.forEach(n=>e.add(n)),l.outputs.forEach(n=>t.add(n)),[e,t]}function af(l){return Array.isArray(l)&&l.length===0||l===""||l===0||!l}function _f(l,e,t,n,i){return e===!1?!1:e===!0?!0:!!(n.has(l)||!i.has(l)&&af(t))}function cf(l,e,t){return e?e.reduce((n,i)=>(n[i]=async(...o)=>(o.length===1&&(o=o[0]),await t.component_server(l,i,o)),n),{}):{}}function is(l,e,t,n,i){let o=new Map;l==="dataset"&&i&&i.forEach(a=>{if(o.has(a))return;let _;const r=n.find(c=>c.type===a);r&&(_=vl({api_url:t,name:a,id:r.component_class_id,variant:"example"}),o.set(a,_.component))});const s=vl({api_url:t,name:l,id:e,variant:"component"});return{component:s.component,name:s.name,example_components:o.size>0?o:void 0}}function jo(l,e){let t=new Map;return l.forEach(n=>{const{component:i,example_components:o}=is(n.type,n.component_class_id,e,l);if(t.set(n.component_class_id||n.type,i),o)for(const[s,a]of o)t.set(s,a)}),t}const{SvelteComponent:uf,append:Pe,attr:oe,check_outros:vn,component_subscribe:xt,create_component:Un,destroy_component:Bn,detach:Ve,element:We,empty:Co,flush:fe,globals:ff,group_outros:$n,init:pf,insert:Be,listen:Il,mount_component:Hn,noop:rl,safe_not_equal:mf,set_data:os,set_style:bn,space:et,src_url_equal:ss,text:rs,transition_in:je,transition_out:Ue}=window.__gradio__svelte__internal,{document:$l}=ff,{tick:al}=window.__gradio__svelte__internal;function Ao(l){return $l.title=l[2],{c:rl,m:rl,d:rl}}function Lo(l){let e,t;return e=new lf({props:{rootNode:l[14],root:l[0],target:l[3],theme_mode:l[9],version:l[12],autoscroll:l[4],max_file_size:l[10].config.max_file_size,client:l[10]}}),e.$on("mount",l[26]),{c(){Un(e.$$.fragment)},m(n,i){Hn(e,n,i),t=!0},p(n,i){const o={};i[0]&16384&&(o.rootNode=n[14]),i[0]&1&&(o.root=n[0]),i[0]&8&&(o.target=n[3]),i[0]&512&&(o.theme_mode=n[9]),i[0]&4096&&(o.version=n[12]),i[0]&16&&(o.autoscroll=n[4]),i[0]&1024&&(o.max_file_size=n[10].config.max_file_size),i[0]&1024&&(o.client=n[10]),e.$set(o)},i(n){t||(je(e.$$.fragment,n),t=!0)},o(n){Ue(e.$$.fragment,n),t=!1},d(n){Bn(e,n)}}}function To(l){let e,t,n,i=l[19]("common.built_with_gradio")+"",o,s,a,_,r,c=l[5]&&qo(l);return{c(){e=We("footer"),c&&c.c(),t=et(),n=We("a"),o=rs(i),s=et(),a=We("img"),ss(a.src,_=of)||oe(a,"src",_),oe(a,"alt",r=l[19]("common.logo")),oe(a,"class","svelte-1rjryqp"),oe(n,"href","https://gradio.app"),oe(n,"class","built-with svelte-1rjryqp"),oe(n,"target","_blank"),oe(n,"rel","noreferrer"),oe(e,"class","svelte-1rjryqp")},m(u,f){Be(u,e,f),c&&c.m(e,null),Pe(e,t),Pe(e,n),Pe(n,o),Pe(n,s),Pe(n,a)},p(u,f){u[5]?c?c.p(u,f):(c=qo(u),c.c(),c.m(e,t)):c&&(c.d(1),c=null),f[0]&524288&&i!==(i=u[19]("common.built_with_gradio")+"")&&os(o,i),f[0]&524288&&r!==(r=u[19]("common.logo"))&&oe(a,"alt",r)},d(u){u&&Ve(e),c&&c.d()}}}function qo(l){let e,t=l[19]("errors.use_via_api")+"",n,i,o,s,a,_,r,c,u;return{c(){e=We("button"),n=rs(t),i=et(),o=We("img"),_=et(),r=We("div"),r.textContent="·",ss(o.src,s=Bo)||oe(o,"src",s),oe(o,"alt",a=l[19]("common.logo")),oe(o,"class","svelte-1rjryqp"),oe(e,"class","show-api svelte-1rjryqp"),oe(r,"class","svelte-1rjryqp")},m(f,d){Be(f,e,d),Pe(e,n),Pe(e,i),Pe(e,o),Be(f,_,d),Be(f,r,d),c||(u=Il(e,"click",l[35]),c=!0)},p(f,d){d[0]&524288&&t!==(t=f[19]("errors.use_via_api")+"")&&os(n,t),d[0]&524288&&a!==(a=f[19]("common.logo"))&&oe(o,"alt",a)},d(f){f&&(Ve(e),Ve(_),Ve(r)),c=!1,u()}}}function Po(l){let e,t,n,i,o;return t=new ou({props:{api_calls:l[17],dependencies:l[1]}}),{c(){e=We("div"),Un(t.$$.fragment),oe(e,"id","api-recorder-container"),oe(e,"class","svelte-1rjryqp")},m(s,a){Be(s,e,a),Hn(t,e,null),n=!0,i||(o=Il(e,"click",l[36]),i=!0)},p(s,a){const _={};a[0]&131072&&(_.api_calls=s[17]),a[0]&2&&(_.dependencies=s[1]),t.$set(_)},i(s){n||(je(t.$$.fragment,s),n=!0)},o(s){Ue(t.$$.fragment,s),n=!1},d(s){s&&Ve(e),Bn(t),i=!1,o()}}}function Oo(l){let e,t,n,i,o,s,a,_;return o=new Wc({props:{root_node:l[14],dependencies:l[1],root:l[0],app:l[10],space_id:l[11],api_calls:l[17],username:l[13]}}),o.$on("close",l[38]),{c(){e=We("div"),t=We("div"),n=et(),i=We("div"),Un(o.$$.fragment),oe(t,"class","backdrop svelte-1rjryqp"),oe(i,"class","api-docs-wrap svelte-1rjryqp"),oe(e,"class","api-docs svelte-1rjryqp")},m(r,c){Be(r,e,c),Pe(e,t),Pe(e,n),Pe(e,i),Hn(o,i,null),s=!0,a||(_=Il(t,"click",l[37]),a=!0)},p(r,c){const u={};c[0]&16384&&(u.root_node=r[14]),c[0]&2&&(u.dependencies=r[1]),c[0]&1&&(u.root=r[0]),c[0]&1024&&(u.app=r[10]),c[0]&2048&&(u.space_id=r[11]),c[0]&131072&&(u.api_calls=r[17]),c[0]&8192&&(u.username=r[13]),o.$set(u)},i(r){s||(je(o.$$.fragment,r),s=!0)},o(r){Ue(o.$$.fragment,r),s=!1},d(r){r&&Ve(e),Bn(o),a=!1,_()}}}function Io(l){let e,t;return e=new Qr({props:{messages:l[18]}}),e.$on("close",l[25]),{c(){Un(e.$$.fragment)},m(n,i){Hn(e,n,i),t=!0},p(n,i){const o={};i[0]&262144&&(o.messages=n[18]),e.$set(o)},i(n){t||(je(e.$$.fragment,n),t=!0)},o(n){Ue(e.$$.fragment,n),t=!1},d(n){Bn(e,n)}}}function df(l){let e,t,n,i,o,s,a,_,r,c,u=l[7]&&Ao(l),f=l[14]&&l[10].config&&Lo(l),d=l[6]&&To(l),g=l[16]&&Po(l),m=l[15]&&l[14]&&Oo(l),v=l[18]&&Io(l);return{c(){u&&u.c(),e=Co(),t=et(),n=We("div"),i=We("div"),f&&f.c(),o=et(),d&&d.c(),s=et(),g&&g.c(),a=et(),m&&m.c(),_=et(),v&&v.c(),r=Co(),oe(i,"class","contain svelte-1rjryqp"),bn(i,"flex-grow",l[8]?"1":"auto"),oe(n,"class","wrap svelte-1rjryqp"),bn(n,"min-height",l[8]?"100%":"auto")},m(p,j){u&&u.m($l.head,null),Pe($l.head,e),Be(p,t,j),Be(p,n,j),Pe(n,i),f&&f.m(i,null),Pe(n,o),d&&d.m(n,null),Be(p,s,j),g&&g.m(p,j),Be(p,a,j),m&&m.m(p,j),Be(p,_,j),v&&v.m(p,j),Be(p,r,j),c=!0},p(p,j){p[7]?u||(u=Ao(p),u.c(),u.m(e.parentNode,e)):u&&(u.d(1),u=null),p[14]&&p[10].config?f?(f.p(p,j),j[0]&17408&&je(f,1)):(f=Lo(p),f.c(),je(f,1),f.m(i,null)):f&&($n(),Ue(f,1,1,()=>{f=null}),vn()),j[0]&256&&bn(i,"flex-grow",p[8]?"1":"auto"),p[6]?d?d.p(p,j):(d=To(p),d.c(),d.m(n,null)):d&&(d.d(1),d=null),j[0]&256&&bn(n,"min-height",p[8]?"100%":"auto"),p[16]?g?(g.p(p,j),j[0]&65536&&je(g,1)):(g=Po(p),g.c(),je(g,1),g.m(a.parentNode,a)):g&&($n(),Ue(g,1,1,()=>{g=null}),vn()),p[15]&&p[14]?m?(m.p(p,j),j[0]&49152&&je(m,1)):(m=Oo(p),m.c(),je(m,1),m.m(_.parentNode,_)):m&&($n(),Ue(m,1,1,()=>{m=null}),vn()),p[18]?v?(v.p(p,j),j[0]&262144&&je(v,1)):(v=Io(p),v.c(),je(v,1),v.m(r.parentNode,r)):v&&($n(),Ue(v,1,1,()=>{v=null}),vn())},i(p){c||(je(f),je(g),je(m),je(v),c=!0)},o(p){Ue(f),Ue(g),Ue(m),Ue(v),c=!1},d(p){p&&(Ve(t),Ve(n),Ve(s),Ve(a),Ve(_),Ve(r)),u&&u.d(p),Ve(e),f&&f.d(),d&&d.d(),g&&g.d(p),m&&m.d(p),v&&v.d(p)}}}const hf=/^'([^]+)'$/,gf=15,vf=10;function Do(l){return"detail"in l}function $f(l,e,t){let n,i,o,s,a;xt(l,Ro,A=>t(19,s=A)),Ts();let{root:_}=e,{components:r}=e,{layout:c}=e,{dependencies:u}=e,{title:f="Gradio"}=e,{target:d}=e,{autoscroll:g}=e,{show_api:m=!0}=e,{show_footer:v=!0}=e,{control_page_title:p=!1}=e,{app_mode:j}=e,{theme_mode:w}=e,{app:b}=e,{space_id:C}=e,{version:h}=e,{js:k}=e,{fill_height:L=!1}=e,{ready:y}=e,{username:$}=e;const{layout:E,targets:P,update_value:D,get_data:U,loading_status:O,scheduled_updates:V,create_layout:M,rerender_layout:R}=rf();xt(l,E,A=>t(14,a=A)),xt(l,P,A=>t(43,i=A)),xt(l,O,A=>t(34,n=A)),xt(l,V,A=>t(44,o=A));let se=new URLSearchParams(window.location.search),de=se.get("view")==="api"&&m,Y=se.get("view")==="api-recorder"&&m;function Qe(A){t(16,Y=!1),t(15,de=A);let x=new URLSearchParams(window.location.search);A?x.set("view","api"):x.delete("view"),history.replaceState(null,"","?"+x.toString())}let an=[],{render_complete:_n=!1}=e;async function Dl(A,x){const W=u.find(ee=>ee.id==x).outputs,B=A?.map((ee,Le)=>({id:W[Le],prop:"value_is_output",value:!0}));D(B),await al();const ne=[];A?.forEach((ee,Le)=>{if(typeof ee=="object"&&ee!==null&&ee.__type__==="update")for(const[Oe,K]of Object.entries(ee))Oe!=="__type__"&&ne.push({id:W[Le],prop:Oe,value:K});else ne.push({id:W[Le],prop:"value",value:ee})}),D(ne),await al()}let Rl=new Map,re=[];function rt(A,x,W,B=10,ne=!0){return{message:A,fn_index:x,type:W,id:++_s,duration:B,visible:ne}}function as(A,x){t(18,re=[rt(A,-1,x),...re])}let _s=-1,Gn=!1;document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(Gn=!0)});const cs=s("blocks.long_requests_queue"),us=s("blocks.connection_can_break"),fs=s("blocks.lost_connection"),Sl=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);let Vl=!1,Ml=!1;function Rt(A,x=null,W=null){let B=()=>{};function ne(){B()}o?B=V.subscribe(ee=>{ee||(Nl(A,x,W),ne())}):Nl(A,x,W)}async function Nl(A,x=null,W=null){let B=u.find(K=>K.id===A);const ne=O.get_status_for_fn(A);t(18,re=re.filter(({fn_index:K})=>K!==A)),(ne==="pending"||ne==="generating")&&(B.pending_request=!0);let ee={fn_index:A,data:await Promise.all(B.inputs.map(K=>U(K))),event_data:B.collects_event_data?W:null,trigger_id:x};B.frontend_fn?B.frontend_fn(ee.data.concat(await Promise.all(B.outputs.map(K=>U(K))))).then(K=>{B.backend_fn?(ee.data=K,Le(B,ee)):Dl(K,A)}):B.types.cancel&&B.cancels?await Promise.all(B.cancels.map(async K=>{const Je=Rl.get(K);return Je?.cancel(),Je})):B.backend_fn&&Le(B,ee);function Le(K,Je){K.trigger_mode==="once"?K.pending_request||Oe(Je):K.trigger_mode==="multiple"?Oe(Je):K.trigger_mode==="always_last"&&(K.pending_request?K.final_event=Je:Oe(Je))}async function Oe(K){Y&&t(17,an=[...an,JSON.parse(JSON.stringify(K))]);let Je;try{Je=b.submit(K.fn_index,K.data,K.event_data,K.trigger_id)}catch(he){t(18,re=[rt(String(he),0,"error"),...re]),O.update({status:"error",fn_index:0,eta:0,queue:!1,queue_position:null}),cn(n);return}Rl.set(A,Je);for await(const he of Je)he.type==="data"?ws(he):he.type==="render"?ys(he):he.type==="status"?js(he):he.type==="log"&&Es(he);function ws(he){const{data:ve,fn_index:le}=he;B.pending_request&&B.final_event&&(B.pending_request=!1,Oe(B.final_event)),B.pending_request=!1,Dl(ve,le),cn(n)}function ys(he){const{data:ve}=he;let le=ve.components,Te=ve.layout,at=ve.dependencies,_t=ve.render_id,Zn=[];u.forEach((Xt,Cs)=>{Xt.rendered_in===_t&&Zn.push(Cs)}),Zn.reverse().forEach(Xt=>{u.splice(Xt,1)}),at.forEach(Xt=>{u.push(Xt)}),R({components:le,layout:Te,root:_,dependencies:u,render_id:_t})}function Es(he){const{log:ve,fn_index:le,level:Te,duration:at,visible:_t}=he;t(18,re=[rt(ve,le,Te,at,_t),...re])}function js(he){const{fn_index:ve,...le}=he;if(O.update({...le,status:le.stage,progress:le.progress_data,fn_index:ve}),cn(n),!Vl&&C!==null&&le.position!==void 0&&le.position>=2&&le.eta!==void 0&&le.eta>gf&&(Vl=!0,t(18,re=[rt(cs,ve,"warning"),...re])),!Ml&&Sl&&le.eta!==void 0&&le.eta>vf&&(Ml=!0,t(18,re=[rt(us,ve,"warning"),...re])),le.stage==="complete"&&(le.changed_state_ids?.forEach(Te=>{u.filter(at=>at.targets.some(([_t,Zn])=>_t===Te)).forEach(at=>{Rt(at.id,K.trigger_id)})}),u.forEach(async Te=>{Te.trigger_after===ve&&Rt(Te.id,K.trigger_id)})),le.broken&&Sl&&Gn)window.setTimeout(()=>{t(18,re=[rt(fs,ve,"error"),...re])},0),Rt(B.id,K.trigger_id,W),Gn=!1;else if(le.stage==="error"){if(le.message){const Te=le.message.replace(hf,(at,_t)=>_t);t(18,re=[rt(Te,ve,"error",le.duration,le.visible),...re])}u.map(async Te=>{Te.trigger_after===ve&&!Te.trigger_only_on_success&&Rt(Te.id,K.trigger_id)})}}}}function ps(A,x){if(C===null)return;const W=new URL(`https://huggingface.co/spaces/${C}/discussions/new`);A!==void 0&&A.length>0&&W.searchParams.set("title",A),W.searchParams.set("description",x),window.open(W.toString(),"_blank")}function ms(A){const x=A.detail;t(18,re=re.filter(W=>W.id!==x))}const ds=A=>!!(A&&new URL(A,location.href).origin!==location.origin);async function hs(){k&&await new ls(`let result = await (${k})();
					return (!Array.isArray(result)) ? [result] : result;`)(),await al();for(var A=d.getElementsByTagName("a"),x=0;x<A.length;x++){const W=A[x].getAttribute("target"),B=A[x].getAttribute("href");ds(B)&&W!=="_blank"&&A[x].setAttribute("target","_blank")}u.forEach(W=>{W.targets.some(B=>B[1]==="load")&&Rt(W.id)}),!_n&&(d.addEventListener("prop_change",W=>{if(!Do(W))throw new Error("not a custom event");const{id:B,prop:ne,value:ee}=W.detail;D([{id:B,prop:ne,value:ee}])}),d.addEventListener("gradio",W=>{if(!Do(W))throw new Error("not a custom event");const{id:B,event:ne,data:ee}=W.detail;if(ne==="share"){const{title:Le,description:Oe}=ee;ps(Le,Oe)}else ne==="error"||ne==="warning"?t(18,re=[rt(ee,-1,ne),...re]):ne=="clear_status"?gs(B,"complete",ee):i[B]?.[ne]?.forEach(Oe=>{requestAnimationFrame(()=>{Rt(Oe,B,ee)})})}),t(28,_n=!0))}function gs(A,x,W){W.status=x,D([{id:A,prop:"loading_status",value:W}])}function cn(A){let x=[];Object.entries(A).forEach(([ne,ee])=>{let Le=u.find(Oe=>Oe.id==ee.fn_index);Le!==void 0&&(ee.scroll_to_output=Le.scroll_to_output,ee.show_progress=Le.show_progress,x.push({id:parseInt(ne),prop:"loading_status",value:ee}))});const W=O.get_inputs_to_update(),B=Array.from(W).map(([ne,ee])=>({id:ne,prop:"pending",value:ee==="pending"}));D([...x,...B])}const vs=()=>{Qe(!de)},$s=()=>{Qe(!0),t(16,Y=!1)},bs=()=>{Qe(!1)},ks=A=>{Qe(!1),t(17,an=[]),t(16,Y=A.detail.api_recorder_visible)};return l.$$set=A=>{"root"in A&&t(0,_=A.root),"components"in A&&t(29,r=A.components),"layout"in A&&t(30,c=A.layout),"dependencies"in A&&t(1,u=A.dependencies),"title"in A&&t(2,f=A.title),"target"in A&&t(3,d=A.target),"autoscroll"in A&&t(4,g=A.autoscroll),"show_api"in A&&t(5,m=A.show_api),"show_footer"in A&&t(6,v=A.show_footer),"control_page_title"in A&&t(7,p=A.control_page_title),"app_mode"in A&&t(8,j=A.app_mode),"theme_mode"in A&&t(9,w=A.theme_mode),"app"in A&&t(10,b=A.app),"space_id"in A&&t(11,C=A.space_id),"version"in A&&t(12,h=A.version),"js"in A&&t(31,k=A.js),"fill_height"in A&&t(32,L=A.fill_height),"ready"in A&&t(27,y=A.ready),"username"in A&&t(13,$=A.username),"render_complete"in A&&t(28,_n=A.render_complete)},l.$$.update=()=>{l.$$.dirty[0]&1610613763|l.$$.dirty[1]&2&&M({components:r,layout:c,dependencies:u,root:_,app:b,options:{fill_height:L}}),l.$$.dirty[0]&16384&&t(27,y=!!a),l.$$.dirty[1]&8&&cn(n)},[_,u,f,d,g,m,v,p,j,w,b,C,h,$,a,de,Y,an,re,s,E,P,O,V,Qe,ms,hs,y,_n,r,c,k,L,as,n,vs,$s,bs,ks]}class bf extends uf{constructor(e){super(),pf(this,e,$f,df,mf,{root:0,components:29,layout:30,dependencies:1,title:2,target:3,autoscroll:4,show_api:5,show_footer:6,control_page_title:7,app_mode:8,theme_mode:9,app:10,space_id:11,version:12,js:31,fill_height:32,ready:27,username:13,render_complete:28,add_new_message:33},null,[-1,-1,-1])}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),fe()}get components(){return this.$$.ctx[29]}set components(e){this.$$set({components:e}),fe()}get layout(){return this.$$.ctx[30]}set layout(e){this.$$set({layout:e}),fe()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),fe()}get title(){return this.$$.ctx[2]}set title(e){this.$$set({title:e}),fe()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),fe()}get autoscroll(){return this.$$.ctx[4]}set autoscroll(e){this.$$set({autoscroll:e}),fe()}get show_api(){return this.$$.ctx[5]}set show_api(e){this.$$set({show_api:e}),fe()}get show_footer(){return this.$$.ctx[6]}set show_footer(e){this.$$set({show_footer:e}),fe()}get control_page_title(){return this.$$.ctx[7]}set control_page_title(e){this.$$set({control_page_title:e}),fe()}get app_mode(){return this.$$.ctx[8]}set app_mode(e){this.$$set({app_mode:e}),fe()}get theme_mode(){return this.$$.ctx[9]}set theme_mode(e){this.$$set({theme_mode:e}),fe()}get app(){return this.$$.ctx[10]}set app(e){this.$$set({app:e}),fe()}get space_id(){return this.$$.ctx[11]}set space_id(e){this.$$set({space_id:e}),fe()}get version(){return this.$$.ctx[12]}set version(e){this.$$set({version:e}),fe()}get js(){return this.$$.ctx[31]}set js(e){this.$$set({js:e}),fe()}get fill_height(){return this.$$.ctx[32]}set fill_height(e){this.$$set({fill_height:e}),fe()}get ready(){return this.$$.ctx[27]}set ready(e){this.$$set({ready:e}),fe()}get username(){return this.$$.ctx[13]}set username(e){this.$$set({username:e}),fe()}get render_complete(){return this.$$.ctx[28]}set render_complete(e){this.$$set({render_complete:e}),fe()}get add_new_message(){return this.$$.ctx[33]}}const Lf=Object.freeze(Object.defineProperty({__proto__:null,default:bf},Symbol.toStringTag,{value:"Module"}));export{Lf as B,Xn as S,Qr as T,Cf as c,Af as f,jf as u};
//# sourceMappingURL=Blocks-CyfcXtBq.js.map
