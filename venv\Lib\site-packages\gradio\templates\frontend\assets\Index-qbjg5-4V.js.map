{"version": 3, "file": "Index-qbjg5-4V.js", "sources": ["../../../../js/image/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as Webcam } from \"./shared/Webcam.svelte\";\n\texport { default as BaseImageUploader } from \"./shared/ImageUploader.svelte\";\n\texport { default as BaseStaticImage } from \"./shared/ImagePreview.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n\texport { default as BaseImage } from \"./shared/Image.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport StaticImage from \"./shared/ImagePreview.svelte\";\n\timport ImageUploader from \"./shared/ImageUploader.svelte\";\n\timport { afterUpdate } from \"svelte\";\n\n\timport { Block, Empty, UploadText } from \"@gradio/atoms\";\n\timport { Image } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { FileData } from \"@gradio/client\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype sources = \"upload\" | \"webcam\" | \"clipboard\" | null;\n\n\texport let value_is_output = false;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\tlet old_value: null | FileData = null;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let show_download_button: boolean;\n\texport let root: string;\n\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\n\texport let _selectable = false;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let show_share_button = false;\n\texport let sources: (\"clipboard\" | \"webcam\" | \"upload\")[] = [\n\t\t\"upload\",\n\t\t\"clipboard\",\n\t\t\"webcam\"\n\t];\n\texport let interactive: boolean;\n\texport let streaming: boolean;\n\texport let pending: boolean;\n\texport let mirror_webcam: boolean;\n\texport let placeholder: string | undefined = undefined;\n\texport let show_fullscreen_button: boolean;\n\n\texport let gradio: Gradio<{\n\t\tinput: never;\n\t\tchange: never;\n\t\terror: string;\n\t\tedit: never;\n\t\tstream: never;\n\t\tdrag: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t\tif (!value_is_output) {\n\t\t\t\tgradio.dispatch(\"input\");\n\t\t\t}\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\tlet dragging: boolean;\n\tlet active_source: sources = null;\n\tlet upload_component: ImageUploader;\n\tconst handle_drag_event = (event: Event): void => {\n\t\tconst drag_event = event as DragEvent;\n\t\tdrag_event.preventDefault();\n\t\tdrag_event.stopPropagation();\n\t\tif (drag_event.type === \"dragenter\" || drag_event.type === \"dragover\") {\n\t\t\tdragging = true;\n\t\t} else if (drag_event.type === \"dragleave\") {\n\t\t\tdragging = false;\n\t\t}\n\t};\n\n\tconst handle_drop = (event: Event): void => {\n\t\tif (interactive) {\n\t\t\tconst drop_event = event as DragEvent;\n\t\t\tdrop_event.preventDefault();\n\t\t\tdrop_event.stopPropagation();\n\t\t\tdragging = false;\n\n\t\t\tif (upload_component) {\n\t\t\t\tupload_component.loadFilesFromDrop(drop_event);\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n{#if !interactive}\n\t<Block\n\t\t{visible}\n\t\tvariant={\"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\t\t<StaticImage\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\tselectable={_selectable}\n\t\t\t{show_share_button}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{show_fullscreen_button}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\ton:dragenter={handle_drag_event}\n\t\ton:dragleave={handle_drag_event}\n\t\ton:dragover={handle_drag_event}\n\t\ton:drop={handle_drop}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<ImageUploader\n\t\t\tbind:this={upload_component}\n\t\t\tbind:active_source\n\t\t\tbind:value\n\t\t\tbind:dragging\n\t\t\tselectable={_selectable}\n\t\t\t{root}\n\t\t\t{sources}\n\t\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\t\ton:clear={() => {\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:stream={() => gradio.dispatch(\"stream\")}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{pending}\n\t\t\t{streaming}\n\t\t\t{mirror_webcam}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={gradio.client.upload}\n\t\t\tstream_handler={gradio.client.stream}\n\t\t>\n\t\t\t{#if active_source === \"upload\" || !active_source}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"image\" {placeholder} />\n\t\t\t{:else if active_source === \"clipboard\"}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"clipboard\" mode=\"short\" />\n\t\t\t{:else}\n\t\t\t\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n\t\t\t{/if}\n\t\t</ImageUploader>\n\t</Block>\n{/if}\n"], "names": ["afterUpdate", "ctx", "dirty", "block_changes", "uploadtext_changes", "imageuploader_changes", "staticimage_changes", "value_is_output", "$$props", "elem_id", "elem_classes", "visible", "value", "old_value", "label", "show_label", "show_download_button", "root", "height", "width", "_selectable", "container", "scale", "min_width", "loading_status", "show_share_button", "sources", "interactive", "streaming", "pending", "mirror_webcam", "placeholder", "show_fullscreen_button", "gradio", "$$invalidate", "dragging", "active_source", "upload_component", "handle_drag_event", "event", "drag_event", "handle_drop", "drop_event", "select_handler", "detail", "share_handler", "error_handler", "clear_status_handler", "$$value", "select_handler_1", "share_handler_1"], "mappings": "m4CAcU,CAAA,YAAAA,WAA2B,sFAsI1B,QAAAC,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,MAAU,kCAEF,2GAIFA,EAAiB,EAAA,CAAA,oBACjBA,EAAiB,EAAA,CAAA,mBAClBA,EAAiB,EAAA,CAAA,eACrBA,EAAW,EAAA,CAAA,2FAdXC,EAAA,CAAA,EAAA,IAAAC,EAAA,QAAAF,EAAU,CAAA,IAAA,KAAO,SAAW,uCACxBA,EAAQ,EAAA,EAAG,QAAU,+DAI1BC,EAAA,CAAA,EAAA,MAAAC,EAAA,OAAAF,MAAU,iVAvCT,oBACIA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,MAAU,kCAEF,gNANHA,EAAQ,EAAA,EAAG,QAAU,+DAI1BC,EAAA,CAAA,EAAA,MAAAC,EAAA,OAAAF,MAAU,yUAyFK,ySAFH,KAAAA,MAAO,qGAAPC,EAAA,CAAA,EAAA,UAAAE,EAAA,KAAAH,MAAO,8IAFP,KAAAA,MAAO,sGAAPC,EAAA,CAAA,EAAA,UAAAE,EAAA,KAAAH,MAAO,qXADrBA,EAAa,EAAA,IAAK,UAAQ,CAAKA,EAAa,EAAA,EAAA,EAEvCA,QAAkB,YAAW,iVAxC3B,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,0LASNA,EAAW,EAAA,uGAsBR,cAAAA,MAAO,cAChB,KAAAA,MAAO,YACLA,EAAM,EAAA,EAAC,OAAO,sBACNA,EAAM,EAAA,EAAC,OAAO,wkBApClB,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,YACbA,EAAc,CAAA,CAAA,qDASNA,EAAW,EAAA,oNAsBRC,EAAA,CAAA,EAAA,UAAAG,EAAA,cAAAJ,MAAO,eAChBC,EAAA,CAAA,EAAA,UAAAG,EAAA,KAAAJ,MAAO,8BACLA,EAAM,EAAA,EAAC,OAAO,wCACNA,EAAM,EAAA,EAAC,OAAO,6ZA1ElB,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,0KAUNA,EAAW,EAAA,0BAEjB,KAAAA,MAAO,4OAdD,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,YACbA,EAAc,CAAA,CAAA,yKAUNA,EAAW,EAAA,2CAEjBC,EAAA,CAAA,EAAA,UAAAI,EAAA,KAAAL,MAAO,2QA9BVA,EAAW,EAAA,IAAA,uTAvFL,GAAA,CAAA,gBAAAM,EAAkB,EAAK,EAAAC,EACvB,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,EACd,CAAA,MAAAI,EAAyB,IAAI,EAAAJ,EACpCK,EAA6B,MACtB,MAAAC,CAAa,EAAAN,GACb,WAAAO,CAAmB,EAAAP,GACnB,qBAAAQ,CAA6B,EAAAR,GAC7B,KAAAS,CAAY,EAAAT,GAEZ,OAAAU,CAA0B,EAAAV,GAC1B,MAAAW,CAAyB,EAAAX,EAEzB,CAAA,YAAAY,EAAc,EAAK,EAAAZ,EACnB,CAAA,UAAAa,EAAY,EAAI,EAAAb,EAChB,CAAA,MAAAc,EAAuB,IAAI,EAAAd,EAC3B,CAAA,UAAAe,EAAgC,MAAS,EAAAf,GACzC,eAAAgB,CAA6B,EAAAhB,EAC7B,CAAA,kBAAAiB,EAAoB,EAAK,EAAAjB,EACzB,CAAA,QAAAkB,EACV,CAAA,SACA,YACA,QAAA,CAAA,EAAAlB,GAEU,YAAAmB,CAAoB,EAAAnB,GACpB,UAAAoB,CAAkB,EAAApB,GAClB,QAAAqB,CAAgB,EAAArB,GAChB,cAAAsB,CAAsB,EAAAtB,EACtB,CAAA,YAAAuB,EAAkC,MAAS,EAAAvB,GAC3C,uBAAAwB,CAA+B,EAAAxB,GAE/B,OAAAyB,CAYT,EAAAzB,EAWFR,GAAW,IAAA,CACVkC,EAAA,GAAA3B,EAAkB,EAAK,QAGpB4B,EACAC,EAAyB,KACzBC,EACE,MAAAC,GAAqBC,GAAY,CAChC,MAAAC,EAAaD,EACnBC,EAAW,eAAc,EACzBA,EAAW,gBAAe,EACtBA,EAAW,OAAS,aAAeA,EAAW,OAAS,WAC1DN,EAAA,GAAAC,EAAW,EAAI,EACLK,EAAW,OAAS,aAC9BN,EAAA,GAAAC,EAAW,EAAK,GAIZM,GAAeF,GAAY,IAC5BZ,EAAW,CACR,MAAAe,EAAaH,EACnBG,EAAW,eAAc,EACzBA,EAAW,gBAAe,EAC1BR,EAAA,GAAAC,EAAW,EAAK,EAEZE,GACHA,EAAiB,kBAAkBK,CAAU,IA2BhCC,GAAA,CAAA,CAAA,OAAAC,KAAaX,EAAO,SAAS,SAAUW,CAAM,EAC9CC,GAAA,CAAA,CAAA,OAAAD,KAAaX,EAAO,SAAS,QAASW,CAAM,EAC5CE,GAAA,CAAA,CAAA,OAAAF,KAAaX,EAAO,SAAS,QAASW,CAAM,EAkClCG,GAAA,IAAAd,EAAO,SAAS,eAAgBT,CAAc,4CAI1Da,EAAgBW,yGAOZf,EAAO,SAAS,MAAM,UAEpCA,EAAO,SAAS,OAAO,UAEPA,EAAO,SAAS,QAAQ,OAC7B,OAAAW,CAAM,IAAAV,EAAA,GAAQC,EAAWS,CAAM,SAC1BX,EAAO,SAAS,QAAQ,EAC3BgB,GAAA,CAAA,CAAA,OAAAL,KAAaX,EAAO,SAAS,SAAUW,CAAM,EAC9CM,GAAA,CAAA,CAAA,OAAAN,KAAaX,EAAO,SAAS,QAASW,CAAM,OAC5C,OAAAA,KAAM,CAClBV,EAAA,EAAAV,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BS,EAAO,SAAS,QAASW,CAAM,igCAxH7B,KAAK,UAAUhC,CAAK,IAAM,KAAK,UAAUC,CAAS,IACrDqB,EAAA,GAAArB,EAAYD,CAAK,EACjBqB,EAAO,SAAS,QAAQ,EACnB1B,GACJ0B,EAAO,SAAS,OAAO"}