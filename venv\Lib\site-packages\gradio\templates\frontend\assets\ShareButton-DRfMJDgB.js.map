{"version": 3, "file": "ShareButton-DRfMJDgB.js", "sources": ["../../../../js/icons/src/Community.svelte", "../../../../js/atoms/src/ShareButton.svelte"], "sourcesContent": ["<svg id=\"icon\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"\n\t><path\n\t\td=\"M23,20a5,5,0,0,0-3.89,1.89L11.8,17.32a4.46,4.46,0,0,0,0-2.64l7.31-4.57A5,5,0,1,0,18,7a4.79,4.79,0,0,0,.2,1.32l-7.31,4.57a5,5,0,1,0,0,6.22l7.31,4.57A4.79,4.79,0,0,0,18,25a5,5,0,1,0,5-5ZM23,4a3,3,0,1,1-3,3A3,3,0,0,1,23,4ZM7,19a3,3,0,1,1,3-3A3,3,0,0,1,7,19Zm16,9a3,3,0,1,1,3-3A3,3,0,0,1,23,28Z\"\n\t\tfill=\"currentColor\"\n\t/></svg\n>\n", "<script lang=\"ts\">\n\timport IconButton from \"./IconButton.svelte\";\n\timport { Community } from \"@gradio/icons\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { ShareData } from \"@gradio/utils\";\n\timport { ShareError } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tshare: ShareData;\n\t\terror: string;\n\t}>();\n\n\texport let formatter: (arg0: any) => Promise<string>;\n\texport let value: any;\n\texport let i18n: I18nFormatter;\n\tlet pending = false;\n</script>\n\n<IconButton\n\tIcon={Community}\n\tlabel={i18n(\"common.share\")}\n\t{pending}\n\ton:click={async () => {\n\t\ttry {\n\t\t\tpending = true;\n\t\t\tconst formatted = await formatter(value);\n\t\t\tdispatch(\"share\", {\n\t\t\t\tdescription: formatted\n\t\t\t});\n\t\t} catch (e) {\n\t\t\tconsole.error(e);\n\t\t\tlet message = e instanceof ShareError ? e.message : \"Share failed.\";\n\t\t\tdispatch(\"error\", message);\n\t\t} finally {\n\t\t\tpending = false;\n\t\t}\n\t}}\n/>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "createEventDispatcher", "Community", "ctx", "dirty", "iconbutton_changes", "dispatch", "formatter", "$$props", "value", "i18n", "pending", "$$invalidate", "formatted", "e", "message", "ShareError"], "mappings": "0rBAAAA,EAKAC,EAAAC,EAAAC,CAAA,EAJEC,EAGCF,EAAAG,CAAA,mRCDO,CAAA,sBAAAC,UAAqC,6EAiBxCC,EACC,MAAAC,KAAK,cAAc,uGAAnBC,EAAA,IAAAC,EAAA,MAAAF,KAAK,cAAc,qIAbpB,MAAAG,EAAWL,QAKN,UAAAM,CAAyC,EAAAC,GACzC,MAAAC,CAAU,EAAAD,GACV,KAAAE,CAAmB,EAAAF,EAC1BG,EAAU,yBASZC,EAAA,EAAAD,EAAU,EAAI,QACRE,EAAS,MAASN,EAAUE,CAAK,EACvCH,EAAS,QACR,CAAA,YAAaO,CAAA,CAAA,QAENC,EAAC,CACT,QAAQ,MAAMA,CAAC,MACXC,EAAUD,aAAaE,EAAaF,EAAE,QAAU,gBACpDR,EAAS,QAASS,CAAO,UAEzBH,EAAA,EAAAD,EAAU,EAAK"}