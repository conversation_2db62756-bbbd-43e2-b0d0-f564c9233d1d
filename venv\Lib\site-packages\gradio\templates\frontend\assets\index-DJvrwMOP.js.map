{"version": 3, "file": "index-DJvrwMOP.js", "sources": ["../../../../js/video/shared/InteractiveVideo.svelte", "../../../../js/video/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Webcam } from \"@gradio/image\";\n\timport { Video } from \"@gradio/icons\";\n\n\timport { prettyBytes, playable } from \"./utils\";\n\timport Player from \"./Player.svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\n\texport let value: FileData | null = null;\n\texport let subtitle: FileData | null = null;\n\texport let sources:\n\t\t| [\"webcam\"]\n\t\t| [\"upload\"]\n\t\t| [\"webcam\", \"upload\"]\n\t\t| [\"upload\", \"webcam\"] = [\"webcam\", \"upload\"];\n\texport let label: string | undefined = undefined;\n\texport let show_download_button = false;\n\texport let show_label = true;\n\texport let mirror_webcam = false;\n\texport let include_audio: boolean;\n\texport let autoplay: boolean;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\texport let active_source: \"webcam\" | \"upload\" = \"webcam\";\n\texport let handle_reset_value: () => void = () => {};\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let loop: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tclear?: never;\n\t\tplay?: never;\n\t\tpause?: never;\n\t\tend?: never;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tstart_recording?: never;\n\t\tstop_recording?: never;\n\t}>();\n\n\tfunction handle_load({ detail }: CustomEvent<FileData | null>): void {\n\t\tvalue = detail;\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"upload\", detail!);\n\t}\n\n\tfunction handle_clear(): void {\n\t\tvalue = null;\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t}\n\n\tfunction handle_change(video: FileData): void {\n\t\tdispatch(\"change\", video);\n\t}\n\n\tfunction handle_capture({\n\t\tdetail\n\t}: CustomEvent<FileData | any | null>): void {\n\t\tdispatch(\"change\", detail);\n\t}\n\n\tlet dragging = false;\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={Video} label={label || \"Video\"} />\n<div data-testid=\"video\" class=\"video-container\">\n\t{#if value === null || value.url === undefined}\n\t\t<div class=\"upload-container\">\n\t\t\t{#if active_source === \"upload\"}\n\t\t\t\t<Upload\n\t\t\t\t\tbind:dragging\n\t\t\t\t\tfiletype=\"video/x-m4v,video/*\"\n\t\t\t\t\ton:load={handle_load}\n\t\t\t\t\t{max_file_size}\n\t\t\t\t\ton:error={({ detail }) => dispatch(\"error\", detail)}\n\t\t\t\t\t{root}\n\t\t\t\t\t{upload}\n\t\t\t\t\t{stream_handler}\n\t\t\t\t>\n\t\t\t\t\t<slot />\n\t\t\t\t</Upload>\n\t\t\t{:else if active_source === \"webcam\"}\n\t\t\t\t<Webcam\n\t\t\t\t\t{root}\n\t\t\t\t\t{mirror_webcam}\n\t\t\t\t\t{include_audio}\n\t\t\t\t\tmode=\"video\"\n\t\t\t\t\ton:error\n\t\t\t\t\ton:capture={handle_capture}\n\t\t\t\t\ton:start_recording\n\t\t\t\t\ton:stop_recording\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{upload}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</div>\n\t{:else}\n\t\t<ModifyUpload\n\t\t\t{i18n}\n\t\t\ton:clear={handle_clear}\n\t\t\tdownload={show_download_button ? value.url : null}\n\t\t/>\n\t\t{#if playable()}\n\t\t\t{#key value?.url}\n\t\t\t\t<Player\n\t\t\t\t\t{upload}\n\t\t\t\t\t{root}\n\t\t\t\t\tinteractive\n\t\t\t\t\t{autoplay}\n\t\t\t\t\tsrc={value.url}\n\t\t\t\t\tsubtitle={subtitle?.url}\n\t\t\t\t\ton:play\n\t\t\t\t\ton:pause\n\t\t\t\t\ton:stop\n\t\t\t\t\ton:end\n\t\t\t\t\tmirror={mirror_webcam && active_source === \"webcam\"}\n\t\t\t\t\t{label}\n\t\t\t\t\t{handle_change}\n\t\t\t\t\t{handle_reset_value}\n\t\t\t\t\t{loop}\n\t\t\t\t/>\n\t\t\t{/key}\n\t\t{:else if value.size}\n\t\t\t<div class=\"file-name\">{value.orig_name || value.url}</div>\n\t\t\t<div class=\"file-size\">\n\t\t\t\t{prettyBytes(value.size)}\n\t\t\t</div>\n\t\t{/if}\n\t{/if}\n\n\t<SelectSource {sources} bind:active_source {handle_clear} />\n</div>\n\n<style>\n\t.file-name {\n\t\tpadding: var(--size-6);\n\t\tfont-size: var(--text-xxl);\n\t\tword-break: break-all;\n\t}\n\n\t.file-size {\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.upload-container {\n\t\theight: 100%;\n\t\twidth: 100%;\n\t}\n\n\t.video-container {\n\t\tdisplay: flex;\n\t\theight: 100%;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\n\timport type { FileData } from \"@gradio/client\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport StaticVideo from \"./shared/VideoPreview.svelte\";\n\timport Video from \"./shared/InteractiveVideo.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { video: FileData; subtitles: FileData | null } | null =\n\t\tnull;\n\tlet old_value: { video: FileData; subtitles: FileData | null } | null = null;\n\n\texport let label: string;\n\texport let sources:\n\t\t| [\"webcam\"]\n\t\t| [\"upload\"]\n\t\t| [\"webcam\", \"upload\"]\n\t\t| [\"upload\", \"webcam\"];\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\n\texport let container = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let autoplay = false;\n\texport let show_share_button = true;\n\texport let show_download_button: boolean;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tupload: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tstop_recording: never;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\twarning: string;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let interactive: boolean;\n\texport let mirror_webcam: boolean;\n\texport let include_audio: boolean;\n\texport let loop = false;\n\n\tlet _video: FileData | null = null;\n\tlet _subtitle: FileData | null = null;\n\n\tlet active_source: \"webcam\" | \"upload\";\n\n\tlet initial_value: { video: FileData; subtitles: FileData | null } | null =\n\t\tvalue;\n\n\t$: if (value && initial_value === null) {\n\t\tinitial_value = value;\n\t}\n\n\tconst handle_reset_value = (): void => {\n\t\tif (initial_value === null || value === initial_value) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalue = initial_value;\n\t};\n\n\t$: if (sources && !active_source) {\n\t\tactive_source = sources[0];\n\t}\n\n\t$: {\n\t\tif (value != null) {\n\t\t\t_video = value.video;\n\t\t\t_subtitle = value.subtitles;\n\t\t} else {\n\t\t\t_video = null;\n\t\t\t_subtitle = null;\n\t\t}\n\t}\n\n\tlet dragging = false;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tfunction handle_change({ detail }: CustomEvent<FileData | null>): void {\n\t\tif (detail != null) {\n\t\t\tvalue = { video: detail, subtitles: null } as {\n\t\t\t\tvideo: FileData;\n\t\t\t\tsubtitles: FileData | null;\n\t\t\t} | null;\n\t\t} else {\n\t\t\tvalue = null;\n\t\t}\n\t}\n\n\tfunction handle_error({ detail }: CustomEvent<string>): void {\n\t\tconst [level, status] = detail.includes(\"Invalid file type\")\n\t\t\t? [\"warning\", \"complete\"]\n\t\t\t: [\"error\", \"error\"];\n\t\tloading_status = loading_status || {};\n\t\tloading_status.status = status as LoadingStatus[\"status\"];\n\t\tloading_status.message = detail;\n\t\tgradio.dispatch(level as \"error\" | \"warning\", detail);\n\t}\n</script>\n\n{#if !interactive}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{height}\n\t\t{width}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\tallow_overflow={false}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<StaticVideo\n\t\t\tvalue={_video}\n\t\t\tsubtitle={_subtitle}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{autoplay}\n\t\t\t{loop}\n\t\t\t{show_share_button}\n\t\t\t{show_download_button}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={gradio.client.upload}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{height}\n\t\t{width}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\tallow_overflow={false}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<Video\n\t\t\tvalue={_video}\n\t\t\tsubtitle={_subtitle}\n\t\t\ton:change={handle_change}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:error={handle_error}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{sources}\n\t\t\t{active_source}\n\t\t\t{mirror_webcam}\n\t\t\t{include_audio}\n\t\t\t{autoplay}\n\t\t\t{root}\n\t\t\t{loop}\n\t\t\t{handle_reset_value}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\t\ton:stop_recording={() => gradio.dispatch(\"stop_recording\")}\n\t\t\ti18n={gradio.i18n}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\tupload={gradio.client.upload}\n\t\t\tstream_handler={gradio.client.stream}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"video\" />\n\t\t</Video>\n\t</Block>\n{/if}\n"], "names": ["createEventDispatcher", "ctx", "playable", "insert", "target", "div", "anchor", "t0_value", "prettyBytes", "div0", "div1", "dirty", "set_data", "t0", "t2", "t2_value", "previous_key", "safe_not_equal", "player_changes", "Video", "blocklabel_changes", "value", "$$props", "subtitle", "sources", "label", "show_download_button", "show_label", "mirror_webcam", "include_audio", "autoplay", "root", "i18n", "active_source", "handle_reset_value", "max_file_size", "upload", "stream_handler", "loop", "dispatch", "handle_load", "detail", "$$invalidate", "handle_clear", "handle_change", "video", "handle_capture", "dragging", "error_handler_1", "block_changes", "uploadtext_changes", "video_changes", "staticvideo_changes", "elem_id", "elem_classes", "visible", "old_value", "loading_status", "height", "width", "container", "scale", "min_width", "show_share_button", "gradio", "interactive", "_video", "_subtitle", "initial_value", "handle_error", "level", "status", "clear_status_handler", "share_handler", "error_handler", "clear_status_handler_1"], "mappings": "ipDACU,CAAA,sBAAAA,EAAA,SAAqC,iGA6GlCC,EAAoB,CAAA,EAAGA,KAAM,IAAM,sBADnCA,EAAY,EAAA,CAAA,4DAGlBC,GAAQ,OAoBHD,KAAM,KAAI,6MAtBTA,EAAoB,CAAA,EAAGA,KAAM,IAAM,uWAhCxC,OAAAA,OAAkB,SAAQ,EAarBA,OAAkB,SAAQ,uHAdrCE,EA4BKC,EAAAC,EAAAC,CAAA,iRA4BoBC,GAAAN,EAAM,CAAA,EAAA,WAAaA,KAAM,KAAG,WAElDO,GAAYP,EAAK,CAAA,EAAC,IAAI,EAAA,oJAFxBE,EAA0DC,EAAAK,EAAAH,CAAA,mBAC1DH,EAEKC,EAAAM,EAAAJ,CAAA,kBAHmBK,EAAA,CAAA,EAAA,GAAAJ,KAAAA,GAAAN,EAAM,CAAA,EAAA,WAAaA,KAAM,KAAG,KAAAW,GAAAC,EAAAN,CAAA,iBAElDC,GAAYP,EAAK,CAAA,EAAC,IAAI,EAAA,KAAAW,GAAAE,EAAAC,CAAA,uDAtBlB,IAAAC,EAAAf,MAAO,+EAAPU,EAAA,CAAA,EAAA,GAAAM,GAAAD,EAAAA,EAAAf,MAAO,GAAG,0OAMT,IAAAA,KAAM,IACD,SAAAA,MAAU,WAKZA,EAAa,CAAA,GAAIA,EAAa,CAAA,IAAK,iTANtCU,EAAA,CAAA,EAAA,IAAAO,EAAA,IAAAjB,KAAM,KACDU,EAAA,CAAA,EAAA,IAAAO,EAAA,SAAAjB,MAAU,yBAKZA,EAAa,CAAA,GAAIA,EAAa,CAAA,IAAK,4WA3B/BA,EAAc,EAAA,CAAA,qpBAhBjBA,EAAW,EAAA,CAAA,8sBARMkB,GAAc,MAAAlB,MAAS,gDAEhD,OAAAA,OAAU,MAAQA,EAAM,CAAA,EAAA,MAAQ,OAAS,sWAD/CE,EAkEKC,EAAAC,EAAAC,CAAA,0FAnEwCK,EAAA,CAAA,EAAA,KAAAS,EAAA,MAAAnB,MAAS,ocA7D1C,CAAA,MAAAoB,EAAyB,IAAI,EAAAC,EAC7B,CAAA,SAAAC,EAA4B,IAAI,EAAAD,GAChC,QAAAE,EAAO,CAIS,SAAU,QAAQ,CAAA,EAAAF,EAClC,CAAA,MAAAG,EAA4B,MAAS,EAAAH,EACrC,CAAA,qBAAAI,EAAuB,EAAK,EAAAJ,EAC5B,CAAA,WAAAK,EAAa,EAAI,EAAAL,EACjB,CAAA,cAAAM,EAAgB,EAAK,EAAAN,GACrB,cAAAO,CAAsB,EAAAP,GACtB,SAAAQ,CAAiB,EAAAR,GACjB,KAAAS,CAAY,EAAAT,GACZ,KAAAU,CAAmB,EAAAV,EACnB,CAAA,cAAAW,EAAqC,QAAQ,EAAAX,GAC7C,mBAAAY,EAAkB,IAAA,MAClB,CAAA,cAAAC,EAA+B,IAAI,EAAAb,GACnC,OAAAc,CAAwB,EAAAd,GACxB,eAAAe,CAAgC,EAAAf,GAChC,KAAAgB,CAAa,EAAAhB,EAElB,MAAAiB,EAAWvC,KAaR,SAAAwC,GAAc,OAAAC,GAAM,CAC5BC,EAAA,EAAArB,EAAQoB,CAAM,EACdF,EAAS,SAAUE,CAAM,EACzBF,EAAS,SAAUE,CAAO,WAGlBE,GAAY,CACpBD,EAAA,EAAArB,EAAQ,IAAI,EACZkB,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,EAGR,SAAAK,EAAcC,EAAe,CACrCN,EAAS,SAAUM,CAAK,EAGhB,SAAAC,GACR,OAAAL,GAAM,CAENF,EAAS,SAAUE,CAAM,EAGtB,IAAAM,EAAW,6BAcE,MAAAC,EAAA,CAAA,CAAA,OAAAP,CAAM,IAAOF,EAAS,QAASE,CAAM,y9BAbnDF,EAAS,OAAQQ,CAAQ,osEC+FlB,QAAA9C,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,EAAA,EAAG,QAAU,eACzB,qHAQO,sIAVPU,EAAA,CAAA,EAAA,UAAAsC,EAAA,QAAAhD,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,uCACtDA,EAAQ,EAAA,EAAG,QAAU,0ZA1CzB,QAAAA,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,EAAA,EAAG,QAAU,eACzB,qHAQO,sIAVPU,EAAA,CAAA,EAAA,UAAAsC,EAAA,QAAAhD,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,uCACtDA,EAAQ,EAAA,EAAG,QAAU,6YAyFf,KAAAA,MAAO,oFAAPU,EAAA,CAAA,EAAA,SAAAuC,EAAA,KAAAjD,MAAO,qIApCb,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,mIAKXA,EAAM,EAAA,WACHA,EAAS,EAAA,8LAuBb,KAAAA,MAAO,KACE,cAAAA,MAAO,qBACdA,EAAM,EAAA,EAAC,OAAO,sBACNA,EAAM,EAAA,EAAC,OAAO,gEAzBnBA,EAAa,EAAA,CAAA,oCAEdA,EAAY,EAAA,CAAA,iUAXV,WAAAA,MAAO,YACbU,EAAA,CAAA,EAAA,QAAA,CAAA,KAAAV,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,mDAKXA,EAAM,EAAA,8BACHA,EAAS,EAAA,mUAuBbU,EAAA,CAAA,EAAA,SAAAwC,EAAA,KAAAlD,MAAO,MACEU,EAAA,CAAA,EAAA,SAAAwC,EAAA,cAAAlD,MAAO,sCACdA,EAAM,EAAA,EAAC,OAAO,uCACNA,EAAM,EAAA,EAAC,OAAO,kPA3ElB,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,mIAKXA,EAAM,EAAA,WACHA,EAAS,EAAA,0GAab,KAAAA,MAAO,YACLA,EAAM,EAAA,EAAC,OAAO,2QAtBV,WAAAA,MAAO,YACbU,EAAA,CAAA,EAAA,QAAA,CAAA,KAAAV,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,mDAKXA,EAAM,EAAA,8BACHA,EAAS,EAAA,wMAabU,EAAA,CAAA,EAAA,SAAAyC,EAAA,KAAAnD,MAAO,6BACLA,EAAM,EAAA,EAAC,OAAO,+NAtCnBA,EAAW,EAAA,IAAA,4TA9GL,GAAA,CAAA,QAAAoD,EAAU,EAAE,EAAA/B,GACZ,aAAAgC,EAAY,EAAA,EAAAhC,EACZ,CAAA,QAAAiC,EAAU,EAAI,EAAAjC,EACd,CAAA,MAAAD,EACV,IAAI,EAAAC,EACDkC,EAAoE,MAE7D,MAAA/B,CAAa,EAAAH,GACb,QAAAE,CAIY,EAAAF,GACZ,KAAAS,CAAY,EAAAT,GACZ,WAAAK,CAAmB,EAAAL,GACnB,eAAAmC,CAA6B,EAAAnC,GAC7B,OAAAoC,CAA0B,EAAApC,GAC1B,MAAAqC,CAAyB,EAAArC,EAEzB,CAAA,UAAAsC,EAAY,EAAK,EAAAtC,EACjB,CAAA,MAAAuC,EAAuB,IAAI,EAAAvC,EAC3B,CAAA,UAAAwC,EAAgC,MAAS,EAAAxC,EACzC,CAAA,SAAAQ,EAAW,EAAK,EAAAR,EAChB,CAAA,kBAAAyC,EAAoB,EAAI,EAAAzC,GACxB,qBAAAI,CAA6B,EAAAJ,GAC7B,OAAA0C,CAcT,EAAA1C,GACS,YAAA2C,CAAoB,EAAA3C,GACpB,cAAAM,CAAsB,EAAAN,GACtB,cAAAO,CAAsB,EAAAP,EACtB,CAAA,KAAAgB,EAAO,EAAK,EAAAhB,EAEnB4C,EAA0B,KAC1BC,EAA6B,KAE7BlC,EAEAmC,EACH/C,QAMKa,EAAkB,IAAA,CACnBkC,IAAkB,MAAQ/C,IAAU+C,GAIxC1B,EAAA,EAAArB,EAAQ+C,CAAa,GAiBlB,IAAArB,EAAW,GASN,SAAAH,IAAgB,OAAAH,GAAM,CAC1BA,GAAU,KACbC,EAAA,EAAArB,GAAU,MAAOoB,EAAQ,UAAW,IAAI,CAAA,EAKxCC,EAAA,EAAArB,EAAQ,IAAI,EAIL,SAAAgD,IAAe,OAAA5B,GAAM,CACtB,KAAA,CAAA6B,GAAOC,EAAM,EAAI9B,EAAO,SAAS,mBAAmB,EACvD,CAAA,UAAW,UAAU,EACrB,CAAA,QAAS,OAAO,EACpBC,EAAA,EAAAe,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAASc,GAAiCd,CAAA,MACzDA,EAAe,QAAUhB,EAAMgB,CAAA,EAC/BO,EAAO,SAASM,GAA8B7B,CAAM,EAuB5B,MAAA+B,GAAA,IAAAR,EAAO,SAAS,eAAgBP,CAAc,SAYtDO,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,QACvBA,EAAO,SAAS,KAAK,EACtBS,GAAA,CAAA,CAAA,OAAAhC,KAAauB,EAAO,SAAS,QAASvB,CAAM,EAC5CiC,GAAA,CAAA,CAAA,OAAAjC,KAAauB,EAAO,SAAS,QAASvB,CAAM,EAwBlCkC,GAAA,IAAAX,EAAO,SAAS,eAAgBP,CAAc,OAOzD,OAAAhB,CAAM,IAAAC,EAAA,GAAQK,EAAWN,CAAM,SAa3BuB,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACtBA,EAAO,SAAS,QAAQ,SAC1BA,EAAO,SAAS,MAAM,SACvBA,EAAO,SAAS,KAAK,SACTA,EAAO,SAAS,iBAAiB,SAClCA,EAAO,SAAS,gBAAgB,m2BAhJpD3C,GAAS+C,IAAkB,MACjC1B,EAAA,GAAA0B,EAAgB/C,CAAK,yBAWfG,GAAO,CAAKS,QAClBA,EAAgBT,EAAQ,CAAC,CAAA,oBAIrBH,GAAS,WACZ6C,EAAS7C,EAAM,KAAK,OACpB8C,EAAY9C,EAAM,SAAS,IAE3BqB,EAAA,GAAAwB,EAAS,IAAI,EACbxB,EAAA,GAAAyB,EAAY,IAAI,6BAOb,KAAK,UAAU9C,CAAK,IAAM,KAAK,UAAUmC,CAAS,IACrDd,EAAA,GAAAc,EAAYnC,CAAK,EACjB2C,EAAO,SAAS,QAAQ"}