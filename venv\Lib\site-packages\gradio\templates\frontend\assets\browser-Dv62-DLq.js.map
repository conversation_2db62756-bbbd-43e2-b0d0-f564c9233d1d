{"version": 3, "file": "browser-Dv62-DLq.js", "sources": ["../../../../node_modules/.pnpm/ws@8.13.0_bufferutil@4.0.7/node_modules/ws/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n"], "names": ["browser"], "mappings": "8XAEA,IAAAA,EAAiB,UAAY,CAC3B,MAAM,IAAI,MACR,uFAEJ,CACA", "x_google_ignoreList": [0]}