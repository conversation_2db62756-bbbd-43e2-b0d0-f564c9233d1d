const{SvelteComponent:g,attr:o,detach:y,element:_,flush:n,init:v,insert:m,noop:f,safe_not_equal:b,toggle_class:a}=window.__gradio__svelte__internal;function x(i){let e;return{c(){e=_("div"),e.textContent=`${i[2]}`,o(e,"class","svelte-1ayixqk"),a(e,"table",i[0]==="table"),a(e,"gallery",i[0]==="gallery"),a(e,"selected",i[1])},m(t,l){m(t,e,l)},p(t,[l]){l&1&&a(e,"table",t[0]==="table"),l&1&&a(e,"gallery",t[0]==="gallery"),l&2&&a(e,"selected",t[1])},i:f,o:f,d(t){t&&y(e)}}}function q(i,e,t){let{value:l}=e,{type:r}=e,{selected:u=!1}=e,{choices:c}=e,h=(l?Array.isArray(l)?l:[l]:[]).map(s=>c.find(d=>d[1]===s)?.[0]).filter(s=>s!==void 0).join(", ");return i.$$set=s=>{"value"in s&&t(3,l=s.value),"type"in s&&t(0,r=s.type),"selected"in s&&t(1,u=s.selected),"choices"in s&&t(4,c=s.choices)},[r,u,h,l,c]}class C extends g{constructor(e){super(),v(this,e,q,x,b,{value:3,type:0,selected:1,choices:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),n()}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),n()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),n()}get choices(){return this.$$.ctx[4]}set choices(e){this.$$set({choices:e}),n()}}export{C as default};
//# sourceMappingURL=Example-CUwox43B.js.map
