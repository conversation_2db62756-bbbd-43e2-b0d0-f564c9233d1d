import"./Index-DB1XLvMK.js";const{SvelteComponent:b,append:z,attr:r,binding_callbacks:v,create_slot:E,detach:C,element:g,flush:h,get_all_dirty_from_scope:w,get_slot_changes:B,init:R,insert:k,safe_not_equal:q,toggle_class:_,transition_in:S,transition_out:j,update_slot_base:y}=window.__gradio__svelte__internal;function A(n){let e,o,i;const u=n[5].default,l=E(u,n,n[4],null);return{c(){e=g("div"),o=g("div"),l&&l.c(),r(o,"class","icon svelte-1oiin9d"),r(e,"class","empty svelte-1oiin9d"),r(e,"aria-label","Empty value"),_(e,"small",n[0]==="small"),_(e,"large",n[0]==="large"),_(e,"unpadded_box",n[1]),_(e,"small_parent",n[3])},m(t,a){k(t,e,a),z(e,o),l&&l.m(o,null),n[6](e),i=!0},p(t,[a]){l&&l.p&&(!i||a&16)&&y(l,u,t,t[4],i?B(u,t[4],a,null):w(t[4]),null),(!i||a&1)&&_(e,"small",t[0]==="small"),(!i||a&1)&&_(e,"large",t[0]==="large"),(!i||a&2)&&_(e,"unpadded_box",t[1]),(!i||a&8)&&_(e,"small_parent",t[3])},i(t){i||(S(l,t),i=!0)},o(t){j(l,t),i=!1},d(t){t&&C(e),l&&l.d(t),n[6](null)}}}function D(n,e,o){let i,{$$slots:u={},$$scope:l}=e,{size:t="small"}=e,{unpadded_box:a=!1}=e,d;function m(s){if(!s)return!1;const{height:f}=s.getBoundingClientRect(),{height:c}=s.parentElement?.getBoundingClientRect()||{height:f};return f>c+2}function p(s){v[s?"unshift":"push"](()=>{d=s,o(2,d)})}return n.$$set=s=>{"size"in s&&o(0,t=s.size),"unpadded_box"in s&&o(1,a=s.unpadded_box),"$$scope"in s&&o(4,l=s.$$scope)},n.$$.update=()=>{n.$$.dirty&4&&o(3,i=m(d))},[t,a,d,i,l,u,p]}class G extends b{constructor(e){super(),R(this,e,D,A,q,{size:0,unpadded_box:1})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),h()}get unpadded_box(){return this.$$.ctx[1]}set unpadded_box(e){this.$$set({unpadded_box:e}),h()}}export{G as E};
//# sourceMappingURL=Empty-BgF7sXBn.js.map
