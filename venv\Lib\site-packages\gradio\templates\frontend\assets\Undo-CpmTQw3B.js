const{SvelteComponent:p,append:i,attr:t,detach:d,init:h,insert:w,noop:s,safe_not_equal:_,svg_element:l}=window.__gradio__svelte__internal;function u(a){let e,n,o;return{c(){e=l("svg"),n=l("polyline"),o=l("path"),t(n,"points","1 4 1 10 7 10"),t(o,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-rotate-ccw")},m(r,c){w(r,e,c),i(e,n),i(e,o)},p:s,i:s,o:s,d(r){r&&d(e)}}}class g extends p{constructor(e){super(),h(this,e,null,u,_,{})}}export{g as U};
//# sourceMappingURL=Undo-CpmTQw3B.js.map
