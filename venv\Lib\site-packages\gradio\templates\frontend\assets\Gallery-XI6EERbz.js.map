{"version": 3, "file": "Gallery-XI6EERbz.js", "sources": ["../../../../node_modules/.pnpm/dequal@2.0.2/node_modules/dequal/dist/index.mjs", "../../../../js/gallery/shared/utils.ts", "../../../../js/gallery/shared/Gallery.svelte"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "import { uploadToHuggingFace } from \"@gradio/utils\";\nimport type { FileData } from \"@gradio/client\";\n\nexport async function format_gallery_for_sharing(\n\tvalue: [FileData, string | null][] | null\n): Promise<string> {\n\tif (!value) return \"\";\n\tlet urls = await Promise.all(\n\t\tvalue.map(async ([image, _]) => {\n\t\t\tif (image === null || !image.url) return \"\";\n\t\t\treturn await uploadToHuggingFace(image.url, \"url\");\n\t\t})\n\t);\n\n\treturn `<div style=\"display: flex; flex-wrap: wrap; gap: 16px\">${urls\n\t\t.map((url) => `<img src=\"${url}\" style=\"height: 400px\" />`)\n\t\t.join(\"\")}</div>`;\n}\n", "<script lang=\"ts\">\n\timport { BlockLabel, Empty, ShareButton } from \"@gradio/atoms\";\n\timport { ModifyUpload } from \"@gradio/upload\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport { dequal } from \"dequal\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { tick } from \"svelte\";\n\n\timport {\n\t\tDownload,\n\t\tImage as ImageIcon,\n\t\tMaximize,\n\t\tMinimize,\n\t\tClear\n\t} from \"@gradio/icons\";\n\timport { FileData } from \"@gradio/client\";\n\timport { format_gallery_for_sharing } from \"./utils\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\ttype GalleryImage = { image: FileData; caption: string | null };\n\ttype GalleryData = GalleryImage[];\n\n\texport let show_label = true;\n\texport let label: string;\n\texport let value: GalleryData | null = null;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\texport let i18n: I18nFormatter;\n\texport let selected_index: number | null = null;\n\texport let interactive: boolean;\n\texport let _fetch: typeof fetch;\n\texport let mode: \"normal\" | \"minimal\" = \"normal\";\n\texport let show_fullscreen_button = true;\n\n\tlet is_full_screen = false;\n\tlet gallery_container: HTMLElement;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\t// tracks whether the value of the gallery was reset\n\tlet was_reset = true;\n\n\t$: was_reset = value == null || value.length === 0 ? true : was_reset;\n\n\tlet resolved_value: GalleryData | null = null;\n\t$: resolved_value =\n\t\tvalue == null\n\t\t\t? null\n\t\t\t: value.map((data) => ({\n\t\t\t\t\timage: data.image as FileData,\n\t\t\t\t\tcaption: data.caption\n\t\t\t\t}));\n\n\tlet prev_value: GalleryData | null = value;\n\tif (selected_index == null && preview && value?.length) {\n\t\tselected_index = 0;\n\t}\n\tlet old_selected_index: number | null = selected_index;\n\n\t$: if (!dequal(prev_value, value)) {\n\t\t// When value is falsy (clear button or first load),\n\t\t// preview determines the selected image\n\t\tif (was_reset) {\n\t\t\tselected_index = preview && value?.length ? 0 : null;\n\t\t\twas_reset = false;\n\t\t\t// Otherwise we keep the selected_index the same if the\n\t\t\t// gallery has at least as many elements as it did before\n\t\t} else {\n\t\t\tselected_index =\n\t\t\t\tselected_index != null && value != null && selected_index < value.length\n\t\t\t\t\t? selected_index\n\t\t\t\t\t: null;\n\t\t}\n\t\tdispatch(\"change\");\n\t\tprev_value = value;\n\t}\n\n\t$: previous =\n\t\t((selected_index ?? 0) + (resolved_value?.length ?? 0) - 1) %\n\t\t(resolved_value?.length ?? 0);\n\t$: next = ((selected_index ?? 0) + 1) % (resolved_value?.length ?? 0);\n\n\tfunction handle_preview_click(event: MouseEvent): void {\n\t\tconst element = event.target as HTMLElement;\n\t\tconst x = event.offsetX;\n\t\tconst width = element.offsetWidth;\n\t\tconst centerX = width / 2;\n\n\t\tif (x < centerX) {\n\t\t\tselected_index = previous;\n\t\t} else {\n\t\t\tselected_index = next;\n\t\t}\n\t}\n\n\tfunction on_keydown(e: KeyboardEvent): void {\n\t\tswitch (e.code) {\n\t\t\tcase \"Escape\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = null;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowLeft\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = previous;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowRight\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = next;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (selected_index !== old_selected_index) {\n\t\t\told_selected_index = selected_index;\n\t\t\tif (selected_index !== null) {\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: selected_index,\n\t\t\t\t\tvalue: resolved_value?.[selected_index]\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\t$: if (allow_preview) {\n\t\tscroll_to_img(selected_index);\n\t}\n\n\tlet el: HTMLButtonElement[] = [];\n\tlet container_element: HTMLDivElement;\n\n\tasync function scroll_to_img(index: number | null): Promise<void> {\n\t\tif (typeof index !== \"number\") return;\n\t\tawait tick();\n\n\t\tif (el[index] === undefined) return;\n\n\t\tel[index]?.focus();\n\n\t\tconst { left: container_left, width: container_width } =\n\t\t\tcontainer_element.getBoundingClientRect();\n\t\tconst { left, width } = el[index].getBoundingClientRect();\n\n\t\tconst relative_left = left - container_left;\n\n\t\tconst pos =\n\t\t\trelative_left +\n\t\t\twidth / 2 -\n\t\t\tcontainer_width / 2 +\n\t\t\tcontainer_element.scrollLeft;\n\n\t\tif (container_element && typeof container_element.scrollTo === \"function\") {\n\t\t\tcontainer_element.scrollTo({\n\t\t\t\tleft: pos < 0 ? 0 : pos,\n\t\t\t\tbehavior: \"smooth\"\n\t\t\t});\n\t\t}\n\t}\n\n\tlet window_height = 0;\n\n\t// Unlike `gr.Image()`, images specified via remote URLs are not cached in the server\n\t// and their remote URLs are directly passed to the client as `value[].image.url`.\n\t// The `download` attribute of the <a> tag doesn't work for remote URLs (https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#download),\n\t// so we need to download the image via JS as below.\n\tasync function download(file_url: string, name: string): Promise<void> {\n\t\tlet response;\n\t\ttry {\n\t\t\tresponse = await _fetch(file_url);\n\t\t} catch (error) {\n\t\t\tif (error instanceof TypeError) {\n\t\t\t\t// If CORS is not allowed (https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch#checking_that_the_fetch_was_successful),\n\t\t\t\t// open the link in a new tab instead, mimicing the behavior of the `download` attribute for remote URLs,\n\t\t\t\t// which is not ideal, but a reasonable fallback.\n\t\t\t\twindow.open(file_url, \"_blank\", \"noreferrer\");\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthrow error;\n\t\t}\n\t\tconst blob = await response.blob();\n\t\tconst url = URL.createObjectURL(blob);\n\t\tconst link = document.createElement(\"a\");\n\t\tlink.href = url;\n\t\tlink.download = name;\n\t\tlink.click();\n\t\tURL.revokeObjectURL(url);\n\t}\n\n\t$: selected_image =\n\t\tselected_index != null && resolved_value != null\n\t\t\t? resolved_value[selected_index]\n\t\t\t: null;\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t});\n\t});\n\n\tconst toggle_full_screen = async (): Promise<void> => {\n\t\tif (!is_full_screen) {\n\t\t\tawait gallery_container.requestFullscreen();\n\t\t} else {\n\t\t\tawait document.exitFullscreen();\n\t\t}\n\t};\n</script>\n\n<svelte:window bind:innerHeight={window_height} />\n\n{#if show_label}\n\t<BlockLabel {show_label} Icon={ImageIcon} label={label || \"Gallery\"} />\n{/if}\n{#if value == null || resolved_value == null || resolved_value.length === 0}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t<div class=\"gallery-container\" bind:this={gallery_container}>\n\t\t{#if selected_image && allow_preview}\n\t\t\t<button\n\t\t\t\ton:keydown={on_keydown}\n\t\t\t\tclass=\"preview\"\n\t\t\t\tclass:minimal={mode === \"minimal\"}\n\t\t\t>\n\t\t\t\t<div class=\"icon-buttons\">\n\t\t\t\t\t{#if show_download_button}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Download}\n\t\t\t\t\t\t\tlabel={i18n(\"common.download\")}\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tconst image = selected_image?.image;\n\t\t\t\t\t\t\t\tif (image == null) {\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconst { url, orig_name } = image;\n\t\t\t\t\t\t\t\tif (url) {\n\t\t\t\t\t\t\t\t\tdownload(url, orig_name ?? \"image\");\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if show_fullscreen_button && !is_full_screen}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={is_full_screen ? Minimize : Maximize}\n\t\t\t\t\t\t\tlabel={is_full_screen\n\t\t\t\t\t\t\t\t? \"Exit full screen\"\n\t\t\t\t\t\t\t\t: \"View in full screen\"}\n\t\t\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if show_fullscreen_button && is_full_screen}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Minimize}\n\t\t\t\t\t\t\tlabel=\"Exit full screen\"\n\t\t\t\t\t\t\ton:click={toggle_full_screen}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if !is_full_screen}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Clear}\n\t\t\t\t\t\t\tlabel=\"Close\"\n\t\t\t\t\t\t\ton:click={() => (selected_index = null)}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"image-button\"\n\t\t\t\t\ton:click={(event) => handle_preview_click(event)}\n\t\t\t\t\tstyle=\"height: calc(100% - {selected_image.caption\n\t\t\t\t\t\t? '80px'\n\t\t\t\t\t\t: '60px'})\"\n\t\t\t\t\taria-label=\"detailed view of selected image\"\n\t\t\t\t>\n\t\t\t\t\t<Image\n\t\t\t\t\t\tdata-testid=\"detailed-image\"\n\t\t\t\t\t\tsrc={selected_image.image.url}\n\t\t\t\t\t\talt={selected_image.caption || \"\"}\n\t\t\t\t\t\ttitle={selected_image.caption || null}\n\t\t\t\t\t\tclass={selected_image.caption && \"with-caption\"}\n\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t/>\n\t\t\t\t</button>\n\t\t\t\t{#if selected_image?.caption}\n\t\t\t\t\t<caption class=\"caption\">\n\t\t\t\t\t\t{selected_image.caption}\n\t\t\t\t\t</caption>\n\t\t\t\t{/if}\n\t\t\t\t<div\n\t\t\t\t\tbind:this={container_element}\n\t\t\t\t\tclass=\"thumbnails scroll-hide\"\n\t\t\t\t\tdata-testid=\"container_el\"\n\t\t\t\t>\n\t\t\t\t\t{#each resolved_value as image, i}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tbind:this={el[i]}\n\t\t\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-small\"\n\t\t\t\t\t\t\tclass:selected={selected_index === i && mode !== \"minimal\"}\n\t\t\t\t\t\t\taria-label={\"Thumbnail \" +\n\t\t\t\t\t\t\t\t(i + 1) +\n\t\t\t\t\t\t\t\t\" of \" +\n\t\t\t\t\t\t\t\tresolved_value.length}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\tsrc={image.image.url}\n\t\t\t\t\t\t\t\ttitle={image.caption || null}\n\t\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</button>\n\t\t{/if}\n\n\t\t<div\n\t\t\tclass=\"grid-wrap\"\n\t\t\tclass:minimal={mode === \"minimal\"}\n\t\t\tclass:fixed-height={mode !== \"minimal\" && (!height || height == \"auto\")}\n\t\t\tclass:hidden={is_full_screen}\n\t\t>\n\t\t\t<div\n\t\t\t\tclass=\"grid-container\"\n\t\t\t\tstyle=\"--grid-cols:{columns}; --grid-rows:{rows}; --object-fit: {object_fit}; height: {height};\"\n\t\t\t\tclass:pt-6={show_label}\n\t\t\t>\n\t\t\t\t{#if interactive}\n\t\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t\t<ModifyUpload\n\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\tabsolute={false}\n\t\t\t\t\t\t\ton:clear={() => (value = null)}\n\t\t\t\t\t\t/>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t{#if show_share_button}\n\t\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t\t<ShareButton\n\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\ton:share\n\t\t\t\t\t\t\ton:error\n\t\t\t\t\t\t\tvalue={resolved_value}\n\t\t\t\t\t\t\tformatter={format_gallery_for_sharing}\n\t\t\t\t\t\t/>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t{#each resolved_value as entry, i}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-lg\"\n\t\t\t\t\t\tclass:selected={selected_index === i}\n\t\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\t\taria-label={\"Thumbnail \" + (i + 1) + \" of \" + resolved_value.length}\n\t\t\t\t\t>\n\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\talt={entry.caption || \"\"}\n\t\t\t\t\t\t\tsrc={typeof entry.image === \"string\"\n\t\t\t\t\t\t\t\t? entry.image\n\t\t\t\t\t\t\t\t: entry.image.url}\n\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t{#if entry.caption}\n\t\t\t\t\t\t\t<div class=\"caption-label\">\n\t\t\t\t\t\t\t\t{entry.caption}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</button>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t</div>\n{/if}\n\n<style lang=\"postcss\">\n\t.image-container {\n\t\theight: 100%;\n\t\tposition: relative;\n\t}\n\t.image-container :global(img),\n\tbutton {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\tdisplay: block;\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.preview {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tflex-direction: column;\n\t\tz-index: var(--layer-2);\n\t\tborder-radius: calc(var(--block-radius) - var(--block-border-width));\n\t\t-webkit-backdrop-filter: blur(8px);\n\t\tbackdrop-filter: blur(8px);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.preview.minimal {\n\t\twidth: fit-content;\n\t\theight: fit-content;\n\t}\n\n\t.preview::before {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tz-index: var(--layer-below);\n\t\tbackground: var(--background-fill-primary);\n\t\topacity: 0.9;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.fixed-height {\n\t\tmin-height: var(--size-80);\n\t\tmax-height: 55vh;\n\t}\n\n\t@media (--screen-xl) {\n\t\t.fixed-height {\n\t\t\tmin-height: 450px;\n\t\t}\n\t}\n\n\t.image-button {\n\t\theight: calc(100% - 60px);\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t}\n\t.image-button :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\t.thumbnails :global(img) {\n\t\tobject-fit: cover;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.preview :global(img.with-caption) {\n\t\theight: var(--size-full);\n\t}\n\n\t.preview.minimal :global(img.with-caption) {\n\t\theight: auto;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.caption {\n\t\tpadding: var(--size-2) var(--size-3);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\ttext-align: center;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\talign-self: center;\n\t}\n\n\t.thumbnails {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t\toverflow-x: scroll;\n\t}\n\n\t.thumbnail-item {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow:\n\t\t\tinset 0 0 0 1px var(--ring-color),\n\t\t\tvar(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\taspect-ratio: var(--ratio-square);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: clip;\n\t}\n\n\t.thumbnail-item:hover {\n\t\t--ring-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t\tfilter: brightness(1.1);\n\t}\n\n\t.thumbnail-item.selected {\n\t\t--ring-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small {\n\t\tflex: none;\n\t\ttransform: scale(0.9);\n\t\ttransition: 0.075s;\n\t\twidth: var(--size-9);\n\t\theight: var(--size-9);\n\t}\n\n\t.thumbnail-small.selected {\n\t\t--ring-color: var(--color-accent);\n\t\ttransform: scale(1);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.grid-wrap {\n\t\tposition: relative;\n\t\tpadding: var(--size-2);\n\t\theight: var(--size-full);\n\t\toverflow-y: scroll;\n\t}\n\n\t.grid-container {\n\t\tdisplay: grid;\n\t\tposition: relative;\n\t\tgrid-template-rows: repeat(var(--grid-rows), minmax(100px, 1fr));\n\t\tgrid-template-columns: repeat(var(--grid-cols), minmax(100px, 1fr));\n\t\tgrid-auto-rows: minmax(100px, 1fr);\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.thumbnail-lg > :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.thumbnail-lg:hover .caption-label {\n\t\topacity: 0.5;\n\t}\n\n\t.caption-label {\n\t\tposition: absolute;\n\t\tright: var(--block-label-margin);\n\t\tbottom: var(--block-label-margin);\n\t\tz-index: var(--layer-1);\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\tborder-left: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-label-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--block-label-padding);\n\t\tmax-width: 80%;\n\t\toverflow: hidden;\n\t\tfont-size: var(--block-label-text-size);\n\t\ttext-align: left;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.icon-button {\n\t\tposition: absolute;\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tright: 0;\n\t\tgap: var(--size-1);\n\t\tz-index: 1;\n\t\tmargin: var(--size-1);\n\t}\n\n\t.grid-wrap.minimal {\n\t\tpadding: 0;\n\t}\n</style>\n"], "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp", "format_gallery_for_sharing", "value", "image", "_", "uploadToHuggingFace", "url", "onMount", "tick", "ImageIcon", "ctx", "dirty", "blocklabel_changes", "if_block0", "create_if_block_4", "create_if_block_3", "create_if_block_2", "i", "toggle_class", "div1", "insert", "target", "div2", "anchor", "append", "div0", "each_blocks", "create_if_block_9", "if_block1", "create_if_block_8", "if_block2", "create_if_block_7", "create_if_block_6", "if_block4", "create_if_block_5", "set_style", "button0", "button1", "Download", "iconbutton_changes", "Minimize", "Maximize", "Clear", "t_value", "caption", "set_data", "t", "button", "div", "if_block", "create_if_block_1", "create_if_block_10", "show_label", "$$props", "label", "columns", "rows", "height", "preview", "allow_preview", "object_fit", "show_share_button", "show_download_button", "i18n", "selected_index", "interactive", "_fetch", "mode", "show_fullscreen_button", "is_full_screen", "gallery_container", "dispatch", "createEventDispatcher", "was_reset", "resolved_value", "prev_value", "old_selected_index", "handle_preview_click", "event", "element", "x", "centerX", "$$invalidate", "previous", "next", "on_keydown", "e", "el", "container_element", "scroll_to_img", "index", "container_left", "container_width", "left", "width", "pos", "window_height", "download", "file_url", "name", "response", "error", "blob", "link", "toggle_full_screen", "selected_image", "orig_name", "click_handler_1", "$$value", "click_handler_3", "clear_handler", "click_handler_4", "data"], "mappings": "svBAAA,IAAIA,GAAM,OAAO,UAAU,eAE3B,SAASC,GAAKC,EAAMC,EAAKC,EAAK,CAC7B,IAAKA,KAAOF,EAAK,OAChB,GAAIG,EAAOD,EAAKD,CAAG,EAAG,OAAOC,CAE/B,CAEO,SAASC,EAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EAAKC,EACf,GAAIJ,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,EAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,EACf,CAED,GAAID,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EACFC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,MAAO,GAE3B,MAAO,EACP,CAED,GAAIF,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EAAI,CAAC,EACPC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACL,EAAOI,EAAI,CAAC,EAAGF,EAAI,IAAIG,CAAG,CAAC,EAC/B,MAAO,GAGT,MAAO,EACP,CAED,GAAIF,IAAS,YACZF,EAAM,IAAI,WAAWA,CAAG,EACxBC,EAAM,IAAI,WAAWA,CAAG,UACdC,IAAS,SAAU,CAC7B,IAAKC,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAI,QAAQG,CAAG,IAAMF,EAAI,QAAQE,CAAG,GAAE,CAEvD,OAAOA,IAAQ,EACf,CAED,GAAI,YAAY,OAAOH,CAAG,EAAG,CAC5B,IAAKG,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAIG,CAAG,IAAMF,EAAIE,CAAG,GAAE,CAEvC,OAAOA,IAAQ,EACf,CAED,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIN,GAAI,KAAKM,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACT,GAAI,KAAKO,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,EAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,CACnC,CACD,CAED,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B,CChFA,eAAsBI,GACrBC,EACkB,CAClB,OAAKA,EAQE,2DAPI,MAAM,QAAQ,IACxBA,EAAM,IAAI,MAAO,CAACC,EAAOC,CAAC,IACrBD,IAAU,MAAQ,CAACA,EAAM,IAAY,GAClC,MAAME,GAAoBF,EAAM,GAAU,CACjD,CAAA,GAIA,IAAKG,GAAQ,aAAaA,CAAG,4BAA4B,EACzD,KAAK,EAAE,CAAC,SAVS,EAWpB,8eCX+B,QAAAC,EAAS,EAAA,OAAgB,2BAC9C,CAAA,KAAAC,EAAA,SAAoB,yOA2NEC,GAAkB,MAAAC,MAAS,wGAATC,EAAA,CAAA,EAAA,IAAAC,EAAA,MAAAF,MAAS,sIAMpDG,EAAAH,OAAkBA,EAAa,CAAA,GAAAI,GAAAJ,CAAA,IAgH7BA,EAAW,EAAA,GAAAK,GAAAL,CAAA,IASXA,EAAiB,CAAA,GAAAM,GAAAN,CAAA,OAWfA,EAAc,EAAA,CAAA,uBAAnB,OAAIO,GAAA,yPAvBcP,EAAO,CAAA,CAAA,oBAAgBA,EAAI,CAAA,CAAA,qBAAkBA,EAAU,CAAA,CAAA,eAAYA,EAAM,CAAA,CAAA,aACjFA,EAAU,CAAA,CAAA,0CAPRQ,EAAAC,EAAA,UAAAT,QAAS,SAAS,EACbQ,EAAAC,EAAA,eAAAT,QAAS,YAAS,CAAMA,EAAU,CAAA,GAAAA,MAAU,OAAM,eACxDA,EAAc,EAAA,CAAA,2CA1G9BU,EA4JKC,EAAAC,EAAAC,CAAA,wBAtDJC,EAqDKF,EAAAH,CAAA,EA/CJK,EA8CKL,EAAAM,CAAA,wHAzJDf,OAAkBA,EAAa,CAAA,uGAgH7BA,EAAW,EAAA,oGASXA,EAAiB,CAAA,oHAWfA,EAAc,EAAA,CAAA,oBAAnB,OAAIO,GAAA,EAAA,2GAAJ,OAAIA,EAAAS,EAAA,OAAAT,GAAA,4CAvBcP,EAAO,CAAA,CAAA,mCAAgBA,EAAI,CAAA,CAAA,qCAAkBA,EAAU,CAAA,CAAA,8BAAYA,EAAM,CAAA,CAAA,2BACjFA,EAAU,CAAA,CAAA,mBAPRQ,EAAAC,EAAA,UAAAT,QAAS,SAAS,mBACbQ,EAAAC,EAAA,eAAAT,QAAS,YAAS,CAAMA,EAAU,CAAA,GAAAA,MAAU,OAAM,iCACxDA,EAAc,EAAA,CAAA,8CA2BzB,OAAIO,GAAA,6NAvIY,8SAUZP,EAAoB,EAAA,GAAAiB,GAAAjB,CAAA,EAiBpBkB,EAAAlB,QAA2BA,EAAc,EAAA,GAAAmB,GAAAnB,CAAA,EAUzCoB,EAAApB,OAA0BA,EAAc,EAAA,GAAAqB,GAAArB,CAAA,KAQvCA,EAAc,EAAA,GAAAsB,GAAAtB,CAAA,sDAkBdA,EAAc,EAAA,EAAC,MAAM,QACrBA,EAAc,EAAA,EAAC,SAAW,SACxBA,EAAc,EAAA,EAAC,SAAW,WAC1BA,EAAc,EAAA,EAAC,SAAW,iCAI9B,IAAAuB,EAAAvB,OAAgB,SAAOwB,GAAAxB,CAAA,OAUpBA,EAAc,EAAA,CAAA,uBAAnB,OAAIO,GAAA,sVAxBsBkB,EAAAC,EAAA,SAAA,gBAAA1B,EAAe,EAAA,EAAA,QACxC,OACA,QAAM,GAAA,mLAnDKQ,EAAAmB,EAAA,UAAA3B,QAAS,SAAS,UAHlCU,EAiGQC,EAAAgB,EAAAd,CAAA,EA5FPC,EA2CKa,EAAAZ,CAAA,0FACLD,EAgBQa,EAAAD,CAAA,2CAMRZ,EAyBKa,EAAAlB,CAAA,yGA/FOT,EAAU,EAAA,CAAA,iBAKhBA,EAAoB,EAAA,oGAiBpBA,QAA2BA,EAAc,EAAA,qGAUzCA,OAA0BA,EAAc,EAAA,qGAQvCA,EAAc,EAAA,2IAkBdA,EAAc,EAAA,EAAC,MAAM,0BACrBA,EAAc,EAAA,EAAC,SAAW,2BACxBA,EAAc,EAAA,EAAC,SAAW,6BAC1BA,EAAc,EAAA,EAAC,SAAW,8CAVNyB,EAAAC,EAAA,SAAA,gBAAA1B,EAAe,EAAA,EAAA,QACxC,OACA,QAAM,GAAA,EAYLA,OAAgB,iFAUbA,EAAc,EAAA,CAAA,oBAAnB,OAAIO,GAAA,EAAA,2GAAJ,OAAIA,EAAAS,EAAA,OAAAT,GAAA,4BAzEQC,EAAAmB,EAAA,UAAA3B,QAAS,SAAS,sEAyE9B,OAAIO,GAAA,+QApEEqB,GACC,MAAA5B,MAAK,iBAAiB,yFAAtBC,EAAA,CAAA,EAAA,OAAA4B,EAAA,MAAA7B,MAAK,iBAAiB,iJAgBvBA,EAAc,EAAA,EAAG8B,GAAWC,GAC3B,MAAA/B,EAAA,EAAA,EACJ,mBACA,uCACOA,EAAkB,EAAA,CAAA,qFAJtBA,EAAc,EAAA,EAAG8B,GAAWC,IAC3B9B,EAAA,CAAA,EAAA,QAAA4B,EAAA,MAAA7B,EAAA,EAAA,EACJ,mBACA,qKAOG8B,6CAEI9B,EAAkB,EAAA,CAAA,uLAMtBgC,GAAK,MAAA,OAAA,CAAA,CAAA,6KAyBXC,EAAAjC,MAAe,QAAO,qFADxBU,EAESC,EAAAuB,EAAArB,CAAA,iBADPZ,EAAA,CAAA,EAAA,SAAAgC,KAAAA,EAAAjC,MAAe,QAAO,KAAAmC,GAAAC,EAAAH,CAAA,gFAoBhBjC,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,mBACX,cAAgBA,EAAC,EAAA,EAAG,wOARtB,cACVA,EAAI,EAAA,EAAA,GACL,OACAA,EAAc,EAAA,EAAC,MAAM,EAJNQ,EAAA6B,EAAA,WAAArC,EAAmB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAS,SAAS,UAJ3DU,EAiBQC,EAAA0B,EAAAxB,CAAA,mGANDb,EAAK,EAAA,EAAC,MAAM,0BACVA,EAAK,EAAA,EAAC,SAAW,wCAPb,cACVA,EAAI,EAAA,EAAA,GACL,OACAA,EAAc,EAAA,EAAC,4EAJAQ,EAAA6B,EAAA,WAAArC,EAAmB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAS,SAAS,0KAkChD,EAAK,4GAHjBU,EAMKC,EAAA2B,EAAAzB,CAAA,kOAQIb,EAAc,EAAA,YACVT,mIANbmB,EAQKC,EAAA2B,EAAAzB,CAAA,qFAHIb,EAAc,EAAA,wHAqBnBiC,EAAAjC,MAAM,QAAO,uFADfU,EAEKC,EAAA2B,EAAAzB,CAAA,iBADHZ,EAAA,CAAA,EAAA,OAAAgC,KAAAA,EAAAjC,MAAM,QAAO,KAAAmC,GAAAC,EAAAH,CAAA,0EARVjC,EAAK,EAAA,EAAC,SAAW,cACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,sBAGX,IAAAuC,EAAAvC,MAAM,SAAOwC,GAAAxC,CAAA,+KATN,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,MAAM,EAFnDQ,EAAA6B,EAAA,WAAArC,OAAmBA,EAAC,EAAA,CAAA,UAFrCU,EAkBQC,EAAA0B,EAAAxB,CAAA,qHAXDb,EAAK,EAAA,EAAC,SAAW,8BACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,eAGXA,MAAM,uFATC,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,4CAF7CQ,EAAA6B,EAAA,WAAArC,OAAmBA,EAAC,EAAA,CAAA,kUA9IrCA,EAAU,CAAA,GAAAyC,GAAAzC,CAAA,8CAGVA,EAAK,CAAA,GAAI,MAAQA,EAAc,EAAA,GAAI,MAAQA,EAAc,EAAA,EAAC,SAAW,EAAC,kKAHtEA,EAAU,CAAA,uXAzMH,CAAA,WAAA0C,EAAa,EAAI,EAAAC,GACjB,MAAAC,CAAa,EAAAD,EACb,CAAA,MAAAnD,EAA4B,IAAI,EAAAmD,EAChC,CAAA,QAAAE,GAA0C,CAAC,CAAA,EAAAF,EAC3C,CAAA,KAAAG,EAAsC,MAAS,EAAAH,EAC/C,CAAA,OAAAI,EAA0B,MAAM,EAAAJ,GAChC,QAAAK,CAAgB,EAAAL,EAChB,CAAA,cAAAM,EAAgB,EAAI,EAAAN,EACpB,CAAA,WAAAO,EACV,OAAO,EAAAP,EACG,CAAA,kBAAAQ,EAAoB,EAAK,EAAAR,EACzB,CAAA,qBAAAS,EAAuB,EAAK,EAAAT,GAC5B,KAAAU,CAAmB,EAAAV,EACnB,CAAA,eAAAW,EAAgC,IAAI,EAAAX,GACpC,YAAAY,CAAoB,EAAAZ,GACpB,OAAAa,CAAoB,EAAAb,EACpB,CAAA,KAAAc,EAA6B,QAAQ,EAAAd,EACrC,CAAA,uBAAAe,EAAyB,EAAI,EAAAf,EAEpCgB,EAAiB,GACjBC,EAEE,MAAAC,EAAWC,KAMb,IAAAC,EAAY,GAIZC,EAAqC,KASrCC,GAAiCzE,EACjC8D,GAAkB,MAAQN,GAAWxD,GAAO,SAC/C8D,EAAiB,GAEd,IAAAY,GAAoCZ,EAyB/B,SAAAa,GAAqBC,EAAiB,OACxCC,EAAUD,EAAM,OAChBE,EAAIF,EAAM,QAEVG,EADQF,EAAQ,YACE,EAEpBC,EAAIC,EACPC,EAAA,EAAAlB,EAAiBmB,CAAQ,EAEzBD,EAAA,EAAAlB,EAAiBoB,CAAI,EAId,SAAAC,GAAWC,EAAgB,CAC3B,OAAAA,EAAE,KAAI,KACR,SACJA,EAAE,eAAc,EAChBJ,EAAA,EAAAlB,EAAiB,IAAI,YAEjB,YACJsB,EAAE,eAAc,EAChBJ,EAAA,EAAAlB,EAAiBmB,CAAQ,YAErB,aACJG,EAAE,eAAc,EAChBJ,EAAA,EAAAlB,EAAiBoB,CAAI,aAuBpBG,EAAE,CAAA,EACFC,EAEW,eAAAC,GAAcC,EAAoB,IACrC,OAAAA,GAAU,iBACflF,GAAI,EAEN+E,EAAGG,CAAK,IAAM,QAAS,OAE3BH,EAAGG,CAAK,GAAG,cAEH,KAAMC,EAAgB,MAAOC,GACpCJ,EAAkB,wBACX,CAAA,KAAAK,EAAM,MAAAC,CAAK,EAAKP,EAAGG,CAAK,EAAE,wBAI5BK,EAFgBF,EAAOF,EAI5BG,EAAQ,EACRF,EAAkB,EAClBJ,EAAkB,WAEfA,GAA4B,OAAAA,EAAkB,UAAa,YAC9DA,EAAkB,SAAQ,CACzB,KAAMO,EAAM,EAAI,EAAIA,EACpB,SAAU,WAKT,IAAAC,GAAgB,iBAMLC,GAASC,EAAkBC,EAAY,KACjDC,MAEHA,EAAQ,MAASlC,EAAOgC,CAAQ,QACxBG,EAAK,CACT,GAAAA,aAAiB,UAAS,CAI7B,OAAO,KAAKH,EAAU,SAAU,YAAY,eAIvCG,QAEDC,EAAI,MAASF,EAAS,OACtB9F,EAAM,IAAI,gBAAgBgG,CAAI,EAC9BC,GAAO,SAAS,cAAc,GAAG,EACvCA,GAAK,KAAOjG,EACZiG,GAAK,SAAWJ,EAChBI,GAAK,MAAK,EACV,IAAI,gBAAgBjG,CAAG,EAQxBC,GAAO,IAAA,CACN,SAAS,iBAAiB,mBAAkB,IAAA,MAC3C8D,EAAc,CAAA,CAAK,SAAS,iBAAiB,YAIzCmC,GAAkB,SAAA,CAClBnC,EAGE,MAAA,SAAS,iBAFT,MAAAC,EAAkB,+EA4BbnE,EAAQsG,GAAgB,MAC1B,GAAAtG,GAAS,kBAGL,IAAAG,EAAK,UAAAoG,CAAS,EAAKvG,EACvBG,GACH2F,GAAS3F,EAAKoG,GAAa,OAAO,GA4BnBC,GAAA,IAAAzB,EAAA,EAAAlB,EAAiB,IAAI,KAM7Bc,GAAUD,GAAqBC,CAAK,+CA2BlCS,EAAGtE,CAAC,EAAA2F,YACE,MAAAC,GAAA5F,GAAAiE,EAAA,EAAAlB,EAAiB/C,CAAC,6CAP1BuE,EAAiBoB,YA4CT,MAAAE,GAAA,IAAA5B,EAAA,EAAAhF,EAAQ,IAAI,oEAmBb,MAAA6G,GAAA9F,GAAAiE,EAAA,EAAAlB,EAAiB/C,CAAC,6CAzIEqD,EAAiBsC,6sBAjLxD1B,EAAA,GAAAT,EAAYvE,GAAS,MAAQA,EAAM,SAAW,EAAI,GAAOuE,CAAS,mBAGlES,EAAA,GAAAR,EACFxE,GAAS,KACN,KACAA,EAAM,IAAK8G,KACX,MAAOA,EAAK,MACZ,QAASA,EAAK,OAAA,EAAA,CAAA,4BASVrH,EAAOgF,GAAYzE,CAAK,IAG3BuE,OACHT,EAAiBN,GAAWxD,GAAO,OAAS,EAAI,IAAI,EACpDgF,EAAA,GAAAT,EAAY,EAAK,OAIjBT,EACCA,GAAkB,MAAQ9D,GAAS,MAAQ8D,EAAiB9D,EAAM,OAC/D8D,EACA,MAELO,EAAS,QAAQ,EACjBW,EAAA,GAAAP,GAAazE,CAAK,0BAGhBiF,IACAnB,GAAkB,IAAMU,GAAgB,QAAU,GAAK,IACxDA,GAAgB,QAAU,0BACzBU,IAASpB,GAAkB,GAAK,IAAMU,GAAgB,QAAU,8BAmC9DV,IAAmBY,KACtBM,EAAA,GAAAN,GAAqBZ,CAAc,EAC/BA,IAAmB,MACtBO,EAAS,SAAQ,CAChB,MAAOP,EACP,MAAOU,IAAiBV,CAAc,wBAMnCL,GACN8B,GAAczB,CAAc,uBAgE5BkB,EAAA,GAAEuB,EACFzC,GAAkB,MAAQU,GAAkB,KACzCA,EAAeV,CAAc,EAC7B,IAAI", "x_google_ignoreList": [0]}