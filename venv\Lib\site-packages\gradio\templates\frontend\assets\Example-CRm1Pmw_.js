const{SvelteComponent:g,attr:o,detach:_,element:m,flush:c,init:y,insert:v,noop:h,safe_not_equal:b,toggle_class:n}=window.__gradio__svelte__internal;function x(i){let e;return{c(){e=m("div"),e.textContent=`${i[2]}`,o(e,"class","svelte-1ayixqk"),n(e,"table",i[0]==="table"),n(e,"gallery",i[0]==="gallery"),n(e,"selected",i[1])},m(t,s){v(t,e,s)},p(t,[s]){s&1&&n(e,"table",t[0]==="table"),s&1&&n(e,"gallery",t[0]==="gallery"),s&2&&n(e,"selected",t[1])},i:h,o:h,d(t){t&&_(e)}}}function q(i,e,t){let{value:s}=e,{type:u}=e,{selected:f=!1}=e,{choices:a}=e,r=s.map(l=>a.find(d=>d[1]===l)?.[0]).filter(l=>l!==void 0).join(", ");return i.$$set=l=>{"value"in l&&t(3,s=l.value),"type"in l&&t(0,u=l.type),"selected"in l&&t(1,f=l.selected),"choices"in l&&t(4,a=l.choices)},[u,f,r,s,a]}class C extends g{constructor(e){super(),y(this,e,q,x,b,{value:3,type:0,selected:1,choices:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),c()}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),c()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),c()}get choices(){return this.$$.ctx[4]}set choices(e){this.$$set({choices:e}),c()}}export{C as default};
//# sourceMappingURL=Example-CRm1Pmw_.js.map
