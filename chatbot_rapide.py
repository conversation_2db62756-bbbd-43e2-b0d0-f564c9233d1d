#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Enhanced News Chatbot - VERSION RAPIDE OPTIMISÉE
Chatbot optimisé pour des réponses ultra-rapides avec Qwen2
"""

import os
import sys
import time
import logging
import gradio as gr
from datetime import datetime

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

print("🚀 Enhanced News Chatbot - VERSION RAPIDE")
print("=" * 60)

# ============================================================================
# 📦 IMPORTS OPTIMISÉS
# ============================================================================

try:
    import torch
    import feedparser
    import requests
    from bs4 import BeautifulSoup
    from sentence_transformers import SentenceTransformer
    from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
    import faiss
    import numpy as np
    print("✅ Imports terminés")
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    sys.exit(1)

# ============================================================================
# ⚙️ CONFIGURATION RAPIDE
# ============================================================================

class FastConfig:
    """Configuration optimisée pour la vitesse"""
    def __init__(self):
        # Sources RSS limitées pour la rapidité
        self.RSS_FEEDS = {
            'technology': [
                'https://feeds.bbci.co.uk/news/technology/rss.xml',
                'https://techcrunch.com/feed/'
            ],
            'world': [
                'https://feeds.bbci.co.uk/news/world/rss.xml'
            ],
            'business': [
                'https://feeds.bbci.co.uk/news/business/rss.xml'
            ]
        }
        
        # Paramètres optimisés pour la vitesse
        self.MAX_ARTICLES = 20        # Très limité
        self.CHUNK_SIZE = 200         # Petits chunks
        self.MAX_NEW_TOKENS = 100     # Réponses courtes
        self.TEMPERATURE = 0.2        # Plus déterministe
        self.TOP_K = 3                # Moins de documents
        
        # Modèles
        self.EMBEDDING_MODEL = 'all-MiniLM-L6-v2'
        self.LLM_MODEL = 'Qwen/Qwen2-1.5B-Instruct'

config = FastConfig()
print("✅ Configuration rapide initialisée")

# ============================================================================
# 📡 COLLECTEUR RAPIDE
# ============================================================================

class FastNewsCollector:
    """Collecteur de news optimisé pour la vitesse"""
    
    def __init__(self, config):
        self.config = config
        self.articles = []
    
    def collect_fast(self):
        """Collecte rapide des actualités"""
        print("📡 Collecte rapide des actualités...")
        start_time = time.time()
        
        all_articles = []
        
        for category, feeds in self.config.RSS_FEEDS.items():
            for feed_url in feeds[:1]:  # Une seule source par catégorie
                try:
                    feed = feedparser.parse(feed_url)
                    for entry in feed.entries[:5]:  # Seulement 5 articles par source
                        article = {
                            'title': entry.get('title', ''),
                            'description': entry.get('summary', ''),
                            'link': entry.get('link', ''),
                            'category': category,
                            'source': feed.feed.get('title', 'Unknown')
                        }
                        all_articles.append(article)
                except Exception as e:
                    logging.warning(f"Erreur RSS {feed_url}: {e}")
        
        self.articles = all_articles[:self.config.MAX_ARTICLES]
        
        elapsed = time.time() - start_time
        print(f"✅ Collecté {len(self.articles)} articles en {elapsed:.1f}s")
        return self.articles

collector = FastNewsCollector(config)

# ============================================================================
# 🤖 CHATBOT RAPIDE
# ============================================================================

class FastChatbot:
    """Chatbot optimisé pour la vitesse"""
    
    def __init__(self, config):
        self.config = config
        self.articles = []
        self.embeddings = None
        self.index = None
        self.chunks = []
        self.llm = None
        
        self._load_models()
    
    def _load_models(self):
        """Charge les modèles optimisés"""
        print("🧠 Chargement des modèles optimisés...")
        
        # Modèle d'embeddings
        self.embeddings = SentenceTransformer(self.config.EMBEDDING_MODEL)
        print(f"✅ Embeddings chargés: {self.config.EMBEDDING_MODEL}")
        
        # Modèle de génération optimisé
        try:
            tokenizer = AutoTokenizer.from_pretrained(self.config.LLM_MODEL, trust_remote_code=True)
            model = AutoModelForCausalLM.from_pretrained(
                self.config.LLM_MODEL,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            self.llm = pipeline(
                "text-generation",
                model=model,
                tokenizer=tokenizer,
                max_new_tokens=self.config.MAX_NEW_TOKENS,
                temperature=self.config.TEMPERATURE,
                do_sample=True,
                device=0 if torch.cuda.is_available() else -1,
                pad_token_id=tokenizer.eos_token_id,
                top_k=50,
                top_p=0.9
            )
            print(f"✅ LLM chargé: {self.config.LLM_MODEL}")
            
        except Exception as e:
            print(f"❌ Erreur LLM: {e}")
            self.llm = None
    
    def build_index(self, articles):
        """Construit l'index de recherche rapide"""
        print("🔍 Construction de l'index rapide...")
        start_time = time.time()
        
        self.articles = articles
        self.chunks = []
        
        # Créer des chunks simples
        for article in articles:
            text = f"{article['title']}. {article['description']}"
            if len(text) > self.config.CHUNK_SIZE:
                text = text[:self.config.CHUNK_SIZE]
            
            self.chunks.append({
                'text': text,
                'title': article['title'],
                'source': article['source'],
                'link': article['link'],
                'category': article['category']
            })
        
        # Créer l'index FAISS
        if self.chunks:
            texts = [chunk['text'] for chunk in self.chunks]
            embeddings_matrix = self.embeddings.encode(texts)
            
            # Index FAISS simple
            dimension = embeddings_matrix.shape[1]
            self.index = faiss.IndexFlatL2(dimension)
            self.index.add(embeddings_matrix.astype('float32'))
        
        elapsed = time.time() - start_time
        print(f"✅ Index construit avec {len(self.chunks)} chunks en {elapsed:.1f}s")
    
    def search(self, question, k=3):
        """Recherche rapide"""
        if not self.index or not self.chunks:
            return []
        
        # Encoder la question
        question_embedding = self.embeddings.encode([question])
        
        # Recherche
        distances, indices = self.index.search(question_embedding.astype('float32'), k)
        
        results = []
        for i, idx in enumerate(indices[0]):
            if idx < len(self.chunks):
                chunk = self.chunks[idx]
                chunk['score'] = float(distances[0][i])
                results.append(chunk)
        
        return results
    
    def generate_answer(self, question, context_chunks):
        """Génère une réponse rapide"""
        if not self.llm:
            return "❌ Modèle de génération non disponible"
        
        # Créer le contexte
        context = "\n".join([f"- {chunk['text']}" for chunk in context_chunks[:3]])
        
        # Prompt optimisé pour la vitesse
        prompt = f"""Réponds brièvement en français basé sur ce contexte:

CONTEXTE:
{context}

QUESTION: {question}

RÉPONSE (max 80 mots):"""
        
        try:
            result = self.llm(prompt, max_new_tokens=self.config.MAX_NEW_TOKENS)
            answer = result[0]['generated_text'].replace(prompt, '').strip()
            return answer
        except Exception as e:
            return f"❌ Erreur génération: {str(e)}"
    
    def query(self, question):
        """Traite une question rapidement"""
        start_time = time.time()
        
        # Recherche
        results = self.search(question, k=self.config.TOP_K)
        
        if not results:
            return "❌ Aucun article trouvé pour cette question."
        
        # Génération
        answer = self.generate_answer(question, results)
        
        # Sources
        sources = []
        for result in results[:2]:
            sources.append(f"📰 **{result['title']}** ({result['source']})")
        
        elapsed = time.time() - start_time
        
        response = f"{answer}\n\n**Sources:**\n" + "\n".join(sources)
        response += f"\n\n⏱️ *Réponse en {elapsed:.1f}s*"
        
        return response

chatbot = FastChatbot(config)

# ============================================================================
# 🎨 INTERFACE RAPIDE
# ============================================================================

def chat_response(message, history):
    """Réponse de chat optimisée"""
    if not message.strip():
        return "Veuillez poser une question."
    
    return chatbot.query(message)

def start_system():
    """Démarre le système rapide"""
    try:
        # Collecter les articles
        articles = collector.collect_fast()
        
        # Construire l'index
        chatbot.build_index(articles)
        
        return f"✅ Système démarré avec {len(articles)} articles!"
    except Exception as e:
        return f"❌ Erreur: {str(e)}"

# Interface Gradio simplifiée
with gr.Blocks(title="🚀 Fast News Chatbot", theme=gr.themes.Soft()) as interface:
    gr.Markdown("""
    # 🚀 Fast News Chatbot avec Qwen2
    
    **Version optimisée pour des réponses ultra-rapides !**
    
    1. Cliquez sur "Démarrer" ci-dessous
    2. Posez vos questions sur l'actualité
    """)
    
    start_btn = gr.Button("🚀 Démarrer le Système Rapide", variant="primary")
    status_output = gr.Textbox(label="Statut", interactive=False)
    
    start_btn.click(start_system, outputs=status_output)
    
    gr.Markdown("## 💬 Chat")
    
    chat_interface = gr.ChatInterface(
        chat_response,
        title="Posez vos questions sur l'actualité",
        examples=[
            "Quelles sont les dernières nouvelles en technologie ?",
            "Que se passe-t-il dans le monde ?",
            "Résume-moi l'actualité business"
        ]
    )

if __name__ == "__main__":
    print("\n🚀 Lancement du chatbot rapide...")
    interface.launch(
        share=False,  # Pas de lien public pour plus de rapidité
        server_name="0.0.0.0",
        server_port=7861,  # Port différent
        show_error=True
    )
