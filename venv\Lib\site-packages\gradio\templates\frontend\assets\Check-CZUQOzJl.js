const{SvelteComponent:a,append:c,attr:t,detach:p,init:d,insert:u,noop:r,safe_not_equal:_,svg_element:i}=window.__gradio__svelte__internal;function w(s){let e,n;return{c(){e=i("svg"),n=i("polyline"),t(n,"points","20 6 9 17 4 12"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","2 0 20 20"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"aria-hidden","true"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round")},m(o,l){u(o,e,l),c(e,n)},p:r,i:r,o:r,d(o){o&&p(e)}}}class g extends a{constructor(e){super(),d(this,e,null,w,_,{})}}export{g as C};
//# sourceMappingURL=Check-CZUQOzJl.js.map
