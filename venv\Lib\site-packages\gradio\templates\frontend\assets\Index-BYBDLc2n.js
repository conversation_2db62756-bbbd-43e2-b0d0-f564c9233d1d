import{B as ze}from"./Button-BIUaXfcG.js";import{S as Ie,I as ae}from"./Index-DB1XLvMK.js";import{B as Me}from"./BlockLabel-BlSr62f_.js";import{E as Be}from"./Empty-BgF7sXBn.js";import{M as Ee,a as qe}from"./Minimize-X5PPawdt.js";import{I as oe}from"./Image-Bsh8Umrh.js";import"./index-BQPjLIsY.js";import{r as $}from"./file-url-SIRImsEF.js";import"./svelte/svelte.js";const{SvelteComponent:Se,append:S,assign:Fe,attr:k,binding_callbacks:Le,check_outros:U,create_component:P,destroy_component:j,destroy_each:ue,detach:B,element:L,empty:Pe,ensure_array_like:H,flush:w,get_spread_object:je,get_spread_update:Ce,group_outros:W,init:Ne,insert:E,listen:A,mount_component:C,noop:re,run_all:Ve,safe_not_equal:Ae,set_data:De,set_style:y,space:F,src_url_equal:J,text:Ge,toggle_class:q,transition_in:z,transition_out:I}=window.__gradio__svelte__internal,{onMount:He}=window.__gradio__svelte__internal;function x(t,e,n){const l=t.slice();return l[32]=e[n],l[34]=n,l}function ee(t,e,n){const l=t.slice();return l[32]=e[n],l[34]=n,l}function Je(t){let e,n,l,s,i,a,h,v,b,d,m=!t[17]&&t[14]&&le(t),_=t[17]&&te(t),M=H(t[15]?t[15]?.annotations:[]),o=[];for(let r=0;r<M.length;r+=1)o[r]=ne(ee(t,M,r));let f=t[6]&&t[15]&&ie(t);return{c(){e=L("div"),n=L("div"),m&&m.c(),l=F(),_&&_.c(),s=F(),i=L("img"),h=F();for(let r=0;r<o.length;r+=1)o[r].c();v=F(),f&&f.c(),b=Pe(),k(n,"class","icon-buttons svelte-e56hdz"),k(i,"class","base-image svelte-e56hdz"),J(i.src,a=t[15]?t[15].image.url:null)||k(i,"src",a),k(i,"alt","the base file that is annotated"),q(i,"fit-height",t[7]&&!t[17]),k(e,"class","image-container svelte-e56hdz")},m(r,g){E(r,e,g),S(e,n),m&&m.m(n,null),S(n,l),_&&_.m(n,null),S(e,s),S(e,i),S(e,h);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(e,null);t[26](e),E(r,v,g),f&&f.m(r,g),E(r,b,g),d=!0},p(r,g){if(!r[17]&&r[14]?m?(m.p(r,g),g[0]&147456&&z(m,1)):(m=le(r),m.c(),z(m,1),m.m(n,l)):m&&(W(),I(m,1,1,()=>{m=null}),U()),r[17]?_?(_.p(r,g),g[0]&131072&&z(_,1)):(_=te(r),_.c(),z(_,1),_.m(n,null)):_&&(W(),I(_,1,1,()=>{_=null}),U()),(!d||g[0]&32768&&!J(i.src,a=r[15]?r[15].image.url:null))&&k(i,"src",a),(!d||g[0]&131200)&&q(i,"fit-height",r[7]&&!r[17]),g[0]&229904){M=H(r[15]?r[15]?.annotations:[]);let c;for(c=0;c<M.length;c+=1){const N=ee(r,M,c);o[c]?o[c].p(N,g):(o[c]=ne(N),o[c].c(),o[c].m(e,null))}for(;c<o.length;c+=1)o[c].d(1);o.length=M.length}r[6]&&r[15]?f?f.p(r,g):(f=ie(r),f.c(),f.m(b.parentNode,b)):f&&(f.d(1),f=null)},i(r){d||(z(m),z(_),d=!0)},o(r){I(m),I(_),d=!1},d(r){r&&(B(e),B(v),B(b)),m&&m.d(),_&&_.d(),ue(o,r),t[26](null),f&&f.d(r)}}}function Ke(t){let e,n;return e=new Be({props:{size:"large",unpadded_box:!0,$$slots:{default:[Oe]},$$scope:{ctx:t}}}),{c(){P(e.$$.fragment)},m(l,s){C(e,l,s),n=!0},p(l,s){const i={};s[1]&32&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){n||(z(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){j(e,l)}}}function le(t){let e,n;return e=new ae({props:{Icon:Ee,label:"View in full screen"}}),e.$on("click",t[19]),{c(){P(e.$$.fragment)},m(l,s){C(e,l,s),n=!0},p:re,i(l){n||(z(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){j(e,l)}}}function te(t){let e,n;return e=new ae({props:{Icon:qe,label:"Exit full screen"}}),e.$on("click",t[19]),{c(){P(e.$$.fragment)},m(l,s){C(e,l,s),n=!0},p:re,i(l){n||(z(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){j(e,l)}}}function ne(t){let e,n,l,s;return{c(){e=L("img"),k(e,"alt",n="segmentation mask identifying "+t[4]+" within the uploaded file"),k(e,"class","mask fit-height svelte-e56hdz"),J(e.src,l=t[32].image.url)||k(e,"src",l),k(e,"style",s=t[9]&&t[32].label in t[9]?null:`filter: hue-rotate(${Math.round(t[34]*360/t[15]?.annotations.length)}deg);`),q(e,"fit-height",!t[17]),q(e,"active",t[16]==t[32].label),q(e,"inactive",t[16]!=t[32].label&&t[16]!=null)},m(i,a){E(i,e,a)},p(i,a){a[0]&16&&n!==(n="segmentation mask identifying "+i[4]+" within the uploaded file")&&k(e,"alt",n),a[0]&32768&&!J(e.src,l=i[32].image.url)&&k(e,"src",l),a[0]&33280&&s!==(s=i[9]&&i[32].label in i[9]?null:`filter: hue-rotate(${Math.round(i[34]*360/i[15]?.annotations.length)}deg);`)&&k(e,"style",s),a[0]&131072&&q(e,"fit-height",!i[17]),a[0]&98304&&q(e,"active",i[16]==i[32].label),a[0]&98304&&q(e,"inactive",i[16]!=i[32].label&&i[16]!=null)},d(i){i&&B(e)}}}function ie(t){let e,n=H(t[15].annotations),l=[];for(let s=0;s<n.length;s+=1)l[s]=se(x(t,n,s));return{c(){e=L("div");for(let s=0;s<l.length;s+=1)l[s].c();k(e,"class","legend svelte-e56hdz")},m(s,i){E(s,e,i);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(e,null)},p(s,i){if(i[0]&7373312){n=H(s[15].annotations);let a;for(a=0;a<n.length;a+=1){const h=x(s,n,a);l[a]?l[a].p(h,i):(l[a]=se(h),l[a].c(),l[a].m(e,null))}for(;a<l.length;a+=1)l[a].d(1);l.length=n.length}},d(s){s&&B(e),ue(l,s)}}}function se(t){let e,n=t[32].label+"",l,s,i,a;function h(){return t[27](t[32])}function v(){return t[28](t[32])}function b(){return t[31](t[34],t[32])}return{c(){e=L("button"),l=Ge(n),s=F(),k(e,"class","legend-item svelte-e56hdz"),y(e,"background-color",t[9]&&t[32].label in t[9]?t[9][t[32].label]+"88":`hsla(${Math.round(t[34]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},m(d,m){E(d,e,m),S(e,l),S(e,s),i||(a=[A(e,"mouseover",h),A(e,"focus",v),A(e,"mouseout",t[29]),A(e,"blur",t[30]),A(e,"click",b)],i=!0)},p(d,m){t=d,m[0]&32768&&n!==(n=t[32].label+"")&&De(l,n),m[0]&33280&&y(e,"background-color",t[9]&&t[32].label in t[9]?t[9][t[32].label]+"88":`hsla(${Math.round(t[34]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},d(d){d&&B(e),i=!1,Ve(a)}}}function Oe(t){let e,n;return e=new oe({}),{c(){P(e.$$.fragment)},m(l,s){C(e,l,s),n=!0},i(l){n||(z(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){j(e,l)}}}function Qe(t){let e,n,l,s,i,a,h,v;const b=[{autoscroll:t[3].autoscroll},{i18n:t[3].i18n},t[13]];let d={};for(let o=0;o<b.length;o+=1)d=Fe(d,b[o]);e=new Ie({props:d}),l=new Me({props:{show_label:t[5],Icon:oe,label:t[4]||t[3].i18n("image.image")}});const m=[Ke,Je],_=[];function M(o,f){return o[15]==null?0:1}return a=M(t),h=_[a]=m[a](t),{c(){P(e.$$.fragment),n=F(),P(l.$$.fragment),s=F(),i=L("div"),h.c(),k(i,"class","container svelte-e56hdz")},m(o,f){C(e,o,f),E(o,n,f),C(l,o,f),E(o,s,f),E(o,i,f),_[a].m(i,null),v=!0},p(o,f){const r=f[0]&8200?Ce(b,[f[0]&8&&{autoscroll:o[3].autoscroll},f[0]&8&&{i18n:o[3].i18n},f[0]&8192&&je(o[13])]):{};e.$set(r);const g={};f[0]&32&&(g.show_label=o[5]),f[0]&24&&(g.label=o[4]||o[3].i18n("image.image")),l.$set(g);let c=a;a=M(o),a===c?_[a].p(o,f):(W(),I(_[c],1,1,()=>{_[c]=null}),U(),h=_[a],h?h.p(o,f):(h=_[a]=m[a](o),h.c()),z(h,1),h.m(i,null))},i(o){v||(z(e.$$.fragment,o),z(l.$$.fragment,o),z(h),v=!0)},o(o){I(e.$$.fragment,o),I(l.$$.fragment,o),I(h),v=!1},d(o){o&&(B(n),B(s),B(i)),j(e,o),j(l,o),_[a].d()}}}function Re(t){let e,n;return e=new ze({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],padding:!1,height:t[7],width:t[8],allow_overflow:!1,container:t[10],scale:t[11],min_width:t[12],$$slots:{default:[Qe]},$$scope:{ctx:t}}}),{c(){P(e.$$.fragment)},m(l,s){C(e,l,s),n=!0},p(l,s){const i={};s[0]&4&&(i.visible=l[2]),s[0]&1&&(i.elem_id=l[0]),s[0]&2&&(i.elem_classes=l[1]),s[0]&128&&(i.height=l[7]),s[0]&256&&(i.width=l[8]),s[0]&1024&&(i.container=l[10]),s[0]&2048&&(i.scale=l[11]),s[0]&4096&&(i.min_width=l[12]),s[0]&516856|s[1]&32&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){n||(z(e.$$.fragment,l),n=!0)},o(l){I(e.$$.fragment,l),n=!1},d(l){j(e,l)}}}function Te(t,e,n){let{elem_id:l=""}=e,{elem_classes:s=[]}=e,{visible:i=!0}=e,{value:a=null}=e,h=null,v=null,{gradio:b}=e,{label:d=b.i18n("annotated_image.annotated_image")}=e,{show_label:m=!0}=e,{show_legend:_=!0}=e,{height:M}=e,{width:o}=e,{color_map:f}=e,{container:r=!0}=e,{scale:g=null}=e,{min_width:c=void 0}=e,N=null,{loading_status:X}=e,{show_fullscreen_button:Y=!0}=e,K=!1,D;He(()=>{document.addEventListener("fullscreenchange",()=>{n(17,K=!!document.fullscreenElement)})});const fe=async()=>{K?await document.exitFullscreen():await D.requestFullscreen()};let O=null;function Q(u){n(16,N=u)}function R(){n(16,N=null)}function Z(u,G){b.dispatch("select",{value:d,index:u})}function _e(u){Le[u?"unshift":"push"](()=>{D=u,n(18,D)})}const me=u=>Q(u.label),ce=u=>Q(u.label),he=()=>R(),ge=()=>R(),de=(u,G)=>Z(u,G.label);return t.$$set=u=>{"elem_id"in u&&n(0,l=u.elem_id),"elem_classes"in u&&n(1,s=u.elem_classes),"visible"in u&&n(2,i=u.visible),"value"in u&&n(23,a=u.value),"gradio"in u&&n(3,b=u.gradio),"label"in u&&n(4,d=u.label),"show_label"in u&&n(5,m=u.show_label),"show_legend"in u&&n(6,_=u.show_legend),"height"in u&&n(7,M=u.height),"width"in u&&n(8,o=u.width),"color_map"in u&&n(9,f=u.color_map),"container"in u&&n(10,r=u.container),"scale"in u&&n(11,g=u.scale),"min_width"in u&&n(12,c=u.min_width),"loading_status"in u&&n(13,X=u.loading_status),"show_fullscreen_button"in u&&n(14,Y=u.show_fullscreen_button)},t.$$.update=()=>{if(t.$$.dirty[0]&58720264)if(a!==h&&(n(24,h=a),b.dispatch("change")),a){const u={image:a.image,annotations:a.annotations.map(V=>({image:V.image,label:V.label}))};n(15,v=u);const G=$(u.image.url),be=Promise.all(u.annotations.map(V=>$(V.image.url))),T=Promise.all([G,be]);n(25,O=T),T.then(([V,we])=>{if(O!==T)return;const ke={image:{...u.image,url:V??void 0},annotations:u.annotations.map((p,ve)=>({...p,image:{...p.image,url:we[ve]??void 0}}))};n(15,v=ke)})}else n(15,v=null)},[l,s,i,b,d,m,_,M,o,f,r,g,c,X,Y,v,N,K,D,fe,Q,R,Z,a,h,O,_e,me,ce,he,ge,de]}class el extends Se{constructor(e){super(),Ne(this,e,Te,Re,Ae,{elem_id:0,elem_classes:1,visible:2,value:23,gradio:3,label:4,show_label:5,show_legend:6,height:7,width:8,color_map:9,container:10,scale:11,min_width:12,loading_status:13,show_fullscreen_button:14},null,[-1,-1])}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[23]}set value(e){this.$$set({value:e}),w()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),w()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),w()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),w()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),w()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),w()}get width(){return this.$$.ctx[8]}set width(e){this.$$set({width:e}),w()}get color_map(){return this.$$.ctx[9]}set color_map(e){this.$$set({color_map:e}),w()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),w()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),w()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),w()}}export{el as default};
//# sourceMappingURL=Index-BYBDLc2n.js.map
