2025-10-24 18:17:39,675 - INFO - Use pytorch device_name: cpu
2025-10-24 18:17:39,675 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-10-24 18:17:43,851 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-10-24 18:17:54,901 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-10-24 18:39:13,614 - INFO - HTTP Request: GET https://checkip.amazonaws.com/ "HTTP/1.1 200 "
2025-10-24 18:39:17,551 - INFO - HTTP Request: GET https://checkip.amazonaws.com/ "HTTP/1.1 200 "
2025-10-24 18:39:17,867 - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-10-24 18:39:19,909 - INFO - HTTP Request: GET http://localhost:7860/startup-events "HTTP/1.1 200 OK"
2025-10-24 18:39:22,103 - INFO - HTTP Request: HEAD http://localhost:7860/ "HTTP/1.1 200 OK"
2025-10-24 18:39:23,286 - INFO - HTTP Request: GET https://api.gradio.app/v2/tunnel-request "HTTP/1.1 200 OK"
2025-10-25 02:14:07,018 - INFO - Use pytorch device_name: cpu
2025-10-25 02:14:07,019 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-10-25 02:14:13,056 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-10-25 02:14:36,495 - INFO - HTTP Request: GET https://checkip.amazonaws.com/ "HTTP/1.1 200 "
2025-10-25 02:14:40,349 - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-10-25 02:14:40,769 - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-10-25 02:14:42,771 - INFO - HTTP Request: GET http://localhost:7860/startup-events "HTTP/1.1 200 OK"
2025-10-25 02:14:44,906 - INFO - HTTP Request: HEAD http://localhost:7860/ "HTTP/1.1 200 OK"
2025-10-25 02:14:46,955 - INFO - HTTP Request: GET https://api.gradio.app/v2/tunnel-request "HTTP/1.1 200 OK"
