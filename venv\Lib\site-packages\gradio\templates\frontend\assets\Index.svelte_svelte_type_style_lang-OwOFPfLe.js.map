{"version": 3, "file": "Index.svelte_svelte_type_style_lang-OwOFPfLe.js", "sources": ["../../../../js/markdown/shared/Markdown.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { copy } from \"@gradio/utils\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\n\timport MarkdownCode from \"./MarkdownCode.svelte\";\n\timport { fade } from \"svelte/transition\";\n\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string;\n\texport let min_height = false;\n\texport let rtl = false;\n\texport let sanitize_html = true;\n\texport let line_breaks = false;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let header_links = false;\n\texport let height: number | string | undefined = undefined;\n\texport let show_copy_button = false;\n\texport let root: string;\n\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\n\tconst dispatch = createEventDispatcher<{ change: undefined }>();\n\n\tconst css_units = (dimension_value: string | number): string => {\n\t\treturn typeof dimension_value === \"number\"\n\t\t\t? dimension_value + \"px\"\n\t\t\t: dimension_value;\n\t};\n\n\t$: value, dispatch(\"change\");\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n</script>\n\n<div\n\tclass:min={min_height}\n\tclass=\"prose {elem_classes.join(' ')}\"\n\tclass:hide={!visible}\n\tdata-testid=\"markdown\"\n\tdir={rtl ? \"rtl\" : \"ltr\"}\n\tuse:copy\n\tstyle={height ? `max-height: ${css_units(height)}; overflow-y: auto;` : \"\"}\n>\n\t{#if show_copy_button}\n\t\t{#if copied}\n\t\t\t<button\n\t\t\t\tin:fade={{ duration: 300 }}\n\t\t\t\taria-label=\"Copied\"\n\t\t\t\taria-roledescription=\"Text copied\"><Check /></button\n\t\t\t>\n\t\t{:else}\n\t\t\t<button\n\t\t\t\ton:click={handle_copy}\n\t\t\t\taria-label=\"Copy\"\n\t\t\t\taria-roledescription=\"Copy text\"><Copy /></button\n\t\t\t>\n\t\t{/if}\n\t{/if}\n\t<MarkdownCode\n\t\tmessage={value}\n\t\t{latex_delimiters}\n\t\t{sanitize_html}\n\t\t{line_breaks}\n\t\tchatbot={false}\n\t\t{header_links}\n\t\t{root}\n\t/>\n</div>\n\n<style>\n\tdiv :global(.math.inline) {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tpadding: var(--size-1-5) -var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tdiv :global(.math.inline svg) {\n\t\tdisplay: inline;\n\t\tmargin-bottom: 0.22em;\n\t}\n\n\tdiv {\n\t\tmax-width: 100%;\n\t}\n\n\t.min {\n\t\tmin-height: var(--size-24);\n\t}\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\tbutton {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: -10px;\n\t\tright: -10px;\n\t\talign-items: center;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--color-border-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\tpadding: 5px;\n\t\twidth: 22px;\n\t\theight: 22px;\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-color);\n\t\tfont: var(--font-sans);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n</style>\n"], "names": ["createEventDispatcher", "ctx", "insert", "target", "button", "anchor", "button_intro", "create_in_transition", "fade", "create_if_block", "div", "elem_classes", "$$props", "visible", "value", "min_height", "rtl", "sanitize_html", "line_breaks", "latex_delimiters", "header_links", "height", "show_copy_button", "root", "copied", "timer", "dispatch", "css_units", "dimension_value", "handle_copy", "copy_feedback", "$$invalidate"], "mappings": "mnBACU,CAAA,sBAAAA,CAAA,SAAqC,gGA+DxCC,EAAM,EAAA,EAAA,+dAO<PERSON>,EAIAC,EAAAC,EAAAC,CAAA,qCAHWJ,EAAW,EAAA,CAAA,uSAPtBC,EAIAC,EAAAC,EAAAC,CAAA,kEAHYC,EAAAC,EAAAH,EAAAI,EAAA,CAAA,SAAU,GAAG,CAAA,+GAHtBP,EAAgB,EAAA,GAAAQ,EAAAR,CAAA,iCAgBXA,EAAK,CAAA,oEAIL,0GA3BIA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,4CAG9BA,EAAG,CAAA,EAAG,MAAQ,KAAK,gBAEjBA,EAAM,CAAA,EAAkB,eAAAA,MAAUA,EAAM,CAAA,CAAA,CAAA,sBAAyB,EAAE,YAN/DA,EAAU,CAAA,CAAA,cAERA,EAAO,CAAA,CAAA,UAHrBC,EAiCKC,EAAAO,EAAAL,CAAA,iFAxBCJ,EAAgB,EAAA,2HAgBXA,EAAK,CAAA,oLAvBDA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,qDAG9BA,EAAG,CAAA,EAAG,MAAQ,yCAEZA,EAAM,CAAA,EAAkB,eAAAA,MAAUA,EAAM,CAAA,CAAA,CAAA,sBAAyB,0CAN7DA,EAAU,CAAA,CAAA,yBAERA,EAAO,CAAA,CAAA,2IAjDT,aAAAU,EAAY,EAAA,EAAAC,EACZ,CAAA,QAAAC,EAAU,EAAI,EAAAD,GACd,MAAAE,CAAa,EAAAF,EACb,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAClB,CAAA,IAAAI,EAAM,EAAK,EAAAJ,EACX,CAAA,cAAAK,EAAgB,EAAI,EAAAL,EACpB,CAAA,YAAAM,EAAc,EAAK,EAAAN,GACnB,iBAAAO,CAIR,EAAAP,EACQ,CAAA,aAAAQ,EAAe,EAAK,EAAAR,EACpB,CAAA,OAAAS,EAAsC,MAAS,EAAAT,EAC/C,CAAA,iBAAAU,EAAmB,EAAK,EAAAV,GACxB,KAAAW,CAAY,EAAAX,EAEnBY,EAAS,GACTC,EAEE,MAAAC,EAAW1B,IAEX2B,EAAaC,GACJ,OAAAA,GAAoB,SAC/BA,EAAkB,KAClBA,iBAKWC,GAAW,CACrB,cAAe,YACZ,MAAA,UAAU,UAAU,UAAUf,CAAK,EACzCgB,cAIOA,GAAa,CACrBC,EAAA,GAAAP,EAAS,EAAI,EACTC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPM,EAAA,GAAAP,EAAS,EAAK,GACZ,wfAdME,EAAS,QAAQ"}