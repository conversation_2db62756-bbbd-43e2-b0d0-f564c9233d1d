#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 Enhanced News Chatbot with Auto-Update
Basé sur Enhanced_News_Chatbot.ipynb

Features:
- ✅ Flexible Q&A: Répond à diverses questions sur les actualités
- ✅ Auto-Update: Actualisation automatique des données toutes les 10 minutes
- ✅ Multi-Category Search: Recherche dans toutes les catégories de news
- ✅ Smart Caching: Évite les requêtes redondantes
- ✅ User-Friendly Interface: Interface conviviale avec Gradio
- ✅ Real-time Updates: Notifications des nouvelles données
- ✅ Qwen2 LLM: Génération de réponses intelligentes
"""

print("🤖 Enhanced News Chatbot with Auto-Update")
print("=" * 60)

# ============================================================================
# 📦 ÉTAPE 1: Installation et imports (comme dans le notebook)
# ============================================================================

print("📦 Étape 1: Imports et configuration...")

# Imports essentiels (exactement comme dans le notebook)
import feedparser
import requests
import json
import os
import glob
import re
import threading
import time
import schedule
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any

# ML/NLP imports
import nltk
from bs4 import BeautifulSoup
from sentence_transformers import SentenceTransformer
import faiss
import numpy as np
import pickle

# LangChain imports
from langchain.schema import Document
from langchain.vectorstores import FAISS
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.llms import HuggingFacePipeline
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate

# Transformers et Gradio
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
import gradio as gr

# Configuration du logging (comme dans le notebook)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('chatbot.log'),
        logging.StreamHandler()
    ]
)

print("✅ Imports terminés")

# ============================================================================
# 🔧 ÉTAPE 2: Configuration (exactement comme dans le notebook)
# ============================================================================

print("🔧 Étape 2: Configuration du système...")

class NewsConfig:
    """Configuration centralisée - identique au notebook"""
    def __init__(self):
        # Répertoires et fichiers
        self.BASE_DIR = './enhanced_news_data'
        self.CATEGORIES = ['world', 'politics', 'business', 'technology', 'science', 'entertainment', 'sports', 'health']
        
        # Paramètres de mise à jour OPTIMISÉS
        self.UPDATE_INTERVAL_MINUTES = 10
        self.MAX_ARTICLES_PER_SOURCE = 30  # Réduit pour plus de rapidité
        self.CACHE_VALIDITY_MINUTES = 5

        # Modèles IA OPTIMISÉS
        self.EMBEDDING_MODEL = 'all-MiniLM-L6-v2'
        self.LLM_MODEL = 'Qwen/Qwen2-1.5B-Instruct'
        self.CHUNK_SIZE = 300  # Réduit pour traitement plus rapide

        # Paramètres de génération OPTIMISÉS pour la vitesse
        self.MAX_NEW_TOKENS = 150  # Réduit de 400 à 150
        self.TEMPERATURE = 0.3     # Réduit pour plus de cohérence et rapidité
        self.TOP_K = 5             # Limite le nombre de documents récupérés
        
        # Clés API (remplacez par vos vraies clés)
        self.NEWSAPI_KEY = "your_newsapi_key_here"
        self.NEWSDATA_KEY = "your_newsdata_key_here"
        
        # Sources RSS par catégorie (exactement comme dans le notebook)
        self.RSS_FEEDS = {
            'world': [
                'http://feeds.bbci.co.uk/news/world/rss.xml',
                'https://rss.cnn.com/rss/edition.rss',
                'https://feeds.reuters.com/reuters/worldNews'
            ],
            'politics': [
                'http://feeds.bbci.co.uk/news/politics/rss.xml',
                'https://rss.cnn.com/rss/cnn_allpolitics.rss'
            ],
            'business': [
                'http://feeds.bbci.co.uk/news/business/rss.xml',
                'https://feeds.bloomberg.com/markets/news.rss',
                'https://www.reuters.com/business/finance/rss'
            ],
            'technology': [
                'http://feeds.bbci.co.uk/news/technology/rss.xml',
                'https://feeds.feedburner.com/TechCrunch',
                'https://www.wired.com/feed/rss',
                'https://feeds.arstechnica.com/arstechnica/index'
            ],
            'science': [
                'http://feeds.bbci.co.uk/news/science_and_environment/rss.xml',
                'https://www.sciencedaily.com/rss/all.xml'
            ],
            'entertainment': [
                'http://feeds.bbci.co.uk/news/entertainment_and_arts/rss.xml',
                'https://rss.cnn.com/rss/edition_entertainment.rss'
            ],
            'sports': [
                'http://feeds.bbci.co.uk/sport/rss.xml',
                'https://rss.cnn.com/rss/edition_sport.rss'
            ],
            'health': [
                'http://feeds.bbci.co.uk/news/health/rss.xml',
                'https://rss.cnn.com/rss/edition_health.rss'
            ]
        }
        
        # Créer les répertoires
        self._create_directories()
    
    def _create_directories(self):
        """Crée la structure de répertoires"""
        if not os.path.exists(self.BASE_DIR):
            os.makedirs(self.BASE_DIR)
        
        for category in self.CATEGORIES:
            category_path = os.path.join(self.BASE_DIR, category)
            if not os.path.exists(category_path):
                os.makedirs(category_path)

# Initialiser la configuration
config = NewsConfig()
print(f"✅ Configuration initialisée - {len(config.CATEGORIES)} catégories")

# ============================================================================
# 📡 ÉTAPE 3: Collecteur de données (comme dans le notebook)
# ============================================================================

print("📡 Étape 3: Initialisation du collecteur de données...")

class EnhancedNewsCollector:
    """Collecteur de données amélioré - identique au notebook"""
    def __init__(self, config: NewsConfig):
        self.config = config
        self.cache = {}  # Cache pour éviter les requêtes redondantes
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def fetch_rss_with_cache(self, feed_url: str, category: str) -> List[Dict]:
        """Collecte RSS avec cache intelligent"""
        cache_key = f"{feed_url}_{category}"
        current_time = datetime.now()
        
        # Vérifier le cache
        if cache_key in self.cache:
            cache_time, cached_articles = self.cache[cache_key]
            if current_time - cache_time < timedelta(minutes=self.config.CACHE_VALIDITY_MINUTES):
                logging.info(f"📋 Cache hit pour {feed_url}")
                return cached_articles
        
        try:
            logging.info(f"📡 Collecte RSS: {feed_url}")
            feed = feedparser.parse(feed_url)
            
            if not feed.entries:
                logging.warning(f"⚠️ Aucun article trouvé: {feed_url}")
                return []
            
            articles = []
            for entry in feed.entries[:self.config.MAX_ARTICLES_PER_SOURCE]:
                article = {
                    'title': entry.get('title', 'No Title'),
                    'description': entry.get('description', ''),
                    'content': entry.get('content', [{}])[0].get('value', '') if entry.get('content') else '',
                    'link': entry.get('link', ''),
                    'pub_date': entry.get('published', ''),
                    'source': feed.feed.get('title', 'Unknown Source'),
                    'category': category,
                    'source_type': 'RSS'
                }
                articles.append(article)
            
            # Mettre en cache
            self.cache[cache_key] = (current_time, articles)
            logging.info(f"✅ Collecté {len(articles)} articles de {category}")
            return articles
            
        except Exception as e:
            logging.error(f"❌ Erreur RSS {feed_url}: {str(e)}")
            return []
    
    def clean_html(self, text: str) -> str:
        """Nettoie le HTML et normalise le texte"""
        if not text:
            return ""
        
        # Supprimer HTML
        soup = BeautifulSoup(text, 'html.parser')
        text = soup.get_text()
        
        # Normaliser les espaces
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Supprimer caractères spéciaux
        text = re.sub(r'[^\w\s\-.,!?;:()\[\]{}"\']', '', text)
        
        return text
    
    def save_articles_enhanced(self, articles: List[Dict], category: str) -> str:
        """Sauvegarde avec métadonnées enrichies"""
        if not articles:
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"combined_{timestamp}.json"
        filepath = os.path.join(self.config.BASE_DIR, category, filename)
        
        # Enrichir avec métadonnées
        enhanced_data = {
            'metadata': {
                'timestamp': timestamp,
                'category': category,
                'article_count': len(articles),
                'sources': list(set(article.get('source', 'Unknown') for article in articles))
            },
            'articles': articles
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"💾 Sauvegardé {len(articles)} articles: {filepath}")
            return filepath
            
        except Exception as e:
            logging.error(f"❌ Erreur sauvegarde {filepath}: {str(e)}")
            return ""

# Initialiser le collecteur
collector = EnhancedNewsCollector(config)
print("✅ Collecteur de données initialisé")

# ============================================================================
# 🔄 ÉTAPE 4: Gestionnaire d'actualisation automatique (comme dans le notebook)
# ============================================================================

print("🔄 Étape 4: Gestionnaire d'actualisation automatique...")

class AutoUpdateManager:
    """Gestionnaire d'actualisation automatique - identique au notebook"""
    def __init__(self, collector: EnhancedNewsCollector, config: NewsConfig):
        self.collector = collector
        self.config = config
        self.is_running = False
        self.update_thread = None
        self.last_update_time = None
        self.next_update_time = None
        self.chatbot_manager = None  # Sera lié plus tard

        # Statistiques
        self.update_stats = {
            'total_articles': 0,
            'successful_updates': 0,
            'failed_updates': 0
        }

    def collect_all_news(self) -> Dict[str, int]:
        """Collecte toutes les actualités de toutes les catégories"""
        total_collected = 0
        category_counts = {}

        for category in self.config.CATEGORIES:
            category_articles = []

            # Collecter depuis RSS
            for feed_url in self.config.RSS_FEEDS.get(category, []):
                articles = self.collector.fetch_rss_with_cache(feed_url, category)
                category_articles.extend(articles)

            # Sauvegarder si des articles collectés
            if category_articles:
                self.collector.save_articles_enhanced(category_articles, category)
                category_counts[category] = len(category_articles)
                total_collected += len(category_articles)
            else:
                category_counts[category] = 0

        self.update_stats['total_articles'] = total_collected
        return category_counts

    def update_job(self):
        """Job de mise à jour périodique"""
        try:
            logging.info("🔄 Début de la mise à jour automatique")
            self.last_update_time = datetime.now()

            # Collecter les nouvelles
            category_counts = self.collect_all_news()

            # Reconstruire l'index du chatbot si disponible
            if self.chatbot_manager and hasattr(self.chatbot_manager, 'rebuild_index'):
                self.chatbot_manager.rebuild_index()

            # Calculer la prochaine mise à jour
            self.next_update_time = self.last_update_time + timedelta(minutes=self.config.UPDATE_INTERVAL_MINUTES)

            self.update_stats['successful_updates'] += 1

            logging.info(f"✅ Mise à jour terminée: {sum(category_counts.values())} articles")

        except Exception as e:
            self.update_stats['failed_updates'] += 1
            logging.error(f"❌ Erreur mise à jour: {str(e)}")

    def _run_scheduler(self):
        """Thread de surveillance des mises à jour"""
        while self.is_running:
            schedule.run_pending()
            time.sleep(1)

    def start_auto_update(self):
        """Démarre l'actualisation automatique"""
        if self.is_running:
            logging.warning("⚠️ L'actualisation automatique est déjà active")
            return

        logging.info(f"🚀 Démarrage de l'actualisation automatique (toutes les {self.config.UPDATE_INTERVAL_MINUTES} min)")

        # Première collecte immédiate
        self.update_job()

        # Programmer les mises à jour toutes les 10 minutes
        schedule.every(self.config.UPDATE_INTERVAL_MINUTES).minutes.do(self.update_job)

        # Démarrer le thread de surveillance
        self.is_running = True
        self.update_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.update_thread.start()

        logging.info("✅ Actualisation automatique démarrée")

    def stop_auto_update(self):
        """Arrête l'actualisation automatique"""
        self.is_running = False
        schedule.clear()

        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=2)

        logging.info("🛑 Actualisation automatique arrêtée")

    def get_status(self) -> Dict:
        """Retourne le statut du gestionnaire"""
        return {
            'is_running': self.is_running,
            'last_update': self.last_update_time.isoformat() if self.last_update_time else None,
            'next_update': self.next_update_time.isoformat() if self.next_update_time else None,
            'stats': self.update_stats.copy()
        }

# Initialiser le gestionnaire d'actualisation
update_manager = AutoUpdateManager(collector, config)
print("✅ Gestionnaire d'actualisation automatique initialisé")

# ============================================================================
# 🧠 ÉTAPE 5: Système de traitement intelligent des données (comme dans le notebook)
# ============================================================================

print("🧠 Étape 5: Processeur de données intelligent...")

# Télécharger les données NLTK nécessaires
try:
    import nltk
    nltk.download('punkt', quiet=True)
    nltk.download('punkt_tab', quiet=True)
    print("✅ Données NLTK téléchargées")
except:
    print("⚠️ Erreur téléchargement NLTK (continuons quand même)")

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from nltk.tokenize import sent_tokenize

class IntelligentDataProcessor:
    """Processeur de données intelligent - identique au notebook"""
    def __init__(self, config: NewsConfig):
        self.config = config
        self.embedding_model = SentenceTransformer(config.EMBEDDING_MODEL)

    def load_all_articles(self) -> List[Dict]:
        """Charge tous les articles de toutes les catégories"""
        all_articles = []

        for category in self.config.CATEGORIES:
            category_path = os.path.join(self.config.BASE_DIR, category)
            if not os.path.exists(category_path):
                continue

            # Charger les fichiers combinés les plus récents
            combined_files = glob.glob(f"{category_path}/combined_*.json")
            if not combined_files:
                continue

            # Prendre le fichier le plus récent
            latest_file = max(combined_files, key=os.path.getctime)

            try:
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Gérer les deux formats (avec/sans métadonnées)
                articles = data.get('articles', data) if isinstance(data, dict) else data
                all_articles.extend(articles)

            except Exception as e:
                logging.error(f"❌ Erreur chargement {latest_file}: {str(e)}")

        logging.info(f"📚 Chargé {len(all_articles)} articles au total")
        return all_articles

    def deduplicate_articles(self, articles: List[Dict], threshold: float = 0.85) -> List[Dict]:
        """Déduplique les articles basé sur la similarité du contenu"""
        if len(articles) < 2:
            return articles

        # Créer les textes pour la comparaison
        texts = []
        for article in articles:
            title = str(article.get('title', ''))
            description = str(article.get('description', ''))
            content = str(article.get('content', ''))
            combined_text = f"{title} {description} {content}"
            texts.append(self.clean_text(combined_text))

        # Filtrer les textes vides
        valid_indices = [i for i, text in enumerate(texts) if text.strip()]
        if not valid_indices:
            return articles

        valid_texts = [texts[i] for i in valid_indices]
        valid_articles = [articles[i] for i in valid_indices]

        # Calculer la similarité TF-IDF
        try:
            vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
            tfidf_matrix = vectorizer.fit_transform(valid_texts)
            similarity_matrix = cosine_similarity(tfidf_matrix)

            # Identifier les articles uniques
            unique_indices = [0]  # Garder le premier article

            for i in range(1, len(valid_articles)):
                is_unique = True
                for j in unique_indices:
                    if similarity_matrix[i][j] >= threshold:
                        is_unique = False
                        break
                if is_unique:
                    unique_indices.append(i)

            unique_articles = [valid_articles[i] for i in unique_indices]
            logging.info(f"🔄 Déduplication: {len(articles)} → {len(unique_articles)} articles")
            return unique_articles

        except Exception as e:
            logging.error(f"❌ Erreur déduplication: {str(e)}")
            return articles

    def clean_text(self, text: str) -> str:
        """Nettoie et normalise le texte"""
        if not text:
            return ""

        # Supprimer HTML si présent
        soup = BeautifulSoup(text, 'html.parser')
        text = soup.get_text()

        # Normaliser les espaces
        text = re.sub(r'\s+', ' ', text).strip()

        # Supprimer les caractères spéciaux problématiques
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)

        return text

    def chunk_text(self, text: str, max_length: int = None) -> List[str]:
        """Découpe le texte en chunks intelligents"""
        if not text:
            return []

        max_length = max_length or self.config.CHUNK_SIZE

        try:
            sentences = sent_tokenize(text)
        except:
            # Fallback si NLTK échoue
            sentences = text.split('. ')

        chunks = []
        current_chunk = ""

        for sentence in sentences:
            # Vérifier si ajouter cette phrase dépasse la limite
            if len(current_chunk + sentence) <= max_length:
                current_chunk += sentence + " "
            else:
                # Sauvegarder le chunk actuel s'il n'est pas vide
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "

        # Ajouter le dernier chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

# Initialiser le processeur
processor = IntelligentDataProcessor(config)
print("✅ Processeur de données intelligent initialisé")

# ============================================================================
# 🤖 ÉTAPE 6: Chatbot intelligent avec Qwen (amélioré par rapport au notebook)
# ============================================================================

print("🤖 Étape 6: Chatbot intelligent avec Qwen...")

class EnhancedNewsChatbot:
    """Chatbot intelligent avec Qwen - version améliorée du notebook"""
    def __init__(self, config: NewsConfig, processor: IntelligentDataProcessor):
        self.config = config
        self.processor = processor
        self.vectorstore = None
        self.retriever = None
        self.qa_chain = None
        self.embeddings = None
        self.llm = None

        # Statistiques
        self.query_count = 0
        self.last_rebuild = None

        self._initialize_models()

    def _initialize_models(self):
        """Initialise les modèles d'embedding et de génération"""
        try:
            # Modèle d'embeddings
            self.embeddings = HuggingFaceEmbeddings(
                model_name=self.config.EMBEDDING_MODEL,
                model_kwargs={'device': 'cuda' if torch.cuda.is_available() else 'cpu'}
            )
            print(f"✅ Modèle d'embeddings chargé: {self.config.EMBEDDING_MODEL}")

            # Modèle de génération
            self._load_generation_model()

        except Exception as e:
            logging.error(f"❌ Erreur initialisation modèles: {str(e)}")
            raise

    def _load_generation_model(self):
        """Charge le modèle de génération avec fallback"""
        device = 0 if torch.cuda.is_available() else -1

        try:
            # Essayer Qwen2 d'abord
            print(f"🧠 Chargement de {self.config.LLM_MODEL}...")
            tokenizer = AutoTokenizer.from_pretrained(self.config.LLM_MODEL, trust_remote_code=True)
            model = AutoModelForCausalLM.from_pretrained(
                self.config.LLM_MODEL,
                torch_dtype=torch.float16 if device == 0 else torch.float32,
                device_map="auto" if device == 0 else None,
                trust_remote_code=True
            )

            pipe = pipeline(
                "text-generation",
                model=model,
                tokenizer=tokenizer,
                max_new_tokens=self.config.MAX_NEW_TOKENS,  # Optimisé: 150 au lieu de 400
                temperature=self.config.TEMPERATURE,        # Optimisé: 0.3 au lieu de 0.7
                do_sample=True,
                device=device,
                pad_token_id=tokenizer.eos_token_id,
                # Paramètres supplémentaires pour la vitesse
                top_k=50,
                top_p=0.9,
                repetition_penalty=1.1
            )

            self.llm = HuggingFacePipeline(pipeline=pipe)
            print(f"✅ Modèle de génération chargé: {self.config.LLM_MODEL}")

        except Exception as e:
            print(f"⚠️ Erreur avec {self.config.LLM_MODEL}, fallback vers GPT-2")
            self._load_fallback_model()

    def _load_fallback_model(self):
        """Charge GPT-2 comme modèle de fallback"""
        try:
            from transformers import GPT2LMHeadModel, GPT2Tokenizer

            print("🔄 Chargement de GPT-2 comme fallback...")
            tokenizer = GPT2Tokenizer.from_pretrained('gpt2')
            tokenizer.pad_token = tokenizer.eos_token
            model = GPT2LMHeadModel.from_pretrained('gpt2')

            pipe = pipeline(
                "text-generation",
                model=model,
                tokenizer=tokenizer,
                max_new_tokens=200,
                device=-1,
                pad_token_id=tokenizer.eos_token_id
            )

            self.llm = HuggingFacePipeline(pipeline=pipe)
            print("✅ Modèle GPT-2 chargé comme fallback")

        except Exception as e:
            logging.error(f"❌ Erreur critique modèles: {str(e)}")
            raise

    def _create_optimized_prompt(self) -> PromptTemplate:
        """Crée un prompt optimisé pour les actualités en français"""

        prompt_template = """Tu es un assistant expert en actualités qui répond de manière précise et rapide en français.

INSTRUCTIONS OPTIMISÉES:
- Réponds UNIQUEMENT basé sur le contexte fourni
- Sois factuel, précis et CONCIS
- Maximum 100-150 mots
- Cite 1-2 sources principales
- Si l'information n'est pas disponible, dis-le brièvement
- Ton professionnel mais accessible
- Évite les répétitions

CONTEXTE:
{context}

QUESTION: {question}

RÉPONSE COURTE:"""

        return PromptTemplate(
            template=prompt_template,
            input_variables=["context", "question"]
        )

    def rebuild_index(self):
        """Reconstruit l'index de recherche avec les dernières données"""
        try:
            print("🔄 Reconstruction de l'index de recherche...")

            # Charger et traiter les articles
            articles = self.processor.load_all_articles()
            if not articles:
                print("⚠️ Aucun article trouvé pour l'index")
                return False

            # Dédupliquer
            articles = self.processor.deduplicate_articles(articles)

            # Traiter pour la recherche
            processed_chunks = self._process_articles_for_search(articles)

            if not processed_chunks:
                print("⚠️ Aucun chunk généré pour l'index")
                return False

            # Créer les documents LangChain
            documents = []
            for chunk in processed_chunks:
                doc = Document(
                    page_content=chunk['content'],
                    metadata={
                        'title': chunk['title'],
                        'source': chunk['source'],
                        'link': chunk['link'],
                        'category': chunk['category'],
                        'pub_date': chunk['pub_date'],
                        'chunk_index': chunk['chunk_index']
                    }
                )
                documents.append(doc)

            # Construire le vectorstore OPTIMISÉ
            self.vectorstore = FAISS.from_documents(documents, self.embeddings)
            self.retriever = self.vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": self.config.TOP_K}  # Optimisé: 5 au lieu de plus
            )

            # Créer la chaîne QA avec prompt optimisé
            prompt = self._create_optimized_prompt()
            self.qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type="stuff",
                retriever=self.retriever,
                return_source_documents=True,
                chain_type_kwargs={"prompt": prompt}
            )

            self.last_rebuild = datetime.now()
            print(f"✅ Index reconstruit avec {len(documents)} chunks")
            return True

        except Exception as e:
            logging.error(f"❌ Erreur reconstruction index: {str(e)}")
            print(f"❌ Erreur reconstruction: {str(e)}")
            return False

    def _process_articles_for_search(self, articles: List[Dict]) -> List[Dict]:
        """Traite les articles pour la recherche (chunking + métadonnées)"""
        processed_chunks = []

        for article in articles:
            # Combiner tout le texte de l'article
            title = str(article.get('title', ''))
            description = str(article.get('description', ''))
            content = str(article.get('content', ''))

            full_text = self.processor.clean_text(f"{title}. {description}. {content}")

            if not full_text.strip():
                continue

            # Créer des chunks
            chunks = self.processor.chunk_text(full_text)

            for i, chunk in enumerate(chunks):
                if chunk.strip():
                    processed_chunk = {
                        'id': f"{article.get('link', 'unknown')}_{i}",
                        'title': title,
                        'source': article.get('source', 'Unknown'),
                        'link': article.get('link', 'No Link'),
                        'category': article.get('category', 'unknown'),
                        'pub_date': article.get('pub_date', ''),
                        'content': chunk,
                        'chunk_index': i,
                        'total_chunks': len(chunks)
                    }
                    processed_chunks.append(processed_chunk)

        logging.info(f"📝 Traité {len(articles)} articles → {len(processed_chunks)} chunks")
        return processed_chunks

    def query(self, question: str) -> Dict[str, Any]:
        """Traite une question et retourne la réponse avec sources"""
        if not self.qa_chain:
            return {
                "answer": "❌ Le système n'est pas encore initialisé. Veuillez attendre la construction de l'index.",
                "sources": [],
                "error": "Index non construit"
            }

        try:
            self.query_count += 1
            start_time = time.time()

            # Exécuter la requête
            result = self.qa_chain.invoke({"query": question})

            # Extraire les sources
            sources = []
            for doc in result.get('source_documents', []):
                source_info = {
                    'title': doc.metadata.get('title', 'Titre non disponible'),
                    'source': doc.metadata.get('source', 'Source inconnue'),
                    'link': doc.metadata.get('link', '#'),
                    'category': doc.metadata.get('category', 'Non catégorisé'),
                    'pub_date': doc.metadata.get('pub_date', ''),
                    'content_preview': doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content
                }
                sources.append(source_info)

            response_time = time.time() - start_time

            return {
                "answer": result['result'].strip(),
                "sources": sources,
                "response_time": round(response_time, 2),
                "query_count": self.query_count
            }

        except Exception as e:
            logging.error(f"❌ Erreur lors de la requête: {str(e)}")
            return {
                "answer": f"❌ Erreur lors du traitement de votre question: {str(e)}",
                "sources": [],
                "error": str(e)
            }

    def get_stats(self) -> Dict:
        """Retourne les statistiques du chatbot"""
        return {
            "query_count": self.query_count,
            "last_rebuild": self.last_rebuild.isoformat() if self.last_rebuild else None,
            "index_ready": self.qa_chain is not None,
            "vectorstore_size": len(self.vectorstore.docstore._dict) if self.vectorstore else 0
        }

# Initialiser le chatbot
chatbot = EnhancedNewsChatbot(config, processor)
print("✅ Chatbot intelligent initialisé")

# Lier le chatbot au gestionnaire d'actualisation
update_manager.chatbot_manager = chatbot

# ============================================================================
# 🎨 ÉTAPE 7: Interface utilisateur avec Gradio (comme dans le notebook)
# ============================================================================

print("🎨 Étape 7: Interface utilisateur avec Gradio...")

def create_gradio_interface():
    """Crée l'interface utilisateur Gradio - identique au notebook"""

    def chat_response(message, history):
        """Fonction de réponse du chat"""
        if not message.strip():
            return "Veuillez poser une question."

        # Obtenir la réponse du chatbot
        result = chatbot.query(message)

        # Formater la réponse
        response = result['answer']

        # Ajouter les sources si disponibles
        if result.get('sources'):
            response += "\n\n📚 **Sources:**\n"
            for i, source in enumerate(result['sources'][:3], 1):
                response += f"{i}. **{source['title']}** ({source['source']})\n"
                if source['link'] != '#':
                    response += f"   🔗 [Lire l'article]({source['link']})\n"

        # Ajouter les métadonnées
        if 'response_time' in result:
            response += f"\n⏱️ *Temps de réponse: {result['response_time']}s*"

        return response

    def get_system_status():
        """Retourne le statut du système"""
        update_status = update_manager.get_status()
        chatbot_stats = chatbot.get_stats()

        status = f"""## 📊 Statut du Système

### 🔄 Actualisation Automatique
- **État**: {'🟢 Actif' if update_status['is_running'] else '🔴 Inactif'}
- **Dernière mise à jour**: {update_status['last_update'] or 'Jamais'}
- **Prochaine mise à jour**: {update_status['next_update'] or 'Non programmée'}
- **Articles collectés**: {update_status['stats']['total_articles']}
- **Mises à jour réussies**: {update_status['stats']['successful_updates']}
- **Échecs**: {update_status['stats']['failed_updates']}

### 🤖 Chatbot
- **Index prêt**: {'✅ Oui' if chatbot_stats['index_ready'] else '❌ Non'}
- **Requêtes traitées**: {chatbot_stats['query_count']}
- **Taille de l'index**: {chatbot_stats['vectorstore_size']} documents
- **Dernière reconstruction**: {chatbot_stats['last_rebuild'] or 'Jamais'}
"""
        return status

    def start_system():
        """Démarre le système complet"""
        try:
            # Démarrer l'actualisation automatique
            update_manager.start_auto_update()

            # Construire l'index initial
            chatbot.rebuild_index()

            return "✅ Système démarré avec succès!"
        except Exception as e:
            return f"❌ Erreur lors du démarrage: {str(e)}"

    def stop_system():
        """Arrête le système"""
        update_manager.stop_auto_update()
        return "🛑 Système arrêté"

    def manual_update():
        """Lance une mise à jour manuelle"""
        try:
            update_manager.update_job()
            return "✅ Mise à jour manuelle terminée"
        except Exception as e:
            return f"❌ Erreur mise à jour: {str(e)}"

    # Interface Gradio
    with gr.Blocks(title="🤖 Enhanced News Chatbot", theme=gr.themes.Soft()) as interface:
        gr.Markdown("""
        # 🤖 Enhanced News Chatbot avec Qwen2

        Chatbot intelligent alimenté par **Qwen2-1.5B-Instruct** qui répond à vos questions
        sur l'actualité avec des réponses générées et contextualisées.

        **Fonctionnalités:**
        - 🧠 **IA Avancée**: Réponses générées par Qwen2
        - 📰 **Actualités en temps réel**: Base de données mise à jour automatiquement
        - 🔍 **Recherche intelligente**: Trouve les informations pertinentes
        - 📚 **Sources citées**: Liens vers les articles originaux
        - 🔄 **Auto-update**: Mise à jour toutes les 10 minutes

        **Exemples de questions:**
        - "Résume-moi les dernières nouvelles en technologie"
        - "Que se passe-t-il dans le monde actuellement ?"
        - "Quelles sont les tendances en business ?"
        - "Explique-moi les actualités récentes"
        """)

        with gr.Tab("💬 Chat"):
            chatbot_interface = gr.ChatInterface(
                chat_response,
                title="Assistant IA alimenté par Qwen2 - Posez vos questions sur l'actualité",
                description="Le chatbot utilise les dernières actualités pour répondre à vos questions.",
                examples=[
                    "Résume-moi les dernières nouvelles en technologie",
                    "Que se passe-t-il dans le monde actuellement ?",
                    "Explique-moi les tendances en business",
                    "Quelles sont les actualités importantes aujourd'hui ?",
                    "Parle-moi des innovations récentes",
                    "Que dois-je savoir sur l'actualité mondiale ?"
                ],
                retry_btn="🔄 Réessayer",
                undo_btn="↩️ Annuler",
                clear_btn="🗑️ Effacer"
            )

        with gr.Tab("⚙️ Contrôle"):
            with gr.Row():
                start_btn = gr.Button("🚀 Démarrer le Système", variant="primary")
                stop_btn = gr.Button("🛑 Arrêter le Système", variant="secondary")
                update_btn = gr.Button("🔄 Mise à jour manuelle", variant="secondary")

            control_output = gr.Textbox(label="Résultat", interactive=False)

            start_btn.click(start_system, outputs=control_output)
            stop_btn.click(stop_system, outputs=control_output)
            update_btn.click(manual_update, outputs=control_output)

        with gr.Tab("📊 Statut"):
            status_display = gr.Markdown(value=get_system_status())
            refresh_btn = gr.Button("🔄 Actualiser le statut")
            refresh_btn.click(get_system_status, outputs=status_display)

    return interface

# Créer l'interface
interface = create_gradio_interface()
print("✅ Interface Gradio créée")

# ============================================================================
# 🚀 ÉTAPE 8: Lancement du système (comme dans le notebook)
# ============================================================================

def main():
    """Fonction principale de lancement - identique au notebook"""
    print("\n🚀 Démarrage du système Enhanced News Chatbot...")
    print("📊 Configuration:")
    print(f"   - Catégories: {len(config.CATEGORIES)}")
    print(f"   - Sources RSS: {sum(len(feeds) for feeds in config.RSS_FEEDS.values())}")
    print(f"   - Mise à jour: toutes les {config.UPDATE_INTERVAL_MINUTES} minutes")
    print(f"   - Modèle d'embedding: {config.EMBEDDING_MODEL}")
    print(f"   - Modèle de génération: {config.LLM_MODEL}")

    # Lancer l'interface
    print("\n🎨 Lancement de l'interface Gradio...")
    print("\n" + "="*50)
    print("🤖 ENHANCED NEWS CHATBOT PRÊT !")
    print("="*50)
    print("\n📝 Instructions:")
    print("1. Cliquez sur 'Démarrer le Système' dans l'onglet Contrôle")
    print("2. Attendez la collecte initiale des données")
    print("3. Posez vos questions dans l'onglet Chat")
    print("4. Consultez le statut dans l'onglet Statut")
    print("\n🔄 Le système se met à jour automatiquement toutes les 10 minutes")

    # Lancer l'interface Gradio
g        share=True,  # Créer un lien public
        debug=True,  # Mode debug
        show_error=True,  # Afficher les erreurs
        height=600,  # Hauteur de l'interface
        server_name="0.0.0.0",  # Accessible depuis l'extérieur
        server_port=7860  # Port par défaut
    )

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du système...")
        update_manager.stop_auto_update()
        print("✅ Système arrêté proprement")
    except Exception as e:
        print(f"\n❌ Erreur critique: {str(e)}")
        logging.error(f"Erreur critique: {str(e)}")
        update_manager.stop_auto_update()
