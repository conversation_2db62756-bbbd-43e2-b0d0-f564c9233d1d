<!doctype html>
<html
	lang="en"
	style="
		margin: 0;
		padding: 0;
		min-height: 100%;
		display: flex;
		flex-direction: column;
	"
>
	<head>
		<meta charset="utf-8" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1, shrink-to-fit=no"
		/>

		<style>
			:root {
				--bg: {{ config.get('body_css', {}).get('body_background_fill', 'white') }};
				--col:   {{ config.get('body_css', {}).get('body_text_color', '#1f2937') }};
				--bg-dark: {{ config.get('body_css', {}).get('body_background_fill_dark', '#0b0f19') }};
				--col-dark: {{ config.get('body_css', {}).get('body_text_color_dark', '#f3f4f6') }};
			}


			body {
				background: var(--bg);
				color: var(--col);
				font-family: Arial, Helvetica, sans-serif;
			}

			@media (prefers-color-scheme: dark) {
				body {
					background: var(--bg-dark);
					color: var(--col-dark);
				}
			}
		</style>

		<script type="module" src="./src/main.ts"></script>
		<meta property="og:url" content="https://gradio.app/" />
		<meta property="og:type" content="website" />
		<meta property="og:image" content="{{ config['thumbnail'] or '' }}" />
		<meta property="og:title" content="{{ config['title'] or '' }}" />
		<meta
			property="og:description"
			content="{{ config['simple_description'] or '' }}"
		/>
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:creator" content="@teamGradio" />
		<meta name="twitter:title" content="{{ config['title'] or '' }}" />
		<meta
			name="twitter:description"
			content="{{ config['simple_description'] or '' }}"
		/>
		<meta name="twitter:image" content="{{ config['thumbnail'] or '' }}" />

		<script data-gradio-mode>
			window.__gradio_mode__ = "app";
			window.iFrameResizer = {
				heightCalculationMethod: "taggedElement"
			};
			window.parent?.postMessage(
				{ type: "SET_SCROLLING", enabled: false },
				"*"
			);
		</script>

		%gradio_config%%gradio_api_info%

		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link
			rel="preconnect"
			href="https://fonts.gstatic.com"
			crossorigin="anonymous"
		/>
		<script
			src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.1/iframeResizer.contentWindow.min.js"
			async
		></script>
	</head>

	<body
		style="
			width: 100%;
			margin: 0;
			padding: 0;
			display: flex;
			flex-direction: column;
			flex-grow: 1;
		"
	>
		<gradio-app
			control_page_title="true"
			embed="false"
			eager="true"
			style="display: flex; flex-direction: column; flex-grow: 1"
		>
		</gradio-app>
		<script>
			const ce = document.getElementsByTagName("gradio-app");

			if (ce[0]) {
				ce[0].addEventListener("domchange", () => {
					document.body.style.padding = "0";
				});
				document.body.style.padding = "0";
			}
		</script>
	</body>
</html>
