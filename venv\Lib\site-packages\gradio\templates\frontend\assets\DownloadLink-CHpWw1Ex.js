import{g as G}from"./Index-DB1XLvMK.js";import{s as H,g as M}from"./file-url-SIRImsEF.js";const{SvelteComponent:S,assign:h,check_outros:W,compute_rest_props:q,create_slot:v,detach:k,element:j,empty:C,exclude_internal_props:T,flush:D,get_all_dirty_from_scope:y,get_slot_changes:E,get_spread_update:N,group_outros:O,init:V,insert:b,listen:P,prevent_default:z,safe_not_equal:A,set_attributes:p,transition_in:d,transition_out:m,update_slot_base:L}=window.__gradio__svelte__internal,{createEventDispatcher:I,onMount:ee}=window.__gradio__svelte__internal;function J(u){let e,t,o,r,a;const f=u[8].default,s=v(f,u,u[7],null);let i=[{href:u[0]},{target:t=typeof window<"u"&&window.__is_colab__?"_blank":null},{rel:"noopener noreferrer"},{download:u[1]},u[6]],l={};for(let n=0;n<i.length;n+=1)l=h(l,i[n]);return{c(){e=j("a"),s&&s.c(),p(e,l)},m(n,_){b(n,e,_),s&&s.m(e,null),o=!0,r||(a=P(e,"click",u[3].bind(null,"click")),r=!0)},p(n,_){s&&s.p&&(!o||_&128)&&L(s,f,n,n[7],o?E(f,n[7],_,null):y(n[7]),null),p(e,l=N(i,[(!o||_&1)&&{href:n[0]},{target:t},{rel:"noopener noreferrer"},(!o||_&2)&&{download:n[1]},_&64&&n[6]]))},i(n){o||(d(s,n),o=!0)},o(n){m(s,n),o=!1},d(n){n&&k(e),s&&s.d(n),r=!1,a()}}}function K(u){let e,t,o,r;const a=[X,Q],f=[];function s(i,l){return i[2]?0:1}return e=s(u),t=f[e]=a[e](u),{c(){t.c(),o=C()},m(i,l){f[e].m(i,l),b(i,o,l),r=!0},p(i,l){let n=e;e=s(i),e===n?f[e].p(i,l):(O(),m(f[n],1,1,()=>{f[n]=null}),W(),t=f[e],t?t.p(i,l):(t=f[e]=a[e](i),t.c()),d(t,1),t.m(o.parentNode,o))},i(i){r||(d(t),r=!0)},o(i){m(t),r=!1},d(i){i&&k(o),f[e].d(i)}}}function Q(u){let e,t,o,r;const a=u[8].default,f=v(a,u,u[7],null);let s=[u[6],{href:u[0]}],i={};for(let l=0;l<s.length;l+=1)i=h(i,s[l]);return{c(){e=j("a"),f&&f.c(),p(e,i)},m(l,n){b(l,e,n),f&&f.m(e,null),t=!0,o||(r=P(e,"click",z(u[5])),o=!0)},p(l,n){f&&f.p&&(!t||n&128)&&L(f,a,l,l[7],t?E(a,l[7],n,null):y(l[7]),null),p(e,i=N(s,[n&64&&l[6],(!t||n&1)&&{href:l[0]}]))},i(l){t||(d(f,l),t=!0)},o(l){m(f,l),t=!1},d(l){l&&k(e),f&&f.d(l),o=!1,r()}}}function X(u){let e;const t=u[8].default,o=v(t,u,u[7],null);return{c(){o&&o.c()},m(r,a){o&&o.m(r,a),e=!0},p(r,a){o&&o.p&&(!e||a&128)&&L(o,t,r,r[7],e?E(t,r[7],a,null):y(r[7]),null)},i(r){e||(d(o,r),e=!0)},o(r){m(o,r),e=!1},d(r){o&&o.d(r)}}}function Y(u){let e,t,o,r,a;const f=[K,J],s=[];function i(l,n){return n&1&&(e=null),e==null&&(e=!!(l[4]&&H(l[0]))),e?0:1}return t=i(u,-1),o=s[t]=f[t](u),{c(){o.c(),r=C()},m(l,n){s[t].m(l,n),b(l,r,n),a=!0},p(l,[n]){let _=t;t=i(l,n),t===_?s[t].p(l,n):(O(),m(s[_],1,1,()=>{s[_]=null}),W(),o=s[t],o?o.p(l,n):(o=s[t]=f[t](l),o.c()),d(o,1),o.m(r.parentNode,r))},i(l){a||(d(o),a=!0)},o(l){m(o),a=!1},d(l){l&&k(r),s[t].d(l)}}}function Z(u,e,t){const o=["href","download"];let r=q(e,o),{$$slots:a={},$$scope:f}=e,{href:s=void 0}=e,{download:i}=e;const l=I();let n=!1;const _=G();async function B(){if(n)return;if(l("click"),s==null)throw new Error("href is not defined.");if(_==null)throw new Error("Wasm worker proxy is not available.");const R=new URL(s,window.location.href).pathname;t(2,n=!0),_.httpRequest({method:"GET",path:R,headers:{},query_string:""}).then(w=>{if(w.status!==200)throw new Error(`Failed to get file ${R} from the Wasm worker.`);const F=new Blob([w.body],{type:M(w.headers,"content-type")}),U=URL.createObjectURL(F),g=document.createElement("a");g.href=U,g.download=i,g.click(),URL.revokeObjectURL(U)}).finally(()=>{t(2,n=!1)})}return u.$$set=c=>{e=h(h({},e),T(c)),t(6,r=q(e,o)),"href"in c&&t(0,s=c.href),"download"in c&&t(1,i=c.download),"$$scope"in c&&t(7,f=c.$$scope)},[s,i,n,l,_,B,r,f,a]}class le extends S{constructor(e){super(),V(this,e,Z,Y,A,{href:0,download:1})}get href(){return this.$$.ctx[0]}set href(e){this.$$set({href:e}),D()}get download(){return this.$$.ctx[1]}set download(e){this.$$set({download:e}),D()}}export{le as D};
//# sourceMappingURL=DownloadLink-CHpWw1Ex.js.map
