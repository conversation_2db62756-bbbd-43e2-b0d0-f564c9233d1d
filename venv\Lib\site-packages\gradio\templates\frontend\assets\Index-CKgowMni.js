const __vite__fileDeps=["./index-D8nlTjuq.js","./index-mso0hJGt.js","./index-BQPjLIsY.js","./index-BOW2xVAS.css","./Check-CZUQOzJl.js","./Copy-B6RcHnoK.js","./Index-DB1XLvMK.js","./Index-BEyjvDG_.css","./Button-BIUaXfcG.js","./Button-CTZL5Nos.css","./Download-DVtk-Jv3.js","./DownloadLink-CHpWw1Ex.js","./file-url-SIRImsEF.js","./Code-DGNrTu_I.js","./BlockLabel-BlSr62f_.js","./Empty-BgF7sXBn.js","./Example-Wp-_4AVX.js","./Example-oomIF0ca.css","./index-BQ3WHOkp.js","./index-meEBNtyL.js","./index-DajRW7Pk.js","./index-CCmsNWjW.js","./frontmatter-C9bQZm_D.js","./yaml-DsCXHVTH.js","./index-B1iHl3xK.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as ce}from"./index-BQPjLIsY.js";import{C as ra}from"./Check-CZUQOzJl.js";import{C as sf}from"./Copy-B6RcHnoK.js";import{S as rf}from"./Index-DB1XLvMK.js";import{f as Zr,B as of}from"./Button-BIUaXfcG.js";import{D as lf}from"./Download-DVtk-Jv3.js";import{D as af}from"./DownloadLink-CHpWw1Ex.js";import{C as oa}from"./Code-DGNrTu_I.js";import{B as hf}from"./BlockLabel-BlSr62f_.js";import{E as cf}from"./Empty-BgF7sXBn.js";import ff from"./Example-Wp-_4AVX.js";class V{constructor(){}lineAt(e){if(e<0||e>this.length)throw new RangeError(`Invalid position ${e} in document of length ${this.length}`);return this.lineInner(e,!1,1,0)}line(e){if(e<1||e>this.lines)throw new RangeError(`Invalid line number ${e} in ${this.lines}-line document`);return this.lineInner(e,!0,1,0)}replace(e,t,i){let s=[];return this.decompose(0,e,s,2),i.length&&i.decompose(0,i.length,s,3),this.decompose(t,this.length,s,1),je.from(s,this.length-(t-e)+i.length)}append(e){return this.replace(this.length,this.length,e)}slice(e,t=this.length){let i=[];return this.decompose(e,t,i,0),je.from(i,t-e)}eq(e){if(e==this)return!0;if(e.length!=this.length||e.lines!=this.lines)return!1;let t=this.scanIdentical(e,1),i=this.length-this.scanIdentical(e,-1),s=new gi(this),r=new gi(e);for(let o=t,l=t;;){if(s.next(o),r.next(o),o=0,s.lineBreak!=r.lineBreak||s.done!=r.done||s.value!=r.value)return!1;if(l+=s.value.length,s.done||l>=i)return!0}}iter(e=1){return new gi(this,e)}iterRange(e,t=this.length){return new la(this,e,t)}iterLines(e,t){let i;if(e==null)i=this.iter();else{t==null&&(t=this.lines+1);let s=this.line(e).from;i=this.iterRange(s,Math.max(s,t==this.lines+1?this.length:t<=1?0:this.line(t-1).to))}return new aa(i)}toString(){return this.sliceString(0)}toJSON(){let e=[];return this.flatten(e),e}static of(e){if(e.length==0)throw new RangeError("A document must have at least one line");return e.length==1&&!e[0]?V.empty:e.length<=32?new X(e):je.from(X.split(e,[]))}}class X extends V{constructor(e,t=uf(e)){super(),this.text=e,this.length=t}get lines(){return this.text.length}get children(){return null}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.text[r],l=s+o.length;if((t?i:l)>=e)return new df(s,l,i,o);s=l+1,i++}}decompose(e,t,i,s){let r=e<=0&&t>=this.length?this:new X(eo(this.text,e,t),Math.min(t,this.length)-Math.max(0,e));if(s&1){let o=i.pop(),l=rn(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new X(l,o.length+r.length));else{let a=l.length>>1;i.push(new X(l.slice(0,a)),new X(l.slice(a)))}}else i.push(r)}replace(e,t,i){if(!(i instanceof X))return super.replace(e,t,i);let s=rn(this.text,rn(i.text,eo(this.text,0,e)),t),r=this.length+i.length-(t-e);return s.length<=32?new X(s,r):je.from(X.split(s,[]),r)}sliceString(e,t=this.length,i=`
`){let s="";for(let r=0,o=0;r<=t&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>e&&o&&(s+=i),e<a&&t>r&&(s+=l.slice(Math.max(0,e-r),t-r)),r=a+1}return s}flatten(e){for(let t of this.text)e.push(t)}scanIdentical(){return 0}static split(e,t){let i=[],s=-1;for(let r of e)i.push(r),s+=r.length+1,i.length==32&&(t.push(new X(i,s)),i=[],s=-1);return s>-1&&t.push(new X(i,s)),t}}class je extends V{constructor(e,t){super(),this.children=e,this.length=t,this.lines=0;for(let i of e)this.lines+=i.lines}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.children[r],l=s+o.length,a=i+o.lines-1;if((t?a:l)>=e)return o.lineInner(e,t,i,s);s=l+1,i=a+1}}decompose(e,t,i,s){for(let r=0,o=0;o<=t&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(e<=a&&t>=o){let h=s&((o<=e?1:0)|(a>=t?2:0));o>=e&&a<=t&&!h?i.push(l):l.decompose(e-o,t-o,i,h)}o=a+1}}replace(e,t,i){if(i.lines<this.lines)for(let s=0,r=0;s<this.children.length;s++){let o=this.children[s],l=r+o.length;if(e>=r&&t<=l){let a=o.replace(e-r,t-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>4&&a.lines>h>>6){let c=this.children.slice();return c[s]=a,new je(c,this.length-(t-e)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(e,t,i)}sliceString(e,t=this.length,i=`
`){let s="";for(let r=0,o=0;r<this.children.length&&o<=t;r++){let l=this.children[r],a=o+l.length;o>e&&r&&(s+=i),e<a&&t>o&&(s+=l.sliceString(e-o,t-o,i)),o=a+1}return s}flatten(e){for(let t of this.children)t.flatten(e)}scanIdentical(e,t){if(!(e instanceof je))return 0;let i=0,[s,r,o,l]=t>0?[0,0,this.children.length,e.children.length]:[this.children.length-1,e.children.length-1,-1,-1];for(;;s+=t,r+=t){if(s==o||r==l)return i;let a=this.children[s],h=e.children[r];if(a!=h)return i+a.scanIdentical(h,t);i+=a.length+1}}static from(e,t=e.reduce((i,s)=>i+s.length+1,-1)){let i=0;for(let d of e)i+=d.lines;if(i<32){let d=[];for(let p of e)p.flatten(d);return new X(d,t)}let s=Math.max(32,i>>5),r=s<<1,o=s>>1,l=[],a=0,h=-1,c=[];function f(d){let p;if(d.lines>r&&d instanceof je)for(let g of d.children)f(g);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof X&&a&&(p=c[c.length-1])instanceof X&&d.lines+p.lines<=32?(a+=d.lines,h+=d.length+1,c[c.length-1]=new X(p.text.concat(d.text),p.length+1+d.length)):(a+d.lines>s&&u(),a+=d.lines,h+=d.length+1,c.push(d))}function u(){a!=0&&(l.push(c.length==1?c[0]:je.from(c,h)),h=-1,a=c.length=0)}for(let d of e)f(d);return u(),l.length==1?l[0]:new je(l,t)}}V.empty=new X([""],0);function uf(n){let e=-1;for(let t of n)e+=t.length+1;return e}function rn(n,e,t=0,i=1e9){for(let s=0,r=0,o=!0;r<n.length&&s<=i;r++){let l=n[r],a=s+l.length;a>=t&&(a>i&&(l=l.slice(0,i-s)),s<t&&(l=l.slice(t-s)),o?(e[e.length-1]+=l,o=!1):e.push(l)),s=a+1}return e}function eo(n,e,t){return rn(n,[""],e,t)}class gi{constructor(e,t=1){this.dir=t,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[e],this.offsets=[t>0?1:(e instanceof X?e.text.length:e.children.length)<<1]}nextInner(e,t){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,s=this.nodes[i],r=this.offsets[i],o=r>>1,l=s instanceof X?s.text.length:s.children.length;if(o==(t>0?l:0)){if(i==0)return this.done=!0,this.value="",this;t>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((r&1)==(t>0?0:1)){if(this.offsets[i]+=t,e==0)return this.lineBreak=!0,this.value=`
`,this;e--}else if(s instanceof X){let a=s.text[o+(t<0?-1:0)];if(this.offsets[i]+=t,a.length>Math.max(0,e))return this.value=e==0?a:t>0?a.slice(e):a.slice(0,a.length-e),this;e-=a.length}else{let a=s.children[o+(t<0?-1:0)];e>a.length?(e-=a.length,this.offsets[i]+=t):(t<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(t>0?1:(a instanceof X?a.text.length:a.children.length)<<1))}}}next(e=0){return e<0&&(this.nextInner(-e,-this.dir),e=this.value.length),this.nextInner(e,this.dir)}}class la{constructor(e,t,i){this.value="",this.done=!1,this.cursor=new gi(e,t>i?-1:1),this.pos=t>i?e.length:0,this.from=Math.min(t,i),this.to=Math.max(t,i)}nextInner(e,t){if(t<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;e+=Math.max(0,t<0?this.pos-this.to:this.from-this.pos);let i=t<0?this.pos-this.from:this.to-this.pos;e>i&&(e=i),i-=e;let{value:s}=this.cursor.next(e);return this.pos+=(s.length+e)*t,this.value=s.length<=i?s:t<0?s.slice(s.length-i):s.slice(0,i),this.done=!this.value,this}next(e=0){return e<0?e=Math.max(e,this.from-this.pos):e>0&&(e=Math.min(e,this.to-this.pos)),this.nextInner(e,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class aa{constructor(e){this.inner=e,this.afterBreak=!0,this.value="",this.done=!1}next(e=0){let{done:t,lineBreak:i,value:s}=this.inner.next(e);return t?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=s,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(V.prototype[Symbol.iterator]=function(){return this.iter()},gi.prototype[Symbol.iterator]=la.prototype[Symbol.iterator]=aa.prototype[Symbol.iterator]=function(){return this});class df{constructor(e,t,i,s){this.from=e,this.to=t,this.number=i,this.text=s}get length(){return this.to-this.from}}let qt="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(n=>n?parseInt(n,36):1);for(let n=1;n<qt.length;n++)qt[n]+=qt[n-1];function pf(n){for(let e=1;e<qt.length;e+=2)if(qt[e]>n)return qt[e-1]<=n;return!1}function to(n){return n>=127462&&n<=127487}const io=8205;function Ae(n,e,t=!0,i=!0){return(t?ha:mf)(n,e,i)}function ha(n,e,t){if(e==n.length)return e;e&&ca(n.charCodeAt(e))&&fa(n.charCodeAt(e-1))&&e--;let i=ue(n,e);for(e+=De(i);e<n.length;){let s=ue(n,e);if(i==io||s==io||t&&pf(s))e+=De(s),i=s;else if(to(s)){let r=0,o=e-2;for(;o>=0&&to(ue(n,o));)r++,o-=2;if(r%2==0)break;e+=2}else break}return e}function mf(n,e,t){for(;e>0;){let i=ha(n,e-2,t);if(i<e)return i;e--}return 0}function ca(n){return n>=56320&&n<57344}function fa(n){return n>=55296&&n<56320}function ue(n,e){let t=n.charCodeAt(e);if(!fa(t)||e+1==n.length)return t;let i=n.charCodeAt(e+1);return ca(i)?(t-55296<<10)+(i-56320)+65536:t}function ua(n){return n<=65535?String.fromCharCode(n):(n-=65536,String.fromCharCode((n>>10)+55296,(n&1023)+56320))}function De(n){return n<65536?1:2}const Ms=/\r\n?|\n/;var oe=function(n){return n[n.Simple=0]="Simple",n[n.TrackDel=1]="TrackDel",n[n.TrackBefore=2]="TrackBefore",n[n.TrackAfter=3]="TrackAfter",n}(oe||(oe={}));class Je{constructor(e){this.sections=e}get length(){let e=0;for(let t=0;t<this.sections.length;t+=2)e+=this.sections[t];return e}get newLength(){let e=0;for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t+1];e+=i<0?this.sections[t]:i}return e}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(e){for(let t=0,i=0,s=0;t<this.sections.length;){let r=this.sections[t++],o=this.sections[t++];o<0?(e(i,s,r),s+=r):s+=o,i+=r}}iterChangedRanges(e,t=!1){Ds(this,e,t)}get invertedDesc(){let e=[];for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];s<0?e.push(i,s):e.push(s,i)}return new Je(e)}composeDesc(e){return this.empty?e:e.empty?this:da(this,e)}mapDesc(e,t=!1){return e.empty?this:Ts(this,e,t)}mapPos(e,t=-1,i=oe.Simple){let s=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=s+l;if(a<0){if(h>e)return r+(e-s);r+=l}else{if(i!=oe.Simple&&h>=e&&(i==oe.TrackDel&&s<e&&h>e||i==oe.TrackBefore&&s<e||i==oe.TrackAfter&&h>e))return null;if(h>e||h==e&&t<0&&!l)return e==s||t<0?r:r+a;r+=a}s=h}if(e>s)throw new RangeError(`Position ${e} is out of range for changeset of length ${s}`);return r}touchesRange(e,t=e){for(let i=0,s=0;i<this.sections.length&&s<=t;){let r=this.sections[i++],o=this.sections[i++],l=s+r;if(o>=0&&s<=t&&l>=e)return s<e&&l>t?"cover":!0;s=l}return!1}toString(){let e="";for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];e+=(e?" ":"")+i+(s>=0?":"+s:"")}return e}toJSON(){return this.sections}static fromJSON(e){if(!Array.isArray(e)||e.length%2||e.some(t=>typeof t!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new Je(e)}static create(e){return new Je(e)}}class Z extends Je{constructor(e,t){super(e),this.inserted=t}apply(e){if(this.length!=e.length)throw new RangeError("Applying change set to a document with the wrong length");return Ds(this,(t,i,s,r,o)=>e=e.replace(s,s+(i-t),o),!1),e}mapDesc(e,t=!1){return Ts(this,e,t,!0)}invert(e){let t=this.sections.slice(),i=[];for(let s=0,r=0;s<t.length;s+=2){let o=t[s],l=t[s+1];if(l>=0){t[s]=l,t[s+1]=o;let a=s>>1;for(;i.length<a;)i.push(V.empty);i.push(o?e.slice(r,r+o):V.empty)}r+=o}return new Z(t,i)}compose(e){return this.empty?e:e.empty?this:da(this,e,!0)}map(e,t=!1){return e.empty?this:Ts(this,e,t,!0)}iterChanges(e,t=!1){Ds(this,e,t)}get desc(){return Je.create(this.sections)}filter(e){let t=[],i=[],s=[],r=new vi(this);e:for(let o=0,l=0;;){let a=o==e.length?1e9:e[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break e;let c=Math.min(r.len,a-l);fe(s,c,-1);let f=r.ins==-1?-1:r.off==0?r.ins:0;fe(t,c,f),f>0&&lt(i,t,r.text),r.forward(c),l+=c}let h=e[o++];for(;l<h;){if(r.done)break e;let c=Math.min(r.len,h-l);fe(t,c,-1),fe(s,c,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(c),l+=c}}return{changes:new Z(t,i),filtered:Je.create(s)}}toJSON(){let e=[];for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t],s=this.sections[t+1];s<0?e.push(i):s==0?e.push([i]):e.push([i].concat(this.inserted[t>>1].toJSON()))}return e}static of(e,t,i){let s=[],r=[],o=0,l=null;function a(c=!1){if(!c&&!s.length)return;o<t&&fe(s,t-o,-1);let f=new Z(s,r);l=l?l.compose(f.map(l)):f,s=[],r=[],o=0}function h(c){if(Array.isArray(c))for(let f of c)h(f);else if(c instanceof Z){if(c.length!=t)throw new RangeError(`Mismatched change set length (got ${c.length}, expected ${t})`);a(),l=l?l.compose(c.map(l)):c}else{let{from:f,to:u=f,insert:d}=c;if(f>u||f<0||u>t)throw new RangeError(`Invalid change range ${f} to ${u} (in doc of length ${t})`);let p=d?typeof d=="string"?V.of(d.split(i||Ms)):d:V.empty,g=p.length;if(f==u&&g==0)return;f<o&&a(),f>o&&fe(s,f-o,-1),fe(s,u-f,g),lt(r,s,p),o=u}}return h(e),a(!l),l}static empty(e){return new Z(e?[e,-1]:[],[])}static fromJSON(e){if(!Array.isArray(e))throw new RangeError("Invalid JSON representation of ChangeSet");let t=[],i=[];for(let s=0;s<e.length;s++){let r=e[s];if(typeof r=="number")t.push(r,-1);else{if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)t.push(r[0],0);else{for(;i.length<s;)i.push(V.empty);i[s]=V.of(r.slice(1)),t.push(r[0],i[s].length)}}}return new Z(t,i)}static createSet(e,t){return new Z(e,t)}}function fe(n,e,t,i=!1){if(e==0&&t<=0)return;let s=n.length-2;s>=0&&t<=0&&t==n[s+1]?n[s]+=e:e==0&&n[s]==0?n[s+1]+=t:i?(n[s]+=e,n[s+1]+=t):n.push(e,t)}function lt(n,e,t){if(t.length==0)return;let i=e.length-2>>1;if(i<n.length)n[n.length-1]=n[n.length-1].append(t);else{for(;n.length<i;)n.push(V.empty);n.push(t)}}function Ds(n,e,t){let i=n.inserted;for(let s=0,r=0,o=0;o<n.sections.length;){let l=n.sections[o++],a=n.sections[o++];if(a<0)s+=l,r+=l;else{let h=s,c=r,f=V.empty;for(;h+=l,c+=a,a&&i&&(f=f.append(i[o-2>>1])),!(t||o==n.sections.length||n.sections[o+1]<0);)l=n.sections[o++],a=n.sections[o++];e(s,h,r,c,f),s=h,r=c}}}function Ts(n,e,t,i=!1){let s=[],r=i?[]:null,o=new vi(n),l=new vi(e);for(let a=-1;;)if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);fe(s,h,-1),o.forward(h),l.forward(h)}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!t))){let h=l.len;for(fe(s,l.ins,-1);h;){let c=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=c&&(fe(s,0,o.ins),r&&lt(r,s,o.text),a=o.i),o.forward(c),h-=c}l.next()}else if(o.ins>=0){let h=0,c=o.len;for(;c;)if(l.ins==-1){let f=Math.min(c,l.len);h+=f,c-=f,l.forward(f)}else if(l.ins==0&&l.len<c)c-=l.len,l.next();else break;fe(s,h,a<o.i?o.ins:0),r&&a<o.i&&lt(r,s,o.text),a=o.i,o.forward(o.len-c)}else{if(o.done&&l.done)return r?Z.createSet(s,r):Je.create(s);throw new Error("Mismatched change set lengths")}}function da(n,e,t=!1){let i=[],s=t?[]:null,r=new vi(n),o=new vi(e);for(let l=!1;;){if(r.done&&o.done)return s?Z.createSet(i,s):Je.create(i);if(r.ins==0)fe(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)fe(i,0,o.ins,l),s&&lt(s,i,o.text),o.next();else{if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let c=o.ins==-1?-1:o.off?0:o.ins;fe(i,a,c,l),s&&c&&lt(s,i,o.text)}else o.ins==-1?(fe(i,r.off?0:r.len,a,l),s&&lt(s,i,r.textBit(a))):(fe(i,r.off?0:r.len,o.off?0:o.ins,l),s&&!o.off&&lt(s,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a)}}}}class vi{constructor(e){this.set=e,this.i=0,this.next()}next(){let{sections:e}=this.set;this.i<e.length?(this.len=e[this.i++],this.ins=e[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:e}=this.set,t=this.i-2>>1;return t>=e.length?V.empty:e[t]}textBit(e){let{inserted:t}=this.set,i=this.i-2>>1;return i>=t.length&&!e?V.empty:t[i].slice(this.off,e==null?void 0:this.off+e)}forward(e){e==this.len?this.next():(this.len-=e,this.off+=e)}forward2(e){this.ins==-1?this.forward(e):e==this.ins?this.next():(this.ins-=e,this.off+=e)}}class xt{constructor(e,t,i){this.from=e,this.to=t,this.flags=i}get anchor(){return this.flags&16?this.to:this.from}get head(){return this.flags&16?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&4?-1:this.flags&8?1:0}get bidiLevel(){let e=this.flags&3;return e==3?null:e}get goalColumn(){let e=this.flags>>5;return e==33554431?void 0:e}map(e,t=-1){let i,s;return this.empty?i=s=e.mapPos(this.from,t):(i=e.mapPos(this.from,1),s=e.mapPos(this.to,-1)),i==this.from&&s==this.to?this:new xt(i,s,this.flags)}extend(e,t=e){if(e<=this.anchor&&t>=this.anchor)return w.range(e,t);let i=Math.abs(e-this.anchor)>Math.abs(t-this.anchor)?e:t;return w.range(this.anchor,i)}eq(e){return this.anchor==e.anchor&&this.head==e.head}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(e){if(!e||typeof e.anchor!="number"||typeof e.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return w.range(e.anchor,e.head)}static create(e,t,i){return new xt(e,t,i)}}class w{constructor(e,t){this.ranges=e,this.mainIndex=t}map(e,t=-1){return e.empty?this:w.create(this.ranges.map(i=>i.map(e,t)),this.mainIndex)}eq(e){if(this.ranges.length!=e.ranges.length||this.mainIndex!=e.mainIndex)return!1;for(let t=0;t<this.ranges.length;t++)if(!this.ranges[t].eq(e.ranges[t]))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new w([this.main],0)}addRange(e,t=!0){return w.create([e].concat(this.ranges),t?0:this.mainIndex+1)}replaceRange(e,t=this.mainIndex){let i=this.ranges.slice();return i[t]=e,w.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(e=>e.toJSON()),main:this.mainIndex}}static fromJSON(e){if(!e||!Array.isArray(e.ranges)||typeof e.main!="number"||e.main>=e.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new w(e.ranges.map(t=>xt.fromJSON(t)),e.main)}static single(e,t=e){return new w([w.range(e,t)],0)}static create(e,t=0){if(e.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,s=0;s<e.length;s++){let r=e[s];if(r.empty?r.from<=i:r.from<i)return w.normalized(e.slice(),t);i=r.to}return new w(e,t)}static cursor(e,t=0,i,s){return xt.create(e,e,(t==0?0:t<0?4:8)|(i==null?3:Math.min(2,i))|(s??33554431)<<5)}static range(e,t,i){let s=(i??33554431)<<5;return t<e?xt.create(t,e,16|s|8):xt.create(e,t,s|(t>e?4:0))}static normalized(e,t=0){let i=e[t];e.sort((s,r)=>s.from-r.from),t=e.indexOf(i);for(let s=1;s<e.length;s++){let r=e[s],o=e[s-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);s<=t&&t--,e.splice(--s,2,r.anchor>r.head?w.range(a,l):w.range(l,a))}}return new w(e,t)}}function pa(n,e){for(let t of n.ranges)if(t.to>e)throw new RangeError("Selection points outside of document")}let br=0;class T{constructor(e,t,i,s,r){this.combine=e,this.compareInput=t,this.compare=i,this.isStatic=s,this.id=br++,this.default=e([]),this.extensions=typeof r=="function"?r(this):r}static define(e={}){return new T(e.combine||(t=>t),e.compareInput||((t,i)=>t===i),e.compare||(e.combine?(t,i)=>t===i:wr),!!e.static,e.enables)}of(e){return new on([],this,0,e)}compute(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new on(e,this,1,t)}computeN(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new on(e,this,2,t)}from(e,t){return t||(t=i=>i),this.compute([e],i=>t(i.field(e)))}}function wr(n,e){return n==e||n.length==e.length&&n.every((t,i)=>t===e[i])}class on{constructor(e,t,i,s){this.dependencies=e,this.facet=t,this.type=i,this.value=s,this.id=br++}dynamicSlot(e){var t;let i=this.value,s=this.facet.compareInput,r=this.id,o=e[r]>>1,l=this.type==2,a=!1,h=!1,c=[];for(let f of this.dependencies)f=="doc"?a=!0:f=="selection"?h=!0:((t=e[f.id])!==null&&t!==void 0?t:1)&1||c.push(e[f.id]);return{create(f){return f.values[o]=i(f),1},update(f,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||Os(f,c)){let d=i(f);if(l?!no(d,f.values[o],s):!s(d,f.values[o]))return f.values[o]=d,1}return 0},reconfigure:(f,u)=>{let d=i(f),p=u.config.address[r];if(p!=null){let g=pn(u,p);if(this.dependencies.every(y=>y instanceof T?u.facet(y)===f.facet(y):y instanceof xe?u.field(y,!1)==f.field(y,!1):!0)||(l?no(d,g,s):s(d,g)))return f.values[o]=g,0}return f.values[o]=d,1}}}}function no(n,e,t){if(n.length!=e.length)return!1;for(let i=0;i<n.length;i++)if(!t(n[i],e[i]))return!1;return!0}function Os(n,e){let t=!1;for(let i of e)yi(n,i)&1&&(t=!0);return t}function gf(n,e,t){let i=t.map(a=>n[a.id]),s=t.map(a=>a.type),r=i.filter(a=>!(a&1)),o=n[e.id]>>1;function l(a){let h=[];for(let c=0;c<i.length;c++){let f=pn(a,i[c]);if(s[c]==2)for(let u of f)h.push(u);else h.push(f)}return e.combine(h)}return{create(a){for(let h of i)yi(a,h);return a.values[o]=l(a),1},update(a,h){if(!Os(a,r))return 0;let c=l(a);return e.compare(c,a.values[o])?0:(a.values[o]=c,1)},reconfigure(a,h){let c=Os(a,i),f=h.config.facets[e.id],u=h.facet(e);if(f&&!c&&wr(t,f))return a.values[o]=u,0;let d=l(a);return e.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const so=T.define({static:!0});class xe{constructor(e,t,i,s,r){this.id=e,this.createF=t,this.updateF=i,this.compareF=s,this.spec=r,this.provides=void 0}static define(e){let t=new xe(br++,e.create,e.update,e.compare||((i,s)=>i===s),e);return e.provide&&(t.provides=e.provide(t)),t}create(e){let t=e.facet(so).find(i=>i.field==this);return(t?.create||this.createF)(e)}slot(e){let t=e[this.id]>>1;return{create:i=>(i.values[t]=this.create(i),1),update:(i,s)=>{let r=i.values[t],o=this.updateF(r,s);return this.compareF(r,o)?0:(i.values[t]=o,1)},reconfigure:(i,s)=>s.config.address[this.id]!=null?(i.values[t]=s.field(this),0):(i.values[t]=this.create(i),1)}}init(e){return[this,so.of({field:this,create:e})]}get extension(){return this}}const wt={lowest:4,low:3,default:2,high:1,highest:0};function si(n){return e=>new ma(e,n)}const Li={highest:si(wt.highest),high:si(wt.high),default:si(wt.default),low:si(wt.low),lowest:si(wt.lowest)};class ma{constructor(e,t){this.inner=e,this.prec=t}}class Rn{of(e){return new Bs(this,e)}reconfigure(e){return Rn.reconfigure.of({compartment:this,extension:e})}get(e){return e.config.compartments.get(this)}}class Bs{constructor(e,t){this.compartment=e,this.inner=t}}class dn{constructor(e,t,i,s,r,o){for(this.base=e,this.compartments=t,this.dynamicSlots=i,this.address=s,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(e){let t=this.address[e.id];return t==null?e.default:this.staticValues[t>>1]}static resolve(e,t,i){let s=[],r=Object.create(null),o=new Map;for(let u of yf(e,t,o))u instanceof xe?s.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of s)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let c=i?.config.facets;for(let u in r){let d=r[u],p=d[0].facet,g=c&&c[u]||[];if(d.every(y=>y.type==0))if(l[p.id]=a.length<<1|1,wr(g,d))a.push(i.facet(p));else{let y=p.combine(d.map(b=>b.value));a.push(i&&p.compare(y,i.facet(p))?i.facet(p):y)}else{for(let y of d)y.type==0?(l[y.id]=a.length<<1|1,a.push(y.value)):(l[y.id]=h.length<<1,h.push(b=>y.dynamicSlot(b)));l[p.id]=h.length<<1,h.push(y=>gf(y,p,d))}}let f=h.map(u=>u(l));return new dn(e,o,f,l,a,r)}}function yf(n,e,t){let i=[[],[],[],[],[]],s=new Map;function r(o,l){let a=s.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof Bs&&t.delete(o.compartment)}if(s.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof Bs){if(t.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=e.get(o.compartment)||o.inner;t.set(o.compartment,h),r(h,l)}else if(o instanceof ma)r(o.inner,o.prec);else if(o instanceof xe)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof on)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,wt.default);else{let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l)}}return r(n,wt.default),i.reduce((o,l)=>o.concat(l))}function yi(n,e){if(e&1)return 2;let t=e>>1,i=n.status[t];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;n.status[t]=4;let s=n.computeSlot(n,n.config.dynamicSlots[t]);return n.status[t]=2|s}function pn(n,e){return e&1?n.config.staticValues[e>>1]:n.values[e>>1]}const ga=T.define(),ya=T.define({combine:n=>n.some(e=>e),static:!0}),ba=T.define({combine:n=>n.length?n[0]:void 0,static:!0}),wa=T.define(),ka=T.define(),xa=T.define(),va=T.define({combine:n=>n.length?n[0]:!1});class Rt{constructor(e,t){this.type=e,this.value=t}static define(){return new bf}}class bf{of(e){return new Rt(this,e)}}class wf{constructor(e){this.map=e}of(e){return new R(this,e)}}class R{constructor(e,t){this.type=e,this.value=t}map(e){let t=this.type.map(this.value,e);return t===void 0?void 0:t==this.value?this:new R(this.type,t)}is(e){return this.type==e}static define(e={}){return new wf(e.map||(t=>t))}static mapEffects(e,t){if(!e.length)return e;let i=[];for(let s of e){let r=s.map(t);r&&i.push(r)}return i}}R.reconfigure=R.define();R.appendConfig=R.define();class ee{constructor(e,t,i,s,r,o){this.startState=e,this.changes=t,this.selection=i,this.effects=s,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&pa(i,t.newLength),r.some(l=>l.type==ee.time)||(this.annotations=r.concat(ee.time.of(Date.now())))}static create(e,t,i,s,r,o){return new ee(e,t,i,s,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(e){for(let t of this.annotations)if(t.type==e)return t.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(e){let t=this.annotation(ee.userEvent);return!!(t&&(t==e||t.length>e.length&&t.slice(0,e.length)==e&&t[e.length]=="."))}}ee.time=Rt.define();ee.userEvent=Rt.define();ee.addToHistory=Rt.define();ee.remote=Rt.define();function kf(n,e){let t=[];for(let i=0,s=0;;){let r,o;if(i<n.length&&(s==e.length||e[s]>=n[i]))r=n[i++],o=n[i++];else if(s<e.length)r=e[s++],o=e[s++];else return t;!t.length||t[t.length-1]<r?t.push(r,o):t[t.length-1]<o&&(t[t.length-1]=o)}}function Sa(n,e,t){var i;let s,r,o;return t?(s=e.changes,r=Z.empty(e.changes.length),o=n.changes.compose(e.changes)):(s=e.changes.map(n.changes),r=n.changes.mapDesc(e.changes,!0),o=n.changes.compose(s)),{changes:o,selection:e.selection?e.selection.map(r):(i=n.selection)===null||i===void 0?void 0:i.map(s),effects:R.mapEffects(n.effects,s).concat(R.mapEffects(e.effects,r)),annotations:n.annotations.length?n.annotations.concat(e.annotations):e.annotations,scrollIntoView:n.scrollIntoView||e.scrollIntoView}}function Es(n,e,t){let i=e.selection,s=jt(e.annotations);return e.userEvent&&(s=s.concat(ee.userEvent.of(e.userEvent))),{changes:e.changes instanceof Z?e.changes:Z.of(e.changes||[],t,n.facet(ba)),selection:i&&(i instanceof w?i:w.single(i.anchor,i.head)),effects:jt(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function Ca(n,e,t){let i=Es(n,e.length?e[0]:{},n.doc.length);e.length&&e[0].filter===!1&&(t=!1);for(let r=1;r<e.length;r++){e[r].filter===!1&&(t=!1);let o=!!e[r].sequential;i=Sa(i,Es(n,e[r],o?i.changes.newLength:n.doc.length),o)}let s=ee.create(n,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return vf(t?xf(s):s)}function xf(n){let e=n.startState,t=!0;for(let s of e.facet(wa)){let r=s(n);if(r===!1){t=!1;break}Array.isArray(r)&&(t=t===!0?r:kf(t,r))}if(t!==!0){let s,r;if(t===!1)r=n.changes.invertedDesc,s=Z.empty(e.doc.length);else{let o=n.changes.filter(t);s=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc}n=ee.create(e,s,n.selection&&n.selection.map(r),R.mapEffects(n.effects,r),n.annotations,n.scrollIntoView)}let i=e.facet(ka);for(let s=i.length-1;s>=0;s--){let r=i[s](n);r instanceof ee?n=r:Array.isArray(r)&&r.length==1&&r[0]instanceof ee?n=r[0]:n=Ca(e,jt(r),!1)}return n}function vf(n){let e=n.startState,t=e.facet(xa),i=n;for(let s=t.length-1;s>=0;s--){let r=t[s](n);r&&Object.keys(r).length&&(i=Sa(i,Es(e,r,n.changes.newLength),!0))}return i==n?n:ee.create(e,n.changes,n.selection,i.effects,i.annotations,i.scrollIntoView)}const Sf=[];function jt(n){return n==null?Sf:Array.isArray(n)?n:[n]}var Te=function(n){return n[n.Word=0]="Word",n[n.Space=1]="Space",n[n.Other=2]="Other",n}(Te||(Te={}));const Cf=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let Ps;try{Ps=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function Af(n){if(Ps)return Ps.test(n);for(let e=0;e<n.length;e++){let t=n[e];if(/\w/.test(t)||t>""&&(t.toUpperCase()!=t.toLowerCase()||Cf.test(t)))return!0}return!1}function Mf(n){return e=>{if(!/\S/.test(e))return Te.Space;if(Af(e))return Te.Word;for(let t=0;t<n.length;t++)if(e.indexOf(n[t])>-1)return Te.Word;return Te.Other}}class N{constructor(e,t,i,s,r,o){this.config=e,this.doc=t,this.selection=i,this.values=s,this.status=e.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)yi(this,l<<1);this.computeSlot=null}field(e,t=!0){let i=this.config.address[e.id];if(i==null){if(t)throw new RangeError("Field is not present in this state");return}return yi(this,i),pn(this,i)}update(...e){return Ca(this,e,!0)}applyTransaction(e){let t=this.config,{base:i,compartments:s}=t;for(let o of e.effects)o.is(Rn.reconfigure)?(t&&(s=new Map,t.compartments.forEach((l,a)=>s.set(a,l)),t=null),s.set(o.value.compartment,o.value.extension)):o.is(R.reconfigure)?(t=null,i=o.value):o.is(R.appendConfig)&&(t=null,i=jt(i).concat(o.value));let r;t?r=e.startState.values.slice():(t=dn.resolve(i,s,this),r=new N(t,this.doc,this.selection,t.dynamicSlots.map(()=>null),(l,a)=>a.reconfigure(l,this),null).values),new N(t,e.newDoc,e.newSelection,r,(o,l)=>l.update(o,e),e)}replaceSelection(e){return typeof e=="string"&&(e=this.toText(e)),this.changeByRange(t=>({changes:{from:t.from,to:t.to,insert:e},range:w.cursor(t.from+e.length)}))}changeByRange(e){let t=this.selection,i=e(t.ranges[0]),s=this.changes(i.changes),r=[i.range],o=jt(i.effects);for(let l=1;l<t.ranges.length;l++){let a=e(t.ranges[l]),h=this.changes(a.changes),c=h.map(s);for(let u=0;u<l;u++)r[u]=r[u].map(c);let f=s.mapDesc(h,!0);r.push(a.range.map(f)),s=s.compose(c),o=R.mapEffects(o,c).concat(R.mapEffects(jt(a.effects),f))}return{changes:s,selection:w.create(r,t.mainIndex),effects:o}}changes(e=[]){return e instanceof Z?e:Z.of(e,this.doc.length,this.facet(N.lineSeparator))}toText(e){return V.of(e.split(this.facet(N.lineSeparator)||Ms))}sliceDoc(e=0,t=this.doc.length){return this.doc.sliceString(e,t,this.lineBreak)}facet(e){let t=this.config.address[e.id];return t==null?e.default:(yi(this,t),pn(this,t))}toJSON(e){let t={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(e)for(let i in e){let s=e[i];s instanceof xe&&this.config.address[s.id]!=null&&(t[i]=s.spec.toJSON(this.field(e[i]),this))}return t}static fromJSON(e,t={},i){if(!e||typeof e.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let s=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(e,r)){let o=i[r],l=e[r];s.push(o.init(a=>o.spec.fromJSON(l,a)))}}return N.create({doc:e.doc,selection:w.fromJSON(e.selection),extensions:t.extensions?s.concat([t.extensions]):s})}static create(e={}){let t=dn.resolve(e.extensions||[],new Map),i=e.doc instanceof V?e.doc:V.of((e.doc||"").split(t.staticFacet(N.lineSeparator)||Ms)),s=e.selection?e.selection instanceof w?e.selection:w.single(e.selection.anchor,e.selection.head):w.single(0);return pa(s,i.length),t.staticFacet(ya)||(s=s.asSingle()),new N(t,i,s,t.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(N.tabSize)}get lineBreak(){return this.facet(N.lineSeparator)||`
`}get readOnly(){return this.facet(va)}phrase(e,...t){for(let i of this.facet(N.phrases))if(Object.prototype.hasOwnProperty.call(i,e)){e=i[e];break}return t.length&&(e=e.replace(/\$(\$|\d*)/g,(i,s)=>{if(s=="$")return"$";let r=+(s||1);return!r||r>t.length?i:t[r-1]})),e}languageDataAt(e,t,i=-1){let s=[];for(let r of this.facet(ga))for(let o of r(this,t,i))Object.prototype.hasOwnProperty.call(o,e)&&s.push(o[e]);return s}charCategorizer(e){return Mf(this.languageDataAt("wordChars",e).join(""))}wordAt(e){let{text:t,from:i,length:s}=this.doc.lineAt(e),r=this.charCategorizer(e),o=e-i,l=e-i;for(;o>0;){let a=Ae(t,o,!1);if(r(t.slice(a,o))!=Te.Word)break;o=a}for(;l<s;){let a=Ae(t,l);if(r(t.slice(l,a))!=Te.Word)break;l=a}return o==l?null:w.range(o+i,l+i)}}N.allowMultipleSelections=ya;N.tabSize=T.define({combine:n=>n.length?n[0]:4});N.lineSeparator=ba;N.readOnly=va;N.phrases=T.define({compare(n,e){let t=Object.keys(n),i=Object.keys(e);return t.length==i.length&&t.every(s=>n[s]==e[s])}});N.languageData=ga;N.changeFilter=wa;N.transactionFilter=ka;N.transactionExtender=xa;Rn.reconfigure=R.define();function _t(n,e,t={}){let i={};for(let s of n)for(let r of Object.keys(s)){let o=s[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(t,r))i[r]=t[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let s in e)i[s]===void 0&&(i[s]=e[s]);return i}class At{eq(e){return this==e}range(e,t=e){return Ls.create(e,t,this)}}At.prototype.startSide=At.prototype.endSide=0;At.prototype.point=!1;At.prototype.mapMode=oe.TrackDel;let Ls=class Aa{constructor(e,t,i){this.from=e,this.to=t,this.value=i}static create(e,t,i){return new Aa(e,t,i)}};function Rs(n,e){return n.from-e.from||n.value.startSide-e.value.startSide}class kr{constructor(e,t,i,s){this.from=e,this.to=t,this.value=i,this.maxPoint=s}get length(){return this.to[this.to.length-1]}findIndex(e,t,i,s=0){let r=i?this.to:this.from;for(let o=s,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-e||(i?this.value[a].endSide:this.value[a].startSide)-t;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1}}between(e,t,i,s){for(let r=this.findIndex(t,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(s(this.from[r]+e,this.to[r]+e,this.value[r])===!1)return!1}map(e,t){let i=[],s=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],c=this.from[a]+e,f=this.to[a]+e,u,d;if(c==f){let p=t.mapPos(c,h.startSide,h.mapMode);if(p==null||(u=d=p,h.startSide!=h.endSide&&(d=t.mapPos(c,h.endSide),d<u)))continue}else if(u=t.mapPos(c,h.startSide),d=t.mapPos(f,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),s.push(u-o),r.push(d-o))}return{mapped:i.length?new kr(s,r,i,l):null,pos:o}}}class H{constructor(e,t,i,s){this.chunkPos=e,this.chunk=t,this.nextLayer=i,this.maxPoint=s}static create(e,t,i,s){return new H(e,t,i,s)}get length(){let e=this.chunk.length-1;return e<0?0:Math.max(this.chunkEnd(e),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let e=this.nextLayer.size;for(let t of this.chunk)e+=t.value.length;return e}chunkEnd(e){return this.chunkPos[e]+this.chunk[e].length}update(e){let{add:t=[],sort:i=!1,filterFrom:s=0,filterTo:r=this.length}=e,o=e.filter;if(t.length==0&&!o)return this;if(i&&(t=t.slice().sort(Rs)),this.isEmpty)return t.length?H.of(t):this;let l=new Ma(this,null,-1).goto(0),a=0,h=[],c=new Mt;for(;l.value||a<t.length;)if(a<t.length&&(l.from-t[a].from||l.startSide-t[a].value.startSide)>=0){let f=t[a++];c.addInner(f.from,f.to,f.value)||h.push(f)}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==t.length||this.chunkEnd(l.chunkIndex)<t[a].from)&&(!o||s>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&c.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||s>l.to||r<l.from||o(l.from,l.to,l.value))&&(c.addInner(l.from,l.to,l.value)||h.push(Ls.create(l.from,l.to,l.value))),l.next());return c.finishInner(this.nextLayer.isEmpty&&!h.length?H.empty:this.nextLayer.update({add:h,filter:o,filterFrom:s,filterTo:r}))}map(e){if(e.empty||this.isEmpty)return this;let t=[],i=[],s=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=e.touchesRange(l,l+a.length);if(h===!1)s=Math.max(s,a.maxPoint),t.push(a),i.push(e.mapPos(l));else if(h===!0){let{mapped:c,pos:f}=a.map(l,e);c&&(s=Math.max(s,c.maxPoint),t.push(c),i.push(f))}}let r=this.nextLayer.map(e);return t.length==0?r:new H(i,t,r||H.empty,s)}between(e,t,i){if(!this.isEmpty){for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],o=this.chunk[s];if(t>=r&&e<=r+o.length&&o.between(r,e-r,t-r,i)===!1)return}this.nextLayer.between(e,t,i)}}iter(e=0){return Si.from([this]).goto(e)}get isEmpty(){return this.nextLayer==this}static iter(e,t=0){return Si.from(e).goto(t)}static compare(e,t,i,s,r=-1){let o=e.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),l=t.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),a=ro(o,l,i),h=new ri(o,a,r),c=new ri(l,a,r);i.iterGaps((f,u,d)=>oo(h,f,c,u,d,s)),i.empty&&i.length==0&&oo(h,0,c,0,0,s)}static eq(e,t,i=0,s){s==null&&(s=1e9);let r=e.filter(c=>!c.isEmpty&&t.indexOf(c)<0),o=t.filter(c=>!c.isEmpty&&e.indexOf(c)<0);if(r.length!=o.length)return!1;if(!r.length)return!0;let l=ro(r,o),a=new ri(r,l,0).goto(i),h=new ri(o,l,0).goto(i);for(;;){if(a.to!=h.to||!_s(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return!1;if(a.to>s)return!0;a.next(),h.next()}}static spans(e,t,i,s,r=-1){let o=new ri(e,null,r).goto(t),l=t,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point?(s.point(l,h,o.point,o.activeForPoint(o.to),a,o.pointRank),a=o.openEnd(h)+(o.to>h?1:0)):h>l&&(s.span(l,h,o.active,a),a=o.openEnd(h)),o.to>i)break;l=o.to,o.next()}return a}static of(e,t=!1){let i=new Mt;for(let s of e instanceof Ls?[e]:t?Df(e):e)i.add(s.from,s.to,s.value);return i.finish()}}H.empty=new H([],[],null,-1);function Df(n){if(n.length>1)for(let e=n[0],t=1;t<n.length;t++){let i=n[t];if(Rs(e,i)>0)return n.slice().sort(Rs);e=i}return n}H.empty.nextLayer=H.empty;class Mt{constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}finishChunk(e){this.chunks.push(new kr(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,e&&(this.from=[],this.to=[],this.value=[])}add(e,t,i){this.addInner(e,t,i)||(this.nextLayer||(this.nextLayer=new Mt)).add(e,t,i)}addInner(e,t,i){let s=e-this.lastTo||i.startSide-this.last.endSide;if(s<=0&&(e-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return s<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=e),this.from.push(e-this.chunkStart),this.to.push(t-this.chunkStart),this.last=i,this.lastFrom=e,this.lastTo=t,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,t-e)),!0)}addChunk(e,t){if((e-this.lastTo||t.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,t.maxPoint),this.chunks.push(t),this.chunkPos.push(e);let i=t.value.length-1;return this.last=t.value[i],this.lastFrom=t.from[i]+e,this.lastTo=t.to[i]+e,!0}finish(){return this.finishInner(H.empty)}finishInner(e){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return e;let t=H.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(e):e,this.setMaxPoint);return this.from=null,t}}function ro(n,e,t){let i=new Map;for(let r of n)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let s=new Set;for(let r of e)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(t?t.mapPos(l):l)==r.chunkPos[o]&&!t?.touchesRange(l,l+r.chunk[o].length)&&s.add(r.chunk[o])}return s}class Ma{constructor(e,t,i,s=0){this.layer=e,this.skip=t,this.minPoint=i,this.rank=s}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(e,t=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(e,t,!1),this}gotoInner(e,t,i){for(;this.chunkIndex<this.layer.chunk.length;){let s=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(s)||this.layer.chunkEnd(this.chunkIndex)<e||s.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let s=this.layer.chunk[this.chunkIndex].findIndex(e-this.layer.chunkPos[this.chunkIndex],t,!0);(!i||this.rangeIndex<s)&&this.setRangeIndex(s)}this.next()}forward(e,t){(this.to-e||this.endSide-t)<0&&this.gotoInner(e,t,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let e=this.layer.chunkPos[this.chunkIndex],t=this.layer.chunk[this.chunkIndex],i=e+t.from[this.rangeIndex];if(this.from=i,this.to=e+t.to[this.rangeIndex],this.value=t.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(e){if(e==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=e}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(e){return this.from-e.from||this.startSide-e.startSide||this.rank-e.rank||this.to-e.to||this.endSide-e.endSide}}class Si{constructor(e){this.heap=e}static from(e,t=null,i=-1){let s=[];for(let r=0;r<e.length;r++)for(let o=e[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&s.push(new Ma(o,t,i,r));return s.length==1?s[0]:new Si(s)}get startSide(){return this.value?this.value.startSide:0}goto(e,t=-1e9){for(let i of this.heap)i.goto(e,t);for(let i=this.heap.length>>1;i>=0;i--)Jn(this.heap,i);return this.next(),this}forward(e,t){for(let i of this.heap)i.forward(e,t);for(let i=this.heap.length>>1;i>=0;i--)Jn(this.heap,i);(this.to-e||this.value.endSide-t)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let e=this.heap[0];this.from=e.from,this.to=e.to,this.value=e.value,this.rank=e.rank,e.value&&e.next(),Jn(this.heap,0)}}}function Jn(n,e){for(let t=n[e];;){let i=(e<<1)+1;if(i>=n.length)break;let s=n[i];if(i+1<n.length&&s.compare(n[i+1])>=0&&(s=n[i+1],i++),t.compare(s)<0)break;n[i]=t,n[e]=s,e=i}}class ri{constructor(e,t,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Si.from(e,t,i)}goto(e,t=-1e9){return this.cursor.goto(e,t),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=e,this.endSide=t,this.openStart=-1,this.next(),this}forward(e,t){for(;this.minActive>-1&&(this.activeTo[this.minActive]-e||this.active[this.minActive].endSide-t)<0;)this.removeActive(this.minActive);this.cursor.forward(e,t)}removeActive(e){Hi(this.active,e),Hi(this.activeTo,e),Hi(this.activeRank,e),this.minActive=lo(this.active,this.activeTo)}addActive(e){let t=0,{value:i,to:s,rank:r}=this.cursor;for(;t<this.activeRank.length&&this.activeRank[t]<=r;)t++;$i(this.active,t,i),$i(this.activeTo,t,s),$i(this.activeRank,t,r),e&&$i(e,t,this.cursor.from),this.minActive=lo(this.active,this.activeTo)}next(){let e=this.to,t=this.point;this.point=null;let i=this.openStart<0?[]:null,s=0;for(;;){let r=this.minActive;if(r>-1&&(this.activeTo[r]-this.cursor.from||this.active[r].endSide-this.cursor.startSide)<0){if(this.activeTo[r]>e){this.to=this.activeTo[r],this.endSide=this.active[r].endSide;break}this.removeActive(r),i&&Hi(i,r)}else if(this.cursor.value)if(this.cursor.from>e){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let o=this.cursor.value;if(!o.point)this.addActive(i),this.cursor.from<e&&this.cursor.to>e&&s++,this.cursor.next();else if(t&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=o,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=o.endSide,this.cursor.from<e&&(s=1),this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(i){let r=0;for(;r<i.length&&i[r]<e;)r++;this.openStart=r+s}}activeForPoint(e){if(!this.active.length)return this.active;let t=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>e||this.activeTo[i]==e&&this.active[i].endSide>=this.point.endSide)&&t.push(this.active[i]);return t.reverse()}openEnd(e){let t=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>e;i--)t++;return t}}function oo(n,e,t,i,s,r){n.goto(e),t.goto(i);let o=i+s,l=i,a=i-e;for(;;){let h=n.to+a-t.to||n.endSide-t.endSide,c=h<0?n.to+a:t.to,f=Math.min(c,o);if(n.point||t.point?n.point&&t.point&&(n.point==t.point||n.point.eq(t.point))&&_s(n.activeForPoint(n.to+a),t.activeForPoint(t.to))||r.comparePoint(l,f,n.point,t.point):f>l&&!_s(n.active,t.active)&&r.compareRange(l,f,n.active,t.active),c>o)break;l=c,h<=0&&n.next(),h>=0&&t.next()}}function _s(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(n[t]!=e[t]&&!n[t].eq(e[t]))return!1;return!0}function Hi(n,e){for(let t=e,i=n.length-1;t<i;t++)n[t]=n[t+1];n.pop()}function $i(n,e,t){for(let i=n.length-1;i>=e;i--)n[i+1]=n[i];n[e]=t}function lo(n,e){let t=-1,i=1e9;for(let s=0;s<e.length;s++)(e[s]-i||n[s].endSide-n[t].endSide)<0&&(t=s,i=e[s]);return t}function Ri(n,e,t=n.length){let i=0;for(let s=0;s<t;)n.charCodeAt(s)==9?(i+=e-i%e,s++):(i++,s=Ae(n,s));return i}function Is(n,e,t,i){for(let s=0,r=0;;){if(r>=e)return s;if(s==n.length)break;r+=n.charCodeAt(s)==9?t-r%t:1,s=Ae(n,s)}return i===!0?-1:n.length}const Ns="ͼ",ao=typeof Symbol>"u"?"__"+Ns:Symbol.for(Ns),Vs=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),ho=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class ft{constructor(e,t){this.rules=[];let{finish:i}=t||{};function s(o){return/^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let c=[],f=/^@(\w+)\b/.exec(o[0]),u=f&&f[1]=="keyframes";if(f&&l==null)return a.push(o[0]+";");for(let d in l){let p=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(g=>o.map(y=>g.replace(/&/,y))).reduce((g,y)=>g.concat(y)),p,a);else if(p&&typeof p=="object"){if(!f)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(s(d),p,c,u)}else p!=null&&c.push(d.replace(/_.*/,"").replace(/[A-Z]/g,g=>"-"+g.toLowerCase())+": "+p+";")}(c.length||u)&&a.push((i&&!f&&!h?o.map(i):o).join(", ")+" {"+c.join(" ")+"}")}for(let o in e)r(s(o),e[o],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let e=ho[ao]||1;return ho[ao]=e+1,Ns+e.toString(36)}static mount(e,t,i){let s=e[Vs],r=i&&i.nonce;s?r&&s.setNonce(r):s=new Tf(e,r),s.mount(Array.isArray(t)?t:[t])}}let co=new Map;class Tf{constructor(e,t){let i=e.ownerDocument||e,s=i.defaultView;if(!e.head&&e.adoptedStyleSheets&&s.CSSStyleSheet){let r=co.get(i);if(r)return e.adoptedStyleSheets=[r.sheet,...e.adoptedStyleSheets],e[Vs]=r;this.sheet=new s.CSSStyleSheet,e.adoptedStyleSheets=[this.sheet,...e.adoptedStyleSheets],co.set(i,this)}else{this.styleTag=i.createElement("style"),t&&this.styleTag.setAttribute("nonce",t);let r=e.head||e;r.insertBefore(this.styleTag,r.firstChild)}this.modules=[],e[Vs]=this}mount(e){let t=this.sheet,i=0,s=0;for(let r=0;r<e.length;r++){let o=e[r],l=this.modules.indexOf(o);if(l<s&&l>-1&&(this.modules.splice(l,1),s--,l=-1),l==-1){if(this.modules.splice(s++,0,o),t)for(let a=0;a<o.rules.length;a++)t.insertRule(o.rules[a],i++)}else{for(;s<l;)i+=this.modules[s++].rules.length;i+=o.rules.length,s++}}if(!t){let r="";for(let o=0;o<this.modules.length;o++)r+=this.modules[o].getRules()+`
`;this.styleTag.textContent=r}}setNonce(e){this.styleTag&&this.styleTag.getAttribute("nonce")!=e&&this.styleTag.setAttribute("nonce",e)}}var ut={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Ci={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Of=typeof navigator<"u"&&/Mac/.test(navigator.platform),Bf=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var re=0;re<10;re++)ut[48+re]=ut[96+re]=String(re);for(var re=1;re<=24;re++)ut[re+111]="F"+re;for(var re=65;re<=90;re++)ut[re]=String.fromCharCode(re+32),Ci[re]=String.fromCharCode(re);for(var Yn in ut)Ci.hasOwnProperty(Yn)||(Ci[Yn]=ut[Yn]);function Ef(n){var e=Of&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||Bf&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?Ci:ut)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}function mn(n){let e;return n.nodeType==11?e=n.getSelection?n:n.ownerDocument:e=n,e.getSelection()}function Gt(n,e){return e?n==e||n.contains(e.nodeType!=1?e.parentNode:e):!1}function Pf(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function ln(n,e){if(!e.anchorNode)return!1;try{return Gt(n,e.anchorNode)}catch{return!1}}function Ai(n){return n.nodeType==3?Jt(n,0,n.nodeValue.length).getClientRects():n.nodeType==1?n.getClientRects():[]}function gn(n,e,t,i){return t?fo(n,e,t,i,-1)||fo(n,e,t,i,1):!1}function yn(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e}function fo(n,e,t,i,s){for(;;){if(n==t&&e==i)return!0;if(e==(s<0?0:Mi(n))){if(n.nodeName=="DIV")return!1;let r=n.parentNode;if(!r||r.nodeType!=1)return!1;e=yn(n)+(s<0?0:1),n=r}else if(n.nodeType==1){if(n=n.childNodes[e+(s<0?-1:0)],n.nodeType==1&&n.contentEditable=="false")return!1;e=s<0?Mi(n):0}else return!1}}function Mi(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}const Da={left:0,right:0,top:0,bottom:0};function xr(n,e){let t=e?n.left:n.right;return{left:t,right:t,top:n.top,bottom:n.bottom}}function Lf(n){return{left:0,right:n.innerWidth,top:0,bottom:n.innerHeight}}function Rf(n,e,t,i,s,r,o,l){let a=n.ownerDocument,h=a.defaultView||window;for(let c=n;c;)if(c.nodeType==1){let f,u=c==a.body;if(u)f=Lf(h);else{if(c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let g=c.getBoundingClientRect();f={left:g.left,right:g.left+c.clientWidth,top:g.top,bottom:g.top+c.clientHeight}}let d=0,p=0;if(s=="nearest")e.top<f.top?(p=-(f.top-e.top+o),t>0&&e.bottom>f.bottom+p&&(p=e.bottom-f.bottom+p+o)):e.bottom>f.bottom&&(p=e.bottom-f.bottom+o,t<0&&e.top-p<f.top&&(p=-(f.top+p-e.top+o)));else{let g=e.bottom-e.top,y=f.bottom-f.top;p=(s=="center"&&g<=y?e.top+g/2-y/2:s=="start"||s=="center"&&t<0?e.top-o:e.bottom-y+o)-f.top}if(i=="nearest"?e.left<f.left?(d=-(f.left-e.left+r),t>0&&e.right>f.right+d&&(d=e.right-f.right+d+r)):e.right>f.right&&(d=e.right-f.right+r,t<0&&e.left<f.left+d&&(d=-(f.left+d-e.left+r))):d=(i=="center"?e.left+(e.right-e.left)/2-(f.right-f.left)/2:i=="start"==l?e.left-r:e.right-(f.right-f.left)+r)-f.left,d||p)if(u)h.scrollBy(d,p);else{let g=0,y=0;if(p){let b=c.scrollTop;c.scrollTop+=p,y=c.scrollTop-b}if(d){let b=c.scrollLeft;c.scrollLeft+=d,g=c.scrollLeft-b}e={left:e.left-g,top:e.top-y,right:e.right-g,bottom:e.bottom-y},g&&Math.abs(g-d)<1&&(i="nearest"),y&&Math.abs(y-p)<1&&(s="nearest")}if(u)break;c=c.assignedSlot||c.parentNode}else if(c.nodeType==11)c=c.host;else break}class _f{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(e){return this.anchorNode==e.anchorNode&&this.anchorOffset==e.anchorOffset&&this.focusNode==e.focusNode&&this.focusOffset==e.focusOffset}setRange(e){this.set(e.anchorNode,e.anchorOffset,e.focusNode,e.focusOffset)}set(e,t,i,s){this.anchorNode=e,this.anchorOffset=t,this.focusNode=i,this.focusOffset=s}}let Vt=null;function Ta(n){if(n.setActive)return n.setActive();if(Vt)return n.focus(Vt);let e=[];for(let t=n;t&&(e.push(t,t.scrollTop,t.scrollLeft),t!=t.ownerDocument);t=t.parentNode);if(n.focus(Vt==null?{get preventScroll(){return Vt={preventScroll:!0},!0}}:void 0),!Vt){Vt=!1;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],r=e[t++];i.scrollTop!=s&&(i.scrollTop=s),i.scrollLeft!=r&&(i.scrollLeft=r)}}}let uo;function Jt(n,e,t=e){let i=uo||(uo=document.createRange());return i.setEnd(n,t),i.setStart(n,e),i}function Kt(n,e,t){let i={key:e,code:e,keyCode:t,which:t,cancelable:!0},s=new KeyboardEvent("keydown",i);s.synthetic=!0,n.dispatchEvent(s);let r=new KeyboardEvent("keyup",i);return r.synthetic=!0,n.dispatchEvent(r),s.defaultPrevented||r.defaultPrevented}function If(n){for(;n;){if(n&&(n.nodeType==9||n.nodeType==11&&n.host))return n;n=n.assignedSlot||n.parentNode}return null}function Oa(n){for(;n.attributes.length;)n.removeAttributeNode(n.attributes[0])}function Nf(n,e){let t=e.focusNode,i=e.focusOffset;if(!t||e.anchorNode!=t||e.anchorOffset!=i)return!1;for(;;)if(i){if(t.nodeType!=1)return!1;let s=t.childNodes[i-1];s.contentEditable=="false"?i--:(t=s,i=Mi(t))}else{if(t==n)return!0;i=yn(t),t=t.parentNode}}class de{constructor(e,t,i=!0){this.node=e,this.offset=t,this.precise=i}static before(e,t){return new de(e.parentNode,yn(e),t)}static after(e,t){return new de(e.parentNode,yn(e)+1,t)}}const vr=[];class j{constructor(){this.parent=null,this.dom=null,this.dirty=2}get editorView(){if(!this.parent)throw new Error("Accessing view in orphan content view");return this.parent.editorView}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(e){let t=this.posAtStart;for(let i of this.children){if(i==e)return t;t+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(e){return this.posBefore(e)+e.length}coordsAt(e,t){return null}sync(e){if(this.dirty&2){let t=this.dom,i=null,s;for(let r of this.children){if(r.dirty){if(!r.dom&&(s=i?i.nextSibling:t.firstChild)){let o=j.get(s);(!o||!o.parent&&o.canReuseDOM(r))&&r.reuseDOM(s)}r.sync(e),r.dirty=0}if(s=i?i.nextSibling:t.firstChild,e&&!e.written&&e.node==t&&s!=r.dom&&(e.written=!0),r.dom.parentNode==t)for(;s&&s!=r.dom;)s=po(s);else t.insertBefore(r.dom,s);i=r.dom}for(s=i?i.nextSibling:t.firstChild,s&&e&&e.node==t&&(e.written=!0);s;)s=po(s)}else if(this.dirty&1)for(let t of this.children)t.dirty&&(t.sync(e),t.dirty=0)}reuseDOM(e){}localPosFromDOM(e,t){let i;if(e==this.dom)i=this.dom.childNodes[t];else{let s=Mi(e)==0?0:t==0?-1:1;for(;;){let r=e.parentNode;if(r==this.dom)break;s==0&&r.firstChild!=r.lastChild&&(e==r.firstChild?s=-1:s=1),e=r}s<0?i=e:i=e.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!j.get(i);)i=i.nextSibling;if(!i)return this.length;for(let s=0,r=0;;s++){let o=this.children[s];if(o.dom==i)return r;r+=o.length+o.breakAfter}}domBoundsAround(e,t,i=0){let s=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,c=i;a<this.children.length;a++){let f=this.children[a],u=h+f.length;if(h<e&&u>t)return f.domBoundsAround(e,t,h);if(u>=e&&s==-1&&(s=a,r=h),h>t&&f.dom.parentNode==this.dom){o=a,l=c;break}c=u,h=u+f.breakAfter}return{from:r,to:l<0?i+this.length:l,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(e=!1){this.dirty|=2,this.markParentsDirty(e)}markParentsDirty(e){for(let t=this.parent;t;t=t.parent){if(e&&(t.dirty|=2),t.dirty&1)return;t.dirty|=1,e=!1}}setParent(e){this.parent!=e&&(this.parent=e,this.dirty&&this.markParentsDirty(!0))}setDOM(e){this.dom&&(this.dom.cmView=null),this.dom=e,e.cmView=this}get rootView(){for(let e=this;;){let t=e.parent;if(!t)return e;e=t}}replaceChildren(e,t,i=vr){this.markDirty();for(let s=e;s<t;s++){let r=this.children[s];r.parent==this&&r.destroy()}this.children.splice(e,t-e,...i);for(let s=0;s<i.length;s++)i[s].setParent(this)}ignoreMutation(e){return!1}ignoreEvent(e){return!1}childCursor(e=this.length){return new Ba(this.children,e,this.children.length)}childPos(e,t=1){return this.childCursor().findPos(e,t)}toString(){let e=this.constructor.name.replace("View","");return e+(this.children.length?"("+this.children.join()+")":this.length?"["+(e=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(e){return e.cmView}get isEditable(){return!0}merge(e,t,i,s,r,o){return!1}become(e){return!1}canReuseDOM(e){return e.constructor==this.constructor}getSide(){return 0}destroy(){this.parent=null}}j.prototype.breakAfter=0;function po(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class Ba{constructor(e,t,i){this.children=e,this.pos=t,this.i=i,this.off=0}findPos(e,t=1){for(;;){if(e>this.pos||e==this.pos&&(t>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=e-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function Ea(n,e,t,i,s,r,o,l,a){let{children:h}=n,c=h.length?h[e]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(e==i&&c&&!o&&!u&&r.length<2&&c.merge(t,s,r.length?f:null,t==0,l,a))){if(i<h.length){let d=h[i];d&&s<d.length?(e==i&&(d=d.split(s),s=0),!u&&f&&d.merge(0,s,f,!0,0,a)?r[r.length-1]=d:(s&&d.merge(0,s,null,!1,0,a),r.push(d))):d?.breakAfter&&(f?f.breakAfter=1:o=1),i++}for(c&&(c.breakAfter=o,t>0&&(!o&&r.length&&c.merge(t,c.length,r[0],!1,l,0)?c.breakAfter=r.shift().breakAfter:(t<c.length||c.children.length&&c.children[c.children.length-1].length==0)&&c.merge(t,c.length,null,!1,l,0),e++));e<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[e].become(r[0]))e++,r.shift(),l=r.length?0:a;else break;!r.length&&e&&i<h.length&&!h[e-1].breakAfter&&h[i].merge(0,0,h[e-1],!1,l,a)&&e--,(e<i||r.length)&&n.replaceChildren(e,i,r)}}function Pa(n,e,t,i,s,r){let o=n.childCursor(),{i:l,off:a}=o.findPos(t,1),{i:h,off:c}=o.findPos(e,-1),f=e-t;for(let u of i)f+=u.length;n.length+=f,Ea(n,h,c,l,a,i,0,s,r)}let Ce=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},Fs=typeof document<"u"?document:{documentElement:{style:{}}};const Hs=/Edge\/(\d+)/.exec(Ce.userAgent),La=/MSIE \d/.test(Ce.userAgent),$s=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Ce.userAgent),_n=!!(La||$s||Hs),mo=!_n&&/gecko\/(\d+)/i.test(Ce.userAgent),Xn=!_n&&/Chrome\/(\d+)/.exec(Ce.userAgent),go="webkitFontSmoothing"in Fs.documentElement.style,Ra=!_n&&/Apple Computer/.test(Ce.vendor),yo=Ra&&(/Mobile\/\w+/.test(Ce.userAgent)||Ce.maxTouchPoints>2);var M={mac:yo||/Mac/.test(Ce.platform),windows:/Win/.test(Ce.platform),linux:/Linux|X11/.test(Ce.platform),ie:_n,ie_version:La?Fs.documentMode||6:$s?+$s[1]:Hs?+Hs[1]:0,gecko:mo,gecko_version:mo?+(/Firefox\/(\d+)/.exec(Ce.userAgent)||[0,0])[1]:0,chrome:!!Xn,chrome_version:Xn?+Xn[1]:0,ios:yo,android:/Android\b/.test(Ce.userAgent),webkit:go,safari:Ra,webkit_version:go?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:Fs.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const Vf=256;class dt extends j{constructor(e){super(),this.text=e}get length(){return this.text.length}createDOM(e){this.setDOM(e||document.createTextNode(this.text))}sync(e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(e){e.nodeType==3&&this.createDOM(e)}merge(e,t,i){return i&&(!(i instanceof dt)||this.length-(t-e)+i.length>Vf)?!1:(this.text=this.text.slice(0,e)+(i?i.text:"")+this.text.slice(t),this.markDirty(),!0)}split(e){let t=new dt(this.text.slice(e));return this.text=this.text.slice(0,e),this.markDirty(),t}localPosFromDOM(e,t){return e==this.dom?t:t?this.text.length:0}domAtPos(e){return new de(this.dom,e)}domBoundsAround(e,t,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(e,t){return Ws(this.dom,e,t)}}class Xe extends j{constructor(e,t=[],i=0){super(),this.mark=e,this.children=t,this.length=i;for(let s of t)s.setParent(this)}setAttrs(e){if(Oa(e),this.mark.class&&(e.className=this.mark.class),this.mark.attrs)for(let t in this.mark.attrs)e.setAttribute(t,this.mark.attrs[t]);return e}reuseDOM(e){e.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(e),this.dirty|=6)}sync(e){this.dom?this.dirty&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(e)}merge(e,t,i,s,r,o){return i&&(!(i instanceof Xe&&i.mark.eq(this.mark))||e&&r<=0||t<this.length&&o<=0)?!1:(Pa(this,e,t,i?i.children:[],r-1,o-1),this.markDirty(),!0)}split(e){let t=[],i=0,s=-1,r=0;for(let l of this.children){let a=i+l.length;a>e&&t.push(i<e?l.split(e-i):l),s<0&&i>=e&&(s=r),i=a,r++}let o=this.length-e;return this.length=e,s>-1&&(this.children.length=s,this.markDirty()),new Xe(this.mark,t,o)}domAtPos(e){return Na(this,e)}coordsAt(e,t){return Fa(this,e,t)}}function Ws(n,e,t){let i=n.nodeValue.length;e>i&&(e=i);let s=e,r=e,o=0;e==0&&t<0||e==i&&t>=0?M.chrome||M.gecko||(e?(s--,o=1):r<i&&(r++,o=-1)):t<0?s--:r<i&&r++;let l=Jt(n,s,r).getClientRects();if(!l.length)return Da;let a=l[(o?o<0:t>=0)?0:l.length-1];return M.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?xr(a,o<0):a||null}class at extends j{constructor(e,t,i){super(),this.widget=e,this.length=t,this.side=i,this.prevWidget=null}static create(e,t,i){return new(e.customView||at)(e,t,i)}split(e){let t=at.create(this.widget,this.length-e,this.side);return this.length-=e,t}sync(){(!this.dom||!this.widget.updateDOM(this.dom))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}getSide(){return this.side}merge(e,t,i,s,r,o){return i&&(!(i instanceof at)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}become(e){return e.length==this.length&&e instanceof at&&e.side==this.side&&this.widget.constructor==e.widget.constructor?(this.widget.eq(e.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}get overrideDOMText(){if(this.length==0)return V.empty;let e=this;for(;e.parent;)e=e.parent;let t=e.editorView,i=t&&t.state.doc,s=this.posAtStart;return i?i.slice(s,s+this.length):V.empty}domAtPos(e){return e==0?de.before(this.dom):de.after(this.dom,e==this.length)}domBoundsAround(){return null}coordsAt(e,t){let i=this.dom.getClientRects(),s=null;if(!i.length)return Da;for(let r=e>0?i.length-1:0;s=i[r],!(e>0?r==0:r==i.length-1||s.top<s.bottom);r+=e>0?-1:1);return this.length?s:xr(s,this.side>0)}get isEditable(){return!1}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class _a extends at{domAtPos(e){let{topView:t,text:i}=this.widget;return t?zs(e,0,t,i,(s,r)=>s.domAtPos(r),s=>new de(i,Math.min(s,i.nodeValue.length))):new de(i,Math.min(e,i.nodeValue.length))}sync(){this.setDOM(this.widget.toDOM())}localPosFromDOM(e,t){let{topView:i,text:s}=this.widget;return i?Ia(e,t,i,s):Math.min(t,this.length)}ignoreMutation(){return!1}get overrideDOMText(){return null}coordsAt(e,t){let{topView:i,text:s}=this.widget;return i?zs(e,t,i,s,(r,o,l)=>r.coordsAt(o,l),(r,o)=>Ws(s,r,o)):Ws(s,e,t)}destroy(){var e;super.destroy(),(e=this.widget.topView)===null||e===void 0||e.destroy()}get isEditable(){return!0}canReuseDOM(){return!0}}function zs(n,e,t,i,s,r){if(t instanceof Xe){for(let o=t.dom.firstChild;o;o=o.nextSibling){let l=j.get(o);if(!l)return r(n,e);let a=Gt(o,i),h=l.length+(a?i.nodeValue.length:0);if(n<h||n==h&&l.getSide()<=0)return a?zs(n,e,l,i,s,r):s(l,n,e);n-=h}return s(t,t.length,-1)}else return t.dom==i?r(n,e):s(t,n,e)}function Ia(n,e,t,i){if(t instanceof Xe)for(let s of t.children){let r=0,o=Gt(s.dom,i);if(Gt(s.dom,n))return r+(o?Ia(n,e,s,i):s.localPosFromDOM(n,e));r+=o?i.nodeValue.length:s.length}else if(t.dom==i)return Math.min(e,i.nodeValue.length);return t.localPosFromDOM(n,e)}class Yt extends j{constructor(e){super(),this.side=e}get length(){return 0}merge(){return!1}become(e){return e instanceof Yt&&e.side==this.side}split(){return new Yt(this.side)}sync(){if(!this.dom){let e=document.createElement("img");e.className="cm-widgetBuffer",e.setAttribute("aria-hidden","true"),this.setDOM(e)}}getSide(){return this.side}domAtPos(e){return de.before(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(e){let t=this.dom.getBoundingClientRect(),i=Ff(this,this.side>0?-1:1);return i&&i.top<t.bottom&&i.bottom>t.top?{left:t.left,right:t.right,top:i.top,bottom:i.bottom}:t}get overrideDOMText(){return V.empty}}dt.prototype.children=at.prototype.children=Yt.prototype.children=vr;function Ff(n,e){let t=n.parent,i=t?t.children.indexOf(n):-1;for(;t&&i>=0;)if(e<0?i>0:i<t.children.length){let s=t.children[i+e];if(s instanceof dt){let r=s.coordsAt(e<0?s.length:0,e);if(r)return r}i+=e}else if(t instanceof Xe&&t.parent)i=t.parent.children.indexOf(t)+(e<0?0:1),t=t.parent;else{let s=t.dom.lastChild;if(s&&s.nodeName=="BR")return s.getClientRects()[0];break}}function Na(n,e){let t=n.dom,{children:i}=n,s=0;for(let r=0;s<i.length;s++){let o=i[s],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(e>r&&e<l&&o.dom.parentNode==t)return o.domAtPos(e-r);if(e<=r)break;r=l}}for(let r=s;r>0;r--){let o=i[r-1];if(o.dom.parentNode==t)return o.domAtPos(o.length)}for(let r=s;r<i.length;r++){let o=i[r];if(o.dom.parentNode==t)return o.domAtPos(0)}return new de(t,0)}function Va(n,e,t){let i,{children:s}=n;t>0&&e instanceof Xe&&s.length&&(i=s[s.length-1])instanceof Xe&&i.mark.eq(e.mark)?Va(i,e.children[0],t-1):(s.push(e),e.setParent(n)),n.length+=e.length}function Fa(n,e,t){let i=null,s=-1,r=null,o=-1;function l(h,c){for(let f=0,u=0;f<h.children.length&&u<=c;f++){let d=h.children[f],p=u+d.length;p>=c&&(d.children.length?l(d,c-u):!r&&(p>c||u==p&&d.getSide()>0)?(r=d,o=c-u):(u<c||u==p&&d.getSide()<0)&&(i=d,s=c-u)),u=p}}l(n,e);let a=(t<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?s:o),t):Hf(n)}function Hf(n){let e=n.dom.lastChild;if(!e)return n.dom.getBoundingClientRect();let t=Ai(e);return t[t.length-1]||null}function qs(n,e){for(let t in n)t=="class"&&e.class?e.class+=" "+n.class:t=="style"&&e.style?e.style+=";"+n.style:e[t]=n[t];return e}function Sr(n,e){if(n==e)return!0;if(!n||!e)return!1;let t=Object.keys(n),i=Object.keys(e);if(t.length!=i.length)return!1;for(let s of t)if(i.indexOf(s)==-1||n[s]!==e[s])return!1;return!0}function js(n,e,t){let i=null;if(e)for(let s in e)t&&s in t||n.removeAttribute(i=s);if(t)for(let s in t)e&&e[s]==t[s]||n.setAttribute(i=s,t[s]);return!!i}class Qe{eq(e){return!1}updateDOM(e){return!1}compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}get estimatedHeight(){return-1}ignoreEvent(e){return!0}get customView(){return null}destroy(e){}}var $=function(n){return n[n.Text=0]="Text",n[n.WidgetBefore=1]="WidgetBefore",n[n.WidgetAfter=2]="WidgetAfter",n[n.WidgetRange=3]="WidgetRange",n}($||($={}));class L extends At{constructor(e,t,i,s){super(),this.startSide=e,this.endSide=t,this.widget=i,this.spec=s}get heightRelevant(){return!1}static mark(e){return new In(e)}static widget(e){let t=e.side||0,i=!!e.block;return t+=i?t>0?3e8:-4e8:t>0?1e8:-1e8,new Dt(e,t,t,i,e.widget||null,!1)}static replace(e){let t=!!e.block,i,s;if(e.isBlockGap)i=-5e8,s=4e8;else{let{start:r,end:o}=Ha(e,t);i=(r?t?-3e8:-1:5e8)-1,s=(o?t?2e8:1:-6e8)+1}return new Dt(e,i,s,t,e.widget||null,!0)}static line(e){return new _i(e)}static set(e,t=!1){return H.of(e,t)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}L.none=H.empty;class In extends L{constructor(e){let{start:t,end:i}=Ha(e);super(t?-1:5e8,i?1:-6e8,null,e),this.tagName=e.tagName||"span",this.class=e.class||"",this.attrs=e.attributes||null}eq(e){return this==e||e instanceof In&&this.tagName==e.tagName&&this.class==e.class&&Sr(this.attrs,e.attrs)}range(e,t=e){if(e>=t)throw new RangeError("Mark decorations may not be empty");return super.range(e,t)}}In.prototype.point=!1;class _i extends L{constructor(e){super(-2e8,-2e8,null,e)}eq(e){return e instanceof _i&&Sr(this.spec.attributes,e.spec.attributes)}range(e,t=e){if(t!=e)throw new RangeError("Line decoration ranges must be zero-length");return super.range(e,t)}}_i.prototype.mapMode=oe.TrackBefore;_i.prototype.point=!0;class Dt extends L{constructor(e,t,i,s,r,o){super(t,i,r,e),this.block=s,this.isReplace=o,this.mapMode=s?t<=0?oe.TrackBefore:oe.TrackAfter:oe.TrackDel}get type(){return this.startSide<this.endSide?$.WidgetRange:this.startSide<=0?$.WidgetBefore:$.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&this.widget.estimatedHeight>=5}eq(e){return e instanceof Dt&&$f(this.widget,e.widget)&&this.block==e.block&&this.startSide==e.startSide&&this.endSide==e.endSide}range(e,t=e){if(this.isReplace&&(e>t||e==t&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&t!=e)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(e,t)}}Dt.prototype.point=!0;function Ha(n,e=!1){let{inclusiveStart:t,inclusiveEnd:i}=n;return t==null&&(t=n.inclusive),i==null&&(i=n.inclusive),{start:t??e,end:i??e}}function $f(n,e){return n==e||!!(n&&e&&n.compare(e))}function Ks(n,e,t,i=0){let s=t.length-1;s>=0&&t[s]+i>=n?t[s]=Math.max(t[s],e):t.push(n,e)}class ye extends j{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(e,t,i,s,r,o){if(i){if(!(i instanceof ye))return!1;this.dom||i.transferDOM(this)}return s&&this.setDeco(i?i.attrs:null),Pa(this,e,t,i?i.children:[],r,o),!0}split(e){let t=new ye;if(t.breakAfter=this.breakAfter,this.length==0)return t;let{i,off:s}=this.childPos(e);s&&(t.append(this.children[i].split(s),0),this.children[i].merge(s,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)t.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=e,t}transferDOM(e){this.dom&&(this.markDirty(),e.setDOM(this.dom),e.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(e){Sr(this.attrs,e)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=e)}append(e,t){Va(this,e,t)}addLineDeco(e){let t=e.spec.attributes,i=e.spec.class;t&&(this.attrs=qs(t,this.attrs||{})),i&&(this.attrs=qs({class:i},this.attrs||{}))}domAtPos(e){return Na(this,e)}reuseDOM(e){e.nodeName=="DIV"&&(this.setDOM(e),this.dirty|=6)}sync(e){var t;this.dom?this.dirty&4&&(Oa(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(js(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(e);let i=this.dom.lastChild;for(;i&&j.get(i)instanceof Xe;)i=i.lastChild;if(!i||!this.length||i.nodeName!="BR"&&((t=j.get(i))===null||t===void 0?void 0:t.isEditable)==!1&&(!M.ios||!this.children.some(s=>s instanceof dt))){let s=document.createElement("BR");s.cmIgnore=!0,this.dom.appendChild(s)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let e=0;for(let t of this.children){if(!(t instanceof dt)||/[^ -~]/.test(t.text))return null;let i=Ai(t.dom);if(i.length!=1)return null;e+=i[0].width}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length}:null}coordsAt(e,t){return Fa(this,e,t)}become(e){return!1}get type(){return $.Text}static find(e,t){for(let i=0,s=0;i<e.children.length;i++){let r=e.children[i],o=s+r.length;if(o>=t){if(r instanceof ye)return r;if(o>t)break}s=o+r.breakAfter}return null}}class Ct extends j{constructor(e,t,i){super(),this.widget=e,this.length=t,this.type=i,this.breakAfter=0,this.prevWidget=null}merge(e,t,i,s,r,o){return i&&(!(i instanceof Ct)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}domAtPos(e){return e==0?de.before(this.dom):de.after(this.dom,e==this.length)}split(e){let t=this.length-e;this.length=e;let i=new Ct(this.widget,t,this.type);return i.breakAfter=this.breakAfter,i}get children(){return vr}sync(){(!this.dom||!this.widget.updateDOM(this.dom))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):V.empty}domBoundsAround(){return null}become(e){return e instanceof Ct&&e.type==this.type&&e.widget.constructor==this.widget.constructor?(e.widget.eq(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,this.length=e.length,this.breakAfter=e.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Cr{constructor(e,t,i,s){this.doc=e,this.pos=t,this.end=i,this.disallowBlockEffectsFor=s,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=e.iter(),this.skip=t}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let e=this.content[this.content.length-1];return!e.breakAfter&&!(e instanceof Ct&&e.type==$.WidgetBefore)}getLine(){return this.curLine||(this.content.push(this.curLine=new ye),this.atCursorPos=!0),this.curLine}flushBuffer(e){this.pendingBuffer&&(this.curLine.append(Wi(new Yt(-1),e),e.length),this.pendingBuffer=0)}addBlockWidget(e){this.flushBuffer([]),this.curLine=null,this.content.push(e)}finish(e){e?this.pendingBuffer=0:this.flushBuffer([]),this.posCovered()||this.getLine()}buildText(e,t,i){for(;e>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer([]),this.curLine=null,e--;continue}else this.text=r,this.textOff=0}let s=Math.min(this.text.length-this.textOff,e,512);this.flushBuffer(t.slice(0,i)),this.getLine().append(Wi(new dt(this.text.slice(this.textOff,this.textOff+s)),t),i),this.atCursorPos=!0,this.textOff+=s,e-=s,i=0}}span(e,t,i,s){this.buildText(t-e,i,s),this.pos=t,this.openStart<0&&(this.openStart=s)}point(e,t,i,s,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof Dt){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(t>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=t-e;if(i instanceof Dt)if(i.block){let{type:a}=i;a==$.WidgetAfter&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new Ct(i.widget||new bo("div"),l,a))}else{let a=at.create(i.widget||new bo("span"),l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=s.length&&(e<t||i.startSide>0),c=!a.isEditable&&(e<t||i.startSide<=0),f=this.getLine();this.pendingBuffer==2&&!h&&(this.pendingBuffer=0),this.flushBuffer(s),h&&(f.append(Wi(new Yt(1),s),r),r=s.length+Math.max(0,r-s.length)),f.append(Wi(a,s),r),this.atCursorPos=c,this.pendingBuffer=c?e<t?1:2:0}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=t),this.openStart<0&&(this.openStart=r)}static build(e,t,i,s,r){let o=new Cr(e,t,i,r);return o.openEnd=H.spans(s,t,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function Wi(n,e){for(let t of e)n=new Xe(t,[n],n.length);return n}class bo extends Qe{constructor(e){super(),this.tag=e}eq(e){return e.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(e){return e.nodeName.toLowerCase()==this.tag}}const $a=T.define(),Wa=T.define(),za=T.define(),qa=T.define(),Us=T.define(),ja=T.define(),Ka=T.define({combine:n=>n.some(e=>e)}),Ua=T.define({combine:n=>n.some(e=>e)});class bn{constructor(e,t="nearest",i="nearest",s=5,r=5){this.range=e,this.y=t,this.x=i,this.yMargin=s,this.xMargin=r}map(e){return e.empty?this:new bn(this.range.map(e),this.y,this.x,this.yMargin,this.xMargin)}}const wo=R.define({map:(n,e)=>n.map(e)});function _e(n,e,t){let i=n.facet(qa);i.length?i[0](e):window.onerror?window.onerror(String(e),t,void 0,void 0,e):t?console.error(t+":",e):console.error(e)}const Nn=T.define({combine:n=>n.length?n[0]:!0});let Wf=0;const fi=T.define();class pe{constructor(e,t,i,s){this.id=e,this.create=t,this.domEventHandlers=i,this.extension=s(this)}static define(e,t){const{eventHandlers:i,provide:s,decorations:r}=t||{};return new pe(Wf++,e,i,o=>{let l=[fi.of(o)];return r&&l.push(Di.of(a=>{let h=a.plugin(o);return h?r(h):L.none})),s&&l.push(s(o)),l})}static fromClass(e,t){return pe.define(i=>new e(i),t)}}class Qn{constructor(e){this.spec=e,this.mustUpdate=null,this.value=null}update(e){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(i){if(_e(t.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(e)}catch(t){_e(e.state,t,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(e){var t;if(!((t=this.value)===null||t===void 0)&&t.destroy)try{this.value.destroy()}catch(i){_e(e.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const Ga=T.define(),Ja=T.define(),Di=T.define(),Ya=T.define(),Xa=T.define(),ui=T.define();class Ye{constructor(e,t,i,s){this.fromA=e,this.toA=t,this.fromB=i,this.toB=s}join(e){return new Ye(Math.min(this.fromA,e.fromA),Math.max(this.toA,e.toA),Math.min(this.fromB,e.fromB),Math.max(this.toB,e.toB))}addToSet(e){let t=e.length,i=this;for(;t>0;t--){let s=e[t-1];if(!(s.fromA>i.toA)){if(s.toA<i.fromA)break;i=i.join(s),e.splice(t-1,1)}}return e.splice(t,0,i),e}static extendWithRanges(e,t){if(t.length==0)return e;let i=[];for(let s=0,r=0,o=0,l=0;;s++){let a=s==e.length?null:e[s],h=o-l,c=a?a.fromB:1e9;for(;r<t.length&&t[r]<c;){let f=t[r],u=t[r+1],d=Math.max(l,f),p=Math.min(c,u);if(d<=p&&new Ye(d+h,p+h,d,p).addToSet(i),u>c)break;r+=2}if(!a)return i;new Ye(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB}}}class wn{constructor(e,t,i){this.view=e,this.state=t,this.transactions=i,this.flags=0,this.startState=e.state,this.changes=Z.empty(this.startState.doc.length);for(let o of i)this.changes=this.changes.compose(o.changes);let s=[];this.changes.iterChangedRanges((o,l,a,h)=>s.push(new Ye(o,l,a,h))),this.changedRanges=s;let r=e.hasFocus;r!=e.inputState.notifiedFocused&&(e.inputState.notifiedFocused=r,this.flags|=1)}static create(e,t,i){return new wn(e,t,i)}get viewportChanged(){return(this.flags&4)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&10)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(e=>e.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}var G=function(n){return n[n.LTR=0]="LTR",n[n.RTL=1]="RTL",n}(G||(G={}));const Gs=G.LTR,zf=G.RTL;function Qa(n){let e=[];for(let t=0;t<n.length;t++)e.push(1<<+n[t]);return e}const qf=Qa("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),jf=Qa("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),Js=Object.create(null),Ne=[];for(let n of["()","[]","{}"]){let e=n.charCodeAt(0),t=n.charCodeAt(1);Js[e]=t,Js[t]=-e}function Kf(n){return n<=247?qf[n]:1424<=n&&n<=1524?2:1536<=n&&n<=1785?jf[n-1536]:1774<=n&&n<=2220?4:8192<=n&&n<=8203?256:64336<=n&&n<=65023?4:n==8204?256:1}const Uf=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class Ut{constructor(e,t,i){this.from=e,this.to=t,this.level=i}get dir(){return this.level%2?zf:Gs}side(e,t){return this.dir==t==e?this.to:this.from}static find(e,t,i,s){let r=-1;for(let o=0;o<e.length;o++){let l=e[o];if(l.from<=t&&l.to>=t){if(l.level==i)return o;(r<0||(s!=0?s<0?l.from<t:l.to>t:e[r].level>l.level))&&(r=o)}}if(r<0)throw new RangeError("Index out of range");return r}}const U=[];function Gf(n,e){let t=n.length,i=e==Gs?1:2,s=e==Gs?2:1;if(!n||i==1&&!Uf.test(n))return Za(t);for(let o=0,l=i,a=i;o<t;o++){let h=Kf(n.charCodeAt(o));h==512?h=l:h==8&&a==4&&(h=16),U[o]=h==4?2:h,h&7&&(a=h),l=h}for(let o=0,l=i,a=i;o<t;o++){let h=U[o];if(h==128)o<t-1&&l==U[o+1]&&l&24?h=U[o]=l:U[o]=256;else if(h==64){let c=o+1;for(;c<t&&U[c]==64;)c++;let f=o&&l==8||c<t&&U[c]==8?a==1?1:8:256;for(let u=o;u<c;u++)U[u]=f;o=c-1}else h==8&&a==1&&(U[o]=1);l=h,h&7&&(a=h)}for(let o=0,l=0,a=0,h,c,f;o<t;o++)if(c=Js[h=n.charCodeAt(o)])if(c<0){for(let u=l-3;u>=0;u-=3)if(Ne[u+1]==-c){let d=Ne[u+2],p=d&2?i:d&4?d&1?s:i:0;p&&(U[o]=U[Ne[u]]=p),l=u;break}}else{if(Ne.length==189)break;Ne[l++]=o,Ne[l++]=h,Ne[l++]=a}else if((f=U[o])==2||f==1){let u=f==i;a=u?0:1;for(let d=l-3;d>=0;d-=3){let p=Ne[d+2];if(p&2)break;if(u)Ne[d+2]|=2;else{if(p&4)break;Ne[d+2]|=4}}}for(let o=0;o<t;o++)if(U[o]==256){let l=o+1;for(;l<t&&U[l]==256;)l++;let a=(o?U[o-1]:i)==1,h=(l<t?U[l]:i)==1,c=a==h?a?1:2:i;for(let f=o;f<l;f++)U[f]=c;o=l-1}let r=[];if(i==1)for(let o=0;o<t;){let l=o,a=U[o++]!=1;for(;o<t&&a==(U[o]!=1);)o++;if(a)for(let h=o;h>l;){let c=h,f=U[--h]!=2;for(;h>l&&f==(U[h-1]!=2);)h--;r.push(new Ut(h,c,f?2:1))}else r.push(new Ut(l,o,0))}else for(let o=0;o<t;){let l=o,a=U[o++]==2;for(;o<t&&a==(U[o]==2);)o++;r.push(new Ut(l,o,a?1:2))}return r}function Za(n){return[new Ut(0,n,0)]}let eh="";function Jf(n,e,t,i,s){var r;let o=i.head-n.from,l=-1;if(o==0){if(!s||!n.length)return null;e[0].level!=t&&(o=e[0].side(!1,t),l=0)}else if(o==n.length){if(s)return null;let u=e[e.length-1];u.level!=t&&(o=u.side(!0,t),l=e.length-1)}l<0&&(l=Ut.find(e,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc));let a=e[l];o==a.side(s,t)&&(a=e[l+=s?1:-1],o=a.side(!s,t));let h=s==(a.dir==t),c=Ae(n.text,o,h);if(eh=n.text.slice(Math.min(o,c),Math.max(o,c)),c!=a.side(s,t))return w.cursor(c+n.from,h?-1:1,a.level);let f=l==(s?e.length-1:0)?null:e[l+(s?1:-1)];return!f&&a.level!=t?w.cursor(s?n.to:n.from,s?-1:1,t):f&&f.level<a.level?w.cursor(f.side(!s,t)+n.from,s?1:-1,f.level):w.cursor(c+n.from,s?-1:1,a.level)}const ht="￿";class th{constructor(e,t){this.points=e,this.text="",this.lineSeparator=t.facet(N.lineSeparator)}append(e){this.text+=e}lineBreak(){this.text+=ht}readRange(e,t){if(!e)return this;let i=e.parentNode;for(let s=e;;){this.findPointBefore(i,s),this.readNode(s);let r=s.nextSibling;if(r==t)break;let o=j.get(s),l=j.get(r);(o&&l?o.breakAfter:(o?o.breakAfter:ko(s))||ko(r)&&(s.nodeName!="BR"||s.cmIgnore))&&this.lineBreak(),s=r}return this.findPointBefore(i,t),this}readTextNode(e){let t=e.nodeValue;for(let i of this.points)i.node==e&&(i.pos=this.text.length+Math.min(i.offset,t.length));for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=t.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=s.exec(t))&&(r=l.index,o=l[0].length),this.append(t.slice(i,r<0?t.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==e&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o}}readNode(e){if(e.cmIgnore)return;let t=j.get(e),i=t&&t.overrideDOMText;if(i!=null){this.findPointInside(e,i.length);for(let s=i.iter();!s.next().done;)s.lineBreak?this.lineBreak():this.append(s.value)}else e.nodeType==3?this.readTextNode(e):e.nodeName=="BR"?e.nextSibling&&this.lineBreak():e.nodeType==1&&this.readRange(e.firstChild,null)}findPointBefore(e,t){for(let i of this.points)i.node==e&&e.childNodes[i.offset]==t&&(i.pos=this.text.length)}findPointInside(e,t){for(let i of this.points)(e.nodeType==3?i.node==e:e.contains(i.node))&&(i.pos=this.text.length+Math.min(t,i.offset))}}function ko(n){return n.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(n.nodeName)}class xo{constructor(e,t){this.node=e,this.offset=t,this.pos=-1}}class vo extends j{constructor(e){super(),this.view=e,this.compositionDeco=L.none,this.decorations=[],this.dynamicDecorationMap=[],this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(e.contentDOM),this.children=[new ye],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Ye(0,0,0,e.state.doc.length)],0)}get editorView(){return this.view}get length(){return this.view.state.doc.length}update(e){let t=e.changedRanges;this.minWidth>0&&t.length&&(t.every(({fromA:o,toA:l})=>l<this.minWidthFrom||o>this.minWidthTo)?(this.minWidthFrom=e.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=e.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.view.inputState.composing<0?this.compositionDeco=L.none:(e.transactions.length||this.dirty)&&(this.compositionDeco=Xf(this.view,e.changes)),(M.ie||M.chrome)&&!this.compositionDeco.size&&e&&e.state.doc.lines!=e.startState.doc.lines&&(this.forceSelection=!0);let i=this.decorations,s=this.updateDeco(),r=tu(i,s,e.changes);return t=Ye.extendWithRanges(t,r),this.dirty==0&&t.length==0?!1:(this.updateInner(t,e.startState.doc.length),e.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(e,t){this.view.viewState.mustMeasureContent=!0,this.updateChildren(e,t);let{observer:i}=this.view;i.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let r=M.chrome||M.ios?{node:i.selectionRange.focusNode,written:!1}:void 0;this.sync(r),this.dirty=0,r&&(r.written||i.selectionRange.focusNode!=r.node)&&(this.forceSelection=!0),this.dom.style.height=""});let s=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let r of this.children)r instanceof Ct&&r.widget instanceof So&&s.push(r.dom);i.updateGaps(s)}updateChildren(e,t){let i=this.childCursor(t);for(let s=e.length-1;;s--){let r=s>=0?e[s]:null;if(!r)break;let{fromA:o,toA:l,fromB:a,toB:h}=r,{content:c,breakAtStart:f,openStart:u,openEnd:d}=Cr.build(this.view.state.doc,a,h,this.decorations,this.dynamicDecorationMap),{i:p,off:g}=i.findPos(l,1),{i:y,off:b}=i.findPos(o,-1);Ea(this,y,b,p,g,c,f,u,d)}}updateSelection(e=!1,t=!1){if((e||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange(),!(t||this.mayControlSelection()))return;let i=this.forceSelection;this.forceSelection=!1;let s=this.view.state.selection.main,r=this.domAtPos(s.anchor),o=s.empty?r:this.domAtPos(s.head);if(M.gecko&&s.empty&&Yf(r)){let a=document.createTextNode("");this.view.observer.ignore(()=>r.node.insertBefore(a,r.node.childNodes[r.offset]||null)),r=o=new de(a,0),i=!0}let l=this.view.observer.selectionRange;(i||!l.focusNode||!gn(r.node,r.offset,l.anchorNode,l.anchorOffset)||!gn(o.node,o.offset,l.focusNode,l.focusOffset))&&(this.view.observer.ignore(()=>{M.android&&M.chrome&&this.dom.contains(l.focusNode)&&iu(l.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let a=mn(this.view.root);if(a)if(s.empty){if(M.gecko){let h=Zf(r.node,r.offset);if(h&&h!=3){let c=nh(r.node,r.offset,h==1?1:-1);c&&(r=new de(c,h==1?0:c.nodeValue.length))}}a.collapse(r.node,r.offset),s.bidiLevel!=null&&l.cursorBidiLevel!=null&&(l.cursorBidiLevel=s.bidiLevel)}else if(a.extend){a.collapse(r.node,r.offset);try{a.extend(o.node,o.offset)}catch{}}else{let h=document.createRange();s.anchor>s.head&&([r,o]=[o,r]),h.setEnd(o.node,o.offset),h.setStart(r.node,r.offset),a.removeAllRanges(),a.addRange(h)}}),this.view.observer.setSelectionRange(r,o)),this.impreciseAnchor=r.precise?null:new de(l.anchorNode,l.anchorOffset),this.impreciseHead=o.precise?null:new de(l.focusNode,l.focusOffset)}enforceCursorAssoc(){if(this.compositionDeco.size)return;let{view:e}=this,t=e.state.selection.main,i=mn(e.root),{anchorNode:s,anchorOffset:r}=e.observer.selectionRange;if(!i||!t.empty||!t.assoc||!i.modify)return;let o=ye.find(this,t.head);if(!o)return;let l=o.posAtStart;if(t.head==l||t.head==l+o.length)return;let a=this.coordsAt(t.head,-1),h=this.coordsAt(t.head,1);if(!a||!h||a.bottom>h.top)return;let c=this.domAtPos(t.head+t.assoc);i.collapse(c.node,c.offset),i.modify("move",t.assoc<0?"forward":"backward","lineboundary"),e.observer.readSelectionRange();let f=e.observer.selectionRange;e.docView.posFromDOM(f.anchorNode,f.anchorOffset)!=t.from&&i.collapse(s,r)}mayControlSelection(){let e=this.view.root.activeElement;return e==this.dom||ln(this.dom,this.view.observer.selectionRange)&&!(e&&this.dom.contains(e))}nearest(e){for(let t=e;t;){let i=j.get(t);if(i&&i.rootView==this)return i;t=t.parentNode}return null}posFromDOM(e,t){let i=this.nearest(e);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(e,t)+i.posAtStart}domAtPos(e){let{i:t,off:i}=this.childCursor().findPos(e,-1);for(;t<this.children.length-1;){let s=this.children[t];if(i<s.length||s instanceof ye)break;t++,i=0}return this.children[t].domAtPos(i)}coordsAt(e,t){for(let i=this.length,s=this.children.length-1;;s--){let r=this.children[s],o=i-r.breakAfter-r.length;if(e>o||e==o&&r.type!=$.WidgetBefore&&r.type!=$.WidgetAfter&&(!s||t==2||this.children[s-1].breakAfter||this.children[s-1].type==$.WidgetBefore&&t>-2))return r.coordsAt(e-o,t);i=o}}measureVisibleLineHeights(e){let t=[],{from:i,to:s}=e,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==G.LTR;for(let h=0,c=0;c<this.children.length;c++){let f=this.children[c],u=h+f.length;if(u>s)break;if(h>=i){let d=f.dom.getBoundingClientRect();if(t.push(d.height),o){let p=f.dom.lastChild,g=p?Ai(p):[];if(g.length){let y=g[g.length-1],b=a?y.right-d.left:d.right-y.left;b>l&&(l=b,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u)}}}h=u+f.breakAfter}return t}textDirectionAt(e){let{i:t}=this.childPos(e,1);return getComputedStyle(this.children[t].dom).direction=="rtl"?G.RTL:G.LTR}measureTextSize(){for(let s of this.children)if(s instanceof ye){let r=s.measureTextSize();if(r)return r}let e=document.createElement("div"),t,i;return e.className="cm-line",e.style.width="99999px",e.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(e);let s=Ai(e.firstChild)[0];t=e.getBoundingClientRect().height,i=s?s.width/27:7,e.remove()}),{lineHeight:t,charWidth:i}}childCursor(e=this.length){let t=this.children.length;return t&&(e-=this.children[--t].length),new Ba(this.children,e,t)}computeBlockGapDeco(){let e=[],t=this.view.viewState;for(let i=0,s=0;;s++){let r=s==t.viewports.length?null:t.viewports[s],o=r?r.from-1:this.length;if(o>i){let l=t.lineBlockAt(o).bottom-t.lineBlockAt(i).top;e.push(L.replace({widget:new So(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o))}if(!r)break;i=r.to+1}return L.set(e)}updateDeco(){let e=this.view.state.facet(Di).map((t,i)=>(this.dynamicDecorationMap[i]=typeof t=="function")?t(this.view):t);for(let t=e.length;t<e.length+3;t++)this.dynamicDecorationMap[t]=!1;return this.decorations=[...e,this.compositionDeco,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(e){let{range:t}=e,i=this.coordsAt(t.head,t.empty?t.assoc:t.head>t.anchor?-1:1),s;if(!i)return;!t.empty&&(s=this.coordsAt(t.anchor,t.anchor>t.head?-1:1))&&(i={left:Math.min(i.left,s.left),top:Math.min(i.top,s.top),right:Math.max(i.right,s.right),bottom:Math.max(i.bottom,s.bottom)});let r=0,o=0,l=0,a=0;for(let c of this.view.state.facet(Xa).map(f=>f(this.view)))if(c){let{left:f,right:u,top:d,bottom:p}=c;f!=null&&(r=Math.max(r,f)),u!=null&&(o=Math.max(o,u)),d!=null&&(l=Math.max(l,d)),p!=null&&(a=Math.max(a,p))}let h={left:i.left-r,top:i.top-l,right:i.right+o,bottom:i.bottom+a};Rf(this.view.scrollDOM,h,t.head<t.anchor?-1:1,e.x,e.y,e.xMargin,e.yMargin,this.view.textDirection==G.LTR)}}function Yf(n){return n.node.nodeType==1&&n.node.firstChild&&(n.offset==0||n.node.childNodes[n.offset-1].contentEditable=="false")&&(n.offset==n.node.childNodes.length||n.node.childNodes[n.offset].contentEditable=="false")}class So extends Qe{constructor(e){super(),this.height=e}toDOM(){let e=document.createElement("div");return this.updateDOM(e),e}eq(e){return e.height==this.height}updateDOM(e){return e.style.height=this.height+"px",!0}get estimatedHeight(){return this.height}}function ih(n){let e=n.observer.selectionRange,t=e.focusNode&&nh(e.focusNode,e.focusOffset,0);if(!t)return null;let i=n.docView.nearest(t);if(!i)return null;if(i instanceof ye){let s=t;for(;s.parentNode!=i.dom;)s=s.parentNode;let r=s.previousSibling;for(;r&&!j.get(r);)r=r.previousSibling;let o=r?j.get(r).posAtEnd:i.posAtStart;return{from:o,to:o,node:s,text:t}}else{for(;;){let{parent:r}=i;if(!r)return null;if(r instanceof ye)break;i=r}let s=i.posAtStart;return{from:s,to:s+i.length,node:i.dom,text:t}}}function Xf(n,e){let t=ih(n);if(!t)return L.none;let{from:i,to:s,node:r,text:o}=t,l=e.mapPos(i,1),a=Math.max(l,e.mapPos(s,-1)),{state:h}=n,c=r.nodeType==3?r.nodeValue:new th([],h).readRange(r.firstChild,null).text;if(a-l<c.length)if(h.doc.sliceString(l,Math.min(h.doc.length,l+c.length),ht)==c)a=l+c.length;else if(h.doc.sliceString(Math.max(0,a-c.length),a,ht)==c)l=a-c.length;else return L.none;else if(h.doc.sliceString(l,a,ht)!=c)return L.none;let f=j.get(r);return f instanceof _a?f=f.widget.topView:f&&(f.parent=null),L.set(L.replace({widget:new Qf(r,o,f),inclusive:!0}).range(l,a))}class Qf extends Qe{constructor(e,t,i){super(),this.top=e,this.text=t,this.topView=i}eq(e){return this.top==e.top&&this.text==e.text}toDOM(){return this.top}ignoreEvent(){return!1}get customView(){return _a}}function nh(n,e,t){for(;;){if(n.nodeType==3)return n;if(n.nodeType==1&&e>0&&t<=0)n=n.childNodes[e-1],e=Mi(n);else if(n.nodeType==1&&e<n.childNodes.length&&t>=0)n=n.childNodes[e],e=0;else return null}}function Zf(n,e){return n.nodeType!=1?0:(e&&n.childNodes[e-1].contentEditable=="false"?1:0)|(e<n.childNodes.length&&n.childNodes[e].contentEditable=="false"?2:0)}class eu{constructor(){this.changes=[]}compareRange(e,t){Ks(e,t,this.changes)}comparePoint(e,t){Ks(e,t,this.changes)}}function tu(n,e,t){let i=new eu;return H.compare(n,e,t,i),i.changes}function iu(n,e){for(let t=n;t&&t!=e;t=t.assignedSlot||t.parentNode)if(t.nodeType==1&&t.contentEditable=="false")return!0;return!1}function nu(n,e,t=1){let i=n.charCategorizer(e),s=n.doc.lineAt(e),r=e-s.from;if(s.length==0)return w.cursor(e);r==0?t=1:r==s.length&&(t=-1);let o=r,l=r;t<0?o=Ae(s.text,r,!1):l=Ae(s.text,r);let a=i(s.text.slice(o,l));for(;o>0;){let h=Ae(s.text,o,!1);if(i(s.text.slice(h,o))!=a)break;o=h}for(;l<s.length;){let h=Ae(s.text,l);if(i(s.text.slice(l,h))!=a)break;l=h}return w.range(o+s.from,l+s.from)}function su(n,e){return e.left>n?e.left-n:Math.max(0,n-e.right)}function ru(n,e){return e.top>n?e.top-n:Math.max(0,n-e.bottom)}function Zn(n,e){return n.top<e.bottom-1&&n.bottom>e.top+1}function Co(n,e){return e<n.top?{top:e,left:n.left,right:n.right,bottom:n.bottom}:n}function Ao(n,e){return e>n.bottom?{top:n.top,left:n.left,right:n.right,bottom:e}:n}function Ys(n,e,t){let i,s,r,o,l=!1,a,h,c,f;for(let p=n.firstChild;p;p=p.nextSibling){let g=Ai(p);for(let y=0;y<g.length;y++){let b=g[y];s&&Zn(s,b)&&(b=Co(Ao(b,s.bottom),s.top));let k=su(e,b),v=ru(t,b);if(k==0&&v==0)return p.nodeType==3?Mo(p,e,t):Ys(p,e,t);(!i||o>v||o==v&&r>k)&&(i=p,s=b,r=k,o=v,l=!k||(k>0?y<g.length-1:y>0)),k==0?t>b.bottom&&(!c||c.bottom<b.bottom)?(a=p,c=b):t<b.top&&(!f||f.top>b.top)&&(h=p,f=b):c&&Zn(c,b)?c=Ao(c,b.bottom):f&&Zn(f,b)&&(f=Co(f,b.top))}}if(c&&c.bottom>=t?(i=a,s=c):f&&f.top<=t&&(i=h,s=f),!i)return{node:n,offset:0};let u=Math.max(s.left,Math.min(s.right,e));if(i.nodeType==3)return Mo(i,u,t);if(l&&i.contentEditable!="false")return Ys(i,u,t);let d=Array.prototype.indexOf.call(n.childNodes,i)+(e>=(s.left+s.right)/2?1:0);return{node:n,offset:d}}function Mo(n,e,t){let i=n.nodeValue.length,s=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=Jt(n,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let c=a[h];if(c.top==c.bottom)continue;o||(o=e-c.left);let f=(c.top>t?c.top-t:t-c.bottom)-1;if(c.left-1<=e&&c.right+1>=e&&f<r){let u=e>=(c.left+c.right)/2,d=u;if((M.chrome||M.gecko)&&Jt(n,l).getBoundingClientRect().left==c.right&&(d=!u),f<=0)return{node:n,offset:l+(d?1:0)};s=l+(d?1:0),r=f}}}return{node:n,offset:s>-1?s:o>0?n.nodeValue.length:0}}function sh(n,{x:e,y:t},i,s=-1){var r;let o=n.contentDOM.getBoundingClientRect(),l=o.top+n.viewState.paddingTop,a,{docHeight:h}=n.viewState,c=t-l;if(c<0)return 0;if(c>h)return n.state.doc.length;for(let b=n.defaultLineHeight/2,k=!1;a=n.elementAtHeight(c),a.type!=$.Text;)for(;c=s>0?a.bottom+b:a.top-b,!(c>=0&&c<=h);){if(k)return i?null:0;k=!0,s=-s}t=l+c;let f=a.from;if(f<n.viewport.from)return n.viewport.from==0?0:i?null:Do(n,o,a,e,t);if(f>n.viewport.to)return n.viewport.to==n.state.doc.length?n.state.doc.length:i?null:Do(n,o,a,e,t);let u=n.dom.ownerDocument,d=n.root.elementFromPoint?n.root:u,p=d.elementFromPoint(e,t);p&&!n.contentDOM.contains(p)&&(p=null),p||(e=Math.max(o.left+1,Math.min(o.right-1,e)),p=d.elementFromPoint(e,t),p&&!n.contentDOM.contains(p)&&(p=null));let g,y=-1;if(p&&((r=n.docView.nearest(p))===null||r===void 0?void 0:r.isEditable)!=!1){if(u.caretPositionFromPoint){let b=u.caretPositionFromPoint(e,t);b&&({offsetNode:g,offset:y}=b)}else if(u.caretRangeFromPoint){let b=u.caretRangeFromPoint(e,t);b&&({startContainer:g,startOffset:y}=b,(!n.contentDOM.contains(g)||M.safari&&ou(g,y,e)||M.chrome&&lu(g,y,e))&&(g=void 0))}}if(!g||!n.docView.dom.contains(g)){let b=ye.find(n.docView,f);if(!b)return c>a.top+a.height/2?a.to:a.from;({node:g,offset:y}=Ys(b.dom,e,t))}return n.docView.posFromDOM(g,y)}function Do(n,e,t,i,s){let r=Math.round((i-e.left)*n.defaultCharacterWidth);if(n.lineWrapping&&t.height>n.defaultLineHeight*1.5){let l=Math.floor((s-t.top)/n.defaultLineHeight);r+=l*n.viewState.heightOracle.lineLength}let o=n.state.sliceDoc(t.from,t.to);return t.from+Is(o,r,n.state.tabSize)}function ou(n,e,t){let i;if(n.nodeType!=3||e!=(i=n.nodeValue.length))return!1;for(let s=n.nextSibling;s;s=s.nextSibling)if(s.nodeType!=1||s.nodeName!="BR")return!1;return Jt(n,i-1,i).getBoundingClientRect().left>t}function lu(n,e,t){if(e!=0)return!1;for(let s=n;;){let r=s.parentNode;if(!r||r.nodeType!=1||r.firstChild!=s)return!1;if(r.classList.contains("cm-line"))break;s=r}let i=n.nodeType==1?n.getBoundingClientRect():Jt(n,0,Math.max(n.nodeValue.length,1)).getBoundingClientRect();return t-i.left>5}function au(n,e,t,i){let s=n.state.doc.lineAt(e.head),r=!i||!n.lineWrapping?null:n.coordsAtPos(e.assoc<0&&e.head>s.from?e.head-1:e.head);if(r){let a=n.dom.getBoundingClientRect(),h=n.textDirectionAt(s.from),c=n.posAtCoords({x:t==(h==G.LTR)?a.right-1:a.left+1,y:(r.top+r.bottom)/2});if(c!=null)return w.cursor(c,t?-1:1)}let o=ye.find(n.docView,e.head),l=o?t?o.posAtEnd:o.posAtStart:t?s.to:s.from;return w.cursor(l,t?-1:1)}function To(n,e,t,i){let s=n.state.doc.lineAt(e.head),r=n.bidiSpans(s),o=n.textDirectionAt(s.from);for(let l=e,a=null;;){let h=Jf(s,r,o,l,t),c=eh;if(!h){if(s.number==(t?n.state.doc.lines:1))return l;c=`
`,s=n.state.doc.line(s.number+(t?1:-1)),r=n.bidiSpans(s),h=w.cursor(t?s.from:s.to)}if(a){if(!a(c))return l}else{if(!i)return h;a=i(c)}l=h}}function hu(n,e,t){let i=n.state.charCategorizer(e),s=i(t);return r=>{let o=i(r);return s==Te.Space&&(s=o),s==o}}function cu(n,e,t,i){let s=e.head,r=t?1:-1;if(s==(t?n.state.doc.length:0))return w.cursor(s,e.assoc);let o=e.goalColumn,l,a=n.contentDOM.getBoundingClientRect(),h=n.coordsAtPos(s),c=n.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else{let d=n.viewState.lineBlockAt(s);o==null&&(o=Math.min(a.right-a.left,n.defaultCharacterWidth*(s-d.from))),l=(r<0?d.top:d.bottom)+c}let f=a.left+o,u=i??n.defaultLineHeight>>1;for(let d=0;;d+=10){let p=l+(u+d)*r,g=sh(n,{x:f,y:p},!1,r);if(p<a.top||p>a.bottom||(r<0?g<s:g>s))return w.cursor(g,e.assoc,void 0,o)}}function es(n,e,t){let i=n.state.facet(Ya).map(s=>s(n));for(;;){let s=!1;for(let r of i)r.between(t.from-1,t.from+1,(o,l,a)=>{t.from>o&&t.from<l&&(t=e.head>t.from?w.cursor(o,1):w.cursor(l,-1),s=!0)});if(!s)return t}}class fu{constructor(e){this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.chromeScrollHack=-1,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.registeredEvents=[],this.customHandlers=[],this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.mouseSelection=null;for(let t in ie){let i=ie[t];e.contentDOM.addEventListener(t,s=>{!Oo(e,s)||this.ignoreDuringComposition(s)||t=="keydown"&&this.keydown(e,s)||(this.mustFlushObserver(s)&&e.observer.forceFlush(),this.runCustomHandlers(t,e,s)?s.preventDefault():i(e,s))},Xs[t]),this.registeredEvents.push(t)}M.chrome&&M.chrome_version==102&&e.scrollDOM.addEventListener("wheel",()=>{this.chromeScrollHack<0?e.contentDOM.style.pointerEvents="none":window.clearTimeout(this.chromeScrollHack),this.chromeScrollHack=setTimeout(()=>{this.chromeScrollHack=-1,e.contentDOM.style.pointerEvents=""},100)},{passive:!0}),this.notifiedFocused=e.hasFocus,M.safari&&e.contentDOM.addEventListener("input",()=>null)}setSelectionOrigin(e){this.lastSelectionOrigin=e,this.lastSelectionTime=Date.now()}ensureHandlers(e,t){var i;let s;this.customHandlers=[];for(let r of t)if(s=(i=r.update(e).spec)===null||i===void 0?void 0:i.domEventHandlers){this.customHandlers.push({plugin:r.value,handlers:s});for(let o in s)this.registeredEvents.indexOf(o)<0&&o!="scroll"&&(this.registeredEvents.push(o),e.contentDOM.addEventListener(o,l=>{Oo(e,l)&&this.runCustomHandlers(o,e,l)&&l.preventDefault()}))}}runCustomHandlers(e,t,i){for(let s of this.customHandlers){let r=s.handlers[e];if(r)try{if(r.call(s.plugin,i,t)||i.defaultPrevented)return!0}catch(o){_e(t.state,o)}}return!1}runScrollHandlers(e,t){this.lastScrollTop=e.scrollDOM.scrollTop,this.lastScrollLeft=e.scrollDOM.scrollLeft;for(let i of this.customHandlers){let s=i.handlers.scroll;if(s)try{s.call(i.plugin,t,e)}catch(r){_e(e.state,r)}}}keydown(e,t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),t.keyCode==9&&Date.now()<this.lastEscPress+2e3)return!0;if(M.android&&M.chrome&&!t.synthetic&&(t.keyCode==13||t.keyCode==8))return e.observer.delayAndroidKey(t.key,t.keyCode),!0;let i;return M.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((i=rh.find(s=>s.keyCode==t.keyCode))&&!t.ctrlKey||uu.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=i||t,setTimeout(()=>this.flushIOSKey(e),250),!0):!1}flushIOSKey(e){let t=this.pendingIOSKey;return t?(this.pendingIOSKey=void 0,Kt(e.contentDOM,t.key,t.keyCode)):!1}ignoreDuringComposition(e){return/^key/.test(e.type)?this.composing>0?!0:M.safari&&!M.ios&&Date.now()-this.compositionEndedAt<100?(this.compositionEndedAt=0,!0):!1:!1}mustFlushObserver(e){return e.type=="keydown"&&e.keyCode!=229}startMouseSelection(e){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=e}update(e){this.mouseSelection&&this.mouseSelection.update(e),e.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}const rh=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],uu="dthko",oh=[16,17,18,20,91,92,224,225];class du{constructor(e,t,i,s){this.view=e,this.style=i,this.mustSelect=s,this.lastEvent=t;let r=e.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=t.shiftKey,this.multiple=e.state.facet(N.allowMultipleSelections)&&pu(e,t),this.dragMove=mu(e,t),this.dragging=gu(e,t)&&ch(t)==1?null:!1,this.dragging===!1&&(t.preventDefault(),this.select(t))}move(e){if(e.buttons==0)return this.destroy();this.dragging===!1&&this.select(this.lastEvent=e)}up(e){this.dragging==null&&this.select(this.lastEvent),this.dragging||e.preventDefault(),this.destroy()}destroy(){let e=this.view.contentDOM.ownerDocument;e.removeEventListener("mousemove",this.move),e.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=null}select(e){let t=this.style.get(e,this.extend,this.multiple);(this.mustSelect||!t.eq(this.view.state.selection)||t.main.assoc!=this.view.state.selection.main.assoc)&&this.view.dispatch({selection:t,userEvent:"select.pointer",scrollIntoView:!0}),this.mustSelect=!1}update(e){e.docChanged&&this.dragging&&(this.dragging=this.dragging.map(e.changes)),this.style.update(e)&&setTimeout(()=>this.select(this.lastEvent),20)}}function pu(n,e){let t=n.state.facet($a);return t.length?t[0](e):M.mac?e.metaKey:e.ctrlKey}function mu(n,e){let t=n.state.facet(Wa);return t.length?t[0](e):M.mac?!e.altKey:!e.ctrlKey}function gu(n,e){let{main:t}=n.state.selection;if(t.empty)return!1;let i=mn(n.root);if(!i||i.rangeCount==0)return!0;let s=i.getRangeAt(0).getClientRects();for(let r=0;r<s.length;r++){let o=s[r];if(o.left<=e.clientX&&o.right>=e.clientX&&o.top<=e.clientY&&o.bottom>=e.clientY)return!0}return!1}function Oo(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target,i;t!=n.contentDOM;t=t.parentNode)if(!t||t.nodeType==11||(i=j.get(t))&&i.ignoreEvent(e))return!1;return!0}const ie=Object.create(null),Xs=Object.create(null),lh=M.ie&&M.ie_version<15||M.ios&&M.webkit_version<604;function yu(n){let e=n.dom.parentNode;if(!e)return;let t=e.appendChild(document.createElement("textarea"));t.style.cssText="position: fixed; left: -10000px; top: 10px",t.focus(),setTimeout(()=>{n.focus(),t.remove(),ah(n,t.value)},50)}function ah(n,e){let{state:t}=n,i,s=1,r=t.toText(e),o=r.lines==t.selection.ranges.length;if(Qs!=null&&t.selection.ranges.every(a=>a.empty)&&Qs==r.toString()){let a=-1;i=t.changeByRange(h=>{let c=t.doc.lineAt(h.from);if(c.from==a)return{range:h};a=c.from;let f=t.toText((o?r.line(s++).text:e)+t.lineBreak);return{changes:{from:c.from,insert:f},range:w.cursor(h.from+f.length)}})}else o?i=t.changeByRange(a=>{let h=r.line(s++);return{changes:{from:a.from,to:a.to,insert:h.text},range:w.cursor(a.from+h.length)}}):i=t.replaceSelection(r);n.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}ie.keydown=(n,e)=>{n.inputState.setSelectionOrigin("select"),e.keyCode==27?n.inputState.lastEscPress=Date.now():oh.indexOf(e.keyCode)<0&&(n.inputState.lastEscPress=0)};ie.touchstart=(n,e)=>{n.inputState.lastTouchTime=Date.now(),n.inputState.setSelectionOrigin("select.pointer")};ie.touchmove=n=>{n.inputState.setSelectionOrigin("select.pointer")};Xs.touchstart=Xs.touchmove={passive:!0};ie.mousedown=(n,e)=>{if(n.observer.flush(),n.inputState.lastTouchTime>Date.now()-2e3)return;let t=null;for(let i of n.state.facet(za))if(t=i(n,e),t)break;if(!t&&e.button==0&&(t=ku(n,e)),t){let i=n.root.activeElement!=n.contentDOM;i&&n.observer.ignore(()=>Ta(n.contentDOM)),n.inputState.startMouseSelection(new du(n,e,t,i))}};function Bo(n,e,t,i){if(i==1)return w.cursor(e,t);if(i==2)return nu(n.state,e,t);{let s=ye.find(n.docView,e),r=n.state.doc.lineAt(s?s.posAtEnd:e),o=s?s.posAtStart:r.from,l=s?s.posAtEnd:r.to;return l<n.state.doc.length&&l==r.to&&l++,w.range(o,l)}}let hh=(n,e)=>n>=e.top&&n<=e.bottom,Eo=(n,e,t)=>hh(e,t)&&n>=t.left&&n<=t.right;function bu(n,e,t,i){let s=ye.find(n.docView,e);if(!s)return 1;let r=e-s.posAtStart;if(r==0)return 1;if(r==s.length)return-1;let o=s.coordsAt(r,-1);if(o&&Eo(t,i,o))return-1;let l=s.coordsAt(r,1);return l&&Eo(t,i,l)?1:o&&hh(i,o)?-1:1}function Po(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:t,bias:bu(n,t,e.clientX,e.clientY)}}const wu=M.ie&&M.ie_version<=11;let Lo=null,Ro=0,_o=0;function ch(n){if(!wu)return n.detail;let e=Lo,t=_o;return Lo=n,_o=Date.now(),Ro=!e||t>Date.now()-400&&Math.abs(e.clientX-n.clientX)<2&&Math.abs(e.clientY-n.clientY)<2?(Ro+1)%3:1}function ku(n,e){let t=Po(n,e),i=ch(e),s=n.state.selection,r=t,o=e;return{update(l){l.docChanged&&(t.pos=l.changes.mapPos(t.pos),s=s.map(l.changes),o=null)},get(l,a,h){let c;o&&l.clientX==o.clientX&&l.clientY==o.clientY?c=r:(c=r=Po(n,l),o=l);let f=Bo(n,c.pos,c.bias,i);if(t.pos!=c.pos&&!a){let u=Bo(n,t.pos,t.bias,i),d=Math.min(u.from,f.from),p=Math.max(u.to,f.to);f=d<f.from?w.range(d,p):w.range(p,d)}return a?s.replaceRange(s.main.extend(f.from,f.to)):h&&s.ranges.length>1&&s.ranges.some(u=>u.eq(f))?xu(s,f):h?s.addRange(f):w.create([f])}}}function xu(n,e){for(let t=0;;t++)if(n.ranges[t].eq(e))return w.create(n.ranges.slice(0,t).concat(n.ranges.slice(t+1)),n.mainIndex==t?0:n.mainIndex-(n.mainIndex>t?1:0))}ie.dragstart=(n,e)=>{let{selection:{main:t}}=n.state,{mouseSelection:i}=n.inputState;i&&(i.dragging=t),e.dataTransfer&&(e.dataTransfer.setData("Text",n.state.sliceDoc(t.from,t.to)),e.dataTransfer.effectAllowed="copyMove")};function Io(n,e,t,i){if(!t)return;let s=n.posAtCoords({x:e.clientX,y:e.clientY},!1);e.preventDefault();let{mouseSelection:r}=n.inputState,o=i&&r&&r.dragging&&r.dragMove?{from:r.dragging.from,to:r.dragging.to}:null,l={from:s,insert:t},a=n.state.changes(o?[o,l]:l);n.focus(),n.dispatch({changes:a,selection:{anchor:a.mapPos(s,-1),head:a.mapPos(s,1)},userEvent:o?"move.drop":"input.drop"})}ie.drop=(n,e)=>{if(!e.dataTransfer)return;if(n.state.readOnly)return e.preventDefault();let t=e.dataTransfer.files;if(t&&t.length){e.preventDefault();let i=Array(t.length),s=0,r=()=>{++s==t.length&&Io(n,e,i.filter(o=>o!=null).join(n.state.lineBreak),!1)};for(let o=0;o<t.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r()},l.readAsText(t[o])}}else Io(n,e,e.dataTransfer.getData("Text"),!0)};ie.paste=(n,e)=>{if(n.state.readOnly)return e.preventDefault();n.observer.flush();let t=lh?null:e.clipboardData;t?(ah(n,t.getData("text/plain")),e.preventDefault()):yu(n)};function vu(n,e){let t=n.dom.parentNode;if(!t)return;let i=t.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=e,i.focus(),i.selectionEnd=e.length,i.selectionStart=0,setTimeout(()=>{i.remove(),n.focus()},50)}function Su(n){let e=[],t=[],i=!1;for(let s of n.selection.ranges)s.empty||(e.push(n.sliceDoc(s.from,s.to)),t.push(s));if(!e.length){let s=-1;for(let{from:r}of n.selection.ranges){let o=n.doc.lineAt(r);o.number>s&&(e.push(o.text),t.push({from:o.from,to:Math.min(n.doc.length,o.to+1)})),s=o.number}i=!0}return{text:e.join(n.lineBreak),ranges:t,linewise:i}}let Qs=null;ie.copy=ie.cut=(n,e)=>{let{text:t,ranges:i,linewise:s}=Su(n.state);if(!t&&!s)return;Qs=s?t:null;let r=lh?null:e.clipboardData;r?(e.preventDefault(),r.clearData(),r.setData("text/plain",t)):vu(n,t),e.type=="cut"&&!n.state.readOnly&&n.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"})};function fh(n){setTimeout(()=>{n.hasFocus!=n.inputState.notifiedFocused&&n.update([])},10)}ie.focus=n=>{n.inputState.lastFocusTime=Date.now(),!n.scrollDOM.scrollTop&&(n.inputState.lastScrollTop||n.inputState.lastScrollLeft)&&(n.scrollDOM.scrollTop=n.inputState.lastScrollTop,n.scrollDOM.scrollLeft=n.inputState.lastScrollLeft),fh(n)};ie.blur=n=>{n.observer.clearSelectionRange(),fh(n)};ie.compositionstart=ie.compositionupdate=n=>{n.inputState.compositionFirstChange==null&&(n.inputState.compositionFirstChange=!0),n.inputState.composing<0&&(n.inputState.composing=0)};ie.compositionend=n=>{n.inputState.composing=-1,n.inputState.compositionEndedAt=Date.now(),n.inputState.compositionFirstChange=null,M.chrome&&M.android&&n.observer.flushSoon(),setTimeout(()=>{n.inputState.composing<0&&n.docView.compositionDeco.size&&n.update([])},50)};ie.contextmenu=n=>{n.inputState.lastContextMenu=Date.now()};ie.beforeinput=(n,e)=>{var t;let i;if(M.chrome&&M.android&&(i=rh.find(s=>s.inputType==e.inputType))&&(n.observer.delayAndroidKey(i.key,i.keyCode),i.key=="Backspace"||i.key=="Delete")){let s=((t=window.visualViewport)===null||t===void 0?void 0:t.height)||0;setTimeout(()=>{var r;(((r=window.visualViewport)===null||r===void 0?void 0:r.height)||0)>s+10&&n.hasFocus&&(n.contentDOM.blur(),n.focus())},100)}};const No=["pre-wrap","normal","pre-line","break-spaces"];class Cu{constructor(){this.doc=V.empty,this.lineWrapping=!1,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.lineLength=30,this.heightChanged=!1}heightForGap(e,t){let i=this.doc.lineAt(t).number-this.doc.lineAt(e).number+1;return this.lineWrapping&&(i+=Math.ceil((t-e-i*this.lineLength*.5)/this.lineLength)),this.lineHeight*i}heightForLine(e){return this.lineWrapping?(1+Math.max(0,Math.ceil((e-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(e){return this.doc=e,this}mustRefreshForWrapping(e){return No.indexOf(e)>-1!=this.lineWrapping}mustRefreshForHeights(e){let t=!1;for(let i=0;i<e.length;i++){let s=e[i];s<0?i++:this.heightSamples[Math.floor(s*10)]||(t=!0,this.heightSamples[Math.floor(s*10)]=!0)}return t}refresh(e,t,i,s,r){let o=No.indexOf(e)>-1,l=Math.round(t)!=Math.round(this.lineHeight)||this.lineWrapping!=o;if(this.lineWrapping=o,this.lineHeight=t,this.charWidth=i,this.lineLength=s,l){this.heightSamples={};for(let a=0;a<r.length;a++){let h=r[a];h<0?a++:this.heightSamples[Math.floor(h*10)]=!0}}return l}}class Au{constructor(e,t){this.from=e,this.heights=t,this.index=0}get more(){return this.index<this.heights.length}}class ct{constructor(e,t,i,s,r){this.from=e,this.length=t,this.top=i,this.height=s,this.type=r}get to(){return this.from+this.length}get bottom(){return this.top+this.height}join(e){let t=(Array.isArray(this.type)?this.type:[this]).concat(Array.isArray(e.type)?e.type:[e]);return new ct(this.from,this.length+e.length,this.top,this.height+e.height,t)}}var q=function(n){return n[n.ByPos=0]="ByPos",n[n.ByHeight=1]="ByHeight",n[n.ByPosNoHeight=2]="ByPosNoHeight",n}(q||(q={}));const an=.001;class be{constructor(e,t,i=2){this.length=e,this.height=t,this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(e){this.flags=(e?2:0)|this.flags&-3}setHeight(e,t){this.height!=t&&(Math.abs(this.height-t)>an&&(e.heightChanged=!0),this.height=t)}replace(e,t,i){return be.of(i)}decomposeLeft(e,t){t.push(this)}decomposeRight(e,t){t.push(this)}applyChanges(e,t,i,s){let r=this;for(let o=s.length-1;o>=0;o--){let{fromA:l,toA:a,fromB:h,toB:c}=s[o],f=r.lineAt(l,q.ByPosNoHeight,t,0,0),u=f.to>=a?f:r.lineAt(a,q.ByPosNoHeight,t,0,0);for(c+=u.to-a,a=u.to;o>0&&f.from<=s[o-1].toA;)l=s[o-1].fromA,h=s[o-1].fromB,o--,l<f.from&&(f=r.lineAt(l,q.ByPosNoHeight,t,0,0));h+=f.from-l,l=f.from;let d=Ar.build(i,e,h,c);r=r.replace(l,a,d)}return r.updateHeight(i,0)}static empty(){return new Se(0,0)}static of(e){if(e.length==1)return e[0];let t=0,i=e.length,s=0,r=0;for(;;)if(t==i)if(s>r*2){let l=e[t-1];l.break?e.splice(--t,1,l.left,null,l.right):e.splice(--t,1,l.left,l.right),i+=1+l.break,s-=l.size}else if(r>s*2){let l=e[i];l.break?e.splice(i,1,l.left,null,l.right):e.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size}else break;else if(s<r){let l=e[t++];l&&(s+=l.size)}else{let l=e[--i];l&&(r+=l.size)}let o=0;return e[t-1]==null?(o=1,t--):e[t]==null&&(o=1,i++),new Mu(be.of(e.slice(0,t)),o,be.of(e.slice(i)))}}be.prototype.size=1;class uh extends be{constructor(e,t,i){super(e,t),this.type=i}blockAt(e,t,i,s){return new ct(s,this.length,i,this.height,this.type)}lineAt(e,t,i,s,r){return this.blockAt(0,i,s,r)}forEachLine(e,t,i,s,r,o){e<=r+this.length&&t>=r&&o(this.blockAt(0,i,s,r))}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more&&this.setHeight(e,s.heights[s.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Se extends uh{constructor(e,t){super(e,t,$.Text),this.collapsed=0,this.widgetHeight=0}replace(e,t,i){let s=i[0];return i.length==1&&(s instanceof Se||s instanceof se&&s.flags&4)&&Math.abs(this.length-s.length)<10?(s instanceof se?s=new Se(s.length,this.height):s.height=this.height,this.outdated||(s.outdated=!1),s):be.of(i)}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more?this.setHeight(e,s.heights[s.index++]):(i||this.outdated)&&this.setHeight(e,Math.max(this.widgetHeight,e.heightForLine(this.length-this.collapsed))),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class se extends be{constructor(e){super(e,0)}lines(e,t){let i=e.lineAt(t).number,s=e.lineAt(t+this.length).number;return{firstLine:i,lastLine:s,lineHeight:this.height/(s-i+1)}}blockAt(e,t,i,s){let{firstLine:r,lastLine:o,lineHeight:l}=this.lines(t,s),a=Math.max(0,Math.min(o-r,Math.floor((e-i)/l))),{from:h,length:c}=t.line(r+a);return new ct(h,c,i+l*a,l,$.Text)}lineAt(e,t,i,s,r){if(t==q.ByHeight)return this.blockAt(e,i,s,r);if(t==q.ByPosNoHeight){let{from:f,to:u}=i.lineAt(e);return new ct(f,u-f,0,0,$.Text)}let{firstLine:o,lineHeight:l}=this.lines(i,r),{from:a,length:h,number:c}=i.lineAt(e);return new ct(a,h,s+l*(c-o),l,$.Text)}forEachLine(e,t,i,s,r,o){let{firstLine:l,lineHeight:a}=this.lines(i,r);for(let h=Math.max(e,r),c=Math.min(r+this.length,t);h<=c;){let f=i.lineAt(h);h==e&&(s+=a*(f.number-l)),o(new ct(f.from,f.length,s,a,$.Text)),s+=a,h=f.to+1}}replace(e,t,i){let s=this.length-t;if(s>0){let r=i[i.length-1];r instanceof se?i[i.length-1]=new se(r.length+s):i.push(null,new se(s-1))}if(e>0){let r=i[0];r instanceof se?i[0]=new se(e+r.length):i.unshift(new se(e-1),null)}return be.of(i)}decomposeLeft(e,t){t.push(new se(e-1),null)}decomposeRight(e,t){t.push(null,new se(this.length-e-1))}updateHeight(e,t=0,i=!1,s){let r=t+this.length;if(s&&s.from<=t+this.length&&s.more){let o=[],l=Math.max(t,s.from),a=-1,h=e.heightChanged;for(s.from>t&&o.push(new se(s.from-t-1).updateHeight(e,t));l<=r&&s.more;){let f=e.doc.lineAt(l).length;o.length&&o.push(null);let u=s.heights[s.index++];a==-1?a=u:Math.abs(u-a)>=an&&(a=-2);let d=new Se(f,u);d.outdated=!1,o.push(d),l+=f+1}l<=r&&o.push(null,new se(r-l).updateHeight(e,l));let c=be.of(o);return e.heightChanged=h||a<0||Math.abs(c.height-this.height)>=an||Math.abs(a-this.lines(e.doc,t).lineHeight)>=an,c}else(i||this.outdated)&&(this.setHeight(e,e.heightForGap(t,t+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class Mu extends be{constructor(e,t,i){super(e.length+t+i.length,e.height+i.height,t|(e.outdated||i.outdated?2:0)),this.left=e,this.right=i,this.size=e.size+i.size}get break(){return this.flags&1}blockAt(e,t,i,s){let r=i+this.left.height;return e<r?this.left.blockAt(e,t,i,s):this.right.blockAt(e,t,r,s+this.left.length+this.break)}lineAt(e,t,i,s,r){let o=s+this.left.height,l=r+this.left.length+this.break,a=t==q.ByHeight?e<o:e<l,h=a?this.left.lineAt(e,t,i,s,r):this.right.lineAt(e,t,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let c=t==q.ByPosNoHeight?q.ByPosNoHeight:q.ByPos;return a?h.join(this.right.lineAt(l,c,i,o,l)):this.left.lineAt(l,c,i,s,r).join(h)}forEachLine(e,t,i,s,r,o){let l=s+this.left.height,a=r+this.left.length+this.break;if(this.break)e<a&&this.left.forEachLine(e,t,i,s,r,o),t>=a&&this.right.forEachLine(e,t,i,l,a,o);else{let h=this.lineAt(a,q.ByPos,i,s,r);e<h.from&&this.left.forEachLine(e,h.from-1,i,s,r,o),h.to>=e&&h.from<=t&&o(h),t>h.to&&this.right.forEachLine(h.to+1,t,i,l,a,o)}}replace(e,t,i){let s=this.left.length+this.break;if(t<s)return this.balanced(this.left.replace(e,t,i),this.right);if(e>this.left.length)return this.balanced(this.left,this.right.replace(e-s,t-s,i));let r=[];e>0&&this.decomposeLeft(e,r);let o=r.length;for(let l of i)r.push(l);if(e>0&&Vo(r,o-1),t<this.length){let l=r.length;this.decomposeRight(t,r),Vo(r,l)}return be.of(r)}decomposeLeft(e,t){let i=this.left.length;if(e<=i)return this.left.decomposeLeft(e,t);t.push(this.left),this.break&&(i++,e>=i&&t.push(null)),e>i&&this.right.decomposeLeft(e-i,t)}decomposeRight(e,t){let i=this.left.length,s=i+this.break;if(e>=s)return this.right.decomposeRight(e-s,t);e<i&&this.left.decomposeRight(e,t),this.break&&e<s&&t.push(null),t.push(this.right)}balanced(e,t){return e.size>2*t.size||t.size>2*e.size?be.of(this.break?[e,null,t]:[e,t]):(this.left=e,this.right=t,this.height=e.height+t.height,this.outdated=e.outdated||t.outdated,this.size=e.size+t.size,this.length=e.length+this.break+t.length,this)}updateHeight(e,t=0,i=!1,s){let{left:r,right:o}=this,l=t+r.length+this.break,a=null;return s&&s.from<=t+r.length&&s.more?a=r=r.updateHeight(e,t,i,s):r.updateHeight(e,t,i),s&&s.from<=l+o.length&&s.more?a=o=o.updateHeight(e,l,i,s):o.updateHeight(e,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function Vo(n,e){let t,i;n[e]==null&&(t=n[e-1])instanceof se&&(i=n[e+1])instanceof se&&n.splice(e-1,3,new se(t.length+1+i.length))}const Du=5;class Ar{constructor(e,t){this.pos=e,this.oracle=t,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=e}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(e,t){if(this.lineStart>-1){let i=Math.min(t,this.lineEnd),s=this.nodes[this.nodes.length-1];s instanceof Se?s.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new Se(i-this.pos,-1)),this.writtenTo=i,t>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=t}point(e,t,i){if(e<t||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0;s<0&&(s=this.oracle.lineHeight);let r=t-e;i.block?this.addBlock(new uh(r,s,i.type)):(r||s>=Du)&&this.addLineDeco(s,r)}else t>e&&this.span(e,t);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:e,to:t}=this.oracle.doc.lineAt(this.pos);this.lineStart=e,this.lineEnd=t,this.writtenTo<e&&((this.writtenTo<e-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,e-1)),this.nodes.push(null)),this.pos>e&&this.nodes.push(new Se(this.pos-e,-1)),this.writtenTo=this.pos}blankContent(e,t){let i=new se(t-e);return this.oracle.doc.lineAt(e).to==t&&(i.flags|=4),i}ensureLine(){this.enterLine();let e=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(e instanceof Se)return e;let t=new Se(0,-1);return this.nodes.push(t),t}addBlock(e){this.enterLine(),e.type==$.WidgetAfter&&!this.isCovered&&this.ensureLine(),this.nodes.push(e),this.writtenTo=this.pos=this.pos+e.length,e.type!=$.WidgetBefore&&(this.covering=e)}addLineDeco(e,t){let i=this.ensureLine();i.length+=t,i.collapsed+=t,i.widgetHeight=Math.max(i.widgetHeight,e),this.writtenTo=this.pos=this.pos+t}finish(e){let t=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(t instanceof Se)&&!this.isCovered?this.nodes.push(new Se(0,-1)):(this.writtenTo<this.pos||t==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=e;for(let s of this.nodes)s instanceof Se&&s.updateHeight(this.oracle,i),i+=s?s.length:1;return this.nodes}static build(e,t,i,s){let r=new Ar(i,e);return H.spans(t,i,s,r,0),r.finish(i)}}function Tu(n,e,t){let i=new Ou;return H.compare(n,e,t,i,0),i.changes}class Ou{constructor(){this.changes=[]}compareRange(){}comparePoint(e,t,i,s){(e<t||i&&i.heightRelevant||s&&s.heightRelevant)&&Ks(e,t,this.changes,5)}}function Bu(n,e){let t=n.getBoundingClientRect(),i=n.ownerDocument,s=i.defaultView||window,r=Math.max(0,t.left),o=Math.min(s.innerWidth,t.right),l=Math.max(0,t.top),a=Math.min(s.innerHeight,t.bottom);for(let h=n.parentNode;h&&h!=i.body;)if(h.nodeType==1){let c=h,f=window.getComputedStyle(c);if((c.scrollHeight>c.clientHeight||c.scrollWidth>c.clientWidth)&&f.overflow!="visible"){let u=c.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=h==n.parentNode?u.bottom:Math.min(a,u.bottom)}h=f.position=="absolute"||f.position=="fixed"?c.offsetParent:c.parentNode}else if(h.nodeType==11)h=h.host;else break;return{left:r-t.left,right:Math.max(r,o)-t.left,top:l-(t.top+e),bottom:Math.max(l,a)-(t.top+e)}}function Eu(n,e){let t=n.getBoundingClientRect();return{left:0,right:t.right-t.left,top:e,bottom:t.bottom-(t.top+e)}}class ts{constructor(e,t,i){this.from=e,this.to=t,this.size=i}static same(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++){let s=e[i],r=t[i];if(s.from!=r.from||s.to!=r.to||s.size!=r.size)return!1}return!0}draw(e){return L.replace({widget:new Pu(this.size,e)}).range(this.from,this.to)}}class Pu extends Qe{constructor(e,t){super(),this.size=e,this.vertical=t}eq(e){return e.size==this.size&&e.vertical==this.vertical}toDOM(){let e=document.createElement("div");return this.vertical?e.style.height=this.size+"px":(e.style.width=this.size+"px",e.style.height="2px",e.style.display="inline-block"),e}get estimatedHeight(){return this.vertical?this.size:-1}}class Fo{constructor(e){this.state=e,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.heightOracle=new Cu,this.scaler=Ho,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=G.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1,this.stateDeco=e.facet(Di).filter(t=>typeof t!="function"),this.heightMap=be.empty().applyChanges(this.stateDeco,V.empty,this.heightOracle.setDoc(e.doc),[new Ye(0,0,0,e.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=L.set(this.lineGaps.map(t=>t.draw(!1))),this.computeVisibleRanges()}updateForViewport(){let e=[this.viewport],{main:t}=this.state.selection;for(let i=0;i<=1;i++){let s=i?t.head:t.anchor;if(!e.some(({from:r,to:o})=>s>=r&&s<=o)){let{from:r,to:o}=this.lineBlockAt(s);e.push(new zi(r,o))}}this.viewports=e.sort((i,s)=>i.from-s.from),this.scaler=this.heightMap.height<=7e6?Ho:new _u(this.heightOracle.doc,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.state.doc,0,0,e=>{this.viewportLines.push(this.scaler.scale==1?e:di(e,this.scaler))})}update(e,t=null){this.state=e.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Di).filter(h=>typeof h!="function");let s=e.changedRanges,r=Ye.extendWithRanges(s,Tu(i,this.stateDeco,e?e.changes:Z.empty(this.state.doc.length))),o=this.heightMap.height;this.heightMap=this.heightMap.applyChanges(this.stateDeco,e.startState.doc,this.heightOracle.setDoc(this.state.doc),r),this.heightMap.height!=o&&(e.flags|=2);let l=r.length?this.mapViewport(this.viewport,e.changes):this.viewport;(t&&(t.range.head<l.from||t.range.head>l.to)||!this.viewportIsAppropriate(l))&&(l=this.getViewport(0,t));let a=!e.changes.empty||e.flags&2||l.from!=this.viewport.from||l.to!=this.viewport.to;this.viewport=l,this.updateForViewport(),a&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,e.changes))),e.flags|=this.computeVisibleRanges(),t&&(this.scrollTarget=t),!this.mustEnforceCursorAssoc&&e.selectionSet&&e.view.lineWrapping&&e.state.selection.main.empty&&e.state.selection.main.assoc&&!e.state.facet(Ua)&&(this.mustEnforceCursorAssoc=!0)}measure(e){let t=e.contentDOM,i=window.getComputedStyle(t),s=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?G.RTL:G.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=o||this.mustMeasureContent||this.contentDOMHeight!=t.clientHeight;this.contentDOMHeight=t.clientHeight,this.mustMeasureContent=!1;let a=0,h=0,c=parseInt(i.paddingTop)||0,f=parseInt(i.paddingBottom)||0;(this.paddingTop!=c||this.paddingBottom!=f)&&(this.paddingTop=c,this.paddingBottom=f,a|=10),this.editorWidth!=e.scrollDOM.clientWidth&&(s.lineWrapping&&(l=!0),this.editorWidth=e.scrollDOM.clientWidth,a|=8);let u=(this.printing?Eu:Bu)(t,this.paddingTop),d=u.top-this.pixelViewport.top,p=u.bottom-this.pixelViewport.bottom;this.pixelViewport=u;let g=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(g!=this.inView&&(this.inView=g,g&&(l=!0)),!this.inView&&!this.scrollTarget)return 0;let y=t.clientWidth;if((this.contentDOMWidth!=y||this.editorHeight!=e.scrollDOM.clientHeight)&&(this.contentDOMWidth=y,this.editorHeight=e.scrollDOM.clientHeight,a|=8),l){let k=e.docView.measureVisibleLineHeights(this.viewport);if(s.mustRefreshForHeights(k)&&(o=!0),o||s.lineWrapping&&Math.abs(y-this.contentDOMWidth)>s.charWidth){let{lineHeight:v,charWidth:S}=e.docView.measureTextSize();o=v>0&&s.refresh(r,v,S,y/S,k),o&&(e.docView.minWidth=0,a|=8)}d>0&&p>0?h=Math.max(d,p):d<0&&p<0&&(h=Math.min(d,p)),s.heightChanged=!1;for(let v of this.viewports){let S=v.from==this.viewport.from?k:e.docView.measureVisibleLineHeights(v);this.heightMap=o?be.empty().applyChanges(this.stateDeco,V.empty,this.heightOracle,[new Ye(0,0,0,e.state.doc.length)]):this.heightMap.updateHeight(s,0,o,new Au(v.from,S))}s.heightChanged&&(a|=2)}let b=!this.viewportIsAppropriate(this.viewport,h)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return b&&(this.viewport=this.getViewport(h,this.scrollTarget)),this.updateForViewport(),(a&2||b)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,e)),a|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,e.docView.enforceCursorAssoc()),a}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(e,t){let i=.5-Math.max(-.5,Math.min(.5,e/1e3/2)),s=this.heightMap,r=this.state.doc,{visibleTop:o,visibleBottom:l}=this,a=new zi(s.lineAt(o-i*1e3,q.ByHeight,r,0,0).from,s.lineAt(l+(1-i)*1e3,q.ByHeight,r,0,0).to);if(t){let{head:h}=t.range;if(h<a.from||h>a.to){let c=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),f=s.lineAt(h,q.ByPos,r,0,0),u;t.y=="center"?u=(f.top+f.bottom)/2-c/2:t.y=="start"||t.y=="nearest"&&h<a.from?u=f.top:u=f.bottom-c,a=new zi(s.lineAt(u-1e3/2,q.ByHeight,r,0,0).from,s.lineAt(u+c+1e3/2,q.ByHeight,r,0,0).to)}}return a}mapViewport(e,t){let i=t.mapPos(e.from,-1),s=t.mapPos(e.to,1);return new zi(this.heightMap.lineAt(i,q.ByPos,this.state.doc,0,0).from,this.heightMap.lineAt(s,q.ByPos,this.state.doc,0,0).to)}viewportIsAppropriate({from:e,to:t},i=0){if(!this.inView)return!0;let{top:s}=this.heightMap.lineAt(e,q.ByPos,this.state.doc,0,0),{bottom:r}=this.heightMap.lineAt(t,q.ByPos,this.state.doc,0,0),{visibleTop:o,visibleBottom:l}=this;return(e==0||s<=o-Math.max(10,Math.min(-i,250)))&&(t==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&s>o-2*1e3&&r<l+2*1e3}mapLineGaps(e,t){if(!e.length||t.empty)return e;let i=[];for(let s of e)t.touchesRange(s.from,s.to)||i.push(new ts(t.mapPos(s.from),t.mapPos(s.to),s.size));return i}ensureLineGaps(e,t){let i=this.heightOracle.lineWrapping,s=i?1e4:2e3,r=s>>1,o=s<<1;if(this.defaultTextDirection!=G.LTR&&!i)return[];let l=[],a=(h,c,f,u)=>{if(c-h<r)return;let d=this.state.selection.main,p=[d.from];d.empty||p.push(d.to);for(let y of p)if(y>h&&y<c){a(h,y-10,f,u),a(y+10,c,f,u);return}let g=Ru(e,y=>y.from>=f.from&&y.to<=f.to&&Math.abs(y.from-h)<r&&Math.abs(y.to-c)<r&&!p.some(b=>y.from<b&&y.to>b));if(!g){if(c<f.to&&t&&i&&t.visibleRanges.some(y=>y.from<=c&&y.to>=c)){let y=t.moveToLineBoundary(w.cursor(c),!1,!0).head;y>h&&(c=y)}g=new ts(h,c,this.gapSize(f,h,c,u))}l.push(g)};for(let h of this.viewportLines){if(h.length<o)continue;let c=Lu(h.from,h.to,this.stateDeco);if(c.total<o)continue;let f=this.scrollTarget?this.scrollTarget.range.head:null,u,d;if(i){let p=s/this.heightOracle.lineLength*this.heightOracle.lineHeight,g,y;if(f!=null){let b=ji(c,f),k=((this.visibleBottom-this.visibleTop)/2+p)/h.height;g=b-k,y=b+k}else g=(this.visibleTop-h.top-p)/h.height,y=(this.visibleBottom-h.top+p)/h.height;u=qi(c,g),d=qi(c,y)}else{let p=c.total*this.heightOracle.charWidth,g=s*this.heightOracle.charWidth,y,b;if(f!=null){let k=ji(c,f),v=((this.pixelViewport.right-this.pixelViewport.left)/2+g)/p;y=k-v,b=k+v}else y=(this.pixelViewport.left-g)/p,b=(this.pixelViewport.right+g)/p;u=qi(c,y),d=qi(c,b)}u>h.from&&a(h.from,u,h,c),d<h.to&&a(d,h.to,h,c)}return l}gapSize(e,t,i,s){let r=ji(s,i)-ji(s,t);return this.heightOracle.lineWrapping?e.height*r:s.total*this.heightOracle.charWidth*r}updateLineGaps(e){ts.same(e,this.lineGaps)||(this.lineGaps=e,this.lineGapDeco=L.set(e.map(t=>t.draw(this.heightOracle.lineWrapping))))}computeVisibleRanges(){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let t=[];H.spans(e,this.viewport.from,this.viewport.to,{span(s,r){t.push({from:s,to:r})},point(){}},20);let i=t.length!=this.visibleRanges.length||this.visibleRanges.some((s,r)=>s.from!=t[r].from||s.to!=t[r].to);return this.visibleRanges=t,i?4:0}lineBlockAt(e){return e>=this.viewport.from&&e<=this.viewport.to&&this.viewportLines.find(t=>t.from<=e&&t.to>=e)||di(this.heightMap.lineAt(e,q.ByPos,this.state.doc,0,0),this.scaler)}lineBlockAtHeight(e){return di(this.heightMap.lineAt(this.scaler.fromDOM(e),q.ByHeight,this.state.doc,0,0),this.scaler)}elementAtHeight(e){return di(this.heightMap.blockAt(this.scaler.fromDOM(e),this.state.doc,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class zi{constructor(e,t){this.from=e,this.to=t}}function Lu(n,e,t){let i=[],s=n,r=0;return H.spans(t,n,e,{span(){},point(o,l){o>s&&(i.push({from:s,to:o}),r+=o-s),s=l}},20),s<e&&(i.push({from:s,to:e}),r+=e-s),{total:r,ranges:i}}function qi({total:n,ranges:e},t){if(t<=0)return e[0].from;if(t>=1)return e[e.length-1].to;let i=Math.floor(n*t);for(let s=0;;s++){let{from:r,to:o}=e[s],l=o-r;if(i<=l)return r+i;i-=l}}function ji(n,e){let t=0;for(let{from:i,to:s}of n.ranges){if(e<=s){t+=e-i;break}t+=s-i}return t/n.total}function Ru(n,e){for(let t of n)if(e(t))return t}const Ho={toDOM(n){return n},fromDOM(n){return n},scale:1};class _u{constructor(e,t,i){let s=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=t.lineAt(l,q.ByPos,e,0,0).top,c=t.lineAt(a,q.ByPos,e,0,0).bottom;return s+=c-h,{from:l,to:a,top:h,bottom:c,domTop:0,domBottom:0}}),this.scale=(7e6-s)/(t.height-s);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom}toDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.top)return s+(e-i)*this.scale;if(e<=r.bottom)return r.domTop+(e-r.top);i=r.bottom,s=r.domBottom}}fromDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.domTop)return i+(e-s)/this.scale;if(e<=r.domBottom)return r.top+(e-r.domTop);i=r.bottom,s=r.domBottom}}}function di(n,e){if(e.scale==1)return n;let t=e.toDOM(n.top),i=e.toDOM(n.bottom);return new ct(n.from,n.length,t,i-t,Array.isArray(n.type)?n.type.map(s=>di(s,e)):n.type)}const Ki=T.define({combine:n=>n.join(" ")}),Zs=T.define({combine:n=>n.indexOf(!0)>-1}),er=ft.newName(),dh=ft.newName(),ph=ft.newName(),mh={"&light":"."+dh,"&dark":"."+ph};function tr(n,e,t){return new ft(e,{finish(i){return/&/.test(i)?i.replace(/&\w*/,s=>{if(s=="&")return n;if(!t||!t[s])throw new RangeError(`Unsupported selector: ${s}`);return t[s]}):n+" "+i}})}const Iu=tr("."+er,{"&.cm-editor":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,minHeight:"100%",display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 4px"},".cm-selectionLayer":{zIndex:-1,contain:"size style"},".cm-selectionBackground":{position:"absolute"},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{zIndex:100,contain:"size style",pointerEvents:"none"},"&.cm-focused .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{position:"absolute",borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},"&.cm-focused .cm-cursor":{display:"block"},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",left:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},mh);class Nu{constructor(e,t,i,s){this.typeOver=s,this.bounds=null,this.text="";let{impreciseHead:r,impreciseAnchor:o}=e.docView;if(t>-1&&!e.state.readOnly&&(this.bounds=e.docView.domBoundsAround(t,i,0))){let l=r||o?[]:Fu(e),a=new th(l,e.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=Hu(l,this.bounds.from)}else{let l=e.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!Gt(e.contentDOM,l.focusNode)?e.state.selection.main.head:e.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!Gt(e.contentDOM,l.anchorNode)?e.state.selection.main.anchor:e.docView.posFromDOM(l.anchorNode,l.anchorOffset);this.newSel=w.single(h,a)}}}function gh(n,e){let t,{newSel:i}=e,s=n.state.selection.main;if(e.bounds){let{from:r,to:o}=e.bounds,l=s.from,a=null;(n.inputState.lastKeyCode===8&&n.inputState.lastKeyTime>Date.now()-100||M.android&&e.text.length<o-r)&&(l=s.to,a="end");let h=Vu(n.state.doc.sliceString(r,o,ht),e.text,l-r,a);h&&(M.chrome&&n.inputState.lastKeyCode==13&&h.toB==h.from+2&&e.text.slice(h.from,h.toB)==ht+ht&&h.toB--,t={from:r+h.from,to:r+h.toA,insert:V.of(e.text.slice(h.from,h.toB).split(ht))})}else i&&(!n.hasFocus||!n.state.facet(Nn)||i.main.eq(s))&&(i=null);if(!t&&!i)return!1;if(!t&&e.typeOver&&!s.empty&&i&&i.main.empty?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,s.to)}:t&&t.from>=s.from&&t.to<=s.to&&(t.from!=s.from||t.to!=s.to)&&s.to-s.from-(t.to-t.from)<=4?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,t.from).append(t.insert).append(n.state.doc.slice(t.to,s.to))}:(M.mac||M.android)&&t&&t.from==t.to&&t.from==s.head-1&&/^\. ?$/.test(t.insert.toString())?(i&&t.insert.length==2&&(i=w.single(i.main.anchor-1,i.main.head-1)),t={from:s.from,to:s.to,insert:V.of([" "])}):M.chrome&&t&&t.from==t.to&&t.from==s.head&&t.insert.toString()==`
 `&&n.lineWrapping&&(i&&(i=w.single(i.main.anchor-1,i.main.head-1)),t={from:s.from,to:s.to,insert:V.of([" "])}),t){let r=n.state;if(M.ios&&n.inputState.flushIOSKey(n)||M.android&&(t.from==s.from&&t.to==s.to&&t.insert.length==1&&t.insert.lines==2&&Kt(n.contentDOM,"Enter",13)||t.from==s.from-1&&t.to==s.to&&t.insert.length==0&&Kt(n.contentDOM,"Backspace",8)||t.from==s.from&&t.to==s.to+1&&t.insert.length==0&&Kt(n.contentDOM,"Delete",46)))return!0;let o=t.insert.toString();if(n.state.facet(ja).some(h=>h(n,t.from,t.to,o)))return!0;n.inputState.composing>=0&&n.inputState.composing++;let l;if(t.from>=s.from&&t.to<=s.to&&t.to-t.from>=(s.to-s.from)/3&&(!i||i.main.empty&&i.main.from==t.from+t.insert.length)&&n.inputState.composing<0){let h=s.from<t.from?r.sliceDoc(s.from,t.from):"",c=s.to>t.to?r.sliceDoc(t.to,s.to):"";l=r.replaceSelection(n.state.toText(h+t.insert.sliceString(0,void 0,n.state.lineBreak)+c))}else{let h=r.changes(t),c=i&&!r.selection.main.eq(i.main)&&i.main.to<=h.newLength?i.main:void 0;if(r.selection.ranges.length>1&&n.inputState.composing>=0&&t.to<=s.to&&t.to>=s.to-10){let f=n.state.sliceDoc(t.from,t.to),u=ih(n)||n.state.doc.lineAt(s.head),d=s.to-t.to,p=s.to-s.from;l=r.changeByRange(g=>{if(g.from==s.from&&g.to==s.to)return{changes:h,range:c||g.map(h)};let y=g.to-d,b=y-f.length;if(g.to-g.from!=p||n.state.sliceDoc(b,y)!=f||u&&g.to>=u.from&&g.from<=u.to)return{range:g};let k=r.changes({from:b,to:y,insert:t.insert}),v=g.to-s.to;return{changes:k,range:c?w.range(Math.max(0,c.anchor+v),Math.max(0,c.head+v)):g.map(k)}})}else l={changes:h,selection:c&&r.selection.replaceRange(c)}}let a="input.type";return n.composing&&(a+=".compose",n.inputState.compositionFirstChange&&(a+=".start",n.inputState.compositionFirstChange=!1)),n.dispatch(l,{scrollIntoView:!0,userEvent:a}),!0}else if(i&&!i.main.eq(s)){let r=!1,o="select";return n.inputState.lastSelectionTime>Date.now()-50&&(n.inputState.lastSelectionOrigin=="select"&&(r=!0),o=n.inputState.lastSelectionOrigin),n.dispatch({selection:i,scrollIntoView:r,userEvent:o}),!0}else return!1}function Vu(n,e,t,i){let s=Math.min(n.length,e.length),r=0;for(;r<s&&n.charCodeAt(r)==e.charCodeAt(r);)r++;if(r==s&&n.length==e.length)return null;let o=n.length,l=e.length;for(;o>0&&l>0&&n.charCodeAt(o-1)==e.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));t-=o+a-r}if(o<r&&n.length<e.length){let a=t<=r&&t>=o?r-t:0;r-=a,l=r+(l-o),o=r}else if(l<r){let a=t<=r&&t>=l?r-t:0;r-=a,o=r+(o-l),l=r}return{from:r,toA:o,toB:l}}function Fu(n){let e=[];if(n.root.activeElement!=n.contentDOM)return e;let{anchorNode:t,anchorOffset:i,focusNode:s,focusOffset:r}=n.observer.selectionRange;return t&&(e.push(new xo(t,i)),(s!=t||r!=i)&&e.push(new xo(s,r))),e}function Hu(n,e){if(n.length==0)return null;let t=n[0].pos,i=n.length==2?n[1].pos:t;return t>-1&&i>-1?w.single(t+e,i+e):null}const $u={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},is=M.ie&&M.ie_version<=11;class Wu{constructor(e){this.view=e,this.active=!1,this.selectionRange=new _f,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resize=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.parentCheck=-1,this.dom=e.contentDOM,this.observer=new MutationObserver(t=>{for(let i of t)this.queue.push(i);(M.ie&&M.ie_version<=11||M.ios&&e.composing)&&t.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),is&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),typeof ResizeObserver=="function"&&(this.resize=new ResizeObserver(()=>{var t;((t=this.view.docView)===null||t===void 0?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()}),this.resize.observe(e.scrollDOM)),this.addWindowListeners(this.win=e.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(e){this.view.inputState.runScrollHandlers(this.view,e),this.intersecting&&this.view.measure()}onScroll(e){this.intersecting&&this.flush(!1),this.onScrollChanged(e)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(){this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500)}updateGaps(e){if(this.gapIntersection&&(e.length!=this.gaps.length||this.gaps.some((t,i)=>t!=e[i]))){this.gapIntersection.disconnect();for(let t of e)this.gapIntersection.observe(t);this.gaps=e}}onSelectionChange(e){let t=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(Nn)?i.root.activeElement!=this.dom:!ln(i.dom,s))return;let r=s.anchorNode&&i.docView.nearest(s.anchorNode);if(r&&r.ignoreEvent(e)){t||(this.selectionChanged=!1);return}(M.ie&&M.ie_version<=11||M.android&&M.chrome)&&!i.state.selection.main.empty&&s.focusNode&&gn(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:e}=this,t=M.safari&&e.root.nodeType==11&&Pf(this.dom.ownerDocument)==this.dom&&zu(this.view)||mn(e.root);if(!t||this.selectionRange.eq(t))return!1;let i=ln(this.dom,t);return i&&!this.selectionChanged&&e.inputState.lastFocusTime>Date.now()-200&&e.inputState.lastTouchTime<Date.now()-300&&Nf(this.dom,t)?(this.view.inputState.lastFocusTime=0,e.docView.updateSelection(),!1):(this.selectionRange.setRange(t),i&&(this.selectionChanged=!0),!0)}setSelectionRange(e,t){this.selectionRange.set(e.node,e.offset,t.node,t.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let e=0,t=null;for(let i=this.dom;i;)if(i.nodeType==1)!t&&e<this.scrollTargets.length&&this.scrollTargets[e]==i?e++:t||(t=this.scrollTargets.slice(0,e)),t&&t.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(e<this.scrollTargets.length&&!t&&(t=this.scrollTargets.slice(0,e)),t){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=t)i.addEventListener("scroll",this.onScroll)}}ignore(e){if(!this.active)return e();try{return this.stop(),e()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,$u),is&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),is&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(e,t){var i;if(!this.delayedAndroidKey){let s=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),!this.flush()&&r.force&&Kt(this.dom,r.key,r.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(s)}(!this.delayedAndroidKey||e=="Enter")&&(this.delayedAndroidKey={key:e,keyCode:t,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}processRecords(){let e=this.queue;for(let r of this.observer.takeRecords())e.push(r);e.length&&(this.queue=[]);let t=-1,i=-1,s=!1;for(let r of e){let o=this.readMutation(r);o&&(o.typeOver&&(s=!0),t==-1?{from:t,to:i}=o:(t=Math.min(o.from,t),i=Math.max(o.to,i)))}return{from:t,to:i,typeOver:s}}readChange(){let{from:e,to:t,typeOver:i}=this.processRecords(),s=this.selectionChanged&&ln(this.dom,this.selectionRange);return e<0&&!s?null:(e>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1,new Nu(this.view,e,t,i))}flush(e=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;e&&this.readSelectionRange();let t=this.readChange();if(!t)return!1;let i=this.view.state,s=gh(this.view,t);return this.view.state==i&&this.view.update([]),s}readMutation(e){let t=this.view.docView.nearest(e.target);if(!t||t.ignoreMutation(e))return null;if(t.markDirty(e.type=="attributes"),e.type=="attributes"&&(t.dirty|=4),e.type=="childList"){let i=$o(t,e.previousSibling||e.target.previousSibling,-1),s=$o(t,e.nextSibling||e.target.nextSibling,1);return{from:i?t.posAfter(i):t.posAtStart,to:s?t.posBefore(s):t.posAtEnd,typeOver:!1}}else return e.type=="characterData"?{from:t.posAtStart,to:t.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}:null}setWindow(e){e!=this.win&&(this.removeWindowListeners(this.win),this.win=e,this.addWindowListeners(this.win))}addWindowListeners(e){e.addEventListener("resize",this.onResize),e.addEventListener("beforeprint",this.onPrint),e.addEventListener("scroll",this.onScroll),e.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(e){e.removeEventListener("scroll",this.onScroll),e.removeEventListener("resize",this.onResize),e.removeEventListener("beforeprint",this.onPrint),e.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var e,t,i;this.stop(),(e=this.intersection)===null||e===void 0||e.disconnect(),(t=this.gapIntersection)===null||t===void 0||t.disconnect(),(i=this.resize)===null||i===void 0||i.disconnect();for(let s of this.scrollTargets)s.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function $o(n,e,t){for(;e;){let i=j.get(e);if(i&&i.parent==n)return i;let s=e.parentNode;e=s!=n.dom?s:t>0?e.nextSibling:e.previousSibling}return null}function zu(n){let e=null;function t(a){a.preventDefault(),a.stopImmediatePropagation(),e=a.getTargetRanges()[0]}if(n.contentDOM.addEventListener("beforeinput",t,!0),n.dom.ownerDocument.execCommand("indent"),n.contentDOM.removeEventListener("beforeinput",t,!0),!e)return null;let i=e.startContainer,s=e.startOffset,r=e.endContainer,o=e.endOffset,l=n.docView.domAtPos(n.state.selection.main.anchor);return gn(l.node,l.offset,r,o)&&([i,s,r,o]=[r,o,i,s]),{anchorNode:i,anchorOffset:s,focusNode:r,focusOffset:o}}class O{constructor(e={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.style.cssText="position: absolute; top: -10000px",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),this._dispatch=e.dispatch||(t=>this.update([t])),this.dispatch=this.dispatch.bind(this),this._root=e.root||If(e.parent)||document,this.viewState=new Fo(e.state||N.create(e)),this.plugins=this.state.facet(fi).map(t=>new Qn(t));for(let t of this.plugins)t.update(this);this.observer=new Wu(this),this.inputState=new fu(this),this.inputState.ensureHandlers(this,this.plugins),this.docView=new vo(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),e.parent&&e.parent.appendChild(this.dom)}get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}dispatch(...e){this._dispatch(e.length==1&&e[0]instanceof ee?e[0]:this.state.update(...e))}update(e){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let t=!1,i=!1,s,r=this.state;for(let h of e){if(h.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=h.state}if(this.destroyed){this.viewState.state=r;return}let o=this.observer.delayedAndroidKey,l=null;if(o?(this.observer.clearDelayedAndroidKey(),l=this.observer.readChange(),(l&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(l=null)):this.observer.clear(),r.facet(N.phrases)!=this.state.facet(N.phrases))return this.setState(r);s=wn.create(this,r,e);let a=this.viewState.scrollTarget;try{this.updateState=2;for(let h of e){if(a&&(a=a.map(h.changes)),h.scrollIntoView){let{main:c}=h.state.selection;a=new bn(c.empty?c:w.cursor(c.head,c.head>c.anchor?-1:1))}for(let c of h.effects)c.is(wo)&&(a=c.value)}this.viewState.update(s,a),this.bidiCache=kn.update(this.bidiCache,s.changes),s.empty||(this.updatePlugins(s),this.inputState.update(s)),t=this.docView.update(s),this.state.facet(ui)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(e),this.docView.updateSelection(t,e.some(h=>h.isUserEvent("select.pointer")))}finally{this.updateState=0}if(s.startState.facet(Ki)!=s.state.facet(Ki)&&(this.viewState.mustMeasureContent=!0),(t||i||a||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),!s.empty)for(let h of this.state.facet(Us))h(s);l&&!gh(this,l)&&o.force&&Kt(this.contentDOM,o.key,o.keyCode)}setState(e){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=e;return}this.updateState=2;let t=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new Fo(e),this.plugins=e.facet(fi).map(i=>new Qn(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView=new vo(this),this.inputState.ensureHandlers(this,this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}t&&this.focus(),this.requestMeasure()}updatePlugins(e){let t=e.startState.facet(fi),i=e.state.facet(fi);if(t!=i){let s=[];for(let r of i){let o=t.indexOf(r);if(o<0)s.push(new Qn(r));else{let l=this.plugins[o];l.mustUpdate=e,s.push(l)}}for(let r of this.plugins)r.mustUpdate!=e&&r.destroy(this);this.plugins=s,this.pluginMap.clear(),this.inputState.ensureHandlers(this,this.plugins)}else for(let s of this.plugins)s.mustUpdate=e;for(let s=0;s<this.plugins.length;s++)this.plugins[s].update(this)}measure(e=!0){if(this.destroyed)return;this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.measureScheduled=0,e&&this.observer.forceFlush();let t=null,{scrollHeight:i,scrollTop:s,clientHeight:r}=this.scrollDOM,o=s>i-r-4?i:s;try{for(let l=0;;l++){this.updateState=1;let a=this.viewport,h=this.viewState.lineBlockAtHeight(o),c=this.viewState.measure(this);if(!c&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let f=[];c&4||([this.measureRequests,f]=[f,this.measureRequests]);let u=f.map(y=>{try{return y.read(this)}catch(b){return _e(this.state,b),Wo}}),d=wn.create(this,this.state,[]),p=!1,g=!1;d.flags|=c,t?t.flags|=c:t=d,this.updateState=2,d.empty||(this.updatePlugins(d),this.inputState.update(d),this.updateAttrs(),p=this.docView.update(d));for(let y=0;y<f.length;y++)if(u[y]!=Wo)try{let b=f[y];b.write&&b.write(u[y],this)}catch(b){_e(this.state,b)}if(this.viewState.editorHeight)if(this.viewState.scrollTarget)this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,g=!0;else{let y=this.viewState.lineBlockAt(h.from).top-h.top;(y>1||y<-1)&&(this.scrollDOM.scrollTop+=y,g=!0)}if(p&&this.docView.updateSelection(!0),this.viewport.from==a.from&&this.viewport.to==a.to&&!g&&this.measureRequests.length==0)break}}finally{this.updateState=0,this.measureScheduled=-1}if(t&&!t.empty)for(let l of this.state.facet(Us))l(t)}get themeClasses(){return er+" "+(this.state.facet(Zs)?ph:dh)+" "+this.state.facet(Ki)}updateAttrs(){let e=zo(this,Ga,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),t={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(Nn)?"true":"false",class:"cm-content",style:`${M.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(t["aria-readonly"]="true"),zo(this,Ja,t);let i=this.observer.ignore(()=>{let s=js(this.contentDOM,this.contentAttrs,t),r=js(this.dom,this.editorAttrs,e);return s||r});return this.editorAttrs=e,this.contentAttrs=t,i}showAnnouncements(e){let t=!0;for(let i of e)for(let s of i.effects)if(s.is(O.announce)){t&&(this.announceDOM.textContent=""),t=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=s.value}}mountStyles(){this.styleModules=this.state.facet(ui),ft.mount(this.root,this.styleModules.concat(Iu).reverse())}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(e){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),e){if(e.key!=null){for(let t=0;t<this.measureRequests.length;t++)if(this.measureRequests[t].key===e.key){this.measureRequests[t]=e;return}}this.measureRequests.push(e)}}plugin(e){let t=this.pluginMap.get(e);return(t===void 0||t&&t.spec!=e)&&this.pluginMap.set(e,t=this.plugins.find(i=>i.spec==e)||null),t&&t.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}elementAtHeight(e){return this.readMeasured(),this.viewState.elementAtHeight(e)}lineBlockAtHeight(e){return this.readMeasured(),this.viewState.lineBlockAtHeight(e)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(e){return this.viewState.lineBlockAt(e)}get contentHeight(){return this.viewState.contentHeight}moveByChar(e,t,i){return es(this,e,To(this,e,t,i))}moveByGroup(e,t){return es(this,e,To(this,e,t,i=>hu(this,e.head,i)))}moveToLineBoundary(e,t,i=!0){return au(this,e,t,i)}moveVertically(e,t,i){return es(this,e,cu(this,e,t,i))}domAtPos(e){return this.docView.domAtPos(e)}posAtDOM(e,t=0){return this.docView.posFromDOM(e,t)}posAtCoords(e,t=!0){return this.readMeasured(),sh(this,e,t)}coordsAtPos(e,t=1){this.readMeasured();let i=this.docView.coordsAt(e,t);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(e),r=this.bidiSpans(s),o=r[Ut.find(r,e-s.from,-1,t)];return xr(i,o.dir==G.LTR==t>0)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(e){return!this.state.facet(Ka)||e<this.viewport.from||e>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(e))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(e){if(e.length>qu)return Za(e.length);let t=this.textDirectionAt(e.from);for(let s of this.bidiCache)if(s.from==e.from&&s.dir==t)return s.order;let i=Gf(e.text,t);return this.bidiCache.push(new kn(e.from,e.to,t,i)),i}get hasFocus(){var e;return(this.dom.ownerDocument.hasFocus()||M.safari&&((e=this.inputState)===null||e===void 0?void 0:e.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{Ta(this.contentDOM),this.docView.updateSelection()})}setRoot(e){this._root!=e&&(this._root=e,this.observer.setWindow((e.nodeType==9?e:e.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let e of this.plugins)e.destroy(this);this.plugins=[],this.inputState.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(e,t={}){return wo.of(new bn(typeof e=="number"?w.cursor(e):e,t.y,t.x,t.yMargin,t.xMargin))}static domEventHandlers(e){return pe.define(()=>({}),{eventHandlers:e})}static theme(e,t){let i=ft.newName(),s=[Ki.of(i),ui.of(tr(`.${i}`,e))];return t&&t.dark&&s.push(Zs.of(!0)),s}static baseTheme(e){return Li.lowest(ui.of(tr("."+er,e,mh)))}static findFromDOM(e){var t;let i=e.querySelector(".cm-content"),s=i&&j.get(i)||j.get(e);return((t=s?.rootView)===null||t===void 0?void 0:t.view)||null}}O.styleModule=ui;O.inputHandler=ja;O.perLineTextDirection=Ka;O.exceptionSink=qa;O.updateListener=Us;O.editable=Nn;O.mouseSelectionStyle=za;O.dragMovesSelection=Wa;O.clickAddsSelectionRange=$a;O.decorations=Di;O.atomicRanges=Ya;O.scrollMargins=Xa;O.darkTheme=Zs;O.contentAttributes=Ja;O.editorAttributes=Ga;O.lineWrapping=O.contentAttributes.of({class:"cm-lineWrapping"});O.announce=R.define();const qu=4096,Wo={};class kn{constructor(e,t,i,s){this.from=e,this.to=t,this.dir=i,this.order=s}static update(e,t){if(t.empty)return e;let i=[],s=e.length?e[e.length-1].dir:G.LTR;for(let r=Math.max(0,e.length-10);r<e.length;r++){let o=e[r];o.dir==s&&!t.touchesRange(o.from,o.to)&&i.push(new kn(t.mapPos(o.from,1),t.mapPos(o.to,-1),o.dir,o.order))}return i}}function zo(n,e,t){for(let i=n.state.facet(e),s=i.length-1;s>=0;s--){let r=i[s],o=typeof r=="function"?r(n):r;o&&qs(o,t)}return t}const ju=M.mac?"mac":M.windows?"win":M.linux?"linux":"key";function Ku(n,e){const t=n.split(/-(?!$)/);let i=t[t.length-1];i=="Space"&&(i=" ");let s,r,o,l;for(let a=0;a<t.length-1;++a){const h=t[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))s=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))e=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return s&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function Ui(n,e,t){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t!==!1&&e.shiftKey&&(n="Shift-"+n),n}const Uu=Li.default(O.domEventHandlers({keydown(n,e){return Xu(Gu(e.state),n,e,"editor")}})),Vn=T.define({enables:Uu}),qo=new WeakMap;function Gu(n){let e=n.facet(Vn),t=qo.get(e);return t||qo.set(e,t=Yu(e.reduce((i,s)=>i.concat(s),[]))),t}let ot=null;const Ju=4e3;function Yu(n,e=ju){let t=Object.create(null),i=Object.create(null),s=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h)=>{var c,f;let u=t[o]||(t[o]=Object.create(null)),d=l.split(/ (?!$)/).map(y=>Ku(y,e));for(let y=1;y<d.length;y++){let b=d.slice(0,y).join(" ");s(b,!0),u[b]||(u[b]={preventDefault:!0,run:[k=>{let v=ot={view:k,prefix:b,scope:o};return setTimeout(()=>{ot==v&&(ot=null)},Ju),!0}]})}let p=d.join(" ");s(p,!1);let g=u[p]||(u[p]={preventDefault:!1,run:((f=(c=u._any)===null||c===void 0?void 0:c.run)===null||f===void 0?void 0:f.slice())||[]});a&&g.run.push(a),h&&(g.preventDefault=!0)};for(let o of n){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let c=t[h]||(t[h]=Object.create(null));c._any||(c._any={preventDefault:!1,run:[]});for(let f in c)c[f].run.push(o.any)}let a=o[e]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault)}return t}function Xu(n,e,t,i){let s=Ef(e),r=ue(s,0),o=De(r)==s.length&&s!=" ",l="",a=!1;ot&&ot.view==t&&ot.scope==i&&(l=ot.prefix+" ",(a=oh.indexOf(e.keyCode)<0)&&(ot=null));let h=new Set,c=p=>{if(p){for(let g of p.run)if(!h.has(g)&&(h.add(g),g(t,e)))return!0;p.preventDefault&&(a=!0)}return!1},f=n[i],u,d;if(f){if(c(f[l+Ui(s,e,!o)]))return!0;if(o&&(e.shiftKey||e.altKey||e.metaKey||r>127)&&(u=ut[e.keyCode])&&u!=s){if(c(f[l+Ui(u,e,!0)]))return!0;if(e.shiftKey&&(d=Ci[e.keyCode])!=s&&d!=u&&c(f[l+Ui(d,e,!1)]))return!0}else if(o&&e.shiftKey&&c(f[l+Ui(s,e,!0)]))return!0;if(c(f._any))return!0}return a}const yh=!M.ios,pi=T.define({combine(n){return _t(n,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(e,t)=>Math.min(e,t),drawRangeCursor:(e,t)=>e||t})}});function Qu(n={}){return[pi.of(n),Zu,ed,Ua.of(!0)]}class bh{constructor(e,t,i,s,r){this.left=e,this.top=t,this.width=i,this.height=s,this.className=r}draw(){let e=document.createElement("div");return e.className=this.className,this.adjust(e),e}adjust(e){e.style.left=this.left+"px",e.style.top=this.top+"px",this.width>=0&&(e.style.width=this.width+"px"),e.style.height=this.height+"px"}eq(e){return this.left==e.left&&this.top==e.top&&this.width==e.width&&this.height==e.height&&this.className==e.className}}const Zu=pe.fromClass(class{constructor(n){this.view=n,this.rangePieces=[],this.cursors=[],this.measureReq={read:this.readPos.bind(this),write:this.drawSel.bind(this)},this.selectionLayer=n.scrollDOM.appendChild(document.createElement("div")),this.selectionLayer.className="cm-selectionLayer",this.selectionLayer.setAttribute("aria-hidden","true"),this.cursorLayer=n.scrollDOM.appendChild(document.createElement("div")),this.cursorLayer.className="cm-cursorLayer",this.cursorLayer.setAttribute("aria-hidden","true"),n.requestMeasure(this.measureReq),this.setBlinkRate()}setBlinkRate(){this.cursorLayer.style.animationDuration=this.view.state.facet(pi).cursorBlinkRate+"ms"}update(n){let e=n.startState.facet(pi)!=n.state.facet(pi);(e||n.selectionSet||n.geometryChanged||n.viewportChanged)&&this.view.requestMeasure(this.measureReq),n.transactions.some(t=>t.scrollIntoView)&&(this.cursorLayer.style.animationName=this.cursorLayer.style.animationName=="cm-blink"?"cm-blink2":"cm-blink"),e&&this.setBlinkRate()}readPos(){let{state:n}=this.view,e=n.facet(pi),t=n.selection.ranges.map(s=>s.empty?[]:td(this.view,s)).reduce((s,r)=>s.concat(r)),i=[];for(let s of n.selection.ranges){let r=s==n.selection.main;if(s.empty?!r||yh:e.drawRangeCursor){let o=id(this.view,s,r);o&&i.push(o)}}return{rangePieces:t,cursors:i}}drawSel({rangePieces:n,cursors:e}){if(n.length!=this.rangePieces.length||n.some((t,i)=>!t.eq(this.rangePieces[i]))){this.selectionLayer.textContent="";for(let t of n)this.selectionLayer.appendChild(t.draw());this.rangePieces=n}if(e.length!=this.cursors.length||e.some((t,i)=>!t.eq(this.cursors[i]))){let t=this.cursorLayer.children;if(t.length!==e.length){this.cursorLayer.textContent="";for(const i of e)this.cursorLayer.appendChild(i.draw())}else e.forEach((i,s)=>i.adjust(t[s]));this.cursors=e}}destroy(){this.selectionLayer.remove(),this.cursorLayer.remove()}}),wh={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};yh&&(wh[".cm-line"].caretColor="transparent !important");const ed=Li.highest(O.theme(wh));function kh(n){let e=n.scrollDOM.getBoundingClientRect();return{left:(n.textDirection==G.LTR?e.left:e.right-n.scrollDOM.clientWidth)-n.scrollDOM.scrollLeft,top:e.top-n.scrollDOM.scrollTop}}function jo(n,e,t){let i=w.cursor(e);return{from:Math.max(t.from,n.moveToLineBoundary(i,!1,!0).from),to:Math.min(t.to,n.moveToLineBoundary(i,!0,!0).from),type:$.Text}}function Ko(n,e){let t=n.lineBlockAt(e);if(Array.isArray(t.type)){for(let i of t.type)if(i.to>e||i.to==e&&(i.to==t.to||i.type==$.Text))return i}return t}function td(n,e){if(e.to<=n.viewport.from||e.from>=n.viewport.to)return[];let t=Math.max(e.from,n.viewport.from),i=Math.min(e.to,n.viewport.to),s=n.textDirection==G.LTR,r=n.contentDOM,o=r.getBoundingClientRect(),l=kh(n),a=window.getComputedStyle(r.firstChild),h=o.left+parseInt(a.paddingLeft)+Math.min(0,parseInt(a.textIndent)),c=o.right-parseInt(a.paddingRight),f=Ko(n,t),u=Ko(n,i),d=f.type==$.Text?f:null,p=u.type==$.Text?u:null;if(n.lineWrapping&&(d&&(d=jo(n,t,d)),p&&(p=jo(n,i,p))),d&&p&&d.from==p.from)return y(b(e.from,e.to,d));{let v=d?b(e.from,null,d):k(f,!1),S=p?b(null,e.to,p):k(u,!0),C=[];return(d||f).to<(p||u).from-1?C.push(g(h,v.bottom,c,S.top)):v.bottom<S.top&&n.elementAtHeight((v.bottom+S.top)/2).type==$.Text&&(v.bottom=S.top=(v.bottom+S.top)/2),y(v).concat(C).concat(y(S))}function g(v,S,C,D){return new bh(v-l.left,S-l.top-.01,C-v,D-S+.01,"cm-selectionBackground")}function y({top:v,bottom:S,horizontal:C}){let D=[];for(let E=0;E<C.length;E+=2)D.push(g(C[E],v,C[E+1],S));return D}function b(v,S,C){let D=1e9,E=-1e9,B=[];function I(K,J,ge,te,A){let Q=n.coordsAtPos(K,K==C.to?-2:2),z=n.coordsAtPos(ge,ge==C.from?2:-2);D=Math.min(Q.top,z.top,D),E=Math.max(Q.bottom,z.bottom,E),A==G.LTR?B.push(s&&J?h:Q.left,s&&te?c:z.right):B.push(!s&&te?h:z.left,!s&&J?c:Q.right)}let P=v??C.from,F=S??C.to;for(let K of n.visibleRanges)if(K.to>P&&K.from<F)for(let J=Math.max(K.from,P),ge=Math.min(K.to,F);;){let te=n.state.doc.lineAt(J);for(let A of n.bidiSpans(te)){let Q=A.from+te.from,z=A.to+te.from;if(Q>=ge)break;z>J&&I(Math.max(Q,J),v==null&&Q<=P,Math.min(z,ge),S==null&&z>=F,A.dir)}if(J=te.to+1,J>=ge)break}return B.length==0&&I(P,v==null,F,S==null,n.textDirection),{top:D,bottom:E,horizontal:B}}function k(v,S){let C=o.top+(S?v.top:v.bottom);return{top:C,bottom:C,horizontal:[]}}}function id(n,e,t){let i=n.coordsAtPos(e.head,e.assoc||1);if(!i)return null;let s=kh(n);return new bh(i.left-s.left,i.top-s.top,-1,i.bottom-i.top,t?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary")}function Uo(n,e,t,i,s){e.lastIndex=0;for(let r=n.iterRange(t,i),o=t,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=e.exec(r.value);)s(o+l.index,l)}function nd(n,e){let t=n.visibleRanges;if(t.length==1&&t[0].from==n.viewport.from&&t[0].to==n.viewport.to)return t;let i=[];for(let{from:s,to:r}of t)s=Math.max(n.state.doc.lineAt(s).from,s-e),r=Math.min(n.state.doc.lineAt(r).to,r+e),i.length&&i[i.length-1].to>=s?i[i.length-1].to=r:i.push({from:s,to:r});return i}class sd{constructor(e){const{regexp:t,decoration:i,decorate:s,boundary:r,maxLength:o=1e3}=e;if(!t.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=t,s)this.addMatch=(l,a,h,c)=>s(c,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,c)=>{let f=i(l,a,h);f&&c(h,h+l[0].length,f)};else if(i)this.addMatch=(l,a,h,c)=>c(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o}createDeco(e){let t=new Mt,i=t.add.bind(t);for(let{from:s,to:r}of nd(e,this.maxLength))Uo(e.state.doc,this.regexp,s,r,(o,l)=>this.addMatch(l,e,o,i));return t.finish()}updateDeco(e,t){let i=1e9,s=-1;return e.docChanged&&e.changes.iterChanges((r,o,l,a)=>{a>e.view.viewport.from&&l<e.view.viewport.to&&(i=Math.min(l,i),s=Math.max(a,s))}),e.viewportChanged||s-i>1e3?this.createDeco(e.view):s>-1?this.updateRange(e.view,t.map(e.changes),i,s):t}updateRange(e,t,i,s){for(let r of e.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,s);if(l>o){let a=e.state.doc.lineAt(o),h=a.to<l?e.state.doc.lineAt(l):a,c=Math.max(r.from,a.from),f=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){c=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){f=l;break}}let u=[],d,p=(g,y,b)=>u.push(b.range(g,y));if(a==h)for(this.regexp.lastIndex=c-a.from;(d=this.regexp.exec(a.text))&&d.index<f-a.from;)this.addMatch(d,e,d.index+a.from,p);else Uo(e.state.doc,this.regexp,c,f,(g,y)=>this.addMatch(y,e,g,p));t=t.update({filterFrom:c,filterTo:f,filter:(g,y)=>g<c||y>f,add:u})}}return t}}const ir=/x/.unicode!=null?"gu":"g",rd=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,ir),od={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let ns=null;function ld(){var n;if(ns==null&&typeof document<"u"&&document.body){let e=document.body.style;ns=((n=e.tabSize)!==null&&n!==void 0?n:e.MozTabSize)!=null}return ns||!1}const hn=T.define({combine(n){let e=_t(n,{render:null,specialChars:rd,addSpecialChars:null});return(e.replaceTabs=!ld())&&(e.specialChars=new RegExp("	|"+e.specialChars.source,ir)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,ir)),e}});function ad(n={}){return[hn.of(n),hd()]}let Go=null;function hd(){return Go||(Go=pe.fromClass(class{constructor(n){this.view=n,this.decorations=L.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(n.state.facet(hn)),this.decorations=this.decorator.createDeco(n)}makeDecorator(n){return new sd({regexp:n.specialChars,decoration:(e,t,i)=>{let{doc:s}=t.state,r=ue(e[0],0);if(r==9){let o=s.lineAt(i),l=t.state.tabSize,a=Ri(o.text,l,i-o.from);return L.replace({widget:new dd((l-a%l)*this.view.defaultCharacterWidth)})}return this.decorationCache[r]||(this.decorationCache[r]=L.replace({widget:new ud(n,r)}))},boundary:n.replaceTabs?void 0:/[^]/})}update(n){let e=n.state.facet(hn);n.startState.facet(hn)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(n.view)):this.decorations=this.decorator.updateDeco(n,this.decorations)}},{decorations:n=>n.decorations}))}const cd="•";function fd(n){return n>=32?cd:n==10?"␤":String.fromCharCode(9216+n)}class ud extends Qe{constructor(e,t){super(),this.options=e,this.code=t}eq(e){return e.code==this.code}toDOM(e){let t=fd(this.code),i=e.state.phrase("Control character")+" "+(od[this.code]||"0x"+this.code.toString(16)),s=this.options.render&&this.options.render(this.code,i,t);if(s)return s;let r=document.createElement("span");return r.textContent=t,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class dd extends Qe{constructor(e){super(),this.width=e}eq(e){return e.width==this.width}toDOM(){let e=document.createElement("span");return e.textContent="	",e.className="cm-tab",e.style.width=this.width+"px",e}ignoreEvent(){return!1}}class pd extends Qe{constructor(e){super(),this.content=e}toDOM(){let e=document.createElement("span");return e.className="cm-placeholder",e.style.pointerEvents="none",e.appendChild(typeof this.content=="string"?document.createTextNode(this.content):this.content),typeof this.content=="string"?e.setAttribute("aria-label","placeholder "+this.content):e.setAttribute("aria-hidden","true"),e}ignoreEvent(){return!1}}function md(n){return pe.fromClass(class{constructor(e){this.view=e,this.placeholder=L.set([L.widget({widget:new pd(n),side:1}).range(0)])}get decorations(){return this.view.state.doc.length?L.none:this.placeholder}},{decorations:e=>e.decorations})}const nr=2e3;function gd(n,e,t){let i=Math.min(e.line,t.line),s=Math.max(e.line,t.line),r=[];if(e.off>nr||t.off>nr||e.col<0||t.col<0){let o=Math.min(e.off,t.off),l=Math.max(e.off,t.off);for(let a=i;a<=s;a++){let h=n.doc.line(a);h.length<=l&&r.push(w.range(h.from+o,h.to+l))}}else{let o=Math.min(e.col,t.col),l=Math.max(e.col,t.col);for(let a=i;a<=s;a++){let h=n.doc.line(a),c=Is(h.text,o,n.tabSize,!0);if(c<0)r.push(w.cursor(h.to));else{let f=Is(h.text,l,n.tabSize);r.push(w.range(h.from+c,h.from+f))}}}return r}function yd(n,e){let t=n.coordsAtPos(n.viewport.from);return t?Math.round(Math.abs((t.left-e)/n.defaultCharacterWidth)):-1}function Jo(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1),i=n.state.doc.lineAt(t),s=t-i.from,r=s>nr?-1:s==i.length?yd(n,e.clientX):Ri(i.text,n.state.tabSize,t-i.from);return{line:i.number,col:r,off:s}}function bd(n,e){let t=Jo(n,e),i=n.state.selection;return t?{update(s){if(s.docChanged){let r=s.changes.mapPos(s.startState.doc.line(t.line).from),o=s.state.doc.lineAt(r);t={line:o.number,col:t.col,off:Math.min(t.off,o.length)},i=i.map(s.changes)}},get(s,r,o){let l=Jo(n,s);if(!l)return i;let a=gd(n.state,t,l);return a.length?o?w.create(a.concat(i.ranges)):w.create(a):i}}:null}function wd(n){let e=t=>t.altKey&&t.button==0;return O.mouseSelectionStyle.of((t,i)=>e(i)?bd(t,i):null)}const kd={Alt:[18,n=>n.altKey],Control:[17,n=>n.ctrlKey],Shift:[16,n=>n.shiftKey],Meta:[91,n=>n.metaKey]},xd={style:"cursor: crosshair"};function vd(n={}){let[e,t]=kd[n.key||"Alt"],i=pe.fromClass(class{constructor(s){this.view=s,this.isDown=!1}set(s){this.isDown!=s&&(this.isDown=s,this.view.update([]))}},{eventHandlers:{keydown(s){this.set(s.keyCode==e||t(s))},keyup(s){(s.keyCode==e||!t(s))&&this.set(!1)},mousemove(s){this.set(t(s))}}});return[i,O.contentAttributes.of(s=>{var r;return!((r=s.plugin(i))===null||r===void 0)&&r.isDown?xd:null})]}const ss="-10000px";class xh{constructor(e,t,i){this.facet=t,this.createTooltipView=i,this.input=e.state.facet(t),this.tooltips=this.input.filter(s=>s),this.tooltipViews=this.tooltips.map(i)}update(e){var t;let i=e.state.facet(this.facet),s=i.filter(o=>o);if(i===this.input){for(let o of this.tooltipViews)o.update&&o.update(e);return!1}let r=[];for(let o=0;o<s.length;o++){let l=s[o],a=-1;if(l){for(let h=0;h<this.tooltips.length;h++){let c=this.tooltips[h];c&&c.create==l.create&&(a=h)}if(a<0)r[o]=this.createTooltipView(l);else{let h=r[o]=this.tooltipViews[a];h.update&&h.update(e)}}}for(let o of this.tooltipViews)r.indexOf(o)<0&&(o.dom.remove(),(t=o.destroy)===null||t===void 0||t.call(o));return this.input=i,this.tooltips=s,this.tooltipViews=r,!0}}function Sd(n){let{win:e}=n;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const rs=T.define({combine:n=>{var e,t,i;return{position:M.ios?"absolute":((e=n.find(s=>s.position))===null||e===void 0?void 0:e.position)||"fixed",parent:((t=n.find(s=>s.parent))===null||t===void 0?void 0:t.parent)||null,tooltipSpace:((i=n.find(s=>s.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||Sd}}}),vh=pe.fromClass(class{constructor(n){this.view=n,this.inView=!0,this.lastTransaction=0,this.measureTimeout=-1;let e=n.state.facet(rs);this.position=e.position,this.parent=e.parent,this.classes=n.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.manager=new xh(n,Mr,t=>this.createTooltip(t)),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),n.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let n of this.manager.tooltipViews)this.intersectionObserver.observe(n.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(n){n.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(n);e&&this.observeIntersection();let t=e||n.geometryChanged,i=n.state.facet(rs);if(i.position!=this.position){this.position=i.position;for(let s of this.manager.tooltipViews)s.dom.style.position=this.position;t=!0}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let s of this.manager.tooltipViews)this.container.appendChild(s.dom);t=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);t&&this.maybeMeasure()}createTooltip(n){let e=n.create(this.view);if(e.dom.classList.add("cm-tooltip"),n.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",e.dom.appendChild(t)}return e.dom.style.position=this.position,e.dom.style.top=ss,this.container.appendChild(e.dom),e.mount&&e.mount(this.view),e}destroy(){var n,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let t of this.manager.tooltipViews)t.dom.remove(),(n=t.destroy)===null||n===void 0||n.call(t);(e=this.intersectionObserver)===null||e===void 0||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let n=this.view.dom.getBoundingClientRect();return{editor:n,parent:this.parent?this.container.getBoundingClientRect():n,pos:this.manager.tooltips.map((e,t)=>{let i=this.manager.tooltipViews[t];return i.getCoords?i.getCoords(e.pos):this.view.coordsAtPos(e.pos)}),size:this.manager.tooltipViews.map(({dom:e})=>e.getBoundingClientRect()),space:this.view.state.facet(rs).tooltipSpace(this.view)}}writeMeasure(n){let{editor:e,space:t}=n,i=[];for(let s=0;s<this.manager.tooltips.length;s++){let r=this.manager.tooltips[s],o=this.manager.tooltipViews[s],{dom:l}=o,a=n.pos[s],h=n.size[s];if(!a||a.bottom<=Math.max(e.top,t.top)||a.top>=Math.min(e.bottom,t.bottom)||a.right<Math.max(e.left,t.left)-.1||a.left>Math.min(e.right,t.right)+.1){l.style.top=ss;continue}let c=r.arrow?o.dom.querySelector(".cm-tooltip-arrow"):null,f=c?7:0,u=h.right-h.left,d=h.bottom-h.top,p=o.offset||Ad,g=this.view.textDirection==G.LTR,y=h.width>t.right-t.left?g?t.left:t.right-h.width:g?Math.min(a.left-(c?14:0)+p.x,t.right-u):Math.max(t.left,a.left-u+(c?14:0)-p.x),b=!!r.above;!r.strictSide&&(b?a.top-(h.bottom-h.top)-p.y<t.top:a.bottom+(h.bottom-h.top)+p.y>t.bottom)&&b==t.bottom-a.bottom>a.top-t.top&&(b=!b);let k=b?a.top-d-f-p.y:a.bottom+f+p.y,v=y+u;if(o.overlap!==!0)for(let S of i)S.left<v&&S.right>y&&S.top<k+d&&S.bottom>k&&(k=b?S.top-d-2-f:S.bottom+f+2);this.position=="absolute"?(l.style.top=k-n.parent.top+"px",l.style.left=y-n.parent.left+"px"):(l.style.top=k+"px",l.style.left=y+"px"),c&&(c.style.left=`${a.left+(g?p.x:-p.x)-(y+14-7)}px`),o.overlap!==!0&&i.push({left:y,top:k,right:v,bottom:k+d}),l.classList.toggle("cm-tooltip-above",b),l.classList.toggle("cm-tooltip-below",!b),o.positioned&&o.positioned()}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let n of this.manager.tooltipViews)n.dom.style.top=ss}},{eventHandlers:{scroll(){this.maybeMeasure()}}}),Cd=O.baseTheme({".cm-tooltip":{zIndex:100},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),Ad={x:0,y:0},Mr=T.define({enables:[vh,Cd]}),xn=T.define();class Dr{constructor(e){this.view=e,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new xh(e,xn,t=>this.createHostedView(t))}static create(e){return new Dr(e)}createHostedView(e){let t=e.create(this.view);return t.dom.classList.add("cm-tooltip-section"),this.dom.appendChild(t.dom),this.mounted&&t.mount&&t.mount(this.view),t}mount(e){for(let t of this.manager.tooltipViews)t.mount&&t.mount(e);this.mounted=!0}positioned(){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned()}update(e){this.manager.update(e)}}const Md=Mr.compute([xn],n=>{let e=n.facet(xn).filter(t=>t);return e.length===0?null:{pos:Math.min(...e.map(t=>t.pos)),end:Math.max(...e.filter(t=>t.end!=null).map(t=>t.end)),create:Dr.create,above:e[0].above,arrow:e.some(t=>t.arrow)}});class Dd{constructor(e,t,i,s,r){this.view=e,this.source=t,this.field=i,this.setHover=s,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:e.dom,time:0},this.checkHover=this.checkHover.bind(this),e.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),e.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active)return;let e=Date.now()-this.lastMove.time;e<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-e):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{lastMove:e}=this,t=this.view.contentDOM.contains(e.target)?this.view.posAtCoords(e):null;if(t==null)return;let i=this.view.coordsAtPos(t);if(i==null||e.y<i.top||e.y>i.bottom||e.x<i.left-this.view.defaultCharacterWidth||e.x>i.right+this.view.defaultCharacterWidth)return;let s=this.view.bidiSpans(this.view.state.doc.lineAt(t)).find(l=>l.from<=t&&l.to>=t),r=s&&s.dir==G.RTL?-1:1,o=this.source(this.view,t,e.x<i.left?-r:r);if(o?.then){let l=this.pending={pos:t};o.then(a=>{this.pending==l&&(this.pending=null,a&&this.view.dispatch({effects:this.setHover.of(a)}))},a=>_e(this.view.state,a,"hover tooltip"))}else o&&this.view.dispatch({effects:this.setHover.of(o)})}mousemove(e){var t;this.lastMove={x:e.clientX,y:e.clientY,target:e.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let i=this.active;if(i&&!Td(this.lastMove.target)||this.pending){let{pos:s}=i||this.pending,r=(t=i?.end)!==null&&t!==void 0?t:s;(s==r?this.view.posAtCoords(this.lastMove)!=s:!Od(this.view,s,r,e.clientX,e.clientY,6))&&(this.view.dispatch({effects:this.setHover.of(null)}),this.pending=null)}}mouseleave(){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1,this.active&&this.view.dispatch({effects:this.setHover.of(null)})}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}function Td(n){for(let e=n;e;e=e.parentNode)if(e.nodeType==1&&e.classList.contains("cm-tooltip"))return!0;return!1}function Od(n,e,t,i,s,r){let o=document.createRange(),l=n.domAtPos(e),a=n.domAtPos(t);o.setEnd(a.node,a.offset),o.setStart(l.node,l.offset);let h=o.getClientRects();o.detach();for(let c=0;c<h.length;c++){let f=h[c];if(Math.max(f.top-s,s-f.bottom,f.left-i,i-f.right)<=r)return!0}return!1}function Bd(n,e={}){let t=R.define(),i=xe.define({create(){return null},update(s,r){if(s&&(e.hideOnChange&&(r.docChanged||r.selection)||e.hideOn&&e.hideOn(r,s)))return null;if(s&&r.docChanged){let o=r.changes.mapPos(s.pos,-1,oe.TrackDel);if(o==null)return null;let l=Object.assign(Object.create(null),s);l.pos=o,s.end!=null&&(l.end=r.changes.mapPos(s.end)),s=l}for(let o of r.effects)o.is(t)&&(s=o.value),o.is(Pd)&&(s=null);return s},provide:s=>xn.from(s)});return[i,pe.define(s=>new Dd(s,n,i,t,e.hoverTime||300)),Md]}function Ed(n,e){let t=n.plugin(vh);if(!t)return null;let i=t.manager.tooltips.indexOf(e);return i<0?null:t.manager.tooltipViews[i]}const Pd=R.define(),Yo=T.define({combine(n){let e,t;for(let i of n)e=e||i.topContainer,t=t||i.bottomContainer;return{topContainer:e,bottomContainer:t}}});function Ld(n,e){let t=n.plugin(Sh),i=t?t.specs.indexOf(e):-1;return i>-1?t.panels[i]:null}const Sh=pe.fromClass(class{constructor(n){this.input=n.state.facet(sr),this.specs=this.input.filter(t=>t),this.panels=this.specs.map(t=>t(n));let e=n.state.facet(Yo);this.top=new Gi(n,!0,e.topContainer),this.bottom=new Gi(n,!1,e.bottomContainer),this.top.sync(this.panels.filter(t=>t.top)),this.bottom.sync(this.panels.filter(t=>!t.top));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(n){let e=n.state.facet(Yo);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new Gi(n.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new Gi(n.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let t=n.state.facet(sr);if(t!=this.input){let i=t.filter(a=>a),s=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),c;h<0?(c=a(n.view),l.push(c)):(c=this.panels[h],c.update&&c.update(n)),s.push(c),(c.top?r:o).push(c)}this.specs=i,this.panels=s,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount()}else for(let i of this.panels)i.update&&i.update(n)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:n=>O.scrollMargins.of(e=>{let t=e.plugin(n);return t&&{top:t.top.scrollMargin(),bottom:t.bottom.scrollMargin()}})});class Gi{constructor(e,t,i){this.view=e,this.top=t,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(e){for(let t of this.panels)t.destroy&&e.indexOf(t)<0&&t.destroy();this.panels=e,this.syncDOM()}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let e=this.dom.firstChild;for(let t of this.panels)if(t.dom.parentNode==this.dom){for(;e!=t.dom;)e=Xo(e);e=e.nextSibling}else this.dom.insertBefore(t.dom,e);for(;e;)e=Xo(e)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let e of this.classes.split(" "))e&&this.container.classList.remove(e);for(let e of(this.classes=this.view.themeClasses).split(" "))e&&this.container.classList.add(e)}}}function Xo(n){let e=n.nextSibling;return n.remove(),e}const sr=T.define({enables:Sh});class pt extends At{compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}eq(e){return!1}destroy(e){}}pt.prototype.elementClass="";pt.prototype.toDOM=void 0;pt.prototype.mapMode=oe.TrackBefore;pt.prototype.startSide=pt.prototype.endSide=-1;pt.prototype.point=!0;const os=T.define(),Rd={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>H.empty,lineMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},bi=T.define();function _d(n){return[Ch(),bi.of(Object.assign(Object.assign({},Rd),n))]}const Qo=T.define({combine:n=>n.some(e=>e)});function Ch(n){return[Id]}const Id=pe.fromClass(class{constructor(n){this.view=n,this.prevViewport=n.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight+"px",this.gutters=n.state.facet(bi).map(e=>new el(n,e));for(let e of this.gutters)this.dom.appendChild(e.dom);this.fixed=!n.state.facet(Qo),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),n.scrollDOM.insertBefore(this.dom,n.contentDOM)}update(n){if(this.updateGutters(n)){let e=this.prevViewport,t=n.view.viewport,i=Math.min(e.to,t.to)-Math.max(e.from,t.from);this.syncGutters(i<(t.to-t.from)*.8)}n.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight+"px"),this.view.state.facet(Qo)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=n.view.viewport}syncGutters(n){let e=this.dom.nextSibling;n&&this.dom.remove();let t=H.iter(this.view.state.facet(os),this.view.viewport.from),i=[],s=this.gutters.map(r=>new Nd(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks){let o;if(Array.isArray(r.type)){for(let l of r.type)if(l.type==$.Text){o=l;break}}else o=r.type==$.Text?r:void 0;if(o){i.length&&(i=[]),Ah(t,i,r.from);for(let l of s)l.line(this.view,o,i)}}for(let r of s)r.finish();n&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(n){let e=n.startState.facet(bi),t=n.state.facet(bi),i=n.docChanged||n.heightChanged||n.viewportChanged||!H.eq(n.startState.facet(os),n.state.facet(os),n.view.viewport.from,n.view.viewport.to);if(e==t)for(let s of this.gutters)s.update(n)&&(i=!0);else{i=!0;let s=[];for(let r of t){let o=e.indexOf(r);o<0?s.push(new el(this.view,r)):(this.gutters[o].update(n),s.push(this.gutters[o]))}for(let r of this.gutters)r.dom.remove(),s.indexOf(r)<0&&r.destroy();for(let r of s)this.dom.appendChild(r.dom);this.gutters=s}return i}destroy(){for(let n of this.gutters)n.destroy();this.dom.remove()}},{provide:n=>O.scrollMargins.of(e=>{let t=e.plugin(n);return!t||t.gutters.length==0||!t.fixed?null:e.textDirection==G.LTR?{left:t.dom.offsetWidth}:{right:t.dom.offsetWidth}})});function Zo(n){return Array.isArray(n)?n:[n]}function Ah(n,e,t){for(;n.value&&n.from<=t;)n.from==t&&e.push(n.value),n.next()}class Nd{constructor(e,t,i){this.gutter=e,this.height=i,this.localMarkers=[],this.i=0,this.cursor=H.iter(e.markers,t.from)}line(e,t,i){this.localMarkers.length&&(this.localMarkers=[]),Ah(this.cursor,this.localMarkers,t.from);let s=i.length?this.localMarkers.concat(i):this.localMarkers,r=this.gutter.config.lineMarker(e,t,s);r&&s.unshift(r);let o=this.gutter;if(s.length==0&&!o.config.renderEmptyElements)return;let l=t.top-this.height;if(this.i==o.elements.length){let a=new Mh(e,t.height,l,s);o.elements.push(a),o.dom.appendChild(a.dom)}else o.elements[this.i].update(e,t.height,l,s);this.height=t.bottom,this.i++}finish(){let e=this.gutter;for(;e.elements.length>this.i;){let t=e.elements.pop();e.dom.removeChild(t.dom),t.destroy()}}}class el{constructor(e,t){this.view=e,this.config=t,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in t.domEventHandlers)this.dom.addEventListener(i,s=>{let r=e.lineBlockAtHeight(s.clientY-e.documentTop);t.domEventHandlers[i](e,r,s)&&s.preventDefault()});this.markers=Zo(t.markers(e)),t.initialSpacer&&(this.spacer=new Mh(e,0,0,[t.initialSpacer(e)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(e){let t=this.markers;if(this.markers=Zo(this.config.markers(e.view)),this.spacer&&this.config.updateSpacer){let s=this.config.updateSpacer(this.spacer.markers[0],e);s!=this.spacer.markers[0]&&this.spacer.update(e.view,0,0,[s])}let i=e.view.viewport;return!H.eq(this.markers,t,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(e):!1)}destroy(){for(let e of this.elements)e.destroy()}}class Mh{constructor(e,t,i,s){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(e,t,i,s)}update(e,t,i,s){this.height!=t&&(this.dom.style.height=(this.height=t)+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),Vd(this.markers,s)||this.setMarkers(e,s)}setMarkers(e,t){let i="cm-gutterElement",s=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<t.length?t[r++]:null,h=!1;if(a){let c=a.elementClass;c&&(i+=" "+c);for(let f=o;f<this.markers.length;f++)if(this.markers[f].compare(a)){l=f,h=!0;break}}else l=this.markers.length;for(;o<l;){let c=this.markers[o++];if(c.toDOM){c.destroy(s);let f=s.nextSibling;s.remove(),s=f}}if(!a)break;a.toDOM&&(h?s=s.nextSibling:this.dom.insertBefore(a.toDOM(e),s)),h&&o++}this.dom.className=i,this.markers=t}destroy(){this.setMarkers(null,[])}}function Vd(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].compare(e[t]))return!1;return!0}const Fd=T.define(),Ht=T.define({combine(n){return _t(n,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(e,t){let i=Object.assign({},e);for(let s in t){let r=i[s],o=t[s];i[s]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o}return i}})}});class ls extends pt{constructor(e){super(),this.number=e}eq(e){return this.number==e.number}toDOM(){return document.createTextNode(this.number)}}function as(n,e){return n.state.facet(Ht).formatNumber(e,n.state)}const Hd=bi.compute([Ht],n=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(e){return e.state.facet(Fd)},lineMarker(e,t,i){return i.some(s=>s.toDOM)?null:new ls(as(e,e.state.doc.lineAt(t.from).number))},lineMarkerChange:e=>e.startState.facet(Ht)!=e.state.facet(Ht),initialSpacer(e){return new ls(as(e,tl(e.state.doc.lines)))},updateSpacer(e,t){let i=as(t.view,tl(t.view.state.doc.lines));return i==e.number?e:new ls(i)},domEventHandlers:n.facet(Ht).domEventHandlers}));function $d(n={}){return[Ht.of(n),Ch(),Hd]}function tl(n){let e=9;for(;e<n;)e=e*10+9;return e}const Wd=1024;let zd=0;class Oe{constructor(e,t){this.from=e,this.to=t}}class _{constructor(e={}){this.id=zd++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof e!="function"&&(e=we.match(e)),t=>{let i=e(t);return i===void 0?null:[this,i]}}}_.closedBy=new _({deserialize:n=>n.split(" ")});_.openedBy=new _({deserialize:n=>n.split(" ")});_.group=new _({deserialize:n=>n.split(" ")});_.contextHash=new _({perNode:!0});_.lookAhead=new _({perNode:!0});_.mounted=new _({perNode:!0});class qd{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}}const jd=Object.create(null);class we{constructor(e,t,i,s=0){this.name=e,this.props=t,this.id=i,this.flags=s}static define(e){let t=e.props&&e.props.length?Object.create(null):jd,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(e.name==null?8:0),s=new we(e.name||"",t,e.id,i);if(e.props){for(let r of e.props)if(Array.isArray(r)||(r=r(s)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[r[0].id]=r[1]}}return s}prop(e){return this.props[e.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(e){if(typeof e=="string"){if(this.name==e)return!0;let t=this.prop(_.group);return t?t.indexOf(e)>-1:!1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let s of i.split(" "))t[s]=e[i];return i=>{for(let s=i.prop(_.group),r=-1;r<(s?s.length:0);r++){let o=t[r<0?i.name:s[r]];if(o)return o}}}}we.none=new we("",Object.create(null),0,8);class Tr{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let s=null;for(let r of e){let o=r(i);o&&(s||(s=Object.assign({},i.props)),s[o[0].id]=o[1])}t.push(s?new we(i.name,s,i.id,i.flags):i)}return new Tr(t)}}const Ji=new WeakMap,il=new WeakMap;var Y;(function(n){n[n.ExcludeBuffers=1]="ExcludeBuffers",n[n.IncludeAnonymous=2]="IncludeAnonymous",n[n.IgnoreMounts=4]="IgnoreMounts",n[n.IgnoreOverlays=8]="IgnoreOverlays"})(Y||(Y={}));class W{constructor(e,t,i,s,r){if(this.type=e,this.children=t,this.positions=i,this.length=s,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l}}toString(){let e=this.prop(_.mounted);if(e&&!e.overlay)return e.tree.toString();let t="";for(let i of this.children){let s=i.toString();s&&(t&&(t+=","),t+=s)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new Ti(this.topNode,e)}cursorAt(e,t=0,i=0){let s=Ji.get(this)||this.topNode,r=new Ti(s);return r.moveTo(e,t),Ji.set(this,r._tree),r}get topNode(){return new Pe(this,0,0,null)}resolve(e,t=0){let i=Xt(Ji.get(this)||this.topNode,e,t,!1);return Ji.set(this,i),i}resolveInner(e,t=0){let i=Xt(il.get(this)||this.topNode,e,t,!0);return il.set(this,i),i}iterate(e){let{enter:t,leave:i,from:s=0,to:r=this.length}=e;for(let o=this.cursor((e.mode||0)|Y.IncludeAnonymous);;){let l=!1;if(o.from<=r&&o.to>=s&&(o.type.isAnonymous||t(o)!==!1)){if(o.firstChild())continue;l=!0}for(;l&&i&&!o.type.isAnonymous&&i(o),!o.nextSibling();){if(!o.parent())return;l=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:Er(we.none,this.children,this.positions,0,this.children.length,0,this.length,(t,i,s)=>new W(this.type,t,i,s,this.propValues),e.makeTree||((t,i,s)=>new W(we.none,t,i,s)))}static build(e){return Ud(e)}}W.empty=new W(we.none,[],[],0);class Or{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new Or(this.buffer,this.index)}}class It{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return we.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],s=this.set.types[t],r=s.name;if(/\W/.test(r)&&!s.isError&&(r=JSON.stringify(r)),e+=4,i==e)return r;let o=[];for(;e<i;)o.push(this.childString(e)),e=this.buffer[e+3];return r+"("+o.join(",")+")"}findChild(e,t,i,s,r){let{buffer:o}=this,l=-1;for(let a=e;a!=t&&!(Dh(r,s,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(e,t,i){let s=this.buffer,r=new Uint16Array(t-e),o=0;for(let l=e,a=0;l<t;){r[a++]=s[l++],r[a++]=s[l++]-i;let h=r[a++]=s[l++]-i;r[a++]=s[l++]-e,o=Math.max(o,h)}return new It(r,o,this.set)}}function Dh(n,e,t,i){switch(n){case-2:return t<e;case-1:return i>=e&&t<e;case 0:return t<e&&i>e;case 1:return t<=e&&i>e;case 2:return i>e;case 4:return!0}}function Th(n,e){let t=n.childBefore(e);for(;t;){let i=t.lastChild;if(!i||i.to!=t.to)break;i.type.isError&&i.from==i.to?(n=t,t=i.prevSibling):t=i}return n}function Xt(n,e,t,i){for(var s;n.from==n.to||(t<1?n.from>=e:n.from>e)||(t>-1?n.to<=e:n.to<e);){let o=!i&&n instanceof Pe&&n.index<0?null:n.parent;if(!o)return n;n=o}let r=i?0:Y.IgnoreOverlays;if(i)for(let o=n,l=o.parent;l;o=l,l=o.parent)o instanceof Pe&&o.index<0&&((s=l.enter(e,t,r))===null||s===void 0?void 0:s.from)!=o.from&&(n=l);for(;;){let o=n.enter(e,t,r);if(!o)return n;n=o}}class Pe{constructor(e,t,i,s){this._tree=e,this.from=t,this.index=i,this._parent=s}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,s,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=t>0?l.length:-1;e!=h;e+=t){let c=l[e],f=a[e]+o.from;if(Dh(s,i,f,f+c.length)){if(c instanceof It){if(r&Y.ExcludeBuffers)continue;let u=c.findChild(0,c.buffer.length,t,i-f,s);if(u>-1)return new Ue(new Kd(o,c,e,f),null,u)}else if(r&Y.IncludeAnonymous||!c.type.isAnonymous||Br(c)){let u;if(!(r&Y.IgnoreMounts)&&c.props&&(u=c.prop(_.mounted))&&!u.overlay)return new Pe(u.tree,f,e,o);let d=new Pe(c,f,e,o);return r&Y.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(t<0?c.children.length-1:0,t,i,s)}}}if(r&Y.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?e=o.index+t:e=t<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let s;if(!(i&Y.IgnoreOverlays)&&(s=this._tree.prop(_.mounted))&&s.overlay){let r=e-this.from;for(let{from:o,to:l}of s.overlay)if((t>0?o<=r:o<r)&&(t<0?l>=r:l>r))return new Pe(s.tree,s.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}cursor(e=0){return new Ti(this,e)}get tree(){return this._tree}toTree(){return this._tree}resolve(e,t=0){return Xt(this,e,t,!1)}resolveInner(e,t=0){return Xt(this,e,t,!0)}enterUnfinishedNodesBefore(e){return Th(this,e)}getChild(e,t=null,i=null){let s=vn(this,e,t,i);return s.length?s[0]:null}getChildren(e,t=null,i=null){return vn(this,e,t,i)}toString(){return this._tree.toString()}get node(){return this}matchContext(e){return Sn(this,e)}}function vn(n,e,t,i){let s=n.cursor(),r=[];if(!s.firstChild())return r;if(t!=null){for(;!s.type.is(t);)if(!s.nextSibling())return r}for(;;){if(i!=null&&s.type.is(i))return r;if(s.type.is(e)&&r.push(s.node),!s.nextSibling())return i==null?r:[]}}function Sn(n,e,t=e.length-1){for(let i=n.parent;t>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(e[t]&&e[t]!=i.name)return!1;t--}}return!0}class Kd{constructor(e,t,i,s){this.parent=e,this.buffer=t,this.index=i,this.start=s}}class Ue{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.context.start,i);return r<0?null:new Ue(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&Y.ExcludeBuffers)return null;let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return r<0?null:new Ue(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new Ue(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new Ue(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}cursor(e=0){return new Ti(this,e)}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,s=this.index+4,r=i.buffer[this.index+3];if(r>s){let o=i.buffer[this.index+1];e.push(i.slice(s,r,o)),t.push(0)}return new W(this.type,e,t,this.to-this.from)}resolve(e,t=0){return Xt(this,e,t,!1)}resolveInner(e,t=0){return Xt(this,e,t,!0)}enterUnfinishedNodesBefore(e){return Th(this,e)}toString(){return this.context.buffer.childString(this.index)}getChild(e,t=null,i=null){let s=vn(this,e,t,i);return s.length?s[0]:null}getChildren(e,t=null,i=null){return vn(this,e,t,i)}get node(){return this}matchContext(e){return Sn(this,e)}}class Ti{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof Pe)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let i=e._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return e?(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0):!1}yieldBuf(e,t){this.index=e;let{start:i,buffer:s}=this.buffer;return this.type=t||s.set.types[s.buffer[e]],this.from=i+s.buffer[e+1],this.to=i+s.buffer[e+2],!0}yield(e){return e?e instanceof Pe?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:s}=this.buffer,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?i&Y.ExcludeBuffers?!1:this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&Y.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&Y.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode)):!1;let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let s=i<0?0:this.stack[i]+4;if(this.index!=s)return this.yieldBuf(t.findChild(s,this.index,-1,0,4))}else{let s=t.buffer[this.index+3];if(s<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(s)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:s}=this;if(s){if(e>0){if(this.index<s.buffer.buffer.length)return!1}else for(let r=0;r<this.index;r++)if(s.buffer.buffer[r+3]<this.index)return!1;({index:t,parent:i}=s)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,o=e<0?-1:i._tree.children.length;r!=o;r+=e){let l=i._tree.children[r];if(this.mode&Y.IncludeAnonymous||l instanceof It||!l.type.isAnonymous||Br(l))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let s=this.index,r=this.stack.length;r>=0;){for(let o=e;o;o=o._parent)if(o.index==s){if(s==this.index)return o;t=o,i=r+1;break e}s=this.stack[--r]}for(let s=i;s<this.stack.length;s++)t=new Ue(this.buffer,t,this.stack[s]);return this.bufferNode=new Ue(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let s=!1;if(this.type.isAnonymous||e(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(s=!0)}for(;s&&t&&t(this),s=this.type.isAnonymous,!this.nextSibling();){if(!i)return;this.parent(),i--,s=!0}}}matchContext(e){if(!this.buffer)return Sn(this.node,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let s=e.length-1,r=this.stack.length-1;s>=0;r--){if(r<0)return Sn(this.node,e,s);let o=i[t.buffer[this.stack[r]]];if(!o.isAnonymous){if(e[s]&&e[s]!=o.name)return!1;s--}}return!0}}function Br(n){return n.children.some(e=>e instanceof It||!e.type.isAnonymous||Br(e))}function Ud(n){var e;let{buffer:t,nodeSet:i,maxBufferLength:s=Wd,reused:r=[],minRepeatType:o=i.types.length}=n,l=Array.isArray(t)?new Or(t,t.length):t,a=i.types,h=0,c=0;function f(S,C,D,E,B){let{id:I,start:P,end:F,size:K}=l,J=c;for(;K<0;)if(l.next(),K==-1){let z=r[I];D.push(z),E.push(P-S);return}else if(K==-3){h=I;return}else if(K==-4){c=I;return}else throw new RangeError(`Unrecognized record size: ${K}`);let ge=a[I],te,A,Q=P-S;if(F-P<=s&&(A=g(l.pos-C,B))){let z=new Uint16Array(A.size-A.skip),ne=l.pos-A.size,ae=z.length;for(;l.pos>ne;)ae=y(A.start,z,ae);te=new It(z,F-A.start,i),Q=A.start-S}else{let z=l.pos-K;l.next();let ne=[],ae=[],he=I>=o?I:-1,Nt=0,Fi=F;for(;l.pos>z;)he>=0&&l.id==he&&l.size>=0?(l.end<=Fi-s&&(d(ne,ae,P,Nt,l.end,Fi,he,J),Nt=ne.length,Fi=l.end),l.next()):f(P,z,ne,ae,he);if(he>=0&&Nt>0&&Nt<ne.length&&d(ne,ae,P,Nt,P,Fi,he,J),ne.reverse(),ae.reverse(),he>-1&&Nt>0){let Qr=u(ge);te=Er(ge,ne,ae,0,ne.length,0,F-P,Qr,Qr)}else te=p(ge,ne,ae,F-P,J-F)}D.push(te),E.push(Q)}function u(S){return(C,D,E)=>{let B=0,I=C.length-1,P,F;if(I>=0&&(P=C[I])instanceof W){if(!I&&P.type==S&&P.length==E)return P;(F=P.prop(_.lookAhead))&&(B=D[I]+P.length+F)}return p(S,C,D,E,B)}}function d(S,C,D,E,B,I,P,F){let K=[],J=[];for(;S.length>E;)K.push(S.pop()),J.push(C.pop()+D-B);S.push(p(i.types[P],K,J,I-B,F-I)),C.push(B-D)}function p(S,C,D,E,B=0,I){if(h){let P=[_.contextHash,h];I=I?[P].concat(I):[P]}if(B>25){let P=[_.lookAhead,B];I=I?[P].concat(I):[P]}return new W(S,C,D,E,I)}function g(S,C){let D=l.fork(),E=0,B=0,I=0,P=D.end-s,F={size:0,start:0,skip:0};e:for(let K=D.pos-S;D.pos>K;){let J=D.size;if(D.id==C&&J>=0){F.size=E,F.start=B,F.skip=I,I+=4,E+=4,D.next();continue}let ge=D.pos-J;if(J<0||ge<K||D.start<P)break;let te=D.id>=o?4:0,A=D.start;for(D.next();D.pos>ge;){if(D.size<0)if(D.size==-3)te+=4;else break e;else D.id>=o&&(te+=4);D.next()}B=A,E+=J,I+=te}return(C<0||E==S)&&(F.size=E,F.start=B,F.skip=I),F.size>4?F:void 0}function y(S,C,D){let{id:E,start:B,end:I,size:P}=l;if(l.next(),P>=0&&E<o){let F=D;if(P>4){let K=l.pos-(P-4);for(;l.pos>K;)D=y(S,C,D)}C[--D]=F,C[--D]=I-S,C[--D]=B-S,C[--D]=E}else P==-3?h=E:P==-4&&(c=E);return D}let b=[],k=[];for(;l.pos>0;)f(n.start||0,n.bufferStart||0,b,k,-1);let v=(e=n.length)!==null&&e!==void 0?e:b.length?k[0]+b[0].length:0;return new W(a[n.topID],b.reverse(),k.reverse(),v)}const nl=new WeakMap;function cn(n,e){if(!n.isAnonymous||e instanceof It||e.type!=n)return 1;let t=nl.get(e);if(t==null){t=1;for(let i of e.children){if(i.type!=n||!(i instanceof W)){t=1;break}t+=cn(n,i)}nl.set(e,t)}return t}function Er(n,e,t,i,s,r,o,l,a){let h=0;for(let p=i;p<s;p++)h+=cn(n,e[p]);let c=Math.ceil(h*1.5/8),f=[],u=[];function d(p,g,y,b,k){for(let v=y;v<b;){let S=v,C=g[v],D=cn(n,p[v]);for(v++;v<b;v++){let E=cn(n,p[v]);if(D+E>=c)break;D+=E}if(v==S+1){if(D>c){let E=p[S];d(E.children,E.positions,0,E.children.length,g[S]+k);continue}f.push(p[S])}else{let E=g[v-1]+p[v-1].length-C;f.push(Er(n,p,g,S,v,C,E,null,a))}u.push(C+k-r)}}return d(e,t,i,s,0),(l||a)(f,u,o)}class ob{constructor(){this.map=new WeakMap}setBuffer(e,t,i){let s=this.map.get(e);s||this.map.set(e,s=new Map),s.set(t,i)}getBuffer(e,t){let i=this.map.get(e);return i&&i.get(t)}set(e,t){e instanceof Ue?this.setBuffer(e.context.buffer,e.index,t):e instanceof Pe&&this.map.set(e.tree,t)}get(e){return e instanceof Ue?this.getBuffer(e.context.buffer,e.index):e instanceof Pe?this.map.get(e.tree):void 0}cursorSet(e,t){e.buffer?this.setBuffer(e.buffer.buffer,e.index,t):this.map.set(e.tree,t)}cursorGet(e){return e.buffer?this.getBuffer(e.buffer.buffer,e.index):this.map.get(e.tree)}}class nt{constructor(e,t,i,s,r=!1,o=!1){this.from=e,this.to=t,this.tree=i,this.offset=s,this.open=(r?1:0)|(o?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(e,t=[],i=!1){let s=[new nt(0,e.length,e,0,!1,i)];for(let r of t)r.to>e.length&&s.push(r);return s}static applyChanges(e,t,i=128){if(!t.length)return e;let s=[],r=1,o=e.length?e[0]:null;for(let l=0,a=0,h=0;;l++){let c=l<t.length?t[l]:null,f=c?c.fromA:1e9;if(f-a>=i)for(;o&&o.from<f;){let u=o;if(a>=u.from||f<=u.to||h){let d=Math.max(u.from,a)-h,p=Math.min(u.to,f)-h;u=d>=p?null:new nt(d,p,u.tree,u.offset+h,l>0,!!c)}if(u&&s.push(u),o.to>f)break;o=r<e.length?e[r++]:null}if(!c)break;a=c.toA,h=c.toA-c.toB}return s}}class Oh{startParse(e,t,i){return typeof e=="string"&&(e=new Gd(e)),i=i?i.length?i.map(s=>new Oe(s.from,s.to)):[new Oe(0,0)]:[new Oe(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let s=this.startParse(e,t,i);for(;;){let r=s.advance();if(r)return r}}}class Gd{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}function lb(n){return(e,t,i,s)=>new Yd(e,n,t,i,s)}class sl{constructor(e,t,i,s,r){this.parser=e,this.parse=t,this.overlay=i,this.target=s,this.ranges=r}}class Jd{constructor(e,t,i,s,r,o,l){this.parser=e,this.predicate=t,this.mounts=i,this.index=s,this.start=r,this.target=o,this.prev=l,this.depth=0,this.ranges=[]}}const rr=new _({perNode:!0});class Yd{constructor(e,t,i,s,r){this.nest=t,this.input=i,this.fragments=s,this.ranges=r,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=e}advance(){if(this.baseParse){let i=this.baseParse.advance();if(!i)return null;if(this.baseParse=null,this.baseTree=i,this.startInner(),this.stoppedAt!=null)for(let s of this.inner)s.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let i=this.baseTree;return this.stoppedAt!=null&&(i=new W(i.type,i.children,i.positions,i.length,i.propValues.concat([[rr,this.stoppedAt]]))),i}let e=this.inner[this.innerDone],t=e.parse.advance();if(t){this.innerDone++;let i=Object.assign(Object.create(null),e.target.props);i[_.mounted.id]=new qd(t,e.overlay,e.parser),e.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let e=this.input.length;for(let t=this.innerDone;t<this.inner.length;t++)this.inner[t].ranges[0].from<e&&(e=Math.min(e,this.inner[t].parse.parsedPos));return e}stopAt(e){if(this.stoppedAt=e,this.baseParse)this.baseParse.stopAt(e);else for(let t=this.innerDone;t<this.inner.length;t++)this.inner[t].parse.stopAt(e)}startInner(){let e=new Zd(this.fragments),t=null,i=null,s=new Ti(new Pe(this.baseTree,this.ranges[0].from,0,null),Y.IncludeAnonymous|Y.IgnoreMounts);e:for(let r,o;this.stoppedAt==null||s.from<this.stoppedAt;){let l=!0,a;if(e.hasNode(s)){if(t){let h=t.mounts.find(c=>c.frag.from<=s.from&&c.frag.to>=s.to&&c.mount.overlay);if(h)for(let c of h.mount.overlay){let f=c.from+h.pos,u=c.to+h.pos;f>=s.from&&u<=s.to&&!t.ranges.some(d=>d.from<u&&d.to>f)&&t.ranges.push({from:f,to:u})}}l=!1}else if(i&&(o=Xd(i.ranges,s.from,s.to)))l=o!=2;else if(!s.type.isAnonymous&&s.from<s.to&&(r=this.nest(s,this.input))){s.tree||Qd(s);let h=e.findMounts(s.from,r.parser);if(typeof r.overlay=="function")t=new Jd(r.parser,r.overlay,h,this.inner.length,s.from,s.tree,t);else{let c=ll(this.ranges,r.overlay||[new Oe(s.from,s.to)]);c.length&&this.inner.push(new sl(r.parser,r.parser.startParse(this.input,al(h,c),c),r.overlay?r.overlay.map(f=>new Oe(f.from-s.from,f.to-s.from)):null,s.tree,c)),r.overlay?c.length&&(i={ranges:c,depth:0,prev:i}):l=!1}}else t&&(a=t.predicate(s))&&(a===!0&&(a=new Oe(s.from,s.to)),a.from<a.to&&t.ranges.push(a));if(l&&s.firstChild())t&&t.depth++,i&&i.depth++;else for(;!s.nextSibling();){if(!s.parent())break e;if(t&&!--t.depth){let h=ll(this.ranges,t.ranges);h.length&&this.inner.splice(t.index,0,new sl(t.parser,t.parser.startParse(this.input,al(t.mounts,h),h),t.ranges.map(c=>new Oe(c.from-t.start,c.to-t.start)),t.target,h)),t=t.prev}i&&!--i.depth&&(i=i.prev)}}}}function Xd(n,e,t){for(let i of n){if(i.from>=t)break;if(i.to>e)return i.from<=e&&i.to>=t?2:1}return 0}function rl(n,e,t,i,s,r){if(e<t){let o=n.buffer[e+1];i.push(n.slice(e,t,o)),s.push(o-r)}}function Qd(n){let{node:e}=n,t=0;do n.parent(),t++;while(!n.tree);let i=0,s=n.tree,r=0;for(;r=s.positions[i]+n.from,!(r<=e.from&&r+s.children[i].length>=e.to);i++);let o=s.children[i],l=o.buffer;function a(h,c,f,u,d){let p=h;for(;l[p+2]+r<=e.from;)p=l[p+3];let g=[],y=[];rl(o,h,p,g,y,u);let b=l[p+1],k=l[p+2],v=b+r==e.from&&k+r==e.to&&l[p]==e.type.id;return g.push(v?e.toTree():a(p+4,l[p+3],o.set.types[l[p]],b,k-b)),y.push(b-u),rl(o,l[p+3],c,g,y,u),new W(f,g,y,d)}s.children[i]=a(0,l.length,we.none,0,o.length);for(let h=0;h<=t;h++)n.childAfter(e.from)}class ol{constructor(e,t){this.offset=t,this.done=!1,this.cursor=e.cursor(Y.IncludeAnonymous|Y.IgnoreMounts)}moveTo(e){let{cursor:t}=this,i=e-this.offset;for(;!this.done&&t.from<i;)t.to>=e&&t.enter(i,1,Y.IgnoreOverlays|Y.ExcludeBuffers)||t.next(!1)||(this.done=!0)}hasNode(e){if(this.moveTo(e.from),!this.done&&this.cursor.from+this.offset==e.from&&this.cursor.tree)for(let t=this.cursor.tree;;){if(t==e.tree)return!0;if(t.children.length&&t.positions[0]==0&&t.children[0]instanceof W)t=t.children[0];else break}return!1}}class Zd{constructor(e){var t;if(this.fragments=e,this.curTo=0,this.fragI=0,e.length){let i=this.curFrag=e[0];this.curTo=(t=i.tree.prop(rr))!==null&&t!==void 0?t:i.to,this.inner=new ol(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(e){for(;this.curFrag&&e.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=e.from&&this.curTo>=e.to&&this.inner.hasNode(e)}nextFrag(){var e;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let t=this.curFrag=this.fragments[this.fragI];this.curTo=(e=t.tree.prop(rr))!==null&&e!==void 0?e:t.to,this.inner=new ol(t.tree,-t.offset)}}findMounts(e,t){var i;let s=[];if(this.inner){this.inner.cursor.moveTo(e,1);for(let r=this.inner.cursor.node;r;r=r.parent){let o=(i=r.tree)===null||i===void 0?void 0:i.prop(_.mounted);if(o&&o.parser==t)for(let l=this.fragI;l<this.fragments.length;l++){let a=this.fragments[l];if(a.from>=r.to)break;a.tree==this.curFrag.tree&&s.push({frag:a,pos:r.from-a.offset,mount:o})}}}return s}}function ll(n,e){let t=null,i=e;for(let s=1,r=0;s<n.length;s++){let o=n[s-1].to,l=n[s].from;for(;r<i.length;r++){let a=i[r];if(a.from>=l)break;a.to<=o||(t||(i=t=e.slice()),a.from<o?(t[r]=new Oe(a.from,o),a.to>l&&t.splice(r+1,0,new Oe(l,a.to))):a.to>l?t[r--]=new Oe(l,a.to):t.splice(r--,1))}}return i}function ep(n,e,t,i){let s=0,r=0,o=!1,l=!1,a=-1e9,h=[];for(;;){let c=s==n.length?1e9:o?n[s].to:n[s].from,f=r==e.length?1e9:l?e[r].to:e[r].from;if(o!=l){let u=Math.max(a,t),d=Math.min(c,f,i);u<d&&h.push(new Oe(u,d))}if(a=Math.min(c,f),a==1e9)break;c==a&&(o?(o=!1,s++):o=!0),f==a&&(l?(l=!1,r++):l=!0)}return h}function al(n,e){let t=[];for(let{pos:i,mount:s,frag:r}of n){let o=i+(s.overlay?s.overlay[0].from:0),l=o+s.tree.length,a=Math.max(r.from,o),h=Math.min(r.to,l);if(s.overlay){let c=s.overlay.map(u=>new Oe(u.from+i,u.to+i)),f=ep(e,c,a,h);for(let u=0,d=a;;u++){let p=u==f.length,g=p?h:f[u].from;if(g>d&&t.push(new nt(d,g,s.tree,-o,r.from>=d||r.openStart,r.to<=g||r.openEnd)),p)break;d=f[u].to}}else t.push(new nt(a,h,s.tree,-o,r.from>=o||r.openStart,r.to<=l||r.openEnd))}return t}let tp=0;class qe{constructor(e,t,i){this.set=e,this.base=t,this.modified=i,this.id=tp++}static define(e){if(e?.base)throw new Error("Can not derive from a modified tag");let t=new qe([],null,[]);if(t.set.push(t),e)for(let i of e.set)t.set.push(i);return t}static defineModifier(){let e=new Cn;return t=>t.modified.indexOf(e)>-1?t:Cn.get(t.base||t,t.modified.concat(e).sort((i,s)=>i.id-s.id))}}let ip=0;class Cn{constructor(){this.instances=[],this.id=ip++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(l=>l.base==e&&np(t,l.modified));if(i)return i;let s=[],r=new qe(s,e,t);for(let l of t)l.instances.push(r);let o=sp(t);for(let l of e.set)if(!l.modified.length)for(let a of o)s.push(Cn.get(l,a));return r}}function np(n,e){return n.length==e.length&&n.every((t,i)=>t==e[i])}function sp(n){let e=[[]];for(let t=0;t<n.length;t++)for(let i=0,s=e.length;i<s;i++)e.push(e[i].concat(n[t]));return e.sort((t,i)=>i.length-t.length)}function rp(n){let e=Object.create(null);for(let t in n){let i=n[t];Array.isArray(i)||(i=[i]);for(let s of t.split(" "))if(s){let r=[],o=2,l=s;for(let f=0;;){if(l=="..."&&f>0&&f+3==s.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+s);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),f+=u[0].length,f==s.length)break;let d=s[f++];if(f==s.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+s);l=s.slice(f)}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+s);let c=new An(i,o,a>0?r.slice(0,a):null);e[h]=c.sort(e[h])}}return Bh.add(e)}const Bh=new _;class An{constructor(e,t,i,s){this.tags=e,this.mode=t,this.context=i,this.next=s}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}An.empty=new An([],2,null);function Eh(n,e){let t=Object.create(null);for(let r of n)if(!Array.isArray(r.tag))t[r.tag.id]=r.class;else for(let o of r.tag)t[o.id]=r.class;let{scope:i,all:s=null}=e||{};return{style:r=>{let o=s;for(let l of r)for(let a of l.set){let h=t[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function op(n,e){let t=null;for(let i of n){let s=i.style(e);s&&(t=t?t+" "+s:s)}return t}function lp(n,e,t,i=0,s=n.length){let r=new ap(i,Array.isArray(e)?e:[e],t);r.highlightRange(n.cursor(),i,s,"",r.highlighters),r.flush(s)}class ap{constructor(e,t,i){this.at=e,this.highlighters=t,this.span=i,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,i,s,r){let{type:o,from:l,to:a}=e;if(l>=i||a<=t)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=s,c=hp(e)||An.empty,f=op(r,c.tags);if(f&&(h&&(h+=" "),h+=f,c.mode==1&&(s+=(s?" ":"")+f)),this.startSpan(e.from,h),c.opaque)return;let u=e.tree&&e.tree.prop(_.mounted);if(u&&u.overlay){let d=e.node.enter(u.overlay[0].from+l,1),p=this.highlighters.filter(y=>!y.scope||y.scope(u.tree.type)),g=e.firstChild();for(let y=0,b=l;;y++){let k=y<u.overlay.length?u.overlay[y]:null,v=k?k.from+l:a,S=Math.max(t,b),C=Math.min(i,v);if(S<C&&g)for(;e.from<C&&(this.highlightRange(e,S,C,s,r),this.startSpan(Math.min(C,e.to),h),!(e.to>=v||!e.nextSibling())););if(!k||v>i)break;b=k.to+l,b>t&&(this.highlightRange(d.cursor(),Math.max(t,k.from+l),Math.min(i,b),s,p),this.startSpan(b,h))}g&&e.parent()}else if(e.firstChild()){do if(!(e.to<=t)){if(e.from>=i)break;this.highlightRange(e,t,i,s,r),this.startSpan(Math.min(i,e.to),h)}while(e.nextSibling());e.parent()}}}function hp(n){let e=n.type.prop(Bh);for(;e&&e.context&&!n.matchContext(e.context);)e=e.next;return e||null}const x=qe.define,Yi=x(),st=x(),hl=x(st),cl=x(st),rt=x(),Xi=x(rt),hs=x(rt),$e=x(),yt=x($e),Ve=x(),Fe=x(),or=x(),oi=x(or),Qi=x(),m={comment:Yi,lineComment:x(Yi),blockComment:x(Yi),docComment:x(Yi),name:st,variableName:x(st),typeName:hl,tagName:x(hl),propertyName:cl,attributeName:x(cl),className:x(st),labelName:x(st),namespace:x(st),macroName:x(st),literal:rt,string:Xi,docString:x(Xi),character:x(Xi),attributeValue:x(Xi),number:hs,integer:x(hs),float:x(hs),bool:x(rt),regexp:x(rt),escape:x(rt),color:x(rt),url:x(rt),keyword:Ve,self:x(Ve),null:x(Ve),atom:x(Ve),unit:x(Ve),modifier:x(Ve),operatorKeyword:x(Ve),controlKeyword:x(Ve),definitionKeyword:x(Ve),moduleKeyword:x(Ve),operator:Fe,derefOperator:x(Fe),arithmeticOperator:x(Fe),logicOperator:x(Fe),bitwiseOperator:x(Fe),compareOperator:x(Fe),updateOperator:x(Fe),definitionOperator:x(Fe),typeOperator:x(Fe),controlOperator:x(Fe),punctuation:or,separator:x(or),bracket:oi,angleBracket:x(oi),squareBracket:x(oi),paren:x(oi),brace:x(oi),content:$e,heading:yt,heading1:x(yt),heading2:x(yt),heading3:x(yt),heading4:x(yt),heading5:x(yt),heading6:x(yt),contentSeparator:x($e),list:x($e),quote:x($e),emphasis:x($e),strong:x($e),link:x($e),monospace:x($e),strikethrough:x($e),inserted:x(),deleted:x(),changed:x(),invalid:x(),meta:Qi,documentMeta:x(Qi),annotation:x(Qi),processingInstruction:x(Qi),definition:qe.defineModifier(),constant:qe.defineModifier(),function:qe.defineModifier(),standard:qe.defineModifier(),local:qe.defineModifier(),special:qe.defineModifier()};Eh([{tag:m.link,class:"tok-link"},{tag:m.heading,class:"tok-heading"},{tag:m.emphasis,class:"tok-emphasis"},{tag:m.strong,class:"tok-strong"},{tag:m.keyword,class:"tok-keyword"},{tag:m.atom,class:"tok-atom"},{tag:m.bool,class:"tok-bool"},{tag:m.url,class:"tok-url"},{tag:m.labelName,class:"tok-labelName"},{tag:m.inserted,class:"tok-inserted"},{tag:m.deleted,class:"tok-deleted"},{tag:m.literal,class:"tok-literal"},{tag:m.string,class:"tok-string"},{tag:m.number,class:"tok-number"},{tag:[m.regexp,m.escape,m.special(m.string)],class:"tok-string2"},{tag:m.variableName,class:"tok-variableName"},{tag:m.local(m.variableName),class:"tok-variableName tok-local"},{tag:m.definition(m.variableName),class:"tok-variableName tok-definition"},{tag:m.special(m.variableName),class:"tok-variableName2"},{tag:m.definition(m.propertyName),class:"tok-propertyName tok-definition"},{tag:m.typeName,class:"tok-typeName"},{tag:m.namespace,class:"tok-namespace"},{tag:m.className,class:"tok-className"},{tag:m.macroName,class:"tok-macroName"},{tag:m.propertyName,class:"tok-propertyName"},{tag:m.operator,class:"tok-operator"},{tag:m.comment,class:"tok-comment"},{tag:m.meta,class:"tok-meta"},{tag:m.invalid,class:"tok-invalid"},{tag:m.punctuation,class:"tok-punctuation"}]);var cs;const vt=new _;function Ph(n){return T.define({combine:n?e=>e.concat(n):void 0})}const cp=new _;class Be{constructor(e,t,i=[],s=""){this.data=e,this.name=s,N.prototype.hasOwnProperty("tree")||Object.defineProperty(N.prototype,"tree",{get(){return le(this)}}),this.parser=t,this.extension=[mt.of(this),N.languageData.of((r,o,l)=>{let a=fl(r,o,l),h=a.type.prop(vt);if(!h)return[];let c=r.facet(h),f=a.type.prop(cp);if(f){let u=a.resolve(o-a.from,l);for(let d of f)if(d.test(u,r)){let p=r.facet(d.facet);return d.type=="replace"?p:p.concat(c)}}return c})].concat(i)}isActiveAt(e,t,i=-1){return fl(e,t,i).type.prop(vt)==this.data}findRegions(e){let t=e.facet(mt);if(t?.data==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],s=(r,o)=>{if(r.prop(vt)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(_.mounted);if(l){if(l.tree.prop(vt)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(s(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof W&&s(h,r.positions[a]+o)}};return s(le(e),0),i}get allowsNesting(){return!0}}Be.setState=R.define();function fl(n,e,t){let i=n.facet(mt),s=le(n).topNode;if(!i||i.allowsNesting)for(let r=s;r;r=r.enter(e,t,Y.ExcludeBuffers))r.type.isTop&&(s=r);return s}class lr extends Be{constructor(e,t,i){super(e,t,[],i),this.parser=t}static define(e){let t=Ph(e.languageData);return new lr(t,e.parser.configure({props:[vt.add(i=>i.isTop?t:void 0)]}),e.name)}configure(e,t){return new lr(this.data,this.parser.configure(e),t||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function le(n){let e=n.field(Be.state,!1);return e?e.tree:W.empty}class fp{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let li=null;class Qt{constructor(e,t,i=[],s,r,o,l,a){this.parser=e,this.state=t,this.fragments=i,this.tree=s,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new Qt(e,t,[],W.empty,0,i,[],null)}startParse(){return this.parser.startParse(new fp(this.state.doc),this.fragments)}work(e,t){return t!=null&&t>=this.state.doc.length&&(t=void 0),this.tree!=W.empty&&this.isDone(t??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof e=="number"){let s=Date.now()+e;e=()=>Date.now()>s}for(this.parse||(this.parse=this.startParse()),t!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let s=this.parse.advance();if(s)if(this.fragments=this.withoutTempSkipped(nt.addTree(s,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=s,this.parse=null,this.treeLen<(t??this.state.doc.length))this.parse=this.startParse();else return!0;if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(nt.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=li;li=this;try{return e()}finally{li=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=ul(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:s,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!e.empty){let a=[];if(e.iterChangedRanges((h,c,f,u)=>a.push({fromA:h,toA:c,fromB:f,toB:u})),i=nt.applyChanges(i,a),s=W.empty,r=0,o={from:e.mapPos(o.from,-1),to:e.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let c=e.mapPos(h.from,1),f=e.mapPos(h.to,-1);c<f&&l.push({from:c,to:f})}}}return new Qt(this.parser,t,i,s,r,o,l,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:s,to:r}=this.skipped[i];s<e.to&&r>e.from&&(this.fragments=ul(this.fragments,s,r),this.skipped.splice(i--,1))}return this.skipped.length>=t?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends Oh{createParse(t,i,s){let r=s[0].from,o=s[s.length-1].to;return{parsedPos:r,advance(){let a=li;if(a){for(let h of s)a.tempSkipped.push(h);e&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,e]):e)}return this.parsedPos=o,new W(we.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&t[0].from==0&&t[0].to>=e}static get(){return li}}function ul(n,e,t){return nt.applyChanges(n,[{fromA:e,toA:t,fromB:e,toB:t}])}class Zt{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new Zt(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=Qt.create(e.facet(mt).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new Zt(i)}}Be.state=xe.define({create:Zt.init,update(n,e){for(let t of e.effects)if(t.is(Be.setState))return t.value;return e.startState.facet(mt)!=e.state.facet(mt)?Zt.init(e.state):n.apply(e)}});let Lh=n=>{let e=setTimeout(()=>n(),500);return()=>clearTimeout(e)};typeof requestIdleCallback<"u"&&(Lh=n=>{let e=-1,t=setTimeout(()=>{e=requestIdleCallback(n,{timeout:400})},100);return()=>e<0?clearTimeout(t):cancelIdleCallback(e)});const fs=typeof navigator<"u"&&(!((cs=navigator.scheduling)===null||cs===void 0)&&cs.isInputPending)?()=>navigator.scheduling.isInputPending():null,up=pe.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(Be.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),e.docChanged&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(Be.state);(t.tree!=t.context.tree||!t.context.isDone(e.doc.length))&&(this.working=Lh(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:s}}=this.view,r=i.field(Be.state);if(r.tree==r.context.tree&&r.context.isDone(s+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,e&&!fs?Math.max(25,e.timeRemaining()-5):1e9),l=r.context.treeLen<s&&i.doc.length>s+1e3,a=r.context.work(()=>fs&&fs()||Date.now()>o,s+(l?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:Be.setState.of(new Zt(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(t=>_e(this.view.state,t)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),mt=T.define({combine(n){return n.length?n[0]:null},enables:n=>[Be.state,up,O.contentAttributes.compute([n],e=>{let t=e.facet(n);return t&&t.name?{"data-language":t.name}:{}})]});class hb{constructor(e,t=[]){this.language=e,this.support=t,this.extension=[e,t]}}class Rh{constructor(e,t,i,s,r,o=void 0){this.name=e,this.alias=t,this.extensions=i,this.filename=s,this.loadFunc=r,this.support=o,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then(e=>this.support=e,e=>{throw this.loading=null,e}))}static of(e){let{load:t,support:i}=e;if(!t){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");t=()=>Promise.resolve(i)}return new Rh(e.name,(e.alias||[]).concat(e.name).map(s=>s.toLowerCase()),e.extensions||[],e.filename,t,i)}static matchFilename(e,t){for(let s of e)if(s.filename&&s.filename.test(t))return s;let i=/\.([^.]+)$/.exec(t);if(i){for(let s of e)if(s.extensions.indexOf(i[1])>-1)return s}return null}static matchLanguageName(e,t,i=!0){t=t.toLowerCase();for(let s of e)if(s.alias.some(r=>r==t))return s;if(i)for(let s of e)for(let r of s.alias){let o=t.indexOf(r);if(o>-1&&(r.length>2||!/\w/.test(t[o-1])&&!/\w/.test(t[o+r.length])))return s}return null}}const _h=T.define(),Fn=T.define({combine:n=>{if(!n.length)return"  ";let e=n[0];if(!e||/\S/.test(e)||Array.from(e).some(t=>t!=e[0]))throw new Error("Invalid indent unit: "+JSON.stringify(n[0]));return e}});function Tt(n){let e=n.facet(Fn);return e.charCodeAt(0)==9?n.tabSize*e.length:e.length}function Oi(n,e){let t="",i=n.tabSize,s=n.facet(Fn)[0];if(s=="	"){for(;e>=i;)t+="	",e-=i;s=" "}for(let r=0;r<e;r++)t+=s;return t}function Pr(n,e){n instanceof N&&(n=new Hn(n));for(let i of n.state.facet(_h)){let s=i(n,e);if(s!==void 0)return s}let t=le(n.state);return t?pp(n,t,e):null}class Hn{constructor(e,t={}){this.state=e,this.options=t,this.unit=Tt(e)}lineAt(e,t=1){let i=this.state.doc.lineAt(e),{simulateBreak:s,simulateDoubleBreak:r}=this.options;return s!=null&&s>=i.from&&s<=i.to?r&&s==e?{text:"",from:e}:(t<0?s<e:s<=e)?{text:i.text.slice(s-i.from),from:s}:{text:i.text.slice(0,s-i.from),from:i.from}:i}textAfterPos(e,t=1){if(this.options.simulateDoubleBreak&&e==this.options.simulateBreak)return"";let{text:i,from:s}=this.lineAt(e,t);return i.slice(e-s,Math.min(i.length,e+100-s))}column(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.countColumn(i,e-s),o=this.options.overrideIndentation?this.options.overrideIndentation(s):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(e,t=e.length){return Ri(e,this.state.tabSize,t)}lineIndent(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.options.overrideIndentation;if(r){let o=r(s);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const dp=new _;function pp(n,e,t){return Ih(e.resolveInner(t).enterUnfinishedNodesBefore(t),t,n)}function mp(n){return n.pos==n.options.simulateBreak&&n.options.simulateDoubleBreak}function gp(n){let e=n.type.prop(dp);if(e)return e;let t=n.firstChild,i;if(t&&(i=t.type.prop(_.closedBy))){let s=n.lastChild,r=s&&i.indexOf(s.name)>-1;return o=>Nh(o,!0,1,void 0,r&&!mp(o)?s.from:void 0)}return n.parent==null?yp:null}function Ih(n,e,t){for(;n;n=n.parent){let i=gp(n);if(i)return i(Lr.create(t,e,n))}return null}function yp(){return 0}class Lr extends Hn{constructor(e,t,i){super(e.state,e.options),this.base=e,this.pos=t,this.node=i}static create(e,t,i){return new Lr(e,t,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){let e=this.state.doc.lineAt(this.node.from);for(;;){let t=this.node.resolve(e.from);for(;t.parent&&t.parent.from==t.from;)t=t.parent;if(bp(t,this.node))break;e=this.state.doc.lineAt(t.from)}return this.lineIndent(e.from)}continue(){let e=this.node.parent;return e?Ih(e,this.pos,this.base):0}}function bp(n,e){for(let t=e;t;t=t.parent)if(n==t)return!0;return!1}function wp(n){let e=n.node,t=e.childAfter(e.from),i=e.lastChild;if(!t)return null;let s=n.options.simulateBreak,r=n.state.doc.lineAt(t.from),o=s==null||s<=r.from?r.to:Math.min(r.to,s);for(let l=t.to;;){let a=e.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped)return a.from<o?t:null;l=a.to}}function cb({closing:n,align:e=!0,units:t=1}){return i=>Nh(i,e,t,n)}function Nh(n,e,t,i,s){let r=n.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||s==n.pos+o,a=e?wp(n):null;return a?l?n.column(a.from):n.column(a.to):n.baseIndent+(l?0:n.unit*t)}const fb=n=>n.baseIndent;function ub({except:n,units:e=1}={}){return t=>{let i=n&&n.test(t.textAfter);return t.baseIndent+(i?0:e*t.unit)}}const kp=200;function xp(){return N.transactionFilter.of(n=>{if(!n.docChanged||!n.isUserEvent("input.type")&&!n.isUserEvent("input.complete"))return n;let e=n.startState.languageDataAt("indentOnInput",n.startState.selection.main.head);if(!e.length)return n;let t=n.newDoc,{head:i}=n.newSelection.main,s=t.lineAt(i);if(i>s.from+kp)return n;let r=t.sliceString(s.from,i);if(!e.some(h=>h.test(r)))return n;let{state:o}=n,l=-1,a=[];for(let{head:h}of o.selection.ranges){let c=o.doc.lineAt(h);if(c.from==l)continue;l=c.from;let f=Pr(o,c.from);if(f==null)continue;let u=/^\s*/.exec(c.text)[0],d=Oi(o,f);u!=d&&a.push({from:c.from,to:c.from+u.length,insert:d})}return a.length?[n,{changes:a,sequential:!0}]:n})}const vp=T.define(),Sp=new _;function db(n){let e=n.firstChild,t=n.lastChild;return e&&e.to<t.from?{from:e.to,to:t.type.isError?n.to:t.from}:null}function Cp(n,e,t){let i=le(n);if(i.length<t)return null;let s=i.resolveInner(t,1),r=null;for(let o=s;o;o=o.parent){if(o.to<=t||o.from>t)continue;if(r&&o.from<e)break;let l=o.type.prop(Sp);if(l&&(o.to<i.length-50||i.length==n.doc.length||!Ap(o))){let a=l(o,n);a&&a.from<=t&&a.from>=e&&a.to>t&&(r=a)}}return r}function Ap(n){let e=n.lastChild;return e&&e.to==n.to&&e.type.isError}function Mn(n,e,t){for(let i of n.facet(vp)){let s=i(n,e,t);if(s)return s}return Cp(n,e,t)}function Vh(n,e){let t=e.mapPos(n.from,1),i=e.mapPos(n.to,-1);return t>=i?void 0:{from:t,to:i}}const $n=R.define({map:Vh}),Ii=R.define({map:Vh});function Fh(n){let e=[];for(let{head:t}of n.state.selection.ranges)e.some(i=>i.from<=t&&i.to>=t)||e.push(n.lineBlockAt(t));return e}const Ot=xe.define({create(){return L.none},update(n,e){n=n.map(e.changes);for(let t of e.effects)t.is($n)&&!Mp(n,t.value.from,t.value.to)?n=n.update({add:[dl.range(t.value.from,t.value.to)]}):t.is(Ii)&&(n=n.update({filter:(i,s)=>t.value.from!=i||t.value.to!=s,filterFrom:t.value.from,filterTo:t.value.to}));if(e.selection){let t=!1,{head:i}=e.selection.main;n.between(i,i,(s,r)=>{s<i&&r>i&&(t=!0)}),t&&(n=n.update({filterFrom:i,filterTo:i,filter:(s,r)=>r<=i||s>=i}))}return n},provide:n=>O.decorations.from(n),toJSON(n,e){let t=[];return n.between(0,e.doc.length,(i,s)=>{t.push(i,s)}),t},fromJSON(n){if(!Array.isArray(n)||n.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let t=0;t<n.length;){let i=n[t++],s=n[t++];if(typeof i!="number"||typeof s!="number")throw new RangeError("Invalid JSON for fold state");e.push(dl.range(i,s))}return L.set(e,!0)}});function Dn(n,e,t){var i;let s=null;return(i=n.field(Ot,!1))===null||i===void 0||i.between(e,t,(r,o)=>{(!s||s.from>r)&&(s={from:r,to:o})}),s}function Mp(n,e,t){let i=!1;return n.between(e,e,(s,r)=>{s==e&&r==t&&(i=!0)}),i}function Hh(n,e){return n.field(Ot,!1)?e:e.concat(R.appendConfig.of(Wh()))}const Dp=n=>{for(let e of Fh(n)){let t=Mn(n.state,e.from,e.to);if(t)return n.dispatch({effects:Hh(n.state,[$n.of(t),$h(n,t)])}),!0}return!1},Tp=n=>{if(!n.state.field(Ot,!1))return!1;let e=[];for(let t of Fh(n)){let i=Dn(n.state,t.from,t.to);i&&e.push(Ii.of(i),$h(n,i,!1))}return e.length&&n.dispatch({effects:e}),e.length>0};function $h(n,e,t=!0){let i=n.state.doc.lineAt(e.from).number,s=n.state.doc.lineAt(e.to).number;return O.announce.of(`${n.state.phrase(t?"Folded lines":"Unfolded lines")} ${i} ${n.state.phrase("to")} ${s}.`)}const Op=n=>{let{state:e}=n,t=[];for(let i=0;i<e.doc.length;){let s=n.lineBlockAt(i),r=Mn(e,s.from,s.to);r&&t.push($n.of(r)),i=(r?n.lineBlockAt(r.to):s).to+1}return t.length&&n.dispatch({effects:Hh(n.state,t)}),!!t.length},Bp=n=>{let e=n.state.field(Ot,!1);if(!e||!e.size)return!1;let t=[];return e.between(0,n.state.doc.length,(i,s)=>{t.push(Ii.of({from:i,to:s}))}),n.dispatch({effects:t}),!0},Ep=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:Dp},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:Tp},{key:"Ctrl-Alt-[",run:Op},{key:"Ctrl-Alt-]",run:Bp}],Pp={placeholderDOM:null,placeholderText:"…"},Lp=T.define({combine(n){return _t(n,Pp)}});function Wh(n){return[Ot,Ip]}const dl=L.replace({widget:new class extends Qe{toDOM(n){let{state:e}=n,t=e.facet(Lp),i=r=>{let o=n.lineBlockAt(n.posAtDOM(r.target)),l=Dn(n.state,o.from,o.to);l&&n.dispatch({effects:Ii.of(l)}),r.preventDefault()};if(t.placeholderDOM)return t.placeholderDOM(n,i);let s=document.createElement("span");return s.textContent=t.placeholderText,s.setAttribute("aria-label",e.phrase("folded code")),s.title=e.phrase("unfold"),s.className="cm-foldPlaceholder",s.onclick=i,s}}}),Rp={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class us extends pt{constructor(e,t){super(),this.config=e,this.open=t}eq(e){return this.config==e.config&&this.open==e.open}toDOM(e){if(this.config.markerDOM)return this.config.markerDOM(this.open);let t=document.createElement("span");return t.textContent=this.open?this.config.openText:this.config.closedText,t.title=e.state.phrase(this.open?"Fold line":"Unfold line"),t}}function _p(n={}){let e=Object.assign(Object.assign({},Rp),n),t=new us(e,!0),i=new us(e,!1),s=pe.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o)}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(mt)!=o.state.facet(mt)||o.startState.field(Ot,!1)!=o.state.field(Ot,!1)||le(o.startState)!=le(o.state)||e.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view))}buildMarkers(o){let l=new Mt;for(let a of o.viewportLineBlocks){let h=Dn(o.state,a.from,a.to)?i:Mn(o.state,a.from,a.to)?t:null;h&&l.add(a.from,a.from,h)}return l.finish()}}),{domEventHandlers:r}=e;return[s,_d({class:"cm-foldGutter",markers(o){var l;return((l=o.plugin(s))===null||l===void 0?void 0:l.markers)||H.empty},initialSpacer(){return new us(e,!1)},domEventHandlers:Object.assign(Object.assign({},r),{click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return!0;let h=Dn(o.state,l.from,l.to);if(h)return o.dispatch({effects:Ii.of(h)}),!0;let c=Mn(o.state,l.from,l.to);return c?(o.dispatch({effects:$n.of(c)}),!0):!1}})}),Wh()]}const Ip=O.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class ti{constructor(e,t){this.specs=e;let i;function s(l){let a=ft.newName();return(i||(i=Object.create(null)))["."+a]=l,a}const r=typeof t.all=="string"?t.all:t.all?s(t.all):void 0,o=t.scope;this.scope=o instanceof Be?l=>l.prop(vt)==o.data:o?l=>l==o:void 0,this.style=Eh(e.map(l=>({tag:l.tag,class:l.class||s(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new ft(i):null,this.themeType=t.themeType}static define(e,t){return new ti(e,t||{})}}const ar=T.define(),zh=T.define({combine(n){return n.length?[n[0]]:null}});function ds(n){let e=n.facet(ar);return e.length?e:n.facet(zh)}function Rr(n,e){let t=[Vp],i;return n instanceof ti&&(n.module&&t.push(O.styleModule.of(n.module)),i=n.themeType),e?.fallback?t.push(zh.of(n)):i?t.push(ar.computeN([O.darkTheme],s=>s.facet(O.darkTheme)==(i=="dark")?[n]:[])):t.push(ar.of(n)),t}class Np{constructor(e){this.markCache=Object.create(null),this.tree=le(e.state),this.decorations=this.buildDeco(e,ds(e.state))}update(e){let t=le(e.state),i=ds(e.state),s=i!=ds(e.startState);t.length<e.view.viewport.to&&!s&&t.type==this.tree.type?this.decorations=this.decorations.map(e.changes):(t!=this.tree||e.viewportChanged||s)&&(this.tree=t,this.decorations=this.buildDeco(e.view,i))}buildDeco(e,t){if(!t||!this.tree.length)return L.none;let i=new Mt;for(let{from:s,to:r}of e.visibleRanges)lp(this.tree,t,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=L.mark({class:a})))},s,r);return i.finish()}}const Vp=Li.high(pe.fromClass(Np,{decorations:n=>n.decorations})),Fp=ti.define([{tag:m.meta,color:"#404740"},{tag:m.link,textDecoration:"underline"},{tag:m.heading,textDecoration:"underline",fontWeight:"bold"},{tag:m.emphasis,fontStyle:"italic"},{tag:m.strong,fontWeight:"bold"},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.keyword,color:"#708"},{tag:[m.atom,m.bool,m.url,m.contentSeparator,m.labelName],color:"#219"},{tag:[m.literal,m.inserted],color:"#164"},{tag:[m.string,m.deleted],color:"#a11"},{tag:[m.regexp,m.escape,m.special(m.string)],color:"#e40"},{tag:m.definition(m.variableName),color:"#00f"},{tag:m.local(m.variableName),color:"#30a"},{tag:[m.typeName,m.namespace],color:"#085"},{tag:m.className,color:"#167"},{tag:[m.special(m.variableName),m.macroName],color:"#256"},{tag:m.definition(m.propertyName),color:"#00c"},{tag:m.comment,color:"#940"},{tag:m.invalid,color:"#f00"}]),Hp=1e4,$p="()[]{}",Wp=new _;function hr(n,e,t){let i=n.prop(e<0?_.openedBy:_.closedBy);if(i)return i;if(n.name.length==1){let s=t.indexOf(n.name);if(s>-1&&s%2==(e<0?1:0))return[t[s+e]]}return null}function cr(n){let e=n.type.prop(Wp);return e?e(n.node):n}function $t(n,e,t,i={}){let s=i.maxScanDistance||Hp,r=i.brackets||$p,o=le(n),l=o.resolveInner(e,t);for(let a=l;a;a=a.parent){let h=hr(a.type,t,r);if(h&&a.from<a.to){let c=cr(a);if(c&&(t>0?e>=c.from&&e<c.to:e>c.from&&e<=c.to))return zp(n,e,t,a,c,h,r)}}return qp(n,e,t,o,l.type,s,r)}function zp(n,e,t,i,s,r,o){let l=i.parent,a={from:s.from,to:s.to},h=0,c=l?.cursor();if(c&&(t<0?c.childBefore(i.from):c.childAfter(i.to)))do if(t<0?c.to<=i.from:c.from>=i.to){if(h==0&&r.indexOf(c.type.name)>-1&&c.from<c.to){let f=cr(c);return{start:a,end:f?{from:f.from,to:f.to}:void 0,matched:!0}}else if(hr(c.type,t,o))h++;else if(hr(c.type,-t,o)){if(h==0){let f=cr(c);return{start:a,end:f&&f.from<f.to?{from:f.from,to:f.to}:void 0,matched:!1}}h--}}while(t<0?c.prevSibling():c.nextSibling());return{start:a,matched:!1}}function qp(n,e,t,i,s,r,o){let l=t<0?n.sliceDoc(e-1,e):n.sliceDoc(e,e+1),a=o.indexOf(l);if(a<0||a%2==0!=t>0)return null;let h={from:t<0?e-1:e,to:t>0?e+1:e},c=n.doc.iterRange(e,t>0?n.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let d=c.value;t<0&&(u+=d.length);let p=e+u*t;for(let g=t>0?0:d.length-1,y=t>0?d.length:-1;g!=y;g+=t){let b=o.indexOf(d[g]);if(!(b<0||i.resolveInner(p+g,1).type!=s))if(b%2==0==t>0)f++;else{if(f==1)return{start:h,end:{from:p+g,to:p+g+1},matched:b>>1==a>>1};f--}}t>0&&(u+=d.length)}return c.done?{start:h,matched:!1}:null}function pl(n,e,t,i=0,s=0){e==null&&(e=n.search(/[^\s\u00a0]/),e==-1&&(e=n.length));let r=s;for(let o=i;o<e;o++)n.charCodeAt(o)==9?r+=t-r%t:r++;return r}class qh{constructor(e,t,i,s){this.string=e,this.tabSize=t,this.indentUnit=i,this.overrideIndent=s,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return this.pos==0}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(e){let t=this.string.charAt(this.pos),i;if(typeof e=="string"?i=t==e:i=t&&(e instanceof RegExp?e.test(t):e(t)),i)return++this.pos,t}eatWhile(e){let t=this.pos;for(;this.eat(e););return this.pos>t}eatSpace(){let e=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e}skipToEnd(){this.pos=this.string.length}skipTo(e){let t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0}backUp(e){this.pos-=e}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=pl(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var e;return(e=this.overrideIndent)!==null&&e!==void 0?e:pl(this.string,null,this.tabSize)}match(e,t,i){if(typeof e=="string"){let s=o=>i?o.toLowerCase():o,r=this.string.substr(this.pos,e.length);return s(r)==s(e)?(t!==!1&&(this.pos+=e.length),!0):null}else{let s=this.string.slice(this.pos).match(e);return s&&s.index>0?null:(s&&t!==!1&&(this.pos+=s[0].length),s)}}current(){return this.string.slice(this.start,this.pos)}}function jp(n){return{name:n.name||"",token:n.token,blankLine:n.blankLine||(()=>{}),startState:n.startState||(()=>!0),copyState:n.copyState||Kp,indent:n.indent||(()=>null),languageData:n.languageData||{},tokenTable:n.tokenTable||Ir}}function Kp(n){if(typeof n!="object")return n;let e={};for(let t in n){let i=n[t];e[t]=i instanceof Array?i.slice():i}return e}const ml=new WeakMap;class We extends Be{constructor(e){let t=Ph(e.languageData),i=jp(e),s,r=new class extends Oh{createParse(o,l,a){return new Gp(s,o,l,a)}};super(t,r,[_h.of((o,l)=>this.getIndent(o,l))],e.name),this.topNode=Xp(t),s=this,this.streamParser=i,this.stateAfter=new _({perNode:!0}),this.tokenTable=e.tokenTable?new Gh(i.tokenTable):Yp}static define(e){return new We(e)}getIndent(e,t){let i=le(e.state),s=i.resolve(t);for(;s&&s.type!=this.topNode;)s=s.parent;if(!s)return null;let r,{overrideIndentation:o}=e.options;o&&(r=ml.get(e.state),r!=null&&r<t-1e4&&(r=void 0));let l=_r(this,i,0,s.from,r??t),a,h;if(l?(h=l.state,a=l.pos+1):(h=this.streamParser.startState(e.unit),a=0),t-a>1e4)return null;for(;a<t;){let f=e.state.doc.lineAt(a),u=Math.min(t,f.to);if(f.length){let d=o?o(f.from):-1,p=new qh(f.text,e.state.tabSize,e.unit,d<0?void 0:d);for(;p.pos<u-f.from;)Kh(this.streamParser.token,p,h)}else this.streamParser.blankLine(h,e.unit);if(u==t)break;a=f.to+1}let c=e.lineAt(t);return o&&r==null&&ml.set(e.state,c.from),this.streamParser.indent(h,/^\s*(.*)/.exec(c.text)[1],e)}get allowsNesting(){return!1}}function _r(n,e,t,i,s){let r=t>=i&&t+e.length<=s&&e.prop(n.stateAfter);if(r)return{state:n.streamParser.copyState(r),pos:t+e.length};for(let o=e.children.length-1;o>=0;o--){let l=e.children[o],a=t+e.positions[o],h=l instanceof W&&a<s&&_r(n,l,a,i,s);if(h)return h}return null}function jh(n,e,t,i,s){if(s&&t<=0&&i>=e.length)return e;!s&&e.type==n.topNode&&(s=!0);for(let r=e.children.length-1;r>=0;r--){let o=e.positions[r],l=e.children[r],a;if(o<i&&l instanceof W){if(!(a=jh(n,l,t-o,i-o,s)))break;return s?new W(e.type,e.children.slice(0,r).concat(a),e.positions.slice(0,r+1),o+a.length):a}}return null}function Up(n,e,t,i){for(let s of e){let r=s.from+(s.openStart?25:0),o=s.to-(s.openEnd?25:0),l=r<=t&&o>t&&_r(n,s.tree,0-s.offset,t,o),a;if(l&&(a=jh(n,s.tree,t+s.offset,l.pos+s.offset,!1)))return{state:l.state,tree:a}}return{state:n.streamParser.startState(i?Tt(i):4),tree:W.empty}}class Gp{constructor(e,t,i,s){this.lang=e,this.input=t,this.fragments=i,this.ranges=s,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=s[s.length-1].to;let r=Qt.get(),o=s[0].from,{state:l,tree:a}=Up(e,i,o,r?.state);this.state=l,this.parsedPos=this.chunkStart=o+a.length;for(let h=0;h<a.children.length;h++)this.chunks.push(a.children[h]),this.chunkPos.push(a.positions[h]);r&&this.parsedPos<r.viewport.from-1e5&&(this.state=this.lang.streamParser.startState(Tt(r.state)),r.skipUntilInView(this.parsedPos,r.viewport.from),this.parsedPos=r.viewport.from),this.moveRangeIndex()}advance(){let e=Qt.get(),t=this.stoppedAt==null?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(t,this.chunkStart+2048);for(e&&(i=Math.min(i,e.viewport.to));this.parsedPos<i;)this.parseLine(e);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=t?this.finish():e&&this.parsedPos>=e.viewport.to?(e.skipUntilInView(this.parsedPos,t),this.finish()):null}stopAt(e){this.stoppedAt=e}lineAfter(e){let t=this.input.chunk(e);if(this.input.lineChunks)t==`
`&&(t="");else{let i=t.indexOf(`
`);i>-1&&(t=t.slice(0,i))}return e+t.length<=this.to?t:t.slice(0,this.to-e)}nextLine(){let e=this.parsedPos,t=this.lineAfter(e),i=e+t.length;for(let s=this.rangeIndex;;){let r=this.ranges[s].to;if(r>=i||(t=t.slice(0,r-(i-t.length)),s++,s==this.ranges.length))break;let o=this.ranges[s].from,l=this.lineAfter(o);t+=l,i=o+l.length}return{line:t,end:i}}skipGapsTo(e,t,i){for(;;){let s=this.ranges[this.rangeIndex].to,r=e+t;if(i>0?s>r:s>=r)break;let o=this.ranges[++this.rangeIndex].from;t+=o-s}return t}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++}emitToken(e,t,i,s,r){if(this.ranges.length>1){r=this.skipGapsTo(t,r,1),t+=r;let o=this.chunk.length;r=this.skipGapsTo(i,r,-1),i+=r,s+=this.chunk.length-o}return this.chunk.push(e,t,i,s),r}parseLine(e){let{line:t,end:i}=this.nextLine(),s=0,{streamParser:r}=this.lang,o=new qh(t,e?e.state.tabSize:4,e?Tt(e.state):2);if(o.eol())r.blankLine(this.state,o.indentUnit);else for(;!o.eol();){let l=Kh(r.token,o,this.state);if(l&&(s=this.emitToken(this.lang.tokenTable.resolve(l),this.parsedPos+o.start,this.parsedPos+o.pos,4,s)),o.start>1e4)break}this.parsedPos=i,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let e=W.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:Jp,topID:0,maxBufferLength:2048,reused:this.chunkReused});e=new W(e.type,e.children,e.positions,e.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(e),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new W(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function Kh(n,e,t){e.start=e.pos;for(let i=0;i<10;i++){let s=n(e,t);if(e.pos>e.start)return s}throw new Error("Stream parser failed to advance stream.")}const Ir=Object.create(null),Bi=[we.none],Jp=new Tr(Bi),gl=[],Uh=Object.create(null);for(let[n,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])Uh[n]=Jh(Ir,e);class Gh{constructor(e){this.extra=e,this.table=Object.assign(Object.create(null),Uh)}resolve(e){return e?this.table[e]||(this.table[e]=Jh(this.extra,e)):0}}const Yp=new Gh(Ir);function ps(n,e){gl.indexOf(n)>-1||(gl.push(n),console.warn(e))}function Jh(n,e){let t=null;for(let r of e.split(".")){let o=n[r]||m[r];o?typeof o=="function"?t?t=o(t):ps(r,`Modifier ${r} used at start of tag`):t?ps(r,`Tag ${r} used as modifier`):t=o:ps(r,`Unknown highlighting tag ${r}`)}if(!t)return 0;let i=e.replace(/ /g,"_"),s=we.define({id:Bi.length,name:i,props:[rp({[i]:t})]});return Bi.push(s),s.id}function Xp(n){let e=we.define({id:Bi.length,name:"Document",props:[vt.add(()=>n)]});return Bi.push(e),e}const Qp=n=>{let e=Vr(n.state);return e.line?Zp(n):e.block?tm(n):!1};function Nr(n,e){return({state:t,dispatch:i})=>{if(t.readOnly)return!1;let s=n(e,t);return s?(i(t.update(s)),!0):!1}}const Zp=Nr(sm,0),em=Nr(Yh,0),tm=Nr((n,e)=>Yh(n,e,nm(e)),0);function Vr(n,e=n.selection.main.head){let t=n.languageDataAt("commentTokens",e);return t.length?t[0]:{}}const ai=50;function im(n,{open:e,close:t},i,s){let r=n.sliceDoc(i-ai,i),o=n.sliceDoc(s,s+ai),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-e.length,h)==e&&o.slice(a,a+t.length)==t)return{open:{pos:i-l,margin:l&&1},close:{pos:s+a,margin:a&&1}};let c,f;s-i<=2*ai?c=f=n.sliceDoc(i,s):(c=n.sliceDoc(i,i+ai),f=n.sliceDoc(s-ai,s));let u=/^\s*/.exec(c)[0].length,d=/\s*$/.exec(f)[0].length,p=f.length-d-t.length;return c.slice(u,u+e.length)==e&&f.slice(p,p+t.length)==t?{open:{pos:i+u+e.length,margin:/\s/.test(c.charAt(u+e.length))?1:0},close:{pos:s-d-t.length,margin:/\s/.test(f.charAt(p-1))?1:0}}:null}function nm(n){let e=[];for(let t of n.selection.ranges){let i=n.doc.lineAt(t.from),s=t.to<=i.to?i:n.doc.lineAt(t.to),r=e.length-1;r>=0&&e[r].to>i.from?e[r].to=s.to:e.push({from:i.from,to:s.to})}return e}function Yh(n,e,t=e.selection.ranges){let i=t.map(r=>Vr(e,r.from).block);if(!i.every(r=>r))return null;let s=t.map((r,o)=>im(e,i[o],r.from,r.to));if(n!=2&&!s.every(r=>r))return{changes:e.changes(t.map((r,o)=>s[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(n!=1&&s.some(r=>r)){let r=[];for(let o=0,l;o<s.length;o++)if(l=s[o]){let a=i[o],{open:h,close:c}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:c.pos-c.margin,to:c.pos+a.close.length})}return{changes:r}}return null}function sm(n,e,t=e.selection.ranges){let i=[],s=-1;for(let{from:r,to:o}of t){let l=i.length,a=1e9;for(let h=r;h<=o;){let c=e.doc.lineAt(h);if(c.from>s&&(r==o||o>c.from)){s=c.from;let f=Vr(e,h).line;if(!f)continue;let u=/^\s*/.exec(c.text)[0].length,d=u==c.length,p=c.text.slice(u,u+f.length)==f?u:-1;u<c.text.length&&u<a&&(a=u),i.push({line:c,comment:p,token:f,indent:u,empty:d,single:!1})}h=c.to+1}if(a<1e9)for(let h=l;h<i.length;h++)i[h].indent<i[h].line.text.length&&(i[h].indent=a);i.length==l+1&&(i[l].single=!0)}if(n!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:c,single:f}of i)(f||!c)&&r.push({from:l.from+h,insert:a+" "});let o=e.changes(r);return{changes:o,selection:e.selection.map(o,1)}}else if(n!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,c=h+a.length;o.text[c-o.from]==" "&&c++,r.push({from:h,to:c})}return{changes:r}}return null}const fr=Rt.define(),rm=Rt.define(),om=T.define(),Xh=T.define({combine(n){return _t(n,{minDepth:100,newGroupDelay:500},{minDepth:Math.max,newGroupDelay:Math.min})}});function lm(n){let e=0;return n.iterChangedRanges((t,i)=>e=i),e}const Qh=xe.define({create(){return Ge.empty},update(n,e){let t=e.state.facet(Xh),i=e.annotation(fr);if(i){let a=e.docChanged?w.single(lm(e.changes)):void 0,h=ke.fromTransaction(e,a),c=i.side,f=c==0?n.undone:n.done;return h?f=Tn(f,f.length,t.minDepth,h):f=tc(f,e.startState.selection),new Ge(c==0?i.rest:f,c==0?f:i.rest)}let s=e.annotation(rm);if((s=="full"||s=="before")&&(n=n.isolate()),e.annotation(ee.addToHistory)===!1)return e.changes.empty?n:n.addMapping(e.changes.desc);let r=ke.fromTransaction(e),o=e.annotation(ee.time),l=e.annotation(ee.userEvent);return r?n=n.addChanges(r,o,l,t.newGroupDelay,t.minDepth):e.selection&&(n=n.addSelection(e.startState.selection,o,l,t.newGroupDelay)),(s=="full"||s=="after")&&(n=n.isolate()),n},toJSON(n){return{done:n.done.map(e=>e.toJSON()),undone:n.undone.map(e=>e.toJSON())}},fromJSON(n){return new Ge(n.done.map(ke.fromJSON),n.undone.map(ke.fromJSON))}});function am(n={}){return[Qh,Xh.of(n),O.domEventHandlers({beforeinput(e,t){let i=e.inputType=="historyUndo"?Zh:e.inputType=="historyRedo"?ur:null;return i?(e.preventDefault(),i(t)):!1}})]}function Wn(n,e){return function({state:t,dispatch:i}){if(!e&&t.readOnly)return!1;let s=t.field(Qh,!1);if(!s)return!1;let r=s.pop(n,t,e);return r?(i(r),!0):!1}}const Zh=Wn(0,!1),ur=Wn(1,!1),hm=Wn(0,!0),cm=Wn(1,!0);class ke{constructor(e,t,i,s,r){this.changes=e,this.effects=t,this.mapped=i,this.startSelection=s,this.selectionsAfter=r}setSelAfter(e){return new ke(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,i;return{changes:(e=this.changes)===null||e===void 0?void 0:e.toJSON(),mapped:(t=this.mapped)===null||t===void 0?void 0:t.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(s=>s.toJSON())}}static fromJSON(e){return new ke(e.changes&&Z.fromJSON(e.changes),[],e.mapped&&Je.fromJSON(e.mapped),e.startSelection&&w.fromJSON(e.startSelection),e.selectionsAfter.map(w.fromJSON))}static fromTransaction(e,t){let i=Ee;for(let s of e.startState.facet(om)){let r=s(e);r.length&&(i=i.concat(r))}return!i.length&&e.changes.empty?null:new ke(e.changes.invert(e.startState.doc),i,void 0,t||e.startState.selection,Ee)}static selection(e){return new ke(void 0,Ee,void 0,void 0,e)}}function Tn(n,e,t,i){let s=e+1>t+20?e-t-1:0,r=n.slice(s,e);return r.push(i),r}function fm(n,e){let t=[],i=!1;return n.iterChangedRanges((s,r)=>t.push(s,r)),e.iterChangedRanges((s,r,o,l)=>{for(let a=0;a<t.length;){let h=t[a++],c=t[a++];l>=h&&o<=c&&(i=!0)}}),i}function um(n,e){return n.ranges.length==e.ranges.length&&n.ranges.filter((t,i)=>t.empty!=e.ranges[i].empty).length===0}function ec(n,e){return n.length?e.length?n.concat(e):n:e}const Ee=[],dm=200;function tc(n,e){if(n.length){let t=n[n.length-1],i=t.selectionsAfter.slice(Math.max(0,t.selectionsAfter.length-dm));return i.length&&i[i.length-1].eq(e)?n:(i.push(e),Tn(n,n.length-1,1e9,t.setSelAfter(i)))}else return[ke.selection([e])]}function pm(n){let e=n[n.length-1],t=n.slice();return t[n.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),t}function ms(n,e){if(!n.length)return n;let t=n.length,i=Ee;for(;t;){let s=mm(n[t-1],e,i);if(s.changes&&!s.changes.empty||s.effects.length){let r=n.slice(0,t);return r[t-1]=s,r}else e=s.mapped,t--,i=s.selectionsAfter}return i.length?[ke.selection(i)]:Ee}function mm(n,e,t){let i=ec(n.selectionsAfter.length?n.selectionsAfter.map(l=>l.map(e)):Ee,t);if(!n.changes)return ke.selection(i);let s=n.changes.map(e),r=e.mapDesc(n.changes,!0),o=n.mapped?n.mapped.composeDesc(r):r;return new ke(s,R.mapEffects(n.effects,e),o,n.startSelection.map(r),i)}const gm=/^(input\.type|delete)($|\.)/;class Ge{constructor(e,t,i=0,s=void 0){this.done=e,this.undone=t,this.prevTime=i,this.prevUserEvent=s}isolate(){return this.prevTime?new Ge(this.done,this.undone):this}addChanges(e,t,i,s,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&e.changes&&(!i||gm.test(i))&&(!l.selectionsAfter.length&&t-this.prevTime<s&&fm(l.changes,e.changes)||i=="input.type.compose")?o=Tn(o,o.length-1,r,new ke(e.changes.compose(l.changes),ec(e.effects,l.effects),l.mapped,l.startSelection,Ee)):o=Tn(o,o.length,r,e),new Ge(o,Ee,t,i)}addSelection(e,t,i,s){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Ee;return r.length>0&&t-this.prevTime<s&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&um(r[r.length-1],e)?this:new Ge(tc(this.done,e),this.undone,t,i)}addMapping(e){return new Ge(ms(this.done,e),ms(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,i){let s=e==0?this.done:this.undone;if(s.length==0)return null;let r=s[s.length-1];if(i&&r.selectionsAfter.length)return t.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:fr.of({side:e,rest:pm(s)}),userEvent:e==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let o=s.length==1?Ee:s.slice(0,s.length-1);return r.mapped&&(o=ms(o,r.mapped)),t.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:fr.of({side:e,rest:o}),filter:!1,userEvent:e==0?"undo":"redo",scrollIntoView:!0})}else return null}}Ge.empty=new Ge(Ee,Ee);const ym=[{key:"Mod-z",run:Zh,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:ur,preventDefault:!0},{linux:"Ctrl-Shift-z",run:ur,preventDefault:!0},{key:"Mod-u",run:hm,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:cm,preventDefault:!0}];function ii(n,e){return w.create(n.ranges.map(e),n.mainIndex)}function Ze(n,e){return n.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function Ie({state:n,dispatch:e},t){let i=ii(n.selection,t);return i.eq(n.selection)?!1:(e(Ze(n,i)),!0)}function zn(n,e){return w.cursor(e?n.to:n.from)}function ic(n,e){return Ie(n,t=>t.empty?n.moveByChar(t,e):zn(t,e))}function me(n){return n.textDirectionAt(n.state.selection.main.head)==G.LTR}const nc=n=>ic(n,!me(n)),sc=n=>ic(n,me(n));function rc(n,e){return Ie(n,t=>t.empty?n.moveByGroup(t,e):zn(t,e))}const bm=n=>rc(n,!me(n)),wm=n=>rc(n,me(n));function km(n,e,t){if(e.type.prop(t))return!0;let i=e.to-e.from;return i&&(i>2||/[^\s,.;:]/.test(n.sliceDoc(e.from,e.to)))||e.firstChild}function qn(n,e,t){let i=le(n).resolveInner(e.head),s=t?_.closedBy:_.openedBy;for(let a=e.head;;){let h=t?i.childAfter(a):i.childBefore(a);if(!h)break;km(n,h,s)?i=h:a=t?h.to:h.from}let r=i.type.prop(s),o,l;return r&&(o=t?$t(n,i.from,1):$t(n,i.to,-1))&&o.matched?l=t?o.end.to:o.end.from:l=t?i.to:i.from,w.cursor(l,t?-1:1)}const xm=n=>Ie(n,e=>qn(n.state,e,!me(n))),vm=n=>Ie(n,e=>qn(n.state,e,me(n)));function oc(n,e){return Ie(n,t=>{if(!t.empty)return zn(t,e);let i=n.moveVertically(t,e);return i.head!=t.head?i:n.moveToLineBoundary(t,e)})}const lc=n=>oc(n,!1),ac=n=>oc(n,!0);function hc(n){return Math.max(n.defaultLineHeight,Math.min(n.dom.clientHeight,innerHeight)-5)}function cc(n,e){let{state:t}=n,i=ii(t.selection,l=>l.empty?n.moveVertically(l,e,hc(n)):zn(l,e));if(i.eq(t.selection))return!1;let s=n.coordsAtPos(t.selection.main.head),r=n.scrollDOM.getBoundingClientRect(),o;return s&&s.top>r.top&&s.bottom<r.bottom&&s.top-r.top<=n.scrollDOM.scrollHeight-n.scrollDOM.scrollTop-n.scrollDOM.clientHeight&&(o=O.scrollIntoView(i.main.head,{y:"start",yMargin:s.top-r.top})),n.dispatch(Ze(t,i),{effects:o}),!0}const yl=n=>cc(n,!1),dr=n=>cc(n,!0);function gt(n,e,t){let i=n.lineBlockAt(e.head),s=n.moveToLineBoundary(e,t);if(s.head==e.head&&s.head!=(t?i.to:i.from)&&(s=n.moveToLineBoundary(e,t,!1)),!t&&s.head==i.from&&i.length){let r=/^\s*/.exec(n.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&e.head!=i.from+r&&(s=w.cursor(i.from+r))}return s}const Sm=n=>Ie(n,e=>gt(n,e,!0)),Cm=n=>Ie(n,e=>gt(n,e,!1)),Am=n=>Ie(n,e=>gt(n,e,!me(n))),Mm=n=>Ie(n,e=>gt(n,e,me(n))),Dm=n=>Ie(n,e=>w.cursor(n.lineBlockAt(e.head).from,1)),Tm=n=>Ie(n,e=>w.cursor(n.lineBlockAt(e.head).to,-1));function Om(n,e,t){let i=!1,s=ii(n.selection,r=>{let o=$t(n,r.head,-1)||$t(n,r.head,1)||r.head>0&&$t(n,r.head-1,1)||r.head<n.doc.length&&$t(n,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return w.cursor(l)});return i?(e(Ze(n,s)),!0):!1}const Bm=({state:n,dispatch:e})=>Om(n,e);function Le(n,e){let t=ii(n.state.selection,i=>{let s=e(i);return w.range(i.anchor,s.head,s.goalColumn)});return t.eq(n.state.selection)?!1:(n.dispatch(Ze(n.state,t)),!0)}function fc(n,e){return Le(n,t=>n.moveByChar(t,e))}const uc=n=>fc(n,!me(n)),dc=n=>fc(n,me(n));function pc(n,e){return Le(n,t=>n.moveByGroup(t,e))}const Em=n=>pc(n,!me(n)),Pm=n=>pc(n,me(n)),Lm=n=>Le(n,e=>qn(n.state,e,!me(n))),Rm=n=>Le(n,e=>qn(n.state,e,me(n)));function mc(n,e){return Le(n,t=>n.moveVertically(t,e))}const gc=n=>mc(n,!1),yc=n=>mc(n,!0);function bc(n,e){return Le(n,t=>n.moveVertically(t,e,hc(n)))}const bl=n=>bc(n,!1),wl=n=>bc(n,!0),_m=n=>Le(n,e=>gt(n,e,!0)),Im=n=>Le(n,e=>gt(n,e,!1)),Nm=n=>Le(n,e=>gt(n,e,!me(n))),Vm=n=>Le(n,e=>gt(n,e,me(n))),Fm=n=>Le(n,e=>w.cursor(n.lineBlockAt(e.head).from)),Hm=n=>Le(n,e=>w.cursor(n.lineBlockAt(e.head).to)),kl=({state:n,dispatch:e})=>(e(Ze(n,{anchor:0})),!0),xl=({state:n,dispatch:e})=>(e(Ze(n,{anchor:n.doc.length})),!0),vl=({state:n,dispatch:e})=>(e(Ze(n,{anchor:n.selection.main.anchor,head:0})),!0),Sl=({state:n,dispatch:e})=>(e(Ze(n,{anchor:n.selection.main.anchor,head:n.doc.length})),!0),$m=({state:n,dispatch:e})=>(e(n.update({selection:{anchor:0,head:n.doc.length},userEvent:"select"})),!0),Wm=({state:n,dispatch:e})=>{let t=Kn(n).map(({from:i,to:s})=>w.range(i,Math.min(s+1,n.doc.length)));return e(n.update({selection:w.create(t),userEvent:"select"})),!0},zm=({state:n,dispatch:e})=>{let t=ii(n.selection,i=>{var s;let r=le(n).resolveInner(i.head,1);for(;!(r.from<i.from&&r.to>=i.to||r.to>i.to&&r.from<=i.from||!(!((s=r.parent)===null||s===void 0)&&s.parent));)r=r.parent;return w.range(r.to,r.from)});return e(Ze(n,t)),!0},qm=({state:n,dispatch:e})=>{let t=n.selection,i=null;return t.ranges.length>1?i=w.create([t.main]):t.main.empty||(i=w.create([w.cursor(t.main.head)])),i?(e(Ze(n,i)),!0):!1};function jn(n,e){if(n.state.readOnly)return!1;let t="delete.selection",{state:i}=n,s=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=e(o);a<o?(t="delete.backward",a=Zi(n,a,!1)):a>o&&(t="delete.forward",a=Zi(n,a,!0)),o=Math.min(o,a),l=Math.max(l,a)}else o=Zi(n,o,!1),l=Zi(n,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:w.cursor(o)}});return s.changes.empty?!1:(n.dispatch(i.update(s,{scrollIntoView:!0,userEvent:t,effects:t=="delete.selection"?O.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function Zi(n,e,t){if(n instanceof O)for(let i of n.state.facet(O.atomicRanges).map(s=>s(n)))i.between(e,e,(s,r)=>{s<e&&r>e&&(e=t?r:s)});return e}const wc=(n,e)=>jn(n,t=>{let{state:i}=n,s=i.doc.lineAt(t),r,o;if(!e&&t>s.from&&t<s.from+200&&!/[^ \t]/.test(r=s.text.slice(0,t-s.from))){if(r[r.length-1]=="	")return t-1;let l=Ri(r,i.tabSize),a=l%Tt(i)||Tt(i);for(let h=0;h<a&&r[r.length-1-h]==" ";h++)t--;o=t}else o=Ae(s.text,t-s.from,e,e)+s.from,o==t&&s.number!=(e?i.doc.lines:1)&&(o+=e?1:-1);return o}),pr=n=>wc(n,!1),kc=n=>wc(n,!0),xc=(n,e)=>jn(n,t=>{let i=t,{state:s}=n,r=s.doc.lineAt(i),o=s.charCategorizer(i);for(let l=null;;){if(i==(e?r.to:r.from)){i==t&&r.number!=(e?s.doc.lines:1)&&(i+=e?1:-1);break}let a=Ae(r.text,i-r.from,e)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),c=o(h);if(l!=null&&c!=l)break;(h!=" "||i!=t)&&(l=c),i=a}return i}),vc=n=>xc(n,!1),jm=n=>xc(n,!0),Sc=n=>jn(n,e=>{let t=n.lineBlockAt(e).to;return e<t?t:Math.min(n.state.doc.length,e+1)}),Km=n=>jn(n,e=>{let t=n.lineBlockAt(e).from;return e>t?t:Math.max(0,e-1)}),Um=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:V.of(["",""])},range:w.cursor(i.from)}));return e(n.update(t,{scrollIntoView:!0,userEvent:"input"})),!0},Gm=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>{if(!i.empty||i.from==0||i.from==n.doc.length)return{range:i};let s=i.from,r=n.doc.lineAt(s),o=s==r.from?s-1:Ae(r.text,s-r.from,!1)+r.from,l=s==r.to?s+1:Ae(r.text,s-r.from,!0)+r.from;return{changes:{from:o,to:l,insert:n.doc.slice(s,l).append(n.doc.slice(o,s))},range:w.cursor(l)}});return t.changes.empty?!1:(e(n.update(t,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function Kn(n){let e=[],t=-1;for(let i of n.selection.ranges){let s=n.doc.lineAt(i.from),r=n.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=n.doc.lineAt(i.to-1)),t>=s.number){let o=e[e.length-1];o.to=r.to,o.ranges.push(i)}else e.push({from:s.from,to:r.to,ranges:[i]});t=r.number+1}return e}function Cc(n,e,t){if(n.readOnly)return!1;let i=[],s=[];for(let r of Kn(n)){if(t?r.to==n.doc.length:r.from==0)continue;let o=n.doc.lineAt(t?r.to+1:r.from-1),l=o.length+1;if(t){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+n.lineBreak});for(let a of r.ranges)s.push(w.range(Math.min(n.doc.length,a.anchor+l),Math.min(n.doc.length,a.head+l)))}else{i.push({from:o.from,to:r.from},{from:r.to,insert:n.lineBreak+o.text});for(let a of r.ranges)s.push(w.range(a.anchor-l,a.head-l))}}return i.length?(e(n.update({changes:i,scrollIntoView:!0,selection:w.create(s,n.selection.mainIndex),userEvent:"move.line"})),!0):!1}const Jm=({state:n,dispatch:e})=>Cc(n,e,!1),Ym=({state:n,dispatch:e})=>Cc(n,e,!0);function Ac(n,e,t){if(n.readOnly)return!1;let i=[];for(let s of Kn(n))t?i.push({from:s.from,insert:n.doc.slice(s.from,s.to)+n.lineBreak}):i.push({from:s.to,insert:n.lineBreak+n.doc.slice(s.from,s.to)});return e(n.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const Xm=({state:n,dispatch:e})=>Ac(n,e,!1),Qm=({state:n,dispatch:e})=>Ac(n,e,!0),Zm=n=>{if(n.state.readOnly)return!1;let{state:e}=n,t=e.changes(Kn(e).map(({from:s,to:r})=>(s>0?s--:r<e.doc.length&&r++,{from:s,to:r}))),i=ii(e.selection,s=>n.moveVertically(s,!0)).map(t);return n.dispatch({changes:t,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function eg(n,e){if(/\(\)|\[\]|\{\}/.test(n.sliceDoc(e-1,e+1)))return{from:e,to:e};let t=le(n).resolveInner(e),i=t.childBefore(e),s=t.childAfter(e),r;return i&&s&&i.to<=e&&s.from>=e&&(r=i.type.prop(_.closedBy))&&r.indexOf(s.name)>-1&&n.doc.lineAt(i.to).from==n.doc.lineAt(s.from).from?{from:i.to,to:s.from}:null}const tg=Mc(!1),ig=Mc(!0);function Mc(n){return({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(s=>{let{from:r,to:o}=s,l=e.doc.lineAt(r),a=!n&&r==o&&eg(e,r);n&&(r=o=(o<=l.to?l:e.doc.lineAt(o)).to);let h=new Hn(e,{simulateBreak:r,simulateDoubleBreak:!!a}),c=Pr(h,r);for(c==null&&(c=/^\s*/.exec(e.doc.lineAt(r).text)[0].length);o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let f=["",Oi(e,c)];return a&&f.push(Oi(e,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:V.of(f)},range:w.cursor(r+1+f[1].length)}});return t(e.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function Fr(n,e){let t=-1;return n.changeByRange(i=>{let s=[];for(let o=i.from;o<=i.to;){let l=n.doc.lineAt(o);l.number>t&&(i.empty||i.to>l.from)&&(e(l,s,i),t=l.number),o=l.to+1}let r=n.changes(s);return{changes:s,range:w.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const ng=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=Object.create(null),i=new Hn(n,{overrideIndentation:r=>{let o=t[r];return o??-1}}),s=Fr(n,(r,o,l)=>{let a=Pr(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],c=Oi(n,a);(h!=c||l.from<r.from+h.length)&&(t[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:c}))});return s.changes.empty||e(n.update(s,{userEvent:"indent"})),!0},Dc=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(Fr(n,(t,i)=>{i.push({from:t.from,insert:n.facet(Fn)})}),{userEvent:"input.indent"})),!0),Tc=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(Fr(n,(t,i)=>{let s=/^\s*/.exec(t.text)[0];if(!s)return;let r=Ri(s,n.tabSize),o=0,l=Oi(n,Math.max(0,r-Tt(n)));for(;o<s.length&&o<l.length&&s.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:t.from+o,to:t.from+s.length,insert:l.slice(o)})}),{userEvent:"delete.dedent"})),!0),sg=[{key:"Ctrl-b",run:nc,shift:uc,preventDefault:!0},{key:"Ctrl-f",run:sc,shift:dc},{key:"Ctrl-p",run:lc,shift:gc},{key:"Ctrl-n",run:ac,shift:yc},{key:"Ctrl-a",run:Dm,shift:Fm},{key:"Ctrl-e",run:Tm,shift:Hm},{key:"Ctrl-d",run:kc},{key:"Ctrl-h",run:pr},{key:"Ctrl-k",run:Sc},{key:"Ctrl-Alt-h",run:vc},{key:"Ctrl-o",run:Um},{key:"Ctrl-t",run:Gm},{key:"Ctrl-v",run:dr}],rg=[{key:"ArrowLeft",run:nc,shift:uc,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:bm,shift:Em,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:Am,shift:Nm,preventDefault:!0},{key:"ArrowRight",run:sc,shift:dc,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:wm,shift:Pm,preventDefault:!0},{mac:"Cmd-ArrowRight",run:Mm,shift:Vm,preventDefault:!0},{key:"ArrowUp",run:lc,shift:gc,preventDefault:!0},{mac:"Cmd-ArrowUp",run:kl,shift:vl},{mac:"Ctrl-ArrowUp",run:yl,shift:bl},{key:"ArrowDown",run:ac,shift:yc,preventDefault:!0},{mac:"Cmd-ArrowDown",run:xl,shift:Sl},{mac:"Ctrl-ArrowDown",run:dr,shift:wl},{key:"PageUp",run:yl,shift:bl},{key:"PageDown",run:dr,shift:wl},{key:"Home",run:Cm,shift:Im,preventDefault:!0},{key:"Mod-Home",run:kl,shift:vl},{key:"End",run:Sm,shift:_m,preventDefault:!0},{key:"Mod-End",run:xl,shift:Sl},{key:"Enter",run:tg},{key:"Mod-a",run:$m},{key:"Backspace",run:pr,shift:pr},{key:"Delete",run:kc},{key:"Mod-Backspace",mac:"Alt-Backspace",run:vc},{key:"Mod-Delete",mac:"Alt-Delete",run:jm},{mac:"Mod-Backspace",run:Km},{mac:"Mod-Delete",run:Sc}].concat(sg.map(n=>({mac:n.key,run:n.run,shift:n.shift}))),og=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:xm,shift:Lm},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:vm,shift:Rm},{key:"Alt-ArrowUp",run:Jm},{key:"Shift-Alt-ArrowUp",run:Xm},{key:"Alt-ArrowDown",run:Ym},{key:"Shift-Alt-ArrowDown",run:Qm},{key:"Escape",run:qm},{key:"Mod-Enter",run:ig},{key:"Alt-l",mac:"Ctrl-l",run:Wm},{key:"Mod-i",run:zm,preventDefault:!0},{key:"Mod-[",run:Tc},{key:"Mod-]",run:Dc},{key:"Mod-Alt-\\",run:ng},{key:"Shift-Mod-k",run:Zm},{key:"Shift-Mod-\\",run:Bm},{key:"Mod-/",run:Qp},{key:"Alt-A",run:em}].concat(rg),lg={key:"Tab",run:Dc,shift:Tc},ag="#2E3235",ze="#DDDDDD",wi="#B9D2FF",en="#b0b0b0",hg="#e0e0e0",Oc="#808080",gs="#000000",cg="#A54543",Bc="#fc6d24",bt="#fda331",ys="#8abeb7",Cl="#b5bd68",hi="#6fb3d2",ci="#cc99cc",fg="#6987AF",Al=Bc,Ml="#292d30",tn=wi+"30",ug=ag,bs=ze,dg="#202325",Dl=ze,pg=O.theme({"&":{color:ze,backgroundColor:ug},".cm-content":{caretColor:Dl},".cm-cursor, .cm-dropCursor":{borderLeftColor:Dl},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:dg},".cm-panels":{backgroundColor:Ml,color:en},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:wi,outline:`1px solid ${en}`,color:gs},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:hg,color:gs},".cm-activeLine":{backgroundColor:tn},".cm-selectionMatch":{backgroundColor:tn},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${en}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:wi,color:gs},".cm-gutters":{borderRight:"1px solid #ffffff10",color:Oc,backgroundColor:Ml},".cm-activeLineGutter":{backgroundColor:tn},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:wi},".cm-tooltip":{border:"none",backgroundColor:bs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:bs,borderBottomColor:bs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:tn,color:en}}},{dark:!0}),mg=ti.define([{tag:m.keyword,color:bt},{tag:[m.name,m.deleted,m.character,m.propertyName,m.macroName],color:Cl},{tag:[m.variableName],color:hi},{tag:[m.function(m.variableName)],color:bt},{tag:[m.labelName],color:Bc},{tag:[m.color,m.constant(m.name),m.standard(m.name)],color:bt},{tag:[m.definition(m.name),m.separator],color:ci},{tag:[m.brace],color:ci},{tag:[m.annotation],color:Al},{tag:[m.number,m.changed,m.annotation,m.modifier,m.self,m.namespace],color:bt},{tag:[m.typeName,m.className],color:hi},{tag:[m.operator,m.operatorKeyword],color:ci},{tag:[m.tagName],color:bt},{tag:[m.squareBracket],color:ci},{tag:[m.angleBracket],color:ci},{tag:[m.attributeName],color:hi},{tag:[m.regexp],color:bt},{tag:[m.quote],color:ze},{tag:[m.string],color:Cl},{tag:m.link,color:fg,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[m.url,m.escape,m.special(m.string)],color:ys},{tag:[m.meta],color:cg},{tag:[m.comment],color:Oc,fontStyle:"italic"},{tag:m.monospace,color:ze},{tag:m.strong,fontWeight:"bold",color:bt},{tag:m.emphasis,fontStyle:"italic",color:hi},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.heading,fontWeight:"bold",color:ze},{tag:m.special(m.heading1),fontWeight:"bold",color:ze},{tag:m.heading1,fontWeight:"bold",color:ze},{tag:[m.heading2,m.heading3,m.heading4],fontWeight:"bold",color:ze},{tag:[m.heading5,m.heading6],color:ze},{tag:[m.atom,m.bool,m.special(m.variableName)],color:ys},{tag:[m.processingInstruction,m.inserted],color:ys},{tag:[m.contentSeparator],color:hi},{tag:m.invalid,color:wi,borderBottom:`1px dotted ${Al}`}]),gg=[pg,Rr(mg)],Tl="#2e3440",Hr="#3b4252",Ol="#434c5e",nn="#4c566a",Bl="#e5e9f0",mr="#eceff4",ws="#8fbcbb",El="#88c0d0",yg="#81a1c1",Re="#5e81ac",bg="#bf616a",Ft="#d08770",ks="#ebcb8b",Pl="#a3be8c",wg="#b48ead",Ll="#d30102",$r=mr,xs=$r,kg="#ffffff",vs=Hr,xg=$r,Rl=Hr,vg=O.theme({"&":{color:Tl,backgroundColor:kg},".cm-content":{caretColor:Rl},".cm-cursor, .cm-dropCursor":{borderLeftColor:Rl},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:xg},".cm-panels":{backgroundColor:$r,color:nn},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:`1px solid ${nn}`},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:Bl},".cm-activeLine":{backgroundColor:xs},".cm-selectionMatch":{backgroundColor:Bl},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${nn}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:mr},".cm-gutters":{backgroundColor:mr,color:Tl,border:"none"},".cm-activeLineGutter":{backgroundColor:xs},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:vs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:vs,borderBottomColor:vs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:xs,color:nn}}},{dark:!1}),Sg=ti.define([{tag:m.keyword,color:Re},{tag:[m.name,m.deleted,m.character,m.propertyName,m.macroName],color:Ft},{tag:[m.variableName],color:Ft},{tag:[m.function(m.variableName)],color:Re},{tag:[m.labelName],color:yg},{tag:[m.color,m.constant(m.name),m.standard(m.name)],color:Re},{tag:[m.definition(m.name),m.separator],color:Pl},{tag:[m.brace],color:ws},{tag:[m.annotation],color:Ll},{tag:[m.number,m.changed,m.annotation,m.modifier,m.self,m.namespace],color:El},{tag:[m.typeName,m.className],color:ks},{tag:[m.operator,m.operatorKeyword],color:Pl},{tag:[m.tagName],color:wg},{tag:[m.squareBracket],color:bg},{tag:[m.angleBracket],color:Ft},{tag:[m.attributeName],color:ks},{tag:[m.regexp],color:Re},{tag:[m.quote],color:Hr},{tag:[m.string],color:Ft},{tag:m.link,color:ws,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[m.url,m.escape,m.special(m.string)],color:Ft},{tag:[m.meta],color:El},{tag:[m.comment],color:Ol,fontStyle:"italic"},{tag:m.strong,fontWeight:"bold",color:Re},{tag:m.emphasis,fontStyle:"italic",color:Re},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.heading,fontWeight:"bold",color:Re},{tag:m.special(m.heading1),fontWeight:"bold",color:Re},{tag:m.heading1,fontWeight:"bold",color:Re},{tag:[m.heading2,m.heading3,m.heading4],fontWeight:"bold",color:Re},{tag:[m.heading5,m.heading6],color:Re},{tag:[m.atom,m.bool,m.special(m.variableName)],color:Ft},{tag:[m.processingInstruction,m.inserted],color:ws},{tag:[m.contentSeparator],color:ks},{tag:m.invalid,color:Ol,borderBottom:`1px dotted ${Ll}`}]),Cg=[vg,Rr(Sg)];function _l(n){let e=Object.keys(n).join(""),t=/\w/.test(e);return t&&(e=e.replace(/\w/g,"")),`[${t?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function Ag(n){let e=Object.create(null),t=Object.create(null);for(let{label:s}of n){e[s[0]]=!0;for(let r=1;r<s.length;r++)t[s[r]]=!0}let i=_l(e)+_l(t)+"*$";return[new RegExp("^"+i),new RegExp(i)]}function Mg(n){let e=n.map(s=>typeof s=="string"?{label:s}:s),[t,i]=e.every(s=>/^\w+$/.test(s.label))?[/\w*$/,/\w+$/]:Ag(e);return s=>{let r=s.matchBefore(i);return r||s.explicit?{from:r?r.from:s.pos,options:e,validFor:t}:null}}function pb(n,e){return t=>{for(let i=le(t.state).resolveInner(t.pos,-1);i;i=i.parent)if(n.indexOf(i.name)>-1)return null;return e(t)}}class Il{constructor(e,t,i){this.completion=e,this.source=t,this.match=i}}function gr(n){return n.selection.main.head}function Dg(n,e,t,i){return Object.assign(Object.assign({},n.changeByRange(s=>{if(s==n.selection.main)return{changes:{from:t,to:i,insert:e},range:w.cursor(t+e.length)};let r=i-t;return!s.empty||r&&n.sliceDoc(s.from-r,s.from)!=n.sliceDoc(t,i)?{range:s}:{changes:{from:s.from-r,to:s.from,insert:e},range:w.cursor(s.from-r+e.length)}})),{userEvent:"input.complete"})}function Ec(n,e){const t=e.completion.apply||e.completion.label;let i=e.source;typeof t=="string"?n.dispatch(Dg(n.state,t,i.from,i.to)):t(n,e.completion,i.from,i.to)}const Nl=new WeakMap;function Tg(n){if(!Array.isArray(n))return n;let e=Nl.get(n);return e||Nl.set(n,e=Mg(n)),e}class Og{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[];for(let t=0;t<e.length;){let i=ue(e,t),s=De(i);this.chars.push(i);let r=e.slice(t,t+s),o=r.toUpperCase();this.folded.push(ue(o==r?r.toLowerCase():o,0)),t+=s}this.astral=e.length!=this.chars.length}match(e){if(this.pattern.length==0)return[0];if(e.length<this.pattern.length)return null;let{chars:t,folded:i,any:s,precise:r,byWord:o}=this;if(t.length==1){let k=ue(e,0);return k==t[0]?[0,0,De(k)]:k==i[0]?[-200,0,De(k)]:null}let l=e.indexOf(this.pattern);if(l==0)return[0,0,this.pattern.length];let a=t.length,h=0;if(l<0){for(let k=0,v=Math.min(e.length,200);k<v&&h<a;){let S=ue(e,k);(S==t[h]||S==i[h])&&(s[h++]=k),k+=De(S)}if(h<a)return null}let c=0,f=0,u=!1,d=0,p=-1,g=-1,y=/[a-z]/.test(e),b=!0;for(let k=0,v=Math.min(e.length,200),S=0;k<v&&f<a;){let C=ue(e,k);l<0&&(c<a&&C==t[c]&&(r[c++]=k),d<a&&(C==t[d]||C==i[d]?(d==0&&(p=k),g=k+1,d++):d=0));let D,E=C<255?C>=48&&C<=57||C>=97&&C<=122?2:C>=65&&C<=90?1:0:(D=ua(C))!=D.toLowerCase()?1:D!=D.toUpperCase()?2:0;(!k||E==1&&y||S==0&&E!=0)&&(t[f]==C||i[f]==C&&(u=!0)?o[f++]=k:o.length&&(b=!1)),S=E,k+=De(C)}return f==a&&o[0]==0&&b?this.result(-100+(u?-200:0),o,e):d==a&&p==0?[-200-e.length,0,g]:l>-1?[-700-e.length,l,l+this.pattern.length]:d==a?[-900-e.length,p,g]:f==a?this.result(-100+(u?-200:0)+-700+(b?0:-1100),o,e):t.length==2?null:this.result((s[0]?-700:0)+-200+-1100,s,e)}result(e,t,i){let s=[e-i.length],r=1;for(let o of t){let l=o+(this.astral?De(ue(i,o)):1);r>1&&s[r-1]==o?s[r-1]=l:(s[r++]=o,s[r++]=l)}return s}}const Bt=T.define({combine(n){return _t(n,{activateOnTyping:!0,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],compareCompletions:(e,t)=>e.label.localeCompare(t.label),interactionDelay:75},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,optionClass:(e,t)=>i=>Bg(e(i),t(i)),addToOptions:(e,t)=>e.concat(t)})}});function Bg(n,e){return n?e?n+" "+e:n:e}function Eg(n){let e=n.addToOptions.slice();return n.icons&&e.push({render(t){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),t.type&&i.classList.add(...t.type.split(/\s+/g).map(s=>"cm-completionIcon-"+s)),i.setAttribute("aria-hidden","true"),i},position:20}),e.push({render(t,i,s){let r=document.createElement("span");r.className="cm-completionLabel";let{label:o}=t,l=0;for(let a=1;a<s.length;){let h=s[a++],c=s[a++];h>l&&r.appendChild(document.createTextNode(o.slice(l,h)));let f=r.appendChild(document.createElement("span"));f.appendChild(document.createTextNode(o.slice(h,c))),f.className="cm-completionMatchedText",l=c}return l<o.length&&r.appendChild(document.createTextNode(o.slice(l))),r},position:50},{render(t){if(!t.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=t.detail,i},position:80}),e.sort((t,i)=>t.position-i.position).map(t=>t.render)}function Vl(n,e,t){if(n<=t)return{from:0,to:n};if(e<0&&(e=0),e<=n>>1){let s=Math.floor(e/t);return{from:s*t,to:(s+1)*t}}let i=Math.floor((n-e)/t);return{from:n-(i+1)*t,to:n-i*t}}class Pg{constructor(e,t){this.view=e,this.stateField=t,this.info=null,this.placeInfo={read:()=>this.measureInfo(),write:l=>this.positionInfo(l),key:this};let i=e.state.field(t),{options:s,selected:r}=i.open,o=e.state.facet(Bt);this.optionContent=Eg(o),this.optionClass=o.optionClass,this.range=Vl(s.length,r,o.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.dom.addEventListener("mousedown",l=>{for(let a=l.target,h;a&&a!=this.dom;a=a.parentNode)if(a.nodeName=="LI"&&(h=/-(\d+)$/.exec(a.id))&&+h[1]<s.length){Ec(e,s[+h[1]]),l.preventDefault();return}}),this.list=this.dom.appendChild(this.createListBox(s,i.id,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfo)})}mount(){this.updateSel()}update(e){e.state.field(this.stateField)!=e.startState.field(this.stateField)&&this.updateSel()}positioned(){this.info&&this.view.requestMeasure(this.placeInfo)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected>-1&&t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=Vl(t.options.length,t.selected,this.view.state.facet(Bt).maxRenderedOptions),this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t.options,e.id,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfo)})),this.updateSelectedOption(t.selected)){this.info&&(this.info.remove(),this.info=null);let{completion:i}=t.options[t.selected],{info:s}=i;if(!s)return;let r=typeof s=="string"?document.createTextNode(s):s(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(o)}).catch(o=>_e(this.view.state,o,"completion info")):this.addInfoPane(r)}}addInfoPane(e){let t=this.info=document.createElement("div");t.className="cm-tooltip cm-completionInfo",t.appendChild(e),this.dom.appendChild(t),this.view.requestMeasure(this.placeInfo)}updateSelectedOption(e){let t=null;for(let i=this.list.firstChild,s=this.range.from;i;i=i.nextSibling,s++)s==e?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),t=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return t&&Rg(this.list,t),t}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.ownerDocument.defaultView||window,i=this.dom.getBoundingClientRect(),s=this.info.getBoundingClientRect(),r=e.getBoundingClientRect();if(r.top>Math.min(t.innerHeight,i.bottom)-10||r.bottom<Math.max(0,i.top)+10)return null;let o=this.view.textDirection==G.RTL,l=o,a=!1,h,c="",f="",u=i.left,d=t.innerWidth-i.right;if(l&&u<Math.min(s.width,d)?l=!1:!l&&d<Math.min(s.width,u)&&(l=!0),s.width<=(l?u:d))c=Math.max(0,Math.min(r.top,t.innerHeight-s.height))-i.top+"px",h=Math.min(400,l?u:d)+"px";else{a=!0,h=Math.min(400,(o?i.right:t.innerWidth-i.left)-30)+"px";let p=t.innerHeight-i.bottom;p>=s.height||p>i.top?c=r.bottom-i.top+"px":f=i.bottom-r.top+"px"}return{top:c,bottom:f,maxWidth:h,class:a?o?"left-narrow":"right-narrow":l?"left":"right"}}positionInfo(e){this.info&&(e?(this.info.style.top=e.top,this.info.style.bottom=e.bottom,this.info.style.maxWidth=e.maxWidth,this.info.className="cm-tooltip cm-completionInfo cm-completionInfo-"+e.class):this.info.style.top="-1e6px")}createListBox(e,t,i){const s=document.createElement("ul");s.id=t,s.setAttribute("role","listbox"),s.setAttribute("aria-expanded","true"),s.setAttribute("aria-label",this.view.state.phrase("Completions"));for(let r=i.from;r<i.to;r++){let{completion:o,match:l}=e[r];const a=s.appendChild(document.createElement("li"));a.id=t+"-"+r,a.setAttribute("role","option");let h=this.optionClass(o);h&&(a.className=h);for(let c of this.optionContent){let f=c(o,this.view.state,l);f&&a.appendChild(f)}}return i.from&&s.classList.add("cm-completionListIncompleteTop"),i.to<e.length&&s.classList.add("cm-completionListIncompleteBottom"),s}}function Lg(n){return e=>new Pg(e,n)}function Rg(n,e){let t=n.getBoundingClientRect(),i=e.getBoundingClientRect();i.top<t.top?n.scrollTop-=t.top-i.top:i.bottom>t.bottom&&(n.scrollTop+=i.bottom-t.bottom)}function Fl(n){return(n.boost||0)*100+(n.apply?10:0)+(n.info?5:0)+(n.type?1:0)}function _g(n,e){let t=[],i=0;for(let l of n)if(l.hasResult())if(l.result.filter===!1){let a=l.result.getMatch;for(let h of l.result.options){let c=[1e9-i++];if(a)for(let f of a(h))c.push(f);t.push(new Il(h,l,c))}}else{let a=new Og(e.sliceDoc(l.from,l.to)),h;for(let c of l.result.options)(h=a.match(c.label))&&(c.boost!=null&&(h[0]+=c.boost),t.push(new Il(c,l,h)))}let s=[],r=null,o=e.facet(Bt).compareCompletions;for(let l of t.sort((a,h)=>h.match[0]-a.match[0]||o(a.completion,h.completion)))!r||r.label!=l.completion.label||r.detail!=l.completion.detail||r.type!=null&&l.completion.type!=null&&r.type!=l.completion.type||r.apply!=l.completion.apply?s.push(l):Fl(l.completion)>Fl(r)&&(s[s.length-1]=l),r=l.completion;return s}class ki{constructor(e,t,i,s,r){this.options=e,this.attrs=t,this.tooltip=i,this.timestamp=s,this.selected=r}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new ki(this.options,Hl(t,e),this.tooltip,this.timestamp,e)}static build(e,t,i,s,r){let o=_g(e,t);if(!o.length)return null;let l=t.facet(Bt).selectOnOpen?0:-1;if(s&&s.selected!=l&&s.selected!=-1){let a=s.options[s.selected].completion;for(let h=0;h<o.length;h++)if(o[h].completion==a){l=h;break}}return new ki(o,Hl(i,l),{pos:e.reduce((a,h)=>h.hasResult()?Math.min(a,h.from):a,1e8),create:Lg(Ni),above:r.aboveCursor},s?s.timestamp:Date.now(),l)}map(e){return new ki(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected)}}class On{constructor(e,t,i){this.active=e,this.id=t,this.open=i}static start(){return new On(Vg,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(e){let{state:t}=e,i=t.facet(Bt),r=(i.override||t.languageDataAt("autocomplete",gr(t)).map(Tg)).map(l=>(this.active.find(h=>h.source==l)||new et(l,this.active.some(h=>h.state!=0)?1:0)).update(e,i));r.length==this.active.length&&r.every((l,a)=>l==this.active[a])&&(r=this.active);let o=e.selection||r.some(l=>l.hasResult()&&e.changes.touchesRange(l.from,l.to))||!Ig(r,this.active)?ki.build(r,t,this.id,this.open,i):this.open&&e.docChanged?this.open.map(e.changes):this.open;!o&&r.every(l=>l.state!=1)&&r.some(l=>l.hasResult())&&(r=r.map(l=>l.hasResult()?new et(l.source,0):l));for(let l of e.effects)l.is(Rc)&&(o=o&&o.setSelected(l.value,this.id));return r==this.active&&o==this.open?this:new On(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:Ng}}function Ig(n,e){if(n==e)return!0;for(let t=0,i=0;;){for(;t<n.length&&!n[t].hasResult;)t++;for(;i<e.length&&!e[i].hasResult;)i++;let s=t==n.length,r=i==e.length;if(s||r)return s==r;if(n[t++].result!=e[i++].result)return!1}}const Ng={"aria-autocomplete":"list"};function Hl(n,e){let t={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":n};return e>-1&&(t["aria-activedescendant"]=n+"-"+e),t}const Vg=[];function Fg(n){return n.isUserEvent("input.type")?"input":n.isUserEvent("delete.backward")?"delete":null}class et{constructor(e,t,i=-1){this.source=e,this.state=t,this.explicitPos=i}hasResult(){return!1}update(e,t){let i=Fg(e),s=this;i?s=s.handleUserEvent(e,i,t):e.docChanged?s=s.handleChange(e):e.selection&&s.state!=0&&(s=new et(s.source,0));for(let r of e.effects)if(r.is(Pc))s=new et(s.source,1,r.value?gr(e.state):-1);else if(r.is(Lc))s=new et(s.source,0);else if(r.is(Hg))for(let o of r.value)o.source==s.source&&(s=o);return s}handleUserEvent(e,t,i){return t=="delete"||!i.activateOnTyping?this.map(e.changes):new et(this.source,1)}handleChange(e){return e.changes.touchesRange(gr(e.startState))?new et(this.source,0):this.map(e.changes)}map(e){return e.empty||this.explicitPos<0?this:new et(this.source,this.state,e.mapPos(this.explicitPos))}}const Pc=R.define(),Lc=R.define(),Hg=R.define({map(n,e){return n.map(t=>t.map(e))}}),Rc=R.define(),Ni=xe.define({create(){return On.start()},update(n,e){return n.update(e)},provide:n=>[Mr.from(n,e=>e.tooltip),O.contentAttributes.from(n,e=>e.attrs)]});function sn(n,e="option"){return t=>{let i=t.state.field(Ni,!1);if(!i||!i.open||Date.now()-i.open.timestamp<t.state.facet(Bt).interactionDelay)return!1;let s=1,r;e=="page"&&(r=Ed(t,i.open.tooltip))&&(s=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+s*(n?1:-1):n?0:o-1;return l<0?l=e=="page"?0:o-1:l>=o&&(l=e=="page"?o-1:0),t.dispatch({effects:Rc.of(l)}),!0}}const $g=n=>{let e=n.state.field(Ni,!1);return n.state.readOnly||!e||!e.open||e.open.selected<0||Date.now()-e.open.timestamp<n.state.facet(Bt).interactionDelay?!1:(Ec(n,e.open.options[e.open.selected]),!0)},Wg=n=>n.state.field(Ni,!1)?(n.dispatch({effects:Pc.of(!0)}),!0):!1,zg=n=>{let e=n.state.field(Ni,!1);return!e||!e.active.some(t=>t.state!=0)?!1:(n.dispatch({effects:Lc.of(null)}),!0)},qg=O.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",listStyle:"none",margin:0,padding:0,"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer",padding:"1px 3px",lineHeight:1.2}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class jg{constructor(e,t,i,s){this.field=e,this.line=t,this.from=i,this.to=s}}class Wr{constructor(e,t,i){this.field=e,this.from=t,this.to=i}map(e){let t=e.mapPos(this.from,-1,oe.TrackDel),i=e.mapPos(this.to,1,oe.TrackDel);return t==null||i==null?null:new Wr(this.field,t,i)}}class zr{constructor(e,t){this.lines=e,this.fieldPositions=t}instantiate(e,t){let i=[],s=[t],r=e.doc.lineAt(t),o=/^\s*/.exec(r.text)[0];for(let a of this.lines){if(i.length){let h=o,c=/^\t*/.exec(a)[0].length;for(let f=0;f<c;f++)h+=e.facet(Fn);s.push(t+h.length-c),a=h+a.slice(c)}i.push(a),t+=a.length+1}let l=this.fieldPositions.map(a=>new Wr(a.field,s[a.line]+a.from,s[a.line]+a.to));return{text:i,ranges:l}}static parse(e){let t=[],i=[],s=[],r;for(let o of e.split(/\r\n?|\n/)){for(;r=/[#$]\{(?:(\d+)(?::([^}]*))?|([^}]*))\}/.exec(o);){let l=r[1]?+r[1]:null,a=r[2]||r[3]||"",h=-1;for(let c=0;c<t.length;c++)(l!=null?t[c].seq==l:a&&t[c].name==a)&&(h=c);if(h<0){let c=0;for(;c<t.length&&(l==null||t[c].seq!=null&&t[c].seq<l);)c++;t.splice(c,0,{seq:l,name:a}),h=c;for(let f of s)f.field>=h&&f.field++}s.push(new jg(h,i.length,r.index,r.index+a.length)),o=o.slice(0,r.index)+a+o.slice(r.index+r[0].length)}for(let l;l=/([$#])\\{/.exec(o);){o=o.slice(0,l.index)+l[1]+"{"+o.slice(l.index+l[0].length);for(let a of s)a.line==i.length&&a.from>l.index&&(a.from--,a.to--)}i.push(o)}return new zr(i,s)}}let Kg=L.widget({widget:new class extends Qe{toDOM(){let n=document.createElement("span");return n.className="cm-snippetFieldPosition",n}ignoreEvent(){return!1}}}),Ug=L.mark({class:"cm-snippetField"});class ni{constructor(e,t){this.ranges=e,this.active=t,this.deco=L.set(e.map(i=>(i.from==i.to?Kg:Ug).range(i.from,i.to)))}map(e){let t=[];for(let i of this.ranges){let s=i.map(e);if(!s)return null;t.push(s)}return new ni(t,this.active)}selectionInsideField(e){return e.ranges.every(t=>this.ranges.some(i=>i.field==this.active&&i.from<=t.from&&i.to>=t.to))}}const Vi=R.define({map(n,e){return n&&n.map(e)}}),Gg=R.define(),Ei=xe.define({create(){return null},update(n,e){for(let t of e.effects){if(t.is(Vi))return t.value;if(t.is(Gg)&&n)return new ni(n.ranges,t.value)}return n&&e.docChanged&&(n=n.map(e.changes)),n&&e.selection&&!n.selectionInsideField(e.selection)&&(n=null),n},provide:n=>O.decorations.from(n,e=>e?e.deco:L.none)});function qr(n,e){return w.create(n.filter(t=>t.field==e).map(t=>w.range(t.from,t.to)))}function Jg(n){let e=zr.parse(n);return(t,i,s,r)=>{let{text:o,ranges:l}=e.instantiate(t.state,s),a={changes:{from:s,to:r,insert:V.of(o)},scrollIntoView:!0};if(l.length&&(a.selection=qr(l,0)),l.length>1){let h=new ni(l,0),c=a.effects=[Vi.of(h)];t.state.field(Ei,!1)===void 0&&c.push(R.appendConfig.of([Ei,e0,t0,qg]))}t.dispatch(t.state.update(a))}}function _c(n){return({state:e,dispatch:t})=>{let i=e.field(Ei,!1);if(!i||n<0&&i.active==0)return!1;let s=i.active+n,r=n>0&&!i.ranges.some(o=>o.field==s+n);return t(e.update({selection:qr(i.ranges,s),effects:Vi.of(r?null:new ni(i.ranges,s))})),!0}}const Yg=({state:n,dispatch:e})=>n.field(Ei,!1)?(e(n.update({effects:Vi.of(null)})),!0):!1,Xg=_c(1),Qg=_c(-1),Zg=[{key:"Tab",run:Xg,shift:Qg},{key:"Escape",run:Yg}],$l=T.define({combine(n){return n.length?n[0]:Zg}}),e0=Li.highest(Vn.compute([$l],n=>n.facet($l)));function mb(n,e){return Object.assign(Object.assign({},e),{apply:Jg(n)})}const t0=O.domEventHandlers({mousedown(n,e){let t=e.state.field(Ei,!1),i;if(!t||(i=e.posAtCoords({x:n.clientX,y:n.clientY}))==null)return!1;let s=t.ranges.find(r=>r.from<=i&&r.to>=i);return!s||s.field==t.active?!1:(e.dispatch({selection:qr(t.ranges,s.field),effects:Vi.of(t.ranges.some(r=>r.field>s.field)?new ni(t.ranges,s.field):null)}),!0)}}),Pi={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},St=R.define({map(n,e){let t=e.mapPos(n,-1,oe.TrackAfter);return t??void 0}}),jr=R.define({map(n,e){return e.mapPos(n)}}),Kr=new class extends At{};Kr.startSide=1;Kr.endSide=-1;const Ic=xe.define({create(){return H.empty},update(n,e){if(e.selection){let t=e.state.doc.lineAt(e.selection.main.head).from,i=e.startState.doc.lineAt(e.startState.selection.main.head).from;t!=e.changes.mapPos(i,-1)&&(n=H.empty)}n=n.map(e.changes);for(let t of e.effects)t.is(St)?n=n.update({add:[Kr.range(t.value,t.value+1)]}):t.is(jr)&&(n=n.update({filter:i=>i!=t.value}));return n}});function i0(){return[s0,Ic]}const Ss="()[]{}<>";function Nc(n){for(let e=0;e<Ss.length;e+=2)if(Ss.charCodeAt(e)==n)return Ss.charAt(e+1);return ua(n<128?n:n+1)}function Vc(n,e){return n.languageDataAt("closeBrackets",e)[0]||Pi}const n0=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),s0=O.inputHandler.of((n,e,t,i)=>{if((n0?n.composing:n.compositionStarted)||n.state.readOnly)return!1;let s=n.state.selection.main;if(i.length>2||i.length==2&&De(ue(i,0))==1||e!=s.from||t!=s.to)return!1;let r=l0(n.state,i);return r?(n.dispatch(r),!0):!1}),r0=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let i=Vc(n,n.selection.main.head).brackets||Pi.brackets,s=null,r=n.changeByRange(o=>{if(o.empty){let l=a0(n.doc,o.head);for(let a of i)if(a==l&&Un(n.doc,o.head)==Nc(ue(a,0)))return{changes:{from:o.head-a.length,to:o.head+a.length},range:w.cursor(o.head-a.length)}}return{range:s=o}});return s||e(n.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!s},o0=[{key:"Backspace",run:r0}];function l0(n,e){let t=Vc(n,n.selection.main.head),i=t.brackets||Pi.brackets;for(let s of i){let r=Nc(ue(s,0));if(e==s)return r==s?f0(n,s,i.indexOf(s+s+s)>-1,t):h0(n,s,r,t.before||Pi.before);if(e==r&&Fc(n,n.selection.main.from))return c0(n,s,r)}return null}function Fc(n,e){let t=!1;return n.field(Ic).between(0,n.doc.length,i=>{i==e&&(t=!0)}),t}function Un(n,e){let t=n.sliceString(e,e+2);return t.slice(0,De(ue(t,0)))}function a0(n,e){let t=n.sliceString(e-2,e);return De(ue(t,0))==t.length?t:t.slice(1)}function h0(n,e,t,i){let s=null,r=n.changeByRange(o=>{if(!o.empty)return{changes:[{insert:e,from:o.from},{insert:t,from:o.to}],effects:St.of(o.to+e.length),range:w.range(o.anchor+e.length,o.head+e.length)};let l=Un(n.doc,o.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:e+t,from:o.head},effects:St.of(o.head+e.length),range:w.cursor(o.head+e.length)}:{range:s=o}});return s?null:n.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function c0(n,e,t){let i=null,s=n.selection.ranges.map(r=>r.empty&&Un(n.doc,r.head)==t?w.cursor(r.head+t.length):i=r);return i?null:n.update({selection:w.create(s,n.selection.mainIndex),scrollIntoView:!0,effects:n.selection.ranges.map(({from:r})=>jr.of(r))})}function f0(n,e,t,i){let s=i.stringPrefixes||Pi.stringPrefixes,r=null,o=n.changeByRange(l=>{if(!l.empty)return{changes:[{insert:e,from:l.from},{insert:e,from:l.to}],effects:St.of(l.to+e.length),range:w.range(l.anchor+e.length,l.head+e.length)};let a=l.head,h=Un(n.doc,a),c;if(h==e){if(Wl(n,a))return{changes:{insert:e+e,from:a},effects:St.of(a+e.length),range:w.cursor(a+e.length)};if(Fc(n,a)){let f=t&&n.sliceDoc(a,a+e.length*3)==e+e+e;return{range:w.cursor(a+e.length*(f?3:1)),effects:jr.of(a)}}}else{if(t&&n.sliceDoc(a-2*e.length,a)==e+e&&(c=zl(n,a-2*e.length,s))>-1&&Wl(n,c))return{changes:{insert:e+e+e+e,from:a},effects:St.of(a+e.length),range:w.cursor(a+e.length)};if(n.charCategorizer(a)(h)!=Te.Word&&zl(n,a,s)>-1&&!u0(n,a,e,s))return{changes:{insert:e+e,from:a},effects:St.of(a+e.length),range:w.cursor(a+e.length)}}return{range:r=l}});return r?null:n.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function Wl(n,e){let t=le(n).resolveInner(e+1);return t.parent&&t.from==e}function u0(n,e,t,i){let s=le(n).resolveInner(e,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=n.sliceDoc(s.from,Math.min(s.to,s.from+t.length+r)),a=l.indexOf(t);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let c=s.firstChild;for(;c&&c.from==s.from&&c.to-c.from>t.length+a;){if(n.sliceDoc(c.to-t.length,c.to)==t)return!1;c=c.firstChild}return!0}let h=s.to==e&&s.parent;if(!h)break;s=h}return!1}function zl(n,e,t){let i=n.charCategorizer(e);if(i(n.sliceDoc(e-1,e))!=Te.Word)return e;for(let s of t){let r=e-s.length;if(n.sliceDoc(r,e)==s&&i(n.sliceDoc(r-1,r))!=Te.Word)return r}return-1}const d0=[{key:"Ctrl-Space",run:Wg},{key:"Escape",run:zg},{key:"ArrowDown",run:sn(!0)},{key:"ArrowUp",run:sn(!1)},{key:"PageDown",run:sn(!0,"page")},{key:"PageUp",run:sn(!1,"page")},{key:"Enter",run:$g}];function Ke(){var n=arguments[0];typeof n=="string"&&(n=document.createElement(n));var e=1,t=arguments[1];if(t&&typeof t=="object"&&t.nodeType==null&&!Array.isArray(t)){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var s=t[i];typeof s=="string"?n.setAttribute(i,s):s!=null&&(n[i]=s)}e++}for(;e<arguments.length;e++)Hc(n,arguments[e]);return n}function Hc(n,e){if(typeof e=="string")n.appendChild(document.createTextNode(e));else if(e!=null)if(e.nodeType!=null)n.appendChild(e);else if(Array.isArray(e))for(var t=0;t<e.length;t++)Hc(n,e[t]);else throw new RangeError("Unsupported child node: "+e)}class p0{constructor(e,t,i){this.from=e,this.to=t,this.diagnostic=i}}class kt{constructor(e,t,i){this.diagnostics=e,this.panel=t,this.selected=i}static init(e,t,i){let s=e,r=i.facet(Wt).markerFilter;r&&(s=r(s));let o=L.set(s.map(l=>l.from==l.to||l.from==l.to-1&&i.doc.lineAt(l.from).to==l.from?L.widget({widget:new C0(l),diagnostic:l}).range(l.from):L.mark({attributes:{class:"cm-lintRange cm-lintRange-"+l.severity},diagnostic:l}).range(l.from,l.to)),!0);return new kt(o,t,ei(o))}}function ei(n,e=null,t=0){let i=null;return n.between(t,1e9,(s,r,{spec:o})=>{if(!(e&&o.diagnostic!=e))return i=new p0(s,r,o.diagnostic),!1}),i}function m0(n,e){return!!(n.effects.some(t=>t.is(Ur))||n.changes.touchesRange(e.pos))}function $c(n,e){return n.field(Me,!1)?e:e.concat(R.appendConfig.of([Me,O.decorations.compute([Me],t=>{let{selected:i,panel:s}=t.field(Me);return!i||!s||i.from==i.to?L.none:L.set([y0.range(i.from,i.to)])}),Bd(b0,{hideOn:m0}),M0]))}function g0(n,e){return{effects:$c(n,[Ur.of(e)])}}const Ur=R.define(),Gr=R.define(),Wc=R.define(),Me=xe.define({create(){return new kt(L.none,null,null)},update(n,e){if(e.docChanged){let t=n.diagnostics.map(e.changes),i=null;if(n.selected){let s=e.changes.mapPos(n.selected.from,1);i=ei(t,n.selected.diagnostic,s)||ei(t,null,s)}n=new kt(t,n.panel,i)}for(let t of e.effects)t.is(Ur)?n=kt.init(t.value,n.panel,e.state):t.is(Gr)?n=new kt(n.diagnostics,t.value?Gn.open:null,n.selected):t.is(Wc)&&(n=new kt(n.diagnostics,n.panel,t.value));return n},provide:n=>[sr.from(n,e=>e.panel),O.decorations.from(n,e=>e.diagnostics)]}),y0=L.mark({class:"cm-lintRange cm-lintRange-active"});function b0(n,e,t){let{diagnostics:i}=n.state.field(Me),s=[],r=2e8,o=0;i.between(e-(t<0?1:0),e+(t>0?1:0),(a,h,{spec:c})=>{e>=a&&e<=h&&(a==h||(e>a||t>0)&&(e<h||t<0))&&(s.push(c.diagnostic),r=Math.min(a,r),o=Math.max(h,o))});let l=n.state.facet(Wt).tooltipFilter;return l&&(s=l(s)),s.length?{pos:r,end:o,above:n.state.doc.lineAt(r).to<o,create(){return{dom:w0(n,s)}}}:null}function w0(n,e){return Ke("ul",{class:"cm-tooltip-lint"},e.map(t=>qc(n,t,!1)))}const k0=n=>{let e=n.state.field(Me,!1);(!e||!e.panel)&&n.dispatch({effects:$c(n.state,[Gr.of(!0)])});let t=Ld(n,Gn.open);return t&&t.dom.querySelector(".cm-panel-lint ul").focus(),!0},ql=n=>{let e=n.state.field(Me,!1);return!e||!e.panel?!1:(n.dispatch({effects:Gr.of(!1)}),!0)},x0=n=>{let e=n.state.field(Me,!1);if(!e)return!1;let t=n.state.selection.main,i=e.diagnostics.iter(t.to+1);return!i.value&&(i=e.diagnostics.iter(0),!i.value||i.from==t.from&&i.to==t.to)?!1:(n.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},v0=[{key:"Mod-Shift-m",run:k0},{key:"F8",run:x0}],S0=pe.fromClass(class{constructor(n){this.view=n,this.timeout=-1,this.set=!0;let{delay:e}=n.state.facet(Wt);this.lintTime=Date.now()+e,this.run=this.run.bind(this),this.timeout=setTimeout(this.run,e)}run(){let n=Date.now();if(n<this.lintTime-10)setTimeout(this.run,this.lintTime-n);else{this.set=!1;let{state:e}=this.view,{sources:t}=e.facet(Wt);Promise.all(t.map(i=>Promise.resolve(i(this.view)))).then(i=>{let s=i.reduce((r,o)=>r.concat(o));this.view.state.doc==e.doc&&this.view.dispatch(g0(this.view.state,s))},i=>{_e(this.view.state,i)})}}update(n){let e=n.state.facet(Wt);(n.docChanged||e!=n.startState.facet(Wt))&&(this.lintTime=Date.now()+e.delay,this.set||(this.set=!0,this.timeout=setTimeout(this.run,e.delay)))}force(){this.set&&(this.lintTime=Date.now(),this.run())}destroy(){clearTimeout(this.timeout)}}),Wt=T.define({combine(n){return Object.assign({sources:n.map(e=>e.source)},_t(n.map(e=>e.config),{delay:750,markerFilter:null,tooltipFilter:null}))},enables:S0});function zc(n){let e=[];if(n)e:for(let{name:t}of n){for(let i=0;i<t.length;i++){let s=t[i];if(/[a-zA-Z]/.test(s)&&!e.some(r=>r.toLowerCase()==s.toLowerCase())){e.push(s);continue e}}e.push("")}return e}function qc(n,e,t){var i;let s=t?zc(e.actions):[];return Ke("li",{class:"cm-diagnostic cm-diagnostic-"+e.severity},Ke("span",{class:"cm-diagnosticText"},e.renderMessage?e.renderMessage():e.message),(i=e.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=f=>{f.preventDefault();let u=ei(n.state.field(Me).diagnostics,e);u&&r.apply(n,u.from,u.to)},{name:a}=r,h=s[o]?a.indexOf(s[o]):-1,c=h<0?a:[a.slice(0,h),Ke("u",a.slice(h,h+1)),a.slice(h+1)];return Ke("button",{type:"button",class:"cm-diagnosticAction",onclick:l,onmousedown:l,"aria-label":` Action: ${a}${h<0?"":` (access key "${s[o]})"`}.`},c)}),e.source&&Ke("div",{class:"cm-diagnosticSource"},e.source))}class C0 extends Qe{constructor(e){super(),this.diagnostic=e}eq(e){return e.diagnostic==this.diagnostic}toDOM(){return Ke("span",{class:"cm-lintPoint cm-lintPoint-"+this.diagnostic.severity})}}class jl{constructor(e,t){this.diagnostic=t,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=qc(e,t,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class Gn{constructor(e){this.view=e,this.items=[];let t=s=>{if(s.keyCode==27)ql(this.view),this.view.focus();else if(s.keyCode==38||s.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(s.keyCode==40||s.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(s.keyCode==36)this.moveSelection(0);else if(s.keyCode==35)this.moveSelection(this.items.length-1);else if(s.keyCode==13)this.view.focus();else if(s.keyCode>=65&&s.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=zc(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==s.keyCode){let a=ei(this.view.state.field(Me).diagnostics,r);a&&r.actions[l].apply(e,a.from,a.to)}}else return;s.preventDefault()},i=s=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(s.target)&&this.moveSelection(r)};this.list=Ke("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:t,onclick:i}),this.dom=Ke("div",{class:"cm-panel-lint"},this.list,Ke("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>ql(this.view)},"×")),this.update()}get selectedIndex(){let e=this.view.state.field(Me).selected;if(!e)return-1;for(let t=0;t<this.items.length;t++)if(this.items[t].diagnostic==e.diagnostic)return t;return-1}update(){let{diagnostics:e,selected:t}=this.view.state.field(Me),i=0,s=!1,r=null;for(e.between(0,this.view.state.doc.length,(o,l,{spec:a})=>{let h=-1,c;for(let f=i;f<this.items.length;f++)if(this.items[f].diagnostic==a.diagnostic){h=f;break}h<0?(c=new jl(this.view,a.diagnostic),this.items.splice(i,0,c),s=!0):(c=this.items[h],h>i&&(this.items.splice(i,h-i),s=!0)),t&&c.diagnostic==t.diagnostic?c.dom.hasAttribute("aria-selected")||(c.dom.setAttribute("aria-selected","true"),r=c):c.dom.hasAttribute("aria-selected")&&c.dom.removeAttribute("aria-selected"),i++});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)s=!0,this.items.pop();this.items.length==0&&(this.items.push(new jl(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),s=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:o,panel:l})=>{o.top<l.top?this.list.scrollTop-=l.top-o.top:o.bottom>l.bottom&&(this.list.scrollTop+=o.bottom-l.bottom)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),s&&this.sync()}sync(){let e=this.list.firstChild;function t(){let i=e;e=i.nextSibling,i.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;e!=i.dom;)t();e=i.dom.nextSibling}else this.list.insertBefore(i.dom,e);for(;e;)t()}moveSelection(e){if(this.selectedIndex<0)return;let t=this.view.state.field(Me),i=ei(t.diagnostics,this.items[e].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:Wc.of(i)})}static open(e){return new Gn(e)}}function A0(n,e='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${e}>${encodeURIComponent(n)}</svg>')`}function Cs(n){return A0(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${n}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const M0=O.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:Cs("#d11")},".cm-lintRange-warning":{backgroundImage:Cs("orange")},".cm-lintRange-info":{backgroundImage:Cs("#999")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}}),D0=[$d(),ad(),am(),_p(),Qu(),N.allowMultipleSelections.of(!0),xp(),Rr(Fp,{fallback:!0}),i0(),wd(),vd(),Vn.of([...o0,...og,...ym,...Ep,...d0,...v0])],T0=["standardSQL","msSQL","mySQL","mariaDB","sqlite","cassandra","plSQL","hive","pgSQL","gql","gpSQL","sparkSQL","esper"],Kl={python:()=>ce(()=>import("./index-D8nlTjuq.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]),import.meta.url).then(n=>n.python()),c:()=>ce(()=>import("./clike-CFERkOWz.js"),[],import.meta.url).then(n=>We.define(n.c)),cpp:()=>ce(()=>import("./clike-CFERkOWz.js"),[],import.meta.url).then(n=>We.define(n.cpp)),markdown:async()=>{const[n,e]=await Promise.all([ce(()=>import("./index-BQ3WHOkp.js"),__vite__mapDeps([18,19,1,20,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,21]),import.meta.url),ce(()=>import("./frontmatter-C9bQZm_D.js"),__vite__mapDeps([22,23,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]),import.meta.url)]);return n.markdown({extensions:[e.frontmatter]})},json:()=>ce(()=>import("./index-B1iHl3xK.js"),__vite__mapDeps([24,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]),import.meta.url).then(n=>n.json()),html:()=>ce(()=>import("./index-meEBNtyL.js"),__vite__mapDeps([19,1,20,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,21]),import.meta.url).then(n=>n.html()),css:()=>ce(()=>import("./index-DajRW7Pk.js"),__vite__mapDeps([20,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]),import.meta.url).then(n=>n.css()),javascript:()=>ce(()=>import("./index-CCmsNWjW.js"),__vite__mapDeps([21,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]),import.meta.url).then(n=>n.javascript()),typescript:()=>ce(()=>import("./index-CCmsNWjW.js"),__vite__mapDeps([21,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17]),import.meta.url).then(n=>n.javascript({typescript:!0})),yaml:()=>ce(()=>import("./yaml-DsCXHVTH.js"),[],import.meta.url).then(n=>We.define(n.yaml)),dockerfile:()=>ce(()=>import("./dockerfile-C8FRPiPq.js"),[],import.meta.url).then(n=>We.define(n.dockerFile)),shell:()=>ce(()=>import("./shell-bahM2fKw.js"),[],import.meta.url).then(n=>We.define(n.shell)),r:()=>ce(()=>import("./r-DUYO_cvP.js"),[],import.meta.url).then(n=>We.define(n.r)),sql:()=>ce(()=>import("./sql-C4g8LzGK.js"),[],import.meta.url).then(n=>We.define(n.standardSQL)),...Object.fromEntries(T0.map(n=>["sql-"+n,()=>ce(()=>import("./sql-C4g8LzGK.js"),[],import.meta.url).then(e=>We.define(e[n]))]))},O0={py:"python",md:"markdown",js:"javascript",ts:"typescript",sh:"shell"};async function B0(n){const e=Kl[n]||Kl[O0[n]]||void 0;if(e)return e()}const{SvelteComponent:E0,append:P0,attr:As,binding_callbacks:L0,detach:R0,element:Ul,flush:He,init:_0,insert:I0,noop:Gl,safe_not_equal:N0}=window.__gradio__svelte__internal,{createEventDispatcher:V0,onMount:F0}=window.__gradio__svelte__internal;function H0(n){let e,t,i;return{c(){e=Ul("div"),t=Ul("div"),As(t,"class",i="codemirror-wrapper "+n[0]+" svelte-1sc8eck"),As(e,"class","wrap svelte-1sc8eck")},m(s,r){I0(s,e,r),P0(e,t),n[12](t)},p(s,[r]){r&1&&i!==(i="codemirror-wrapper "+s[0]+" svelte-1sc8eck")&&As(t,"class",i)},i:Gl,o:Gl,d(s){s&&R0(e),n[12](null)}}}function $0(n){let e=n.dom.querySelectorAll(".cm-gutterElement");if(e.length===0)return null;for(var t=0;t<e.length;t++){let i=e[t],s=getComputedStyle(i)?.height??"0px";if(s!="0px")return s}return null}function W0(n,e,t){let{class_names:i=""}=e,{value:s=""}=e,{dark_mode:r}=e,{basic:o=!0}=e,{language:l}=e,{lines:a=5}=e,{extensions:h=[]}=e,{use_tab:c=!0}=e,{readonly:f=!1}=e,{placeholder:u=void 0}=e;const d=V0();let p,g,y;async function b(A){const Q=await B0(A);t(11,p=Q)}function k(A){y&&A!==y.state.doc.toString()&&y.dispatch({changes:{from:0,to:y.state.doc.length,insert:A}})}function v(){y&&y.requestMeasure({read:E})}function S(){const A=new O({parent:g,state:F(s)});return A.dom.addEventListener("focus",C,!0),A.dom.addEventListener("blur",D,!0),A}function C(){d("focus")}function D(){d("blur")}function E(A){let Q=A.dom.querySelectorAll(".cm-gutter"),z=a+1,ne=$0(A);if(!ne)return null;for(var ae=0;ae<Q.length;ae++){let he=Q[ae];he.style.minHeight=`calc(${ne} * ${z})`}return null}function B(A){if(A.docChanged){const z=A.state.doc.toString();t(2,s=z),d("change",z)}y.requestMeasure({read:E})}function I(){return[...K(o,c,u,f,p),P,...J(),...h]}const P=O.theme({"&":{fontSize:"var(--text-sm)",backgroundColor:"var(--border-color-secondary)"},".cm-content":{paddingTop:"5px",paddingBottom:"5px",color:"var(--body-text-color)",fontFamily:"var(--font-mono)",minHeight:"100%"},".cm-gutters":{marginRight:"1px",borderRight:"1px solid var(--border-color-primary)",backgroundColor:"var(--block-background-fill);",color:"var(--body-text-color-subdued)"},".cm-focused":{outline:"none"},".cm-scroller":{height:"auto"},".cm-cursor":{borderLeftColor:"var(--body-text-color)"}});function F(A){return N.create({doc:A??void 0,extensions:I()})}function K(A,Q,z,ne,ae){const he=[O.editable.of(!ne),N.readOnly.of(ne),O.contentAttributes.of({"aria-label":"Code input container"})];return A&&he.push(D0),Q&&he.push(Vn.of([lg])),z&&he.push(md(z)),ae&&he.push(ae),he.push(O.updateListener.of(B)),he}function J(){const A=[];return r?A.push(gg):A.push(Cg),A}function ge(){y?.dispatch({effects:R.reconfigure.of(I())})}F0(()=>(y=S(),()=>y?.destroy()));function te(A){L0[A?"unshift":"push"](()=>{g=A,t(1,g)})}return n.$$set=A=>{"class_names"in A&&t(0,i=A.class_names),"value"in A&&t(2,s=A.value),"dark_mode"in A&&t(3,r=A.dark_mode),"basic"in A&&t(4,o=A.basic),"language"in A&&t(5,l=A.language),"lines"in A&&t(6,a=A.lines),"extensions"in A&&t(7,h=A.extensions),"use_tab"in A&&t(8,c=A.use_tab),"readonly"in A&&t(9,f=A.readonly),"placeholder"in A&&t(10,u=A.placeholder)},n.$$.update=()=>{n.$$.dirty&32&&b(l),n.$$.dirty&2560&&ge(),n.$$.dirty&4&&k(s)},v(),[i,g,s,r,o,l,a,h,c,f,u,p,te]}class z0 extends E0{constructor(e){super(),_0(this,e,W0,H0,N0,{class_names:0,value:2,dark_mode:3,basic:4,language:5,lines:6,extensions:7,use_tab:8,readonly:9,placeholder:10})}get class_names(){return this.$$.ctx[0]}set class_names(e){this.$$set({class_names:e}),He()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),He()}get dark_mode(){return this.$$.ctx[3]}set dark_mode(e){this.$$set({dark_mode:e}),He()}get basic(){return this.$$.ctx[4]}set basic(e){this.$$set({basic:e}),He()}get language(){return this.$$.ctx[5]}set language(e){this.$$set({language:e}),He()}get lines(){return this.$$.ctx[6]}set lines(e){this.$$set({lines:e}),He()}get extensions(){return this.$$.ctx[7]}set extensions(e){this.$$set({extensions:e}),He()}get use_tab(){return this.$$.ctx[8]}set use_tab(e){this.$$set({use_tab:e}),He()}get readonly(){return this.$$.ctx[9]}set readonly(e){this.$$set({readonly:e}),He()}get placeholder(){return this.$$.ctx[10]}set placeholder(e){this.$$set({placeholder:e}),He()}}const jc=z0,{SvelteComponent:q0,attr:mi,check_outros:j0,create_component:Kc,destroy_component:Uc,detach:Gc,element:Jc,flush:K0,group_outros:U0,init:G0,insert:Yc,listen:J0,mount_component:Xc,safe_not_equal:Y0,toggle_class:Jl,transition_in:Bn,transition_out:En}=window.__gradio__svelte__internal,{onDestroy:X0}=window.__gradio__svelte__internal;function Q0(n){let e,t,i;return t=new ra({}),{c(){e=Jc("span"),Kc(t.$$.fragment),mi(e,"class","check svelte-1ak3y7y")},m(s,r){Yc(s,e,r),Xc(t,e,null),i=!0},i(s){i||(Bn(t.$$.fragment,s),i=!0)},o(s){En(t.$$.fragment,s),i=!1},d(s){s&&Gc(e),Uc(t)}}}function Z0(n){let e,t;return e=new sf({}),{c(){Kc(e.$$.fragment)},m(i,s){Xc(e,i,s),t=!0},i(i){t||(Bn(e.$$.fragment,i),t=!0)},o(i){En(e.$$.fragment,i),t=!1},d(i){Uc(e,i)}}}function ey(n){let e,t,i,s,r,o,l;const a=[Z0,Q0],h=[];function c(f,u){return f[0]?1:0}return t=c(n),i=h[t]=a[t](n),{c(){e=Jc("button"),i.c(),mi(e,"title","Copy message"),mi(e,"aria-label",s=n[0]?"Message copied":"Copy Message"),mi(e,"class","svelte-1ak3y7y"),Jl(e,"copied",n[0])},m(f,u){Yc(f,e,u),h[t].m(e,null),r=!0,o||(l=J0(e,"click",n[1]),o=!0)},p(f,[u]){let d=t;t=c(f),t!==d&&(U0(),En(h[d],1,1,()=>{h[d]=null}),j0(),i=h[t],i||(i=h[t]=a[t](f),i.c()),Bn(i,1),i.m(e,null)),(!r||u&1&&s!==(s=f[0]?"Message copied":"Copy Message"))&&mi(e,"aria-label",s),(!r||u&1)&&Jl(e,"copied",f[0])},i(f){r||(Bn(i),r=!0)},o(f){En(i),r=!1},d(f){f&&Gc(e),h[t].d(),o=!1,l()}}}function ty(n,e,t){let i=!1,{value:s}=e,r;function o(){t(0,i=!0),r&&clearTimeout(r),r=setTimeout(()=>{t(0,i=!1)},2e3)}async function l(){"clipboard"in navigator&&(await navigator.clipboard.writeText(s),o())}return X0(()=>{r&&clearTimeout(r)}),n.$$set=a=>{"value"in a&&t(2,s=a.value)},[i,l,s]}class iy extends q0{constructor(e){super(),G0(this,e,ty,ey,Y0,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),K0()}}const Qc=iy,{SvelteComponent:ny,add_render_callback:sy,attr:Zc,check_outros:ry,create_bidirectional_transition:Yl,create_component:Jr,destroy_component:Yr,detach:Pn,element:ef,empty:oy,flush:Xl,group_outros:ly,init:ay,insert:Ln,mount_component:Xr,safe_not_equal:hy,space:cy,transition_in:zt,transition_out:xi}=window.__gradio__svelte__internal,{onDestroy:fy}=window.__gradio__svelte__internal;function Ql(n){let e,t,i,s;return t=new ra({}),{c(){e=ef("span"),Jr(t.$$.fragment),Zc(e,"class","check svelte-1vwgyfd")},m(r,o){Ln(r,e,o),Xr(t,e,null),s=!0},i(r){s||(zt(t.$$.fragment,r),r&&sy(()=>{s&&(i||(i=Yl(e,Zr,{},!0)),i.run(1))}),s=!0)},o(r){xi(t.$$.fragment,r),r&&(i||(i=Yl(e,Zr,{},!1)),i.run(0)),s=!1},d(r){r&&Pn(e),Yr(t),r&&i&&i.end()}}}function uy(n){let e,t,i,s;e=new lf({});let r=n[0]&&Ql();return{c(){Jr(e.$$.fragment),t=cy(),r&&r.c(),i=oy()},m(o,l){Xr(e,o,l),Ln(o,t,l),r&&r.m(o,l),Ln(o,i,l),s=!0},p(o,l){o[0]?r?l&1&&zt(r,1):(r=Ql(),r.c(),zt(r,1),r.m(i.parentNode,i)):r&&(ly(),xi(r,1,1,()=>{r=null}),ry())},i(o){s||(zt(e.$$.fragment,o),zt(r),s=!0)},o(o){xi(e.$$.fragment,o),xi(r),s=!1},d(o){o&&(Pn(t),Pn(i)),Yr(e,o),r&&r.d(o)}}}function dy(n){let e,t,i;return t=new af({props:{download:"file."+n[2],href:n[1],$$slots:{default:[uy]},$$scope:{ctx:n}}}),t.$on("click",n[3]),{c(){e=ef("div"),Jr(t.$$.fragment),Zc(e,"class","container svelte-1vwgyfd")},m(s,r){Ln(s,e,r),Xr(t,e,null),i=!0},p(s,[r]){const o={};r&4&&(o.download="file."+s[2]),r&2&&(o.href=s[1]),r&129&&(o.$$scope={dirty:r,ctx:s}),t.$set(o)},i(s){i||(zt(t.$$.fragment,s),i=!0)},o(s){xi(t.$$.fragment,s),i=!1},d(s){s&&Pn(e),Yr(t)}}}function py(n){return{py:"py",python:"py",md:"md",markdown:"md",json:"json",html:"html",css:"css",js:"js",javascript:"js",ts:"ts",typescript:"ts",yaml:"yaml",yml:"yml",dockerfile:"dockerfile",sh:"sh",shell:"sh",r:"r",c:"c",cpp:"cpp"}[n]||"txt"}function my(n,e,t){let i,s,{value:r}=e,{language:o}=e,l=!1,a;function h(){t(0,l=!0),a&&clearTimeout(a),a=setTimeout(()=>{t(0,l=!1)},2e3)}return fy(()=>{a&&clearTimeout(a)}),n.$$set=c=>{"value"in c&&t(4,r=c.value),"language"in c&&t(5,o=c.language)},n.$$.update=()=>{n.$$.dirty&32&&t(2,i=py(o)),n.$$.dirty&16&&t(1,s=URL.createObjectURL(new Blob([r])))},[l,s,i,h,r,o]}class gy extends ny{constructor(e){super(),ay(this,e,my,dy,hy,{value:4,language:5})}get value(){return this.$$.ctx[4]}set value(e){this.$$set({value:e}),Xl()}get language(){return this.$$.ctx[5]}set language(e){this.$$set({language:e}),Xl()}}const tf=gy,{SvelteComponent:yy,append:by,attr:wy,create_component:Zl,destroy_component:ea,detach:ky,element:xy,flush:ta,init:vy,insert:Sy,mount_component:ia,safe_not_equal:Cy,space:Ay,transition_in:na,transition_out:sa}=window.__gradio__svelte__internal;function My(n){let e,t,i,s,r;return t=new tf({props:{value:n[0],language:n[1]}}),s=new Qc({props:{value:n[0]}}),{c(){e=xy("div"),Zl(t.$$.fragment),i=Ay(),Zl(s.$$.fragment),wy(e,"class","svelte-1yin446")},m(o,l){Sy(o,e,l),ia(t,e,null),by(e,i),ia(s,e,null),r=!0},p(o,[l]){const a={};l&1&&(a.value=o[0]),l&2&&(a.language=o[1]),t.$set(a);const h={};l&1&&(h.value=o[0]),s.$set(h)},i(o){r||(na(t.$$.fragment,o),na(s.$$.fragment,o),r=!0)},o(o){sa(t.$$.fragment,o),sa(s.$$.fragment,o),r=!1},d(o){o&&ky(e),ea(t),ea(s)}}}function Dy(n,e,t){let{value:i}=e,{language:s}=e;return n.$$set=r=>{"value"in r&&t(0,i=r.value),"language"in r&&t(1,s=r.language)},[i,s]}class Ty extends yy{constructor(e){super(),vy(this,e,Dy,My,Cy,{value:0,language:1})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ta()}get language(){return this.$$.ctx[1]}set language(e){this.$$set({language:e}),ta()}}const nf=Ty,{SvelteComponent:Oy,add_flush_callback:By,assign:Ey,bind:Py,binding_callbacks:Ly,check_outros:Ry,create_component:Et,destroy_component:Pt,detach:fn,empty:_y,flush:ve,get_spread_object:Iy,get_spread_update:Ny,group_outros:Vy,init:Fy,insert:un,mount_component:Lt,safe_not_equal:Hy,space:yr,transition_in:tt,transition_out:it}=window.__gradio__svelte__internal,{afterUpdate:$y}=window.__gradio__svelte__internal;function Wy(n){let e,t,i,s,r;e=new nf({props:{language:n[2],value:n[0]}});function o(a){n[17](a)}let l={language:n[2],lines:n[3],dark_mode:n[12],readonly:!n[11]};return n[0]!==void 0&&(l.value=n[0]),i=new jc({props:l}),Ly.push(()=>Py(i,"value",o)),i.$on("blur",n[18]),i.$on("focus",n[19]),{c(){Et(e.$$.fragment),t=yr(),Et(i.$$.fragment)},m(a,h){Lt(e,a,h),un(a,t,h),Lt(i,a,h),r=!0},p(a,h){const c={};h&4&&(c.language=a[2]),h&1&&(c.value=a[0]),e.$set(c);const f={};h&4&&(f.language=a[2]),h&8&&(f.lines=a[3]),h&2048&&(f.readonly=!a[11]),!s&&h&1&&(s=!0,f.value=a[0],By(()=>s=!1)),i.$set(f)},i(a){r||(tt(e.$$.fragment,a),tt(i.$$.fragment,a),r=!0)},o(a){it(e.$$.fragment,a),it(i.$$.fragment,a),r=!1},d(a){a&&fn(t),Pt(e,a),Pt(i,a)}}}function zy(n){let e,t;return e=new cf({props:{unpadded_box:!0,size:"large",$$slots:{default:[qy]},$$scope:{ctx:n}}}),{c(){Et(e.$$.fragment)},m(i,s){Lt(e,i,s),t=!0},p(i,s){const r={};s&2097152&&(r.$$scope={dirty:s,ctx:i}),e.$set(r)},i(i){t||(tt(e.$$.fragment,i),t=!0)},o(i){it(e.$$.fragment,i),t=!1},d(i){Pt(e,i)}}}function qy(n){let e,t;return e=new oa({}),{c(){Et(e.$$.fragment)},m(i,s){Lt(e,i,s),t=!0},i(i){t||(tt(e.$$.fragment,i),t=!0)},o(i){it(e.$$.fragment,i),t=!1},d(i){Pt(e,i)}}}function jy(n){let e,t,i,s,r,o,l,a;const h=[{autoscroll:n[1].autoscroll},{i18n:n[1].i18n},n[9]];let c={};for(let p=0;p<h.length;p+=1)c=Ey(c,h[p]);e=new rf({props:c}),e.$on("clear_status",n[16]),i=new hf({props:{Icon:oa,show_label:n[8],label:n[7],float:!1}});const f=[zy,Wy],u=[];function d(p,g){return!p[0]&&!p[11]?0:1}return r=d(n),o=u[r]=f[r](n),{c(){Et(e.$$.fragment),t=yr(),Et(i.$$.fragment),s=yr(),o.c(),l=_y()},m(p,g){Lt(e,p,g),un(p,t,g),Lt(i,p,g),un(p,s,g),u[r].m(p,g),un(p,l,g),a=!0},p(p,g){const y=g&514?Ny(h,[g&2&&{autoscroll:p[1].autoscroll},g&2&&{i18n:p[1].i18n},g&512&&Iy(p[9])]):{};e.$set(y);const b={};g&256&&(b.show_label=p[8]),g&128&&(b.label=p[7]),i.$set(b);let k=r;r=d(p),r===k?u[r].p(p,g):(Vy(),it(u[k],1,1,()=>{u[k]=null}),Ry(),o=u[r],o?o.p(p,g):(o=u[r]=f[r](p),o.c()),tt(o,1),o.m(l.parentNode,l))},i(p){a||(tt(e.$$.fragment,p),tt(i.$$.fragment,p),tt(o),a=!0)},o(p){it(e.$$.fragment,p),it(i.$$.fragment,p),it(o),a=!1},d(p){p&&(fn(t),fn(s),fn(l)),Pt(e,p),Pt(i,p),u[r].d(p)}}}function Ky(n){let e,t;return e=new of({props:{height:n[13]?void 0:n[14]*25+4,variant:"solid",padding:!1,elem_id:n[4],elem_classes:n[5],visible:n[6],scale:n[10],$$slots:{default:[jy]},$$scope:{ctx:n}}}),{c(){Et(e.$$.fragment)},m(i,s){Lt(e,i,s),t=!0},p(i,[s]){const r={};s&16&&(r.elem_id=i[4]),s&32&&(r.elem_classes=i[5]),s&64&&(r.visible=i[6]),s&1024&&(r.scale=i[10]),s&2100111&&(r.$$scope={dirty:s,ctx:i}),e.$set(r)},i(i){t||(tt(e.$$.fragment,i),t=!0)},o(i){it(e.$$.fragment,i),t=!1},d(i){Pt(e,i)}}}function Uy(n,e,t){let{gradio:i}=e,{value:s=""}=e,{value_is_output:r=!1}=e,{language:o=""}=e,{lines:l=5}=e,{elem_id:a=""}=e,{elem_classes:h=[]}=e,{visible:c=!0}=e,{label:f=i.i18n("code.code")}=e,{show_label:u=!0}=e,{loading_status:d}=e,{scale:p=null}=e,{interactive:g}=e,y=i.theme==="dark";function b(){i.dispatch("change",s),r||i.dispatch("input")}$y(()=>{t(15,r=!1)});const k=typeof window<"u",v=g?l:10.35,S=()=>i.dispatch("clear_status",d);function C(B){s=B,t(0,s)}const D=()=>i.dispatch("blur"),E=()=>i.dispatch("focus");return n.$$set=B=>{"gradio"in B&&t(1,i=B.gradio),"value"in B&&t(0,s=B.value),"value_is_output"in B&&t(15,r=B.value_is_output),"language"in B&&t(2,o=B.language),"lines"in B&&t(3,l=B.lines),"elem_id"in B&&t(4,a=B.elem_id),"elem_classes"in B&&t(5,h=B.elem_classes),"visible"in B&&t(6,c=B.visible),"label"in B&&t(7,f=B.label),"show_label"in B&&t(8,u=B.show_label),"loading_status"in B&&t(9,d=B.loading_status),"scale"in B&&t(10,p=B.scale),"interactive"in B&&t(11,g=B.interactive)},n.$$.update=()=>{n.$$.dirty&1&&b()},[s,i,o,l,a,h,c,f,u,d,p,g,y,k,v,r,S,C,D,E]}class Gy extends Oy{constructor(e){super(),Fy(this,e,Uy,Ky,Hy,{gradio:1,value:0,value_is_output:15,language:2,lines:3,elem_id:4,elem_classes:5,visible:6,label:7,show_label:8,loading_status:9,scale:10,interactive:11})}get gradio(){return this.$$.ctx[1]}set gradio(e){this.$$set({gradio:e}),ve()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ve()}get value_is_output(){return this.$$.ctx[15]}set value_is_output(e){this.$$set({value_is_output:e}),ve()}get language(){return this.$$.ctx[2]}set language(e){this.$$set({language:e}),ve()}get lines(){return this.$$.ctx[3]}set lines(e){this.$$set({lines:e}),ve()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),ve()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),ve()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),ve()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),ve()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),ve()}get loading_status(){return this.$$.ctx[9]}set loading_status(e){this.$$set({loading_status:e}),ve()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),ve()}get interactive(){return this.$$.ctx[11]}set interactive(e){this.$$set({interactive:e}),ve()}}const gb=Object.freeze(Object.defineProperty({__proto__:null,BaseCode:jc,BaseCopy:Qc,BaseDownload:tf,BaseExample:ff,BaseWidget:nf,default:Gy},Symbol.toStringTag,{value:"Module"}));export{cp as A,pb as B,Mg as C,Wd as D,w as E,gb as F,Y as I,lr as L,Tr as N,Oh as P,We as S,W as T,we as a,_ as b,db as c,cb as d,hb as e,Sp as f,qe as g,le as h,dp as i,Li as j,Vn as k,Be as l,Ph as m,vt as n,vp as o,lb as p,Rh as q,Qt as r,rp as s,m as t,Wp as u,O as v,ub as w,ob as x,mb as y,fb as z};
//# sourceMappingURL=Index-CKgowMni.js.map
