{"version": 3, "file": "Example-BT2jlY4j.js", "sources": ["../../../../js/dataframe/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: (string | number)[][] | string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let index: number;\n\n\tlet hovered = false;\n\tlet loaded = Array.isArray(value);\n</script>\n\n{#if loaded}\n\t<!-- TODO: fix-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t<div\n\t\tclass:table={type === \"table\"}\n\t\tclass:gallery={type === \"gallery\"}\n\t\tclass:selected\n\t\ton:mouseenter={() => (hovered = true)}\n\t\ton:mouseleave={() => (hovered = false)}\n\t>\n\t\t{#if typeof value === \"string\"}\n\t\t\t{value}\n\t\t{:else}\n\t\t\t<table class=\"\">\n\t\t\t\t{#each value.slice(0, 3) as row, i}\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t{#each row.slice(0, 3) as cell, j}\n\t\t\t\t\t\t\t<td>{cell}</td>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t{#if row.length > 3}\n\t\t\t\t\t\t\t<td>…</td>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</tr>\n\t\t\t\t{/each}\n\t\t\t\t{#if value.length > 3}\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"overlay\"\n\t\t\t\t\t\tclass:odd={index % 2 != 0}\n\t\t\t\t\t\tclass:even={index % 2 == 0}\n\t\t\t\t\t\tclass:button={type === \"gallery\"}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t</table>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\ttable {\n\t\tposition: relative;\n\t}\n\n\ttd {\n\t\tborder: 1px solid var(--table-border-color);\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.selected td {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.table {\n\t\tdisplay: inline-block;\n\t\tmargin: 0 auto;\n\t}\n\n\t.gallery td:first-child {\n\t\tborder-left: none;\n\t}\n\n\t.gallery tr:first-child td {\n\t\tborder-top: none;\n\t}\n\n\t.gallery td:last-child {\n\t\tborder-right: none;\n\t}\n\n\t.gallery tr:last-child td {\n\t\tborder-bottom: none;\n\t}\n\n\t.overlay {\n\t\t--gradient-to: transparent;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(to bottom, transparent, var(--gradient-to));\n\t\twidth: var(--size-full);\n\t\theight: 50%;\n\t}\n\n\t/* i dont know what i've done here but it is what it is */\n\t.odd {\n\t\t--gradient-to: var(--table-even-background-fill);\n\t}\n\n\t.even {\n\t\t--gradient-to: var(--table-odd-background-fill);\n\t}\n\n\t.button {\n\t\t--gradient-to: var(--background-fill-primary);\n\t}\n</style>\n"], "names": ["ctx", "create_if_block_1", "toggle_class", "div", "insert", "target", "anchor", "each_value", "ensure_array_like", "i", "create_if_block_2", "table", "td", "set_data", "t_value", "each_value_1", "create_if_block_3", "tr", "create_if_block", "if_block", "dirty", "value", "$$props", "type", "selected", "index", "hovered", "loaded", "mouseenter_handler", "$$invalidate", "mouseleave_handler"], "mappings": "4ZAoBc,OAAA,OAAAA,MAAU,SAAQC,gFANjBC,EAAAC,EAAA,QAAAH,OAAS,OAAO,EACdE,EAAAC,EAAA,UAAAH,OAAS,SAAS,+BAFlCI,EA+BKC,EAAAF,EAAAG,CAAA,uJA9BSJ,EAAAC,EAAA,QAAAH,OAAS,OAAO,OACdE,EAAAC,EAAA,UAAAH,OAAS,SAAS,kFASxBO,EAAAC,EAAAR,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAArB,OAAIS,GAAA,yBAUDT,EAAK,CAAA,EAAC,OAAS,GAACU,EAAAV,CAAA,qHAXtBI,EAmBOC,EAAAM,EAAAL,CAAA,yFAlBCC,EAAAC,EAAAR,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAArB,OAAIS,GAAA,EAAA,8GAAJ,OAUGT,EAAK,CAAA,EAAC,OAAS,8HAbpBA,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,wCAMGA,EAAI,EAAA,EAAA,uEAATI,EAAcC,EAAAO,EAAAN,CAAA,6BAATN,EAAI,EAAA,EAAA,KAAAa,EAAA,EAAAC,CAAA,mHAGTV,EAASC,EAAAO,EAAAN,CAAA,wCAJHS,EAAAP,EAAAR,EAAI,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAAnB,OAAIS,GAAA,yBAGDT,EAAG,CAAA,EAAC,OAAS,GAACgB,EAAA,mFAJpBZ,EAOIC,EAAAY,EAAAX,CAAA,yFANIS,EAAAP,EAAAR,EAAI,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAAnB,OAAIS,GAAA,EAAA,8GAAJ,OAGGT,EAAG,CAAA,EAAC,OAAS,4KAQPA,EAAK,CAAA,EAAG,GAAK,CAAC,aACbA,EAAK,CAAA,EAAG,GAAK,CAAC,EACZE,EAAAC,EAAA,SAAAH,OAAS,SAAS,UAJjCI,EAKCC,EAAAF,EAAAG,CAAA,yBAHWN,EAAK,CAAA,EAAG,GAAK,CAAC,kBACbA,EAAK,CAAA,EAAG,GAAK,CAAC,OACZE,EAAAC,EAAA,SAAAH,OAAS,SAAS,wCA7BjCA,EAAM,CAAA,GAAAkB,EAAAlB,CAAA,mEAANA,EAAM,CAAA,GAAAmB,EAAA,EAAAnB,EAAAoB,CAAA,0DATC,MAAAC,CAAqC,EAAAC,GACrC,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,GAChB,MAAAG,CAAa,EAAAH,EAEpBI,EAAU,GACVC,EAAS,MAAM,QAAQN,CAAK,EAUT,MAAAO,EAAA,IAAAC,EAAA,EAAAH,EAAU,EAAI,EACdI,EAAA,IAAAD,EAAA,EAAAH,EAAU,EAAK"}