{"version": 3, "file": "Index-CEDCRqJB.js", "sources": ["../../../../js/downloadbutton/shared/DownloadButton.svelte", "../../../../js/downloadbutton/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { type FileData } from \"@gradio/client\";\n\timport { BaseButton } from \"@gradio/button\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let value: null | FileData;\n\texport let icon: null | FileData;\n\texport let disabled = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\tconst dispatch = createEventDispatcher();\n\n\tfunction download_file(): void {\n\t\tdispatch(\"click\");\n\t\tif (!value?.url) {\n\t\t\treturn;\n\t\t}\n\t\tlet file_name;\n\t\tif (!value.orig_name && value.url) {\n\t\t\tconst parts = value.url.split(\"/\");\n\t\t\tfile_name = parts[parts.length - 1];\n\t\t\tfile_name = file_name.split(\"?\")[0].split(\"#\")[0];\n\t\t} else {\n\t\t\tfile_name = value.orig_name;\n\t\t}\n\t\tconst a = document.createElement(\"a\");\n\t\ta.href = value.url;\n\t\ta.download = file_name || \"file\";\n\t\tdocument.body.appendChild(a);\n\t\ta.click();\n\t\tdocument.body.removeChild(a);\n\t}\n</script>\n\n<BaseButton\n\t{size}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\ton:click={download_file}\n\t{scale}\n\t{min_width}\n\t{disabled}\n>\n\t{#if icon}\n\t\t<img class=\"button-icon\" src={icon.url} alt={`${value} icon`} />\n\t{/if}\n\t<slot />\n</BaseButton>\n\n<style>\n\t.button-icon {\n\t\twidth: var(--text-xl);\n\t\theight: var(--text-xl);\n\t\tmargin-right: var(--spacing-xl);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseButton } from \"./shared/DownloadButton.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { type FileData } from \"@gradio/client\";\n\n\timport DownloadButton from \"./shared/DownloadButton.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let interactive: boolean;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let icon: null | FileData = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string | null = null;\n\texport let gradio: Gradio<{\n\t\tclick: never;\n\t}>;\n</script>\n\n<DownloadButton\n\t{value}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{size}\n\t{scale}\n\t{icon}\n\t{min_width}\n\t{visible}\n\tdisabled={!interactive}\n\ton:click={() => gradio.dispatch(\"click\")}\n>\n\t{label ? gradio.i18n(label) : \"\"}\n</DownloadButton>\n"], "names": ["createEventDispatcher", "src_url_equal", "img", "img_src_value", "ctx", "attr", "insert", "target", "anchor", "dirty", "create_if_block", "elem_id", "$$props", "elem_classes", "visible", "variant", "size", "value", "icon", "disabled", "scale", "min_width", "dispatch", "download_file", "file_name", "parts", "a", "set_data", "t", "t_value", "interactive", "label", "gradio"], "mappings": "2bACU,CAAA,sBAAAA,CAAA,SAAqC,kHAkDfC,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,GAAAC,EAAAH,EAAA,MAAAC,CAAA,iBAAUC,EAAK,CAAA,CAAA,OAAA,UAArDE,EAA+DC,EAAAL,EAAAM,CAAA,UAAjCC,EAAA,IAAA,CAAAR,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,gCAAUA,EAAK,CAAA,CAAA,+DADjDA,EAAI,CAAA,GAAAM,EAAAN,CAAA,qIAAJA,EAAI,CAAA,saALCA,EAAa,EAAA,CAAA,2aAxCZ,CAAA,QAAAO,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,QAAAG,EAA4C,WAAW,EAAAH,EACvD,CAAA,KAAAI,EAAoB,IAAI,EAAAJ,GACxB,MAAAK,CAAsB,EAAAL,GACtB,KAAAM,CAAqB,EAAAN,EACrB,CAAA,SAAAO,EAAW,EAAK,EAAAP,EAChB,CAAA,MAAAQ,EAAuB,IAAI,EAAAR,EAC3B,CAAA,UAAAS,EAAgC,MAAS,EAAAT,EAC9C,MAAAU,EAAWtB,aAERuB,GAAa,CAEhB,GADLD,EAAS,OAAO,EACX,CAAAL,GAAO,eAGRO,EACC,GAAA,CAAAP,EAAM,WAAaA,EAAM,IAAG,CAC1B,MAAAQ,EAAQR,EAAM,IAAI,MAAM,GAAG,EACjCO,EAAYC,EAAMA,EAAM,OAAS,CAAC,EAClCD,EAAYA,EAAU,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,OAEhDA,EAAYP,EAAM,UAEb,MAAAS,EAAI,SAAS,cAAc,GAAG,EACpCA,EAAE,KAAOT,EAAM,IACfS,EAAE,SAAWF,GAAa,OAC1B,SAAS,KAAK,YAAYE,CAAC,EAC3BA,EAAE,MAAK,EACP,SAAS,KAAK,YAAYA,CAAC,ujDCI3BtB,EAAK,EAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,EAAA,CAAA,EAAI,IAAE,iEAA/BA,EAAK,EAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,EAAA,CAAA,EAAI,IAAE,KAAAuB,GAAAC,EAAAC,CAAA,4LAHrBzB,EAAW,CAAA,iWAAXA,EAAW,CAAA,uJA1BX,GAAA,CAAA,QAAAO,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAK,CAAsB,EAAAL,EACtB,CAAA,QAAAG,EAA4C,WAAW,EAAAH,GACvD,YAAAkB,CAAoB,EAAAlB,EACpB,CAAA,KAAAI,EAAoB,IAAI,EAAAJ,EACxB,CAAA,MAAAQ,EAAuB,IAAI,EAAAR,EAC3B,CAAA,KAAAM,EAAwB,IAAI,EAAAN,EAC5B,CAAA,UAAAS,EAAgC,MAAS,EAAAT,EACzC,CAAA,MAAAmB,EAAuB,IAAI,EAAAnB,GAC3B,OAAAoB,CAET,EAAApB,cAccoB,EAAO,SAAS,OAAO"}