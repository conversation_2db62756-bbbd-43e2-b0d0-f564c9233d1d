{"version": 3, "file": "Example-CqL1e7EB.js", "sources": ["../../../../js/number/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value !== null ? value : \"\"}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["t_value", "ctx", "toggle_class", "div", "insert", "target", "anchor", "dirty", "set_data", "t", "value", "$$props", "type", "selected"], "mappings": "mMAWEA,GAAAC,EAAU,CAAA,IAAA,KAAOA,KAAQ,IAAE,gEAJfC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,mBADHC,EAAA,GAAAP,KAAAA,GAAAC,EAAU,CAAA,IAAA,KAAOA,KAAQ,IAAE,KAAAO,EAAAC,EAAAT,CAAA,OAJfE,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0EAPtB,MAAAS,CAAoB,EAAAC,GACpB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF"}