{"name": "@gradio/row", "version": "0.1.5", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "main_changeset": true, "devDependencies": {"@gradio/preview": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/utils": "workspace:^"}, "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./package.json": "./package.json"}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/row"}}