import"./Index-DB1XLvMK.js";import{B as he}from"./BlockLabel-BlSr62f_.js";import{E as we}from"./Empty-BgF7sXBn.js";import{F as le}from"./File-BQ_9P3Ye.js";import{U as ke}from"./Upload-1-QDTAlg.js";import{M as pe}from"./ModifyUpload-CEEIIKhx.js";import{D as ve}from"./DownloadLink-CHpWw1Ex.js";const ne=i=>{let e=["B","KB","MB","GB","PB"],l=0;for(;i>1024;)i/=1024,l++;let t=e[l];return i.toFixed(1)+"&nbsp;"+t},{HtmlTag:$e,SvelteComponent:ye,append:k,attr:p,check_outros:me,create_component:ze,destroy_component:qe,detach:M,element:F,ensure_array_like:ie,flush:N,group_outros:ge,init:Fe,insert:P,listen:Z,mount_component:Be,noop:se,outro_and_destroy_block:Ce,run_all:Ee,safe_not_equal:Ae,set_data:x,set_style:oe,space:T,text:H,toggle_class:ae,transition_in:K,transition_out:O,update_keyed_each:De}=window.__gradio__svelte__internal,{createEventDispatcher:Se}=window.__gradio__svelte__internal;function re(i,e,l){const t=i.slice();return t[12]=e[l],t[14]=l,t}function Ue(i){let e=i[2]("file.uploading")+"",l;return{c(){l=H(e)},m(t,n){P(t,l,n)},p(t,n){n&4&&e!==(e=t[2]("file.uploading")+"")&&x(l,e)},i:se,o:se,d(t){t&&M(l)}}}function Me(i){let e,l;return e=new ve({props:{href:i[12].url,download:i[6]&&window.__is_colab__?null:i[12].orig_name,$$slots:{default:[Pe]},$$scope:{ctx:i}}}),{c(){ze(e.$$.fragment)},m(t,n){Be(e,t,n),l=!0},p(t,n){const s={};n&8&&(s.href=t[12].url),n&8&&(s.download=t[6]&&window.__is_colab__?null:t[12].orig_name),n&32776&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){l||(K(e.$$.fragment,t),l=!0)},o(t){O(e.$$.fragment,t),l=!1},d(t){qe(e,t)}}}function Pe(i){let e,l=(i[12].size!=null?ne(i[12].size):"(size unknown)")+"",t;return{c(){e=new $e(!1),t=H(" ⇣"),e.a=t},m(n,s){e.m(l,n,s),P(n,t,s)},p(n,s){s&8&&l!==(l=(n[12].size!=null?ne(n[12].size):"(size unknown)")+"")&&e.p(l)},d(n){n&&(e.d(),M(t))}}}function ue(i){let e,l,t,n;function s(){return i[8](i[14])}function a(...f){return i[9](i[14],...f)}return{c(){e=F("td"),l=F("button"),l.textContent="×",p(l,"class","label-clear-button svelte-18wv37q"),p(l,"aria-label","Remove this file"),p(e,"class","svelte-18wv37q")},m(f,c){P(f,e,c),k(e,l),t||(n=[Z(l,"click",s),Z(l,"keydown",a)],t=!0)},p(f,c){i=f},d(f){f&&M(e),t=!1,Ee(n)}}}function _e(i,e){let l,t,n,s=e[12].filename_stem+"",a,f,c,r=e[12].filename_ext+"",u,o,m,d,g,h,b,v,y,B,C;const I=[Me,Ue],z=[];function L(_,q){return _[12].url?0:1}g=L(e),h=z[g]=I[g](e);let w=e[3].length>1&&ue(e);function X(..._){return e[10](e[14],..._)}return{key:i,first:null,c(){l=F("tr"),t=F("td"),n=F("span"),a=H(s),f=T(),c=F("span"),u=H(r),m=T(),d=F("td"),h.c(),b=T(),w&&w.c(),v=T(),p(n,"class","stem svelte-18wv37q"),p(c,"class","ext svelte-18wv37q"),p(t,"class","filename svelte-18wv37q"),p(t,"aria-label",o=e[12].orig_name),p(d,"class","download svelte-18wv37q"),p(l,"class","file svelte-18wv37q"),ae(l,"selectable",e[0]),this.first=l},m(_,q){P(_,l,q),k(l,t),k(t,n),k(n,a),k(t,f),k(t,c),k(c,u),k(l,m),k(l,d),z[g].m(d,null),k(l,b),w&&w.m(l,null),k(l,v),y=!0,B||(C=Z(l,"click",X),B=!0)},p(_,q){e=_,(!y||q&8)&&s!==(s=e[12].filename_stem+"")&&x(a,s),(!y||q&8)&&r!==(r=e[12].filename_ext+"")&&x(u,r),(!y||q&8&&o!==(o=e[12].orig_name))&&p(t,"aria-label",o);let Y=g;g=L(e),g===Y?z[g].p(e,q):(ge(),O(z[Y],1,1,()=>{z[Y]=null}),me(),h=z[g],h?h.p(e,q):(h=z[g]=I[g](e),h.c()),K(h,1),h.m(d,null)),e[3].length>1?w?w.p(e,q):(w=ue(e),w.c(),w.m(l,v)):w&&(w.d(1),w=null),(!y||q&1)&&ae(l,"selectable",e[0])},i(_){y||(K(h),y=!0)},o(_){O(h),y=!1},d(_){_&&M(l),z[g].d(),w&&w.d(),B=!1,C()}}}function Ie(i){let e,l,t,n=[],s=new Map,a,f=ie(i[3]);const c=r=>r[12];for(let r=0;r<f.length;r+=1){let u=re(i,f,r),o=c(u);s.set(o,n[r]=_e(o,u))}return{c(){e=F("div"),l=F("table"),t=F("tbody");for(let r=0;r<n.length;r+=1)n[r].c();p(t,"class","svelte-18wv37q"),p(l,"class","file-preview svelte-18wv37q"),p(e,"class","file-preview-holder svelte-18wv37q"),oe(e,"max-height",typeof i[1]===void 0?"auto":i[1]+"px")},m(r,u){P(r,e,u),k(e,l),k(l,t);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(t,null);a=!0},p(r,[u]){u&125&&(f=ie(r[3]),ge(),n=De(n,u,c,1,r,f,s,t,Ce,_e,null,re),me()),(!a||u&2)&&oe(e,"max-height",typeof r[1]===void 0?"auto":r[1]+"px")},i(r){if(!a){for(let u=0;u<f.length;u+=1)K(n[u]);a=!0}},o(r){for(let u=0;u<n.length;u+=1)O(n[u]);a=!1},d(r){r&&M(e);for(let u=0;u<n.length;u+=1)n[u].d()}}}function Le(i){const e=i.lastIndexOf(".");return e===-1?[i,""]:[i.slice(0,e),i.slice(e)]}function Ne(i,e,l){let t;const n=Se();let{value:s}=e,{selectable:a=!1}=e,{height:f=void 0}=e,{i18n:c}=e;function r(h,b){const v=h.currentTarget;(h.target===v||v&&v.firstElementChild&&h.composedPath().includes(v.firstElementChild))&&n("select",{value:t[b].orig_name,index:b})}function u(h){const b=t.splice(h,1);l(3,t=[...t]),l(7,s=t),n("delete",b[0]),n("change",t)}const o=typeof window<"u",m=h=>{u(h)},d=(h,b)=>{b.key==="Enter"&&u(h)},g=(h,b)=>{r(b,h)};return i.$$set=h=>{"value"in h&&l(7,s=h.value),"selectable"in h&&l(0,a=h.selectable),"height"in h&&l(1,f=h.height),"i18n"in h&&l(2,c=h.i18n)},i.$$.update=()=>{i.$$.dirty&128&&l(3,t=(Array.isArray(s)?s:[s]).map(h=>{const[b,v]=Le(h.orig_name??"");return{...h,filename_stem:b,filename_ext:v}}))},[a,f,c,t,r,u,o,s,m,d,g]}class Te extends ye{constructor(e){super(),Fe(this,e,Ne,Ie,Ae,{value:7,selectable:0,height:1,i18n:2})}get value(){return this.$$.ctx[7]}set value(e){this.$$set({value:e}),N()}get selectable(){return this.$$.ctx[0]}set selectable(e){this.$$set({selectable:e}),N()}get height(){return this.$$.ctx[1]}set height(e){this.$$set({height:e}),N()}get i18n(){return this.$$.ctx[2]}set i18n(e){this.$$set({i18n:e}),N()}}const de=Te,{SvelteComponent:Ge,bubble:He,check_outros:Ke,create_component:Q,destroy_component:V,detach:fe,empty:Oe,flush:D,group_outros:Re,init:je,insert:ce,mount_component:W,safe_not_equal:Je,space:Qe,transition_in:S,transition_out:U}=window.__gradio__svelte__internal;function Ve(i){let e,l;return e=new we({props:{unpadded_box:!0,size:"large",$$slots:{default:[Xe]},$$scope:{ctx:i}}}),{c(){Q(e.$$.fragment)},m(t,n){W(e,t,n),l=!0},p(t,n){const s={};n&128&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){l||(S(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){V(e,t)}}}function We(i){let e,l;return e=new de({props:{i18n:i[5],selectable:i[3],value:i[0],height:i[4]}}),e.$on("select",i[6]),{c(){Q(e.$$.fragment)},m(t,n){W(e,t,n),l=!0},p(t,n){const s={};n&32&&(s.i18n=t[5]),n&8&&(s.selectable=t[3]),n&1&&(s.value=t[0]),n&16&&(s.height=t[4]),e.$set(s)},i(t){l||(S(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){V(e,t)}}}function Xe(i){let e,l;return e=new le({}),{c(){Q(e.$$.fragment)},m(t,n){W(e,t,n),l=!0},i(t){l||(S(e.$$.fragment,t),l=!0)},o(t){U(e.$$.fragment,t),l=!1},d(t){V(e,t)}}}function Ye(i){let e,l,t,n,s,a,f;e=new he({props:{show_label:i[2],float:i[0]===null,Icon:le,label:i[1]||"File"}});const c=[We,Ve],r=[];function u(o,m){return m&1&&(t=null),t==null&&(t=!!(o[0]&&(!Array.isArray(o[0])||o[0].length>0))),t?0:1}return n=u(i,-1),s=r[n]=c[n](i),{c(){Q(e.$$.fragment),l=Qe(),s.c(),a=Oe()},m(o,m){W(e,o,m),ce(o,l,m),r[n].m(o,m),ce(o,a,m),f=!0},p(o,[m]){const d={};m&4&&(d.show_label=o[2]),m&1&&(d.float=o[0]===null),m&2&&(d.label=o[1]||"File"),e.$set(d);let g=n;n=u(o,m),n===g?r[n].p(o,m):(Re(),U(r[g],1,1,()=>{r[g]=null}),Ke(),s=r[n],s?s.p(o,m):(s=r[n]=c[n](o),s.c()),S(s,1),s.m(a.parentNode,a))},i(o){f||(S(e.$$.fragment,o),S(s),f=!0)},o(o){U(e.$$.fragment,o),U(s),f=!1},d(o){o&&(fe(l),fe(a)),V(e,o),r[n].d(o)}}}function Ze(i,e,l){let{value:t=null}=e,{label:n}=e,{show_label:s=!0}=e,{selectable:a=!1}=e,{height:f=void 0}=e,{i18n:c}=e;function r(u){He.call(this,i,u)}return i.$$set=u=>{"value"in u&&l(0,t=u.value),"label"in u&&l(1,n=u.label),"show_label"in u&&l(2,s=u.show_label),"selectable"in u&&l(3,a=u.selectable),"height"in u&&l(4,f=u.height),"i18n"in u&&l(5,c=u.i18n)},[t,n,s,a,f,c,r]}class xe extends Ge{constructor(e){super(),je(this,e,Ze,Ye,Je,{value:0,label:1,show_label:2,selectable:3,height:4,i18n:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),D()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),D()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),D()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),D()}get height(){return this.$$.ctx[4]}set height(e){this.$$set({height:e}),D()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),D()}}const Ct=xe,{SvelteComponent:et,add_flush_callback:tt,bind:lt,binding_callbacks:nt,bubble:G,check_outros:it,create_component:R,create_slot:st,destroy_component:j,detach:ee,empty:ot,flush:$,get_all_dirty_from_scope:at,get_slot_changes:rt,group_outros:ut,init:_t,insert:te,mount_component:J,safe_not_equal:ft,space:be,transition_in:E,transition_out:A,update_slot_base:ct}=window.__gradio__svelte__internal,{createEventDispatcher:ht,tick:mt}=window.__gradio__svelte__internal;function gt(i){let e,l,t;function n(a){i[19](a)}let s={filetype:i[4],file_count:i[3],max_file_size:i[9],root:i[6],stream_handler:i[11],upload:i[10],$$slots:{default:[bt]},$$scope:{ctx:i}};return i[12]!==void 0&&(s.dragging=i[12]),e=new ke({props:s}),nt.push(()=>lt(e,"dragging",n)),e.$on("load",i[13]),e.$on("error",i[20]),{c(){R(e.$$.fragment)},m(a,f){J(e,a,f),t=!0},p(a,f){const c={};f&16&&(c.filetype=a[4]),f&8&&(c.file_count=a[3]),f&512&&(c.max_file_size=a[9]),f&64&&(c.root=a[6]),f&2048&&(c.stream_handler=a[11]),f&1024&&(c.upload=a[10]),f&2097152&&(c.$$scope={dirty:f,ctx:a}),!l&&f&4096&&(l=!0,c.dragging=a[12],tt(()=>l=!1)),e.$set(c)},i(a){t||(E(e.$$.fragment,a),t=!0)},o(a){A(e.$$.fragment,a),t=!1},d(a){j(e,a)}}}function dt(i){let e,l,t,n;return e=new pe({props:{i18n:i[8],absolute:!0}}),e.$on("clear",i[14]),t=new de({props:{i18n:i[8],selectable:i[5],value:i[0],height:i[7]}}),t.$on("select",i[16]),t.$on("change",i[17]),t.$on("delete",i[18]),{c(){R(e.$$.fragment),l=be(),R(t.$$.fragment)},m(s,a){J(e,s,a),te(s,l,a),J(t,s,a),n=!0},p(s,a){const f={};a&256&&(f.i18n=s[8]),e.$set(f);const c={};a&256&&(c.i18n=s[8]),a&32&&(c.selectable=s[5]),a&1&&(c.value=s[0]),a&128&&(c.height=s[7]),t.$set(c)},i(s){n||(E(e.$$.fragment,s),E(t.$$.fragment,s),n=!0)},o(s){A(e.$$.fragment,s),A(t.$$.fragment,s),n=!1},d(s){s&&ee(l),j(e,s),j(t,s)}}}function bt(i){let e;const l=i[15].default,t=st(l,i,i[21],null);return{c(){t&&t.c()},m(n,s){t&&t.m(n,s),e=!0},p(n,s){t&&t.p&&(!e||s&2097152)&&ct(t,l,n,n[21],e?rt(l,n[21],s,null):at(n[21]),null)},i(n){e||(E(t,n),e=!0)},o(n){A(t,n),e=!1},d(n){t&&t.d(n)}}}function wt(i){let e,l,t,n,s,a,f;e=new he({props:{show_label:i[2],Icon:le,float:!i[0],label:i[1]||"File"}});const c=[dt,gt],r=[];function u(o,m){return m&1&&(t=null),t==null&&(t=!!(o[0]&&(!Array.isArray(o[0])||o[0].length>0))),t?0:1}return n=u(i,-1),s=r[n]=c[n](i),{c(){R(e.$$.fragment),l=be(),s.c(),a=ot()},m(o,m){J(e,o,m),te(o,l,m),r[n].m(o,m),te(o,a,m),f=!0},p(o,[m]){const d={};m&4&&(d.show_label=o[2]),m&1&&(d.float=!o[0]),m&2&&(d.label=o[1]||"File"),e.$set(d);let g=n;n=u(o,m),n===g?r[n].p(o,m):(ut(),A(r[g],1,1,()=>{r[g]=null}),it(),s=r[n],s?s.p(o,m):(s=r[n]=c[n](o),s.c()),E(s,1),s.m(a.parentNode,a))},i(o){f||(E(e.$$.fragment,o),E(s),f=!0)},o(o){A(e.$$.fragment,o),A(s),f=!1},d(o){o&&(ee(l),ee(a)),j(e,o),r[n].d(o)}}}function kt(i,e,l){let{$$slots:t={},$$scope:n}=e,{value:s}=e,{label:a}=e,{show_label:f=!0}=e,{file_count:c="single"}=e,{file_types:r=null}=e,{selectable:u=!1}=e,{root:o}=e,{height:m=void 0}=e,{i18n:d}=e,{max_file_size:g=null}=e,{upload:h}=e,{stream_handler:b}=e;async function v({detail:_}){l(0,s=_),await mt(),B("change",s),B("upload",_)}function y(){l(0,s=null),B("change",null),B("clear")}const B=ht();let C=!1;function I(_){G.call(this,i,_)}function z(_){G.call(this,i,_)}function L(_){G.call(this,i,_)}function w(_){C=_,l(12,C)}function X(_){G.call(this,i,_)}return i.$$set=_=>{"value"in _&&l(0,s=_.value),"label"in _&&l(1,a=_.label),"show_label"in _&&l(2,f=_.show_label),"file_count"in _&&l(3,c=_.file_count),"file_types"in _&&l(4,r=_.file_types),"selectable"in _&&l(5,u=_.selectable),"root"in _&&l(6,o=_.root),"height"in _&&l(7,m=_.height),"i18n"in _&&l(8,d=_.i18n),"max_file_size"in _&&l(9,g=_.max_file_size),"upload"in _&&l(10,h=_.upload),"stream_handler"in _&&l(11,b=_.stream_handler),"$$scope"in _&&l(21,n=_.$$scope)},i.$$.update=()=>{i.$$.dirty&4096&&B("drag",C)},[s,a,f,c,r,u,o,m,d,g,h,b,C,v,y,t,I,z,L,w,X,n]}class pt extends et{constructor(e){super(),_t(this,e,kt,wt,ft,{value:0,label:1,show_label:2,file_count:3,file_types:4,selectable:5,root:6,height:7,i18n:8,max_file_size:9,upload:10,stream_handler:11})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),$()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),$()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),$()}get file_count(){return this.$$.ctx[3]}set file_count(e){this.$$set({file_count:e}),$()}get file_types(){return this.$$.ctx[4]}set file_types(e){this.$$set({file_types:e}),$()}get selectable(){return this.$$.ctx[5]}set selectable(e){this.$$set({selectable:e}),$()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),$()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),$()}get i18n(){return this.$$.ctx[8]}set i18n(e){this.$$set({i18n:e}),$()}get max_file_size(){return this.$$.ctx[9]}set max_file_size(e){this.$$set({max_file_size:e}),$()}get upload(){return this.$$.ctx[10]}set upload(e){this.$$set({upload:e}),$()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),$()}}const Et=pt;export{Et as B,Ct as F,de as a};
//# sourceMappingURL=FileUpload-DIyAvrHT.js.map
