{"name": "@gradio/theme", "version": "0.2.5", "description": "Gradio UI packages", "type": "module", "main": "src/index.ts", "author": "", "license": "ISC", "private": false, "scripts": {"generate": "pollen -c src/pollen.config.cjs"}, "exports": {".": {"gradio": "./src/index.ts", "import": "./dist/src/index.js", "types": "./dist/src/index.d.ts"}, "./reset.css": {"gradio": "./src/reset.css", "import": "./dist/src/reset.css"}, "./global.css": {"gradio": "./src/global.css", "import": "./dist/src/global.css"}, "./tokens": {"gradio": "./src/tokens.ts", "import": "./dist/src/tokens.js"}, "./typography.css": {"gradio": "./src/typography.css", "import": "./dist/src/typography.css"}, "./pollen.css": {"gradio": "./src/pollen.css", "import": "./dist/src/pollen.css"}, "./package.json": "./package.json"}, "peerDependencies": {"svelte": "^4.0.0"}, "main_changeset": true, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/theme"}}