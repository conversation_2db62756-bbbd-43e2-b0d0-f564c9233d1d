.json-node.svelte-19ir0ev{font-family:var(--font-mono);--text-color:#d18770;--key-color:var(--text-color);--string-color:#ce9178;--number-color:#719fad;--bracket-color:#5d8585;--square-bracket-color:#be6069;--punctuation-color:#8fbcbb;--line-number-color:#6a737d;--separator-color:var(--line-number-color)}.json-node.dark-mode.svelte-19ir0ev{--bracket-color:#7eb4b3;--number-color:#638d9a}.json-node.root.svelte-19ir0ev{position:relative;padding-left:var(--size-14)}.json-node.root.svelte-19ir0ev:before{content:"";position:absolute;top:0;bottom:0;left:var(--size-11);width:1px;background-color:var(--separator-color)}.line.svelte-19ir0ev{display:flex;align-items:flex-start;padding:0;margin:0;line-height:var(--line-md)}.line-number.svelte-19ir0ev{position:absolute;left:0;width:calc(var(--size-7));text-align:right;color:var(--line-number-color);user-select:none;text-overflow:ellipsis;direction:rtl;overflow:hidden}.content.svelte-19ir0ev{flex:1;display:flex;align-items:center;padding-left:calc(var(--depth) * var(--size-2));flex-wrap:wrap}.children.svelte-19ir0ev{padding-left:var(--size-4)}.children.hidden.svelte-19ir0ev{display:none}.key.svelte-19ir0ev{color:var(--key-color)}.string.svelte-19ir0ev{color:var(--string-color)}.number.svelte-19ir0ev{color:var(--number-color)}.bool.svelte-19ir0ev,.null.svelte-19ir0ev{color:var(--text-color)}.value.svelte-19ir0ev{margin-left:var(--spacing-md)}.punctuation.svelte-19ir0ev{color:var(--punctuation-color)}.bracket.svelte-19ir0ev{margin-left:var(--spacing-sm);color:var(--bracket-color)}.square-bracket.svelte-19ir0ev{margin-left:var(--spacing-sm);color:var(--square-bracket-color)}.toggle.svelte-19ir0ev,.preview.svelte-19ir0ev{background:none;border:none;color:inherit;cursor:pointer;padding:0;margin:0}.toggle.svelte-19ir0ev{user-select:none;margin-right:var(--spacing-md)}.preview.svelte-19ir0ev{margin:0 var(--spacing-sm) 0 var(--spacing-lg)}.preview.svelte-19ir0ev:hover{text-decoration:underline}[data-pseudo-content]:before{content:attr(data-pseudo-content)}.copied svg{animation:svelte-e8oseo-fade ease .3s;animation-fill-mode:forwards}@keyframes svelte-e8oseo-fade{0%{opacity:0}to{opacity:1}}.json-holder.svelte-e8oseo{padding:var(--size-2);overflow-y:auto}.empty-wrapper.svelte-e8oseo{min-height:calc(var(--size-32) - 20px);height:100%}button.svelte-e8oseo{display:flex;position:absolute;top:var(--block-label-margin);right:var(--block-label-margin);align-items:center;box-shadow:var(--shadow-drop);border:1px solid var(--border-color-primary);border-top:none;border-right:none;border-radius:var(--block-label-right-radius);background:var(--block-label-background-fill);padding:5px;width:22px;height:22px;overflow:hidden;color:var(--block-label-text-color);font:var(--font);font-size:var(--button-small-text-size)}
