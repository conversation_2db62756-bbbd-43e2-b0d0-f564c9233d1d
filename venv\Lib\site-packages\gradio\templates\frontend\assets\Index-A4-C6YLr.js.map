{"version": 3, "file": "Index-A4-C6YLr.js", "sources": ["../../../../js/icons/src/Chat.svelte", "../../../../js/chatbot/shared/utils.ts", "../../../../js/chatbot/shared/Pending.svelte", "../../../../js/chatbot/shared/MessageBox.svelte", "../../../../js/chatbot/shared/Component.svelte", "../../../../js/chatbot/shared/ThumbDownActive.svelte", "../../../../js/chatbot/shared/ThumbDownDefault.svelte", "../../../../js/chatbot/shared/ThumbUpActive.svelte", "../../../../js/chatbot/shared/ThumbUpDefault.svelte", "../../../../js/chatbot/shared/LikeDislike.svelte", "../../../../js/chatbot/shared/Copy.svelte", "../../../../js/chatbot/shared/Download.svelte", "../../../../js/chatbot/shared/ButtonPanel.svelte", "../../../../js/chatbot/shared/CopyAll.svelte", "../../../../js/chatbot/shared/ChatBot.svelte", "../../../../js/chatbot/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "import type { FileData } from \"@gradio/client\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\nimport type {\n\tTupleFormat,\n\tComponentMessage,\n\tComponentData,\n\tTextMessage,\n\tNormalisedMessage,\n\tMessage\n} from \"../types\";\n\nexport const format_chat_for_sharing = async (\n\tchat: [string | FileData | null, string | FileData | null][]\n): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message_pair) => {\n\t\t\treturn await Promise.all(\n\t\t\t\tmessage_pair.map(async (message, i) => {\n\t\t\t\t\tif (message === null) return \"\";\n\t\t\t\t\tlet speaker_emoji = i === 0 ? \"😃\" : \"🤖\";\n\t\t\t\t\tlet html_content = \"\";\n\n\t\t\t\t\tif (typeof message === \"string\") {\n\t\t\t\t\t\tconst regexPatterns = {\n\t\t\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\thtml_content = message;\n\n\t\t\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\t\t\tlet match;\n\n\t\t\t\t\t\t\twhile ((match = regex.exec(message)) !== null) {\n\t\t\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (!message?.url) return \"\";\n\t\t\t\t\t\tconst file_url = await uploadToHuggingFace(message.url, \"url\");\n\t\t\t\t\t\tif (message.mime_type?.includes(\"audio\")) {\n\t\t\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"video\")) {\n\t\t\t\t\t\t\thtml_content = file_url;\n\t\t\t\t\t\t} else if (message.mime_type?.includes(\"image\")) {\n\t\t\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t\t\t})\n\t\t\t);\n\t\t})\n\t);\n\treturn messages\n\t\t.map((message_pair) =>\n\t\t\tmessage_pair.join(\n\t\t\t\tmessage_pair[0] !== \"\" && message_pair[1] !== \"\" ? \"\\n\" : \"\"\n\t\t\t)\n\t\t)\n\t\t.join(\"\\n\");\n};\n\nconst redirect_src_url = (src: string, root: string): string =>\n\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\nfunction get_component_for_mime_type(\n\tmime_type: string | null | undefined\n): string {\n\tif (!mime_type) return \"file\";\n\tif (mime_type.includes(\"audio\")) return \"audio\";\n\tif (mime_type.includes(\"video\")) return \"video\";\n\tif (mime_type.includes(\"image\")) return \"image\";\n\treturn \"file\";\n}\n\nfunction convert_file_message_to_component_message(\n\tmessage: any\n): ComponentData {\n\tconst _file = Array.isArray(message.file) ? message.file[0] : message.file;\n\treturn {\n\t\tcomponent: get_component_for_mime_type(_file?.mime_type),\n\t\tvalue: message.file,\n\t\talt_text: message.alt_text,\n\t\tconstructor_args: {},\n\t\tprops: {}\n\t} as ComponentData;\n}\n\nexport function normalise_messages(\n\tmessages: Message[] | null,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\treturn messages.map((message, i) => {\n\t\tif (typeof message.content === \"string\") {\n\t\t\treturn {\n\t\t\t\trole: message.role,\n\t\t\t\tmetadata: message.metadata,\n\t\t\t\tcontent: redirect_src_url(message.content, root),\n\t\t\t\ttype: \"text\",\n\t\t\t\tindex: i\n\t\t\t};\n\t\t} else if (\"file\" in message.content) {\n\t\t\treturn {\n\t\t\t\tcontent: convert_file_message_to_component_message(message.content),\n\t\t\t\tmetadata: message.metadata,\n\t\t\t\trole: message.role,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: i\n\t\t\t};\n\t\t}\n\t\treturn { type: \"component\", ...message } as ComponentMessage;\n\t});\n}\n\nexport function normalise_tuples(\n\tmessages: TupleFormat,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\tconst msg = messages.flatMap((message_pair, i) => {\n\t\treturn message_pair.map((message, index) => {\n\t\t\tif (message == null) return null;\n\t\t\tconst role = index == 0 ? \"user\" : \"assistant\";\n\n\t\t\tif (typeof message === \"string\") {\n\t\t\t\treturn {\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"text\",\n\t\t\t\t\tcontent: redirect_src_url(message, root),\n\t\t\t\t\tmetadata: { title: null },\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as TextMessage;\n\t\t\t}\n\n\t\t\tif (\"file\" in message) {\n\t\t\t\treturn {\n\t\t\t\t\tcontent: convert_file_message_to_component_message(message),\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"component\",\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as ComponentMessage;\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\trole: role,\n\t\t\t\tcontent: message,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: [i, index]\n\t\t\t} as ComponentMessage;\n\t\t});\n\t});\n\treturn msg.filter((message) => message != null) as NormalisedMessage[];\n}\n\nexport function is_component_message(\n\tmessage: NormalisedMessage\n): message is ComponentMessage {\n\treturn message.type === \"component\";\n}\n", "<script lang=\"ts\">\n\texport let layout = \"bubble\";\n</script>\n\n<div\n\tclass=\"message pending\"\n\trole=\"status\"\n\taria-label=\"Loading response\"\n\taria-live=\"polite\"\n\tstyle:border-radius={layout === \"bubble\" ? \"var(--radius-xxl)\" : \"none\"}\n>\n\t<span class=\"sr-only\">Loading content</span>\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n\t&nbsp;\n\t<div class=\"dot-flashing\" />\n</div>\n\n<style>\n\t.pending {\n\t\tbackground: var(--color-accent-soft);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\talign-self: center;\n\t\tgap: 2px;\n\t\twidth: 100%;\n\t\theight: var(--size-16);\n\t}\n\t.dot-flashing {\n\t\tanimation: flash 1s infinite ease-in-out;\n\t\tborder-radius: 5px;\n\t\tbackground-color: var(--body-text-color);\n\t\twidth: 7px;\n\t\theight: 7px;\n\t\tcolor: var(--body-text-color);\n\t}\n\t@keyframes flash {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 0;\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.dot-flashing:nth-child(1) {\n\t\tanimation-delay: 0s;\n\t}\n\n\t.dot-flashing:nth-child(2) {\n\t\tanimation-delay: 0.33s;\n\t}\n\t.dot-flashing:nth-child(3) {\n\t\tanimation-delay: 0.66s;\n\t}\n</style>\n", "<script lang=\"ts\">\n\tlet expanded = false;\n\texport let title: string;\n\n\tfunction toggleExpanded(): void {\n\t\texpanded = !expanded;\n\t}\n</script>\n\n<button class=\"box\" on:click={toggleExpanded}>\n\t<div class=\"title\">\n\t\t<span class=\"title-text\">{title}</span>\n\t\t<span\n\t\t\tstyle:transform={expanded ? \"rotate(0)\" : \"rotate(90deg)\"}\n\t\t\tclass=\"arrow\"\n\t\t>\n\t\t\t▼\n\t\t</span>\n\t</div>\n\t{#if expanded}\n\t\t<div class=\"content\">\n\t\t\t<slot></slot>\n\t\t</div>\n\t{/if}\n</button>\n\n<style>\n\t.box {\n\t\tborder-radius: 4px;\n\t\tcursor: pointer;\n\t\tmax-width: max-content;\n\t\tbackground: var(--color-accent-soft);\n\t\tborder: 1px solid var(--border-color-accent-subdued);\n\t\tfont-size: 0.8em;\n\t}\n\n\t.title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 3px 6px;\n\t\tcolor: var(--body-text-color);\n\t\topacity: 0.8;\n\t}\n\n\t.content {\n\t\tpadding: 4px 8px;\n\t}\n\n\t.content :global(*) {\n\t\tfont-size: 0.8em;\n\t}\n\n\t.title-text {\n\t\tpadding-right: var(--spacing-lg);\n\t}\n\n\t.arrow {\n\t\tmargin-left: auto;\n\t\topacity: 0.8;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let type: \"gallery\" | \"plot\" | \"audio\" | \"video\" | \"image\" | string;\n\texport let components;\n\texport let value;\n\texport let target;\n\texport let theme_mode;\n\texport let props;\n\texport let i18n;\n\texport let upload;\n\texport let _fetch;\n</script>\n\n{#if type === \"gallery\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\t{_fetch}\n\t\tallow_preview={false}\n\t\tinteractive={false}\n\t\tmode=\"minimal\"\n\t\tfixed_height={1}\n\t\ton:load\n\t/>\n{:else if type === \"plot\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{target}\n\t\t{theme_mode}\n\t\tbokeh_version={props.bokeh_version}\n\t\tcaption=\"\"\n\t\tshow_actions_button={true}\n\t\ton:load\n\t/>\n{:else if type === \"audio\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\twaveform_settings={{}}\n\t\twaveform_options={{}}\n\t\tshow_download_button={false}\n\t\ton:load\n\t/>\n{:else if type === \"video\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\tautoplay={true}\n\t\tvalue={value.video || value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\t{upload}\n\t\tshow_download_button={false}\n\t\ton:load\n\t>\n\t\t<track kind=\"captions\" />\n\t</svelte:component>\n{:else if type === \"image\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_download_button={false}\n\t\ton:load\n\t\t{i18n}\n\t/>\n{:else if type === \"html\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tgradio={{ dispatch: () => {} }}\n\t\ton:load\n\t/>\n{/if}\n", "<svg\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport ThumbDownActive from \"./ThumbDownActive.svelte\";\n\timport ThumbDownDefault from \"./ThumbDownDefault.svelte\";\n\timport ThumbUpActive from \"./ThumbUpActive.svelte\";\n\timport ThumbUpDefault from \"./ThumbUpDefault.svelte\";\n\n\texport let handle_action: (selected: string | null) => void;\n\texport let padded = false;\n\texport let position: \"right\" | \"left\" = \"left\";\n\n\tlet selected: \"like\" | \"dislike\" | null = null;\n</script>\n\n<button\n\ton:click={() => {\n\t\tselected = \"dislike\";\n\t\thandle_action(selected);\n\t}}\n\taria-label={selected === \"dislike\" ? \"clicked dislike\" : \"dislike\"}\n\tclass:padded\n\tclass:selected={selected === \"dislike\"}\n\tclass=\"dislike-button {position}\"\n>\n\t{#if selected === \"dislike\"}\n\t\t<ThumbDownActive />\n\t{:else}\n\t\t<ThumbDownDefault />\n\t{/if}\n</button>\n\n<button\n\tclass=\"like-button\"\n\tclass:padded\n\ton:click={() => {\n\t\tselected = \"like\";\n\t\thandle_action(selected);\n\t}}\n\taria-label={selected === \"like\" ? \"clicked like\" : \"like\"}\n\tclass:selected={selected === \"like\"}\n>\n\t{#if selected === \"like\"}\n\t\t<ThumbUpActive />\n\t{:else}\n\t\t<ThumbUpDefault />\n\t{/if}\n</button>\n\n<style>\n\tbutton {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\tbutton:hover,\n\tbutton:focus {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.selected,\n\t.selected:focus,\n\t.selected:hover {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = value;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<button\n\ton:click={handle_copy}\n\tclass=\"action\"\n\ttitle=\"copy\"\n\taria-label={copied ? \"Copied message\" : \"Copy message\"}\n>\n\t{#if !copied}\n\t\t<Copy />\n\t{/if}\n\t{#if copied}\n\t\t<Check />\n\t{/if}\n</button>\n\n<style>\n\tbutton {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tmargin-right: 5px;\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.action {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n</style>\n", "<svg\n\twidth=\"16\"\n\theight=\"16\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M6.27701 8.253C6.24187 8.29143 6.19912 8.32212 6.15147 8.34311C6.10383 8.36411 6.05233 8.37495 6.00026 8.37495C5.94819 8.37495 5.89669 8.36411 5.84905 8.34311C5.8014 8.32212 5.75865 8.29143 5.72351 8.253L3.72351 6.0655C3.65798 5.99185 3.62408 5.89536 3.62916 5.79691C3.63424 5.69846 3.67788 5.60596 3.75064 5.53945C3.8234 5.47293 3.91943 5.43774 4.01794 5.44149C4.11645 5.44525 4.20952 5.48764 4.27701 5.5595L5.62501 7.0345V1.5C5.62501 1.40054 5.66452 1.30516 5.73485 1.23483C5.80517 1.16451 5.90055 1.125 6.00001 1.125C6.09947 1.125 6.19485 1.16451 6.26517 1.23483C6.3355 1.30516 6.37501 1.40054 6.37501 1.5V7.034L7.72351 5.559C7.79068 5.4856 7.88425 5.44189 7.98364 5.43748C8.08304 5.43308 8.18011 5.46833 8.25351 5.5355C8.32691 5.60267 8.37062 5.69624 8.37503 5.79563C8.37943 5.89503 8.34418 5.9921 8.27701 6.0655L6.27701 8.253Z\"\n\t\tfill=\"currentColor\"\n\t/>\n\t<path\n\t\td=\"M1.875 7.39258C1.875 7.29312 1.83549 7.19774 1.76517 7.12741C1.69484 7.05709 1.59946 7.01758 1.5 7.01758C1.40054 7.01758 1.30516 7.05709 1.23483 7.12741C1.16451 7.19774 1.125 7.29312 1.125 7.39258V7.42008C1.125 8.10358 1.125 8.65508 1.1835 9.08858C1.2435 9.53858 1.3735 9.91758 1.674 10.2186C1.975 10.5196 2.354 10.6486 2.804 10.7096C3.2375 10.7676 3.789 10.7676 4.4725 10.7676H7.5275C8.211 10.7676 8.7625 10.7676 9.196 10.7096C9.646 10.6486 10.025 10.5196 10.326 10.2186C10.627 9.91758 10.756 9.53858 10.817 9.08858C10.875 8.65508 10.875 8.10358 10.875 7.42008V7.39258C10.875 7.29312 10.8355 7.19774 10.7652 7.12741C10.6948 7.05709 10.5995 7.01758 10.5 7.01758C10.4005 7.01758 10.3052 7.05709 10.2348 7.12741C10.1645 7.19774 10.125 7.29312 10.125 7.39258C10.125 8.11008 10.124 8.61058 10.0735 8.98858C10.024 9.35558 9.9335 9.54958 9.7955 9.68808C9.657 9.82658 9.463 9.91658 9.0955 9.96608C8.718 10.0166 8.2175 10.0176 7.5 10.0176H4.5C3.7825 10.0176 3.2815 10.0166 2.904 9.96608C2.537 9.91658 2.343 9.82608 2.2045 9.68808C2.066 9.54958 1.976 9.35558 1.9265 8.98808C1.876 8.61058 1.875 8.11008 1.875 7.39258Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport LikeDislike from \"./LikeDislike.svelte\";\n\timport Copy from \"./Copy.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport DownloadIcon from \"./Download.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport type { NormalisedMessage, TextMessage } from \"../types\";\n\timport { is_component_message } from \"./utils\";\n\n\texport let likeable: boolean;\n\texport let show_copy_button: boolean;\n\texport let show: boolean;\n\texport let message: NormalisedMessage | NormalisedMessage[];\n\texport let position: \"right\" | \"left\";\n\texport let avatar: FileData | null;\n\n\texport let handle_action: (selected: string | null) => void;\n\texport let layout: \"bubble\" | \"panel\";\n\n\tfunction is_all_text(\n\t\tmessage: NormalisedMessage[] | NormalisedMessage\n\t): message is TextMessage[] | TextMessage {\n\t\treturn (\n\t\t\t(Array.isArray(message) &&\n\t\t\t\tmessage.every((m) => typeof m.content === \"string\")) ||\n\t\t\t(!Array.isArray(message) && typeof message.content === \"string\")\n\t\t);\n\t}\n\n\tfunction all_text(message: TextMessage[] | TextMessage): string {\n\t\tif (Array.isArray(message)) {\n\t\t\treturn message.map((m) => m.content).join(\"\\n\");\n\t\t}\n\t\treturn message.content;\n\t}\n\n\t$: message_text = is_all_text(message) ? all_text(message) : \"\";\n\n\t$: show_copy = show_copy_button && message && is_all_text(message);\n\t$: show_download =\n\t\t!Array.isArray(message) &&\n\t\tis_component_message(message) &&\n\t\tmessage.content.value?.url;\n</script>\n\n{#if show}\n\t<div\n\t\tclass=\"message-buttons-{position} {layout}  message-buttons {avatar !==\n\t\t\tnull && 'with-avatar'}\"\n\t>\n\t\t{#if show_copy}\n\t\t\t<Copy value={message_text} />\n\t\t{/if}\n\t\t{#if show_download && !Array.isArray(message) && is_component_message(message)}\n\t\t\t<DownloadLink\n\t\t\t\thref={message?.content?.value.url}\n\t\t\t\tdownload={message.content.value.orig_name || \"image\"}\n\t\t\t>\n\t\t\t\t<span class=\"icon-wrap\">\n\t\t\t\t\t<DownloadIcon />\n\t\t\t\t</span>\n\t\t\t</DownloadLink>\n\t\t{/if}\n\t\t{#if likeable}\n\t\t\t<LikeDislike {handle_action} padded={show_copy || show_download} />\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\t.icon-wrap {\n\t\tdisplay: block;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.icon-wrap:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.message-buttons {\n\t\tborder-radius: var(--radius-md);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\theight: var(--size-7);\n\t\talign-self: self-end;\n\t\tmargin: 0px calc(var(--spacing-xl) * 3);\n\t\tpadding-left: 5px;\n\t\tz-index: 1;\n\t\tpadding-bottom: var(--spacing-xl);\n\t\tpadding: var(--spacing-md) var(--spacing-md);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tbackground: var(--border-color-secondary);\n\t\tgap: var(--spacing-md);\n\t}\n\t.message-buttons-left {\n\t\talign-self: start;\n\t\tleft: 0px;\n\t}\n\n\t.panel.message-buttons-left,\n\t.panel.message-buttons-right {\n\t\tmargin: 10px 0 2px 0;\n\t}\n\n\t/* .message-buttons {\n\t\tleft: 0px;\n\t\tright: 0px;\n\t\ttop: unset;\n\t\tbottom: calc(-30px - var(--spacing-xl));\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: center;\n\t\tgap: 0px;\n\t} */\n\n\t.message-buttons :global(> *) {\n\t\tmargin-right: 0px;\n\t}\n\n\t.with-avatar {\n\t\tmargin-left: calc(var(--spacing-xl) * 4 + 31px);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onD<PERSON>roy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport type { NormalisedMessage } from \"../types\";\n\n\tlet copied = false;\n\texport let value: NormalisedMessage[] | null;\n\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tconst copy_conversation = (): void => {\n\t\tif (value) {\n\t\t\tconst conversation_value = value\n\t\t\t\t.map((message) => {\n\t\t\t\t\tif (message.type === \"text\") {\n\t\t\t\t\t\treturn `${message.role}: ${message.content}`;\n\t\t\t\t\t}\n\t\t\t\t\treturn `${message.role}: ${message.content.value.url}`;\n\t\t\t\t})\n\t\t\t\t.join(\"\\n\\n\");\n\n\t\t\tnavigator.clipboard.writeText(conversation_value).catch((err) => {\n\t\t\t\tconsole.error(\"Failed to copy conversation: \", err);\n\t\t\t});\n\t\t}\n\t};\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tcopy_conversation();\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<button\n\ton:click={handle_copy}\n\ttitle=\"Copy conversation\"\n\tclass={copied ? \"copied\" : \"copy-text\"}\n\taria-label={copied ? \"Copied conversation\" : \"Copy conversation\"}\n>\n\t{#if copied}\n\t\t<Check />\n\t{:else}\n\t\t<Copy />\n\t{/if}\n</button>\n\n<style>\n\tbutton {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\talign-items: center;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\tpadding: var(--spacing-sm);\n\t\twidth: var(--size-6);\n\t\theight: var(--size-6);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { format_chat_for_sharing, is_component_message } from \"./utils\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport { Gradio, copy } from \"@gradio/utils\";\n\n\timport { dequal } from \"dequal/lite\";\n\timport {\n\t\tbeforeUpdate,\n\t\tafterUpdate,\n\t\tcreateEventDispatcher,\n\t\ttype SvelteComponent,\n\t\ttype ComponentType,\n\t\ttick,\n\t\tonMount\n\t} from \"svelte\";\n\timport { ShareButton } from \"@gradio/atoms\";\n\timport { Image } from \"@gradio/image/shared\";\n\n\timport { Clear } from \"@gradio/icons\";\n\timport type { SelectData, LikeData } from \"@gradio/utils\";\n\timport type { MessageRole } from \"../types\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport Pending from \"./Pending.svelte\";\n\timport MessageBox from \"./MessageBox.svelte\";\n\n\texport let value: NormalisedMessage[] | null = [];\n\tlet old_value: NormalisedMessage[] | null = null;\n\n\timport Component from \"./Component.svelte\";\n\timport LikeButtons from \"./ButtonPanel.svelte\";\n\timport type { LoadedComponent } from \"../../core/src/types\";\n\n\timport CopyAll from \"./CopyAll.svelte\";\n\n\texport let _fetch: typeof fetch;\n\texport let load_component: Gradio[\"load_component\"];\n\n\tlet _components: Record<string, ComponentType<SvelteComponent>> = {};\n\n\tasync function load_components(component_names: string[]): Promise<void> {\n\t\tlet names: string[] = [];\n\t\tlet components: ReturnType<typeof load_component>[\"component\"][] = [];\n\t\tcomponent_names.forEach((component_name) => {\n\t\t\tif (_components[component_name] || component_name === \"file\") {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst { name, component } = load_component(component_name, \"base\");\n\t\t\tnames.push(name);\n\t\t\tcomponents.push(component);\n\t\t\tcomponent_name;\n\t\t});\n\n\t\tconst loaded_components: LoadedComponent[] = await Promise.all(components);\n\t\tloaded_components.forEach((component, i) => {\n\t\t\t_components[names[i]] = component.default;\n\t\t});\n\t}\n\n\t$: load_components(get_components_from_messages(value));\n\n\tfunction get_components_from_messages(\n\t\tmessages: NormalisedMessage[] | null\n\t): string[] {\n\t\tif (!messages) return [];\n\t\tlet components: Set<string> = new Set();\n\t\tmessages.forEach((message) => {\n\t\t\tif (message.type === \"component\") {\n\t\t\t\tcomponents.add(message.content.component);\n\t\t\t}\n\t\t});\n\t\treturn Array.from(components);\n\t}\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let show_copy_all_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let i18n: I18nFormatter;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let placeholder: string | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let msg_format: \"tuples\" | \"messages\" = \"tuples\";\n\texport let root: string;\n\n\tlet target: HTMLElement | null = null;\n\n\tonMount(() => {\n\t\ttarget = document.querySelector(\"div.gradio-container\");\n\t\tadjust_text_size();\n\t});\n\n\tlet div: HTMLDivElement;\n\tlet autoscroll: boolean;\n\n\tfunction adjust_text_size(): void {\n\t\tlet style = getComputedStyle(document.body);\n\t\tlet body_text_size = style.getPropertyValue(\"--body-text-size\");\n\t\tlet updated_text_size;\n\n\t\tswitch (body_text_size) {\n\t\t\tcase \"13px\":\n\t\t\t\tupdated_text_size = 14;\n\t\t\t\tbreak;\n\t\t\tcase \"14px\":\n\t\t\t\tupdated_text_size = 16;\n\t\t\t\tbreak;\n\t\t\tcase \"16px\":\n\t\t\t\tupdated_text_size = 20;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tupdated_text_size = 14;\n\t\t\t\tbreak;\n\t\t}\n\n\t\tdocument.body.style.setProperty(\n\t\t\t\"--chatbot-body-text-size\",\n\t\t\tupdated_text_size + \"px\"\n\t\t);\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tlike: LikeData;\n\t}>();\n\n\tbeforeUpdate(() => {\n\t\tautoscroll =\n\t\t\tdiv && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t});\n\n\tasync function scroll(): Promise<void> {\n\t\tif (!div) return;\n\t\tawait tick();\n\t\trequestAnimationFrame(() => {\n\t\t\tif (autoscroll) {\n\t\t\t\tdiv?.scrollTo(0, div.scrollHeight);\n\t\t\t}\n\t\t});\n\t}\n\n\tlet image_preview_source: string;\n\tlet image_preview_source_alt: string;\n\tlet is_image_preview_open = false;\n\n\t$: if (value || autoscroll || _components) {\n\t\tscroll();\n\t}\n\tafterUpdate(() => {\n\t\tif (!div) return;\n\t\tdiv.querySelectorAll(\"img\").forEach((n) => {\n\t\t\tn.addEventListener(\"click\", (e) => {\n\t\t\t\tconst target = e.target as HTMLImageElement;\n\t\t\t\tif (target) {\n\t\t\t\t\timage_preview_source = target.src;\n\t\t\t\t\timage_preview_source_alt = target.alt;\n\t\t\t\t\tis_image_preview_open = true;\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t});\n\n\t$: {\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\n\t$: groupedMessages = value && group_messages(value);\n\n\tfunction handle_select(i: number, message: NormalisedMessage): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: message.index,\n\t\t\tvalue: message.content\n\t\t});\n\t}\n\n\tfunction handle_like(\n\t\ti: number,\n\t\tmessage: NormalisedMessage,\n\t\tselected: string | null\n\t): void {\n\t\tif (msg_format === \"tuples\") {\n\t\t\tdispatch(\"like\", {\n\t\t\t\tindex: message.index,\n\t\t\t\tvalue: message.content,\n\t\t\t\tliked: selected === \"like\"\n\t\t\t});\n\t\t} else {\n\t\t\tif (!groupedMessages) return;\n\n\t\t\tconst message_group = groupedMessages[i];\n\t\t\tconst [first, last] = [\n\t\t\t\tmessage_group[0],\n\t\t\t\tmessage_group[message_group.length - 1]\n\t\t\t];\n\n\t\t\tdispatch(\"like\", {\n\t\t\t\tindex: [first.index, last.index] as [number, number],\n\t\t\t\tvalue: message_group.map((m) => m.content),\n\t\t\t\tliked: selected === \"like\"\n\t\t\t});\n\t\t}\n\t}\n\n\tfunction get_message_label_data(message: NormalisedMessage): string {\n\t\tif (message.type === \"text\") {\n\t\t\treturn message.content;\n\t\t} else if (\n\t\t\tmessage.type === \"component\" &&\n\t\t\tmessage.content.component === \"file\"\n\t\t) {\n\t\t\tif (Array.isArray(message.content.value)) {\n\t\t\t\treturn `file of extension type: ${message.content.value[0].orig_name?.split(\".\").pop()}`;\n\t\t\t}\n\t\t\treturn (\n\t\t\t\t`file of extension type: ${message.content.value?.orig_name?.split(\".\").pop()}` +\n\t\t\t\t(message.content.value?.orig_name ?? \"\")\n\t\t\t);\n\t\t}\n\t\treturn `a component of type ${message.content.component ?? \"unknown\"}`;\n\t}\n\n\tfunction group_messages(\n\t\tmessages: NormalisedMessage[]\n\t): NormalisedMessage[][] {\n\t\tconst groupedMessages: NormalisedMessage[][] = [];\n\t\tlet currentGroup: NormalisedMessage[] = [];\n\t\tlet currentRole: MessageRole | null = null;\n\n\t\tfor (const message of messages) {\n\t\t\tif (msg_format === \"tuples\") {\n\t\t\t\tcurrentRole = null;\n\t\t\t}\n\n\t\t\tif (!(message.role === \"assistant\" || message.role === \"user\")) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (message.role === currentRole) {\n\t\t\t\tcurrentGroup.push(message);\n\t\t\t} else {\n\t\t\t\tif (currentGroup.length > 0) {\n\t\t\t\t\tgroupedMessages.push(currentGroup);\n\t\t\t\t}\n\t\t\t\tcurrentGroup = [message];\n\t\t\t\tcurrentRole = message.role;\n\t\t\t}\n\t\t}\n\n\t\tif (currentGroup.length > 0) {\n\t\t\tgroupedMessages.push(currentGroup);\n\t\t}\n\n\t\treturn groupedMessages;\n\t}\n</script>\n\n{#if show_share_button && value !== null && value.length > 0}\n\t<div class=\"share-button\">\n\t\t<ShareButton\n\t\t\t{i18n}\n\t\t\ton:error\n\t\t\ton:share\n\t\t\tformatter={format_chat_for_sharing}\n\t\t\t{value}\n\t\t/>\n\t</div>\n{/if}\n\n{#if show_copy_all_button}\n\t<CopyAll {value} />\n{/if}\n\n<div\n\tclass={layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"}\n\tclass:placeholder-container={value === null || value.length === 0}\n\tbind:this={div}\n\trole=\"log\"\n\taria-label=\"chatbot conversation\"\n\taria-live=\"polite\"\n>\n\t<div class=\"message-wrap\" use:copy>\n\t\t{#if value !== null && value.length > 0 && groupedMessages !== null}\n\t\t\t{#each groupedMessages as messages, i}\n\t\t\t\t{@const role = messages[0].role === \"user\" ? \"user\" : \"bot\"}\n\t\t\t\t{@const avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{@const opposite_avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{#if is_image_preview_open}\n\t\t\t\t\t<div class=\"image-preview\">\n\t\t\t\t\t\t<img src={image_preview_source} alt={image_preview_source_alt} />\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"image-preview-close-button\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tis_image_preview_open = false;\n\t\t\t\t\t\t\t}}><Clear /></button\n\t\t\t\t\t\t>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t<div\n\t\t\t\t\tclass=\"message-row {layout} {role}-row\"\n\t\t\t\t\tclass:with_avatar={avatar_img !== null}\n\t\t\t\t\tclass:with_opposite_avatar={opposite_avatar_img !== null}\n\t\t\t\t>\n\t\t\t\t\t{#if avatar_img !== null}\n\t\t\t\t\t\t<div class=\"avatar-container\">\n\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\tclass=\"avatar-image\"\n\t\t\t\t\t\t\t\tsrc={avatar_img?.url}\n\t\t\t\t\t\t\t\talt=\"{role} avatar\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"flex-wrap {role} \"\n\t\t\t\t\t\tclass:component-wrap={messages[0].type === \"component\"}\n\t\t\t\t\t>\n\t\t\t\t\t\t{#each messages as message, thought_index}\n\t\t\t\t\t\t\t{@const msg_type = messages[0].type}\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclass=\"message {role} {is_component_message(message)\n\t\t\t\t\t\t\t\t\t? message?.content.component\n\t\t\t\t\t\t\t\t\t: ''}\"\n\t\t\t\t\t\t\t\tclass:message-fit={!bubble_full_width}\n\t\t\t\t\t\t\t\tclass:panel-full-width={true}\n\t\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\t\tstyle:text-align={rtl && role === \"user\" ? \"left\" : \"right\"}\n\t\t\t\t\t\t\t\tclass:component={msg_type === \"component\"}\n\t\t\t\t\t\t\t\tclass:html={is_component_message(message) &&\n\t\t\t\t\t\t\t\t\tmessage.content.component === \"html\"}\n\t\t\t\t\t\t\t\tclass:thought={thought_index > 0}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\tdata-testid={role}\n\t\t\t\t\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\t\t\tstyle:user-select=\"text\"\n\t\t\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\t\t\tstyle:cursor={selectable ? \"pointer\" : \"default\"}\n\t\t\t\t\t\t\t\t\tstyle:text-align={rtl ? \"right\" : \"left\"}\n\t\t\t\t\t\t\t\t\ton:click={() => handle_select(i, message)}\n\t\t\t\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\t\t\thandle_select(i, message);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\t\t\t\t\taria-label={role +\n\t\t\t\t\t\t\t\t\t\t\"'s message: \" +\n\t\t\t\t\t\t\t\t\t\tget_message_label_data(message)}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#if message.type === \"text\"}\n\t\t\t\t\t\t\t\t\t\t{#if message.metadata.title}\n\t\t\t\t\t\t\t\t\t\t\t<MessageBox title={message.metadata.title}>\n\t\t\t\t\t\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\t\t\t\t\t\tmessage={message.content}\n\t\t\t\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</MessageBox>\n\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\t\t\t\t\tmessage={message.content}\n\t\t\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t{:else if message.type === \"component\" && message.content.component in _components}\n\t\t\t\t\t\t\t\t\t\t<Component\n\t\t\t\t\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t\t\t\t\tprops={message.content.props}\n\t\t\t\t\t\t\t\t\t\t\ttype={message.content.component}\n\t\t\t\t\t\t\t\t\t\t\tcomponents={_components}\n\t\t\t\t\t\t\t\t\t\t\tvalue={message.content.value}\n\t\t\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{:else if message.type === \"component\" && message.content.component === \"file\"}\n\t\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\t\t\t\t\t\t\t\tclass=\"file-pil\"\n\t\t\t\t\t\t\t\t\t\t\thref={message.content.value.url}\n\t\t\t\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t\t\t\t\t: message.content.value?.orig_name ||\n\t\t\t\t\t\t\t\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"file\"}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t{message.content.value?.orig_name ||\n\t\t\t\t\t\t\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\t\t\t\t\t\t\"file\"}\n\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<LikeButtons\n\t\t\t\t\tshow={likeable || show_copy_button}\n\t\t\t\t\thandle_action={(selected) => handle_like(i, messages[0], selected)}\n\t\t\t\t\t{likeable}\n\t\t\t\t\t{show_copy_button}\n\t\t\t\t\tmessage={msg_format === \"tuples\" ? messages[0] : messages}\n\t\t\t\t\tposition={role === \"user\" ? \"right\" : \"left\"}\n\t\t\t\t\tavatar={avatar_img}\n\t\t\t\t\t{layout}\n\t\t\t\t/>\n\t\t\t{/each}\n\t\t\t{#if pending_message}\n\t\t\t\t<Pending {layout} />\n\t\t\t{/if}\n\t\t{:else if placeholder !== null}\n\t\t\t<center>\n\t\t\t\t<Markdown message={placeholder} {latex_delimiters} {root} />\n\t\t\t</center>\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.placeholder-container {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 100%;\n\t}\n\t.panel-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.flex-wrap {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.bubble-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t\theight: 100%;\n\t\tpadding-top: var(--spacing-xxl);\n\t}\n\n\t:global(.dark) .bubble-wrap {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.bubble-gap {\n\t\tgap: calc(var(--spacing-xxl) + var(--spacing-lg));\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-body-text-size);\n\t\toverflow-wrap: break-word;\n\t}\n\n\t.thought {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message :global(.prose) {\n\t\tfont-size: var(--chatbot-body-text-size);\n\t}\n\n\t.message-bubble-border {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t.user {\n\t\talign-self: flex-end;\n\t}\n\n\t.message-fit {\n\t\twidth: fit-content !important;\n\t}\n\n\t.panel-full-width {\n\t\twidth: 100%;\n\t}\n\t.message-markdown-disabled {\n\t\twhite-space: pre-line;\n\t}\n\n\t.flex-wrap.user {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t\talign-self: flex-start;\n\t\tborder-bottom-right-radius: 0;\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\ttext-align: right;\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t\tborder-color: var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t:not(.component-wrap).flex-wrap.bot {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-lg);\n\t\talign-self: flex-start;\n\t\tborder-bottom-left-radius: 0;\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\ttext-align: right;\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* Colors */\n\t.bubble .bot {\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.message-row {\n\t\tdisplay: flex;\n\t\t/* flex-direction: column; */\n\t\tposition: relative;\n\t}\n\n\t.message-row.user-row {\n\t\talign-self: flex-end;\n\t}\n\t.message-row.bubble {\n\t\tmargin: calc(var(--spacing-xl) * 3);\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.with_avatar.message-row.panel {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.with_avatar.message-row.bubble.user-row {\n\t\tmargin-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.with_avatar.message-row.bubble.bot-row {\n\t\tmargin-left: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.with_opposite_avatar.message-row.bubble.user-row {\n\t\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t.message-row.panel {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-xl) * 3) calc(var(--spacing-xxl) * 2);\n\t}\n\n\t.message-row.panel.bot-row {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.message-row.panel.user-row {\n\t\talign-self: flex-end;\n\t}\n\n\t.message-row.bubble.bot-row {\n\t\talign-self: flex-start;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.message-row:last-of-type {\n\t\tmargin-bottom: calc(var(--spacing-xxl) * 2);\n\t}\n\n\t.user-row.bubble {\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t}\n\t@media (max-width: 480px) {\n\t\t.user-row.bubble {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row.bubble {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\t.user-row > .avatar-container {\n\t\torder: 2;\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\t.bot-row > .avatar-container {\n\t\tmargin-right: var(--spacing-xxl);\n\t\tmargin-left: 0;\n\t\tmargin-top: -5px;\n\t}\n\n\t.avatar-container:not(.thumbnail-item) :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t\tpadding: 6px;\n\t}\n\n\t.share-button {\n\t\tposition: absolute;\n\t\ttop: 4px;\n\t\tright: 6px;\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\t.message-wrap > .message :not(.image-button) :global(img) {\n\t\tmargin: var(--size-2);\n\t\tmax-height: 200px;\n\t}\n\n\t.message-wrap\n\t\t> div\n\t\t:not(.avatar-container)\n\t\tdiv\n\t\t:not(.image-button)\n\t\t:global(img) {\n\t\tborder-radius: var(--radius-xl);\n\t\tmargin: var(--size-2);\n\t\twidth: 400px;\n\t\tmax-width: 30vw;\n\t\tmax-height: 30vw;\n\t}\n\n\t.message-wrap .message :global(a) {\n\t\tcolor: var(--color-text-link);\n\t\ttext-decoration: underline;\n\t}\n\n\t.message-wrap .bot :global(table),\n\t.message-wrap .bot :global(tr),\n\t.message-wrap .bot :global(td),\n\t.message-wrap .bot :global(th) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.message-wrap .user :global(table),\n\t.message-wrap .user :global(tr),\n\t.message-wrap .user :global(td),\n\t.message-wrap .user :global(th) {\n\t\tborder: 1px solid var(--border-color-accent);\n\t}\n\n\t/* KaTeX */\n\t.message-wrap :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\t/* Copy button */\n\t.message-wrap :global(div[class*=\"code_wrap\"] > button) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\tz-index: 1;\n\t\tcursor: pointer;\n\t\tborder-bottom-left-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-md);\n\t\twidth: 25px;\n\t\theight: 25px;\n\t}\n\n\t.message-wrap :global(code > button > span) {\n\t\tposition: absolute;\n\t\ttop: var(--spacing-md);\n\t\tright: var(--spacing-md);\n\t\twidth: 12px;\n\t\theight: 12px;\n\t}\n\t.message-wrap :global(.check) {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\topacity: 0;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.2s;\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-1);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.message-wrap :global(pre) {\n\t\tposition: relative;\n\t}\n\n\t.message-wrap :global(.grid-wrap) {\n\t\tmax-height: 80% !important;\n\t\tmax-width: 600px;\n\t\tobject-fit: contain;\n\t}\n\n\t/* Image preview */\n\t.message :global(.preview) {\n\t\tobject-fit: contain;\n\t\twidth: 95%;\n\t\tmax-height: 93%;\n\t}\n\t.image-preview {\n\t\tposition: absolute;\n\t\tz-index: 999;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: auto;\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.image-preview :global(svg) {\n\t\tstroke: white;\n\t}\n\t.image-preview-close-button {\n\t\tposition: absolute;\n\t\ttop: 10px;\n\t\tright: 10px;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tfont-size: 1.5em;\n\t\tcursor: pointer;\n\t\theight: 30px;\n\t\twidth: 30px;\n\t\tpadding: 3px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.component {\n\t\tpadding: 0;\n\t\tborder-radius: var(--radius-md);\n\t\twidth: fit-content;\n\t\tmax-width: 80%;\n\t\tmax-height: 80%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\toverflow: hidden;\n\t}\n\n\t.component.gallery {\n\t\tborder: none;\n\t}\n\n\t.file-pil {\n\t\tdisplay: block;\n\t\twidth: fit-content;\n\t\tpadding: var(--spacing-sm) var(--spacing-lg);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t\ttext-decoration: none;\n\t\tmargin: 0;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.file {\n\t\twidth: auto !important;\n\t\tmax-width: fit-content !important;\n\t}\n\n\t@media (max-width: 600px) or (max-width: 480px) {\n\t\t.component {\n\t\t\tmax-width: calc(100% - var(--spacing-xl) * 3);\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t:global(.prose.chatbot.md) {\n\t\topacity: 0.8;\n\t}\n\n\t.message > button {\n\t\twidth: 100%;\n\t}\n\t.html {\n\t\tpadding: 0;\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseChatBot } from \"./shared/ChatBot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, LikeData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./shared/ChatBot.svelte\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { Message, TupleFormat, NormalisedMessage } from \"./types\";\n\n\timport { normalise_tuples, normalise_messages } from \"./shared/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: TupleFormat | Message[] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let _selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = true;\n\texport let show_copy_all_button = false;\n\texport let sanitize_html = true;\n\texport let bubble_full_width = true;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let type: \"tuples\" | \"messages\" = \"tuples\";\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tlike: LikeData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\n\tlet _value: NormalisedMessage[] | null = [];\n\n\t$: _value =\n\t\ttype === \"tuples\"\n\t\t\t? normalise_tuples(value as TupleFormat, root)\n\t\t\t: normalise_messages(value as Message[], root);\n\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height = 400;\n\texport let placeholder: string | null = null;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\tallow_overflow={false}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={true}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\ti18n={gradio.i18n}\n\t\t\tselectable={_selectable}\n\t\t\t{likeable}\n\t\t\t{show_share_button}\n\t\t\t{show_copy_all_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\t{render_markdown}\n\t\t\t{theme_mode}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:like={(e) => gradio.dispatch(\"like\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\t{avatar_images}\n\t\t\t{sanitize_html}\n\t\t\t{bubble_full_width}\n\t\t\t{line_breaks}\n\t\t\t{layout}\n\t\t\t{placeholder}\n\t\t\tupload={gradio.client.upload}\n\t\t\t_fetch={gradio.client.fetch}\n\t\t\tload_component={gradio.load_component}\n\t\t\tmsg_format={type}\n\t\t\troot={gradio.root}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "format_chat_for_sharing", "chat", "message_pair", "message", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "file_url", "redirect_src_url", "src", "root", "get_component_for_mime_type", "mime_type", "convert_file_message_to_component_message", "_file", "normalise_messages", "messages", "i", "normalise_tuples", "index", "role", "is_component_message", "set_style", "div3", "ctx", "layout", "$$props", "div", "create_if_block", "button", "span0", "span1", "expanded", "title", "toggleExpanded", "$$invalidate", "switch_value", "func", "dirty", "switch_instance_changes", "track", "type", "components", "value", "theme_mode", "props", "i18n", "upload", "_fetch", "path", "attr", "button0", "button0_aria_label_value", "toggle_class", "button1", "button1_aria_label_value", "current", "handle_action", "padded", "position", "selected", "onDestroy", "create_if_block_1", "copied", "timer", "copy_feedback", "handle_copy", "textArea", "error", "create_if_block_3", "div_class_value", "downloadlink_changes", "span", "likedislike_changes", "is_all_text", "m", "all_text", "likeable", "show_copy_button", "show", "avatar", "message_text", "show_copy", "show_download", "copy_conversation", "conversation_value", "err", "beforeUpdate", "afterUpdate", "createEventDispatcher", "tick", "onMount", "constants_0", "child_ctx", "constants_1", "constants_2", "div_1", "center", "each_blocks", "img", "img_src_value", "image_changes", "a", "a_href_value", "a_download_value", "set_data", "t", "t_value", "markdown_changes", "button_aria_label_value", "get_message_label_data", "create_if_block_7", "if_block1", "create_if_block_6", "div0", "div1", "div1_class_value", "likebuttons_changes", "create_if_block_10", "create_if_block_9", "null_to_empty", "get_components_from_messages", "adjust_text_size", "body_text_size", "updated_text_size", "old_value", "load_component", "_components", "load_components", "component_names", "names", "component_name", "name", "component", "latex_delimiters", "pending_message", "selectable", "show_share_button", "show_copy_all_button", "rtl", "avatar_images", "sanitize_html", "bubble_full_width", "render_markdown", "line_breaks", "placeholder", "msg_format", "autoscroll", "dispatch", "scroll", "image_preview_source", "image_preview_source_alt", "is_image_preview_open", "n", "e", "handle_select", "handle_like", "groupedMessages", "message_group", "first", "last", "group_messages", "currentGroup", "currentRole", "$$value", "dequal", "Cha<PERSON>", "blocklabel_changes", "chatbot_changes", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "_selectable", "gradio", "_value", "loading_status", "height", "clear_status_handler", "change_handler"], "mappings": "ogDAAAA,GAgBKC,EAAAC,EAAAC,CAAA,EALJC,GAGCF,EAAAG,CAAA,EACDD,GAAyDF,EAAAI,CAAA,uGCJ7C,MAAAC,GAA0B,MACtCC,IAEe,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOC,GACR,MAAM,QAAQ,IACpBA,EAAa,IAAI,MAAOC,EAAS,IAAM,CACtC,GAAIA,IAAY,KAAa,MAAA,GACzB,IAAAC,EAAgB,IAAM,EAAI,KAAO,KACjCC,EAAe,GAEf,GAAA,OAAOF,GAAY,SAAU,CAChC,MAAMG,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGOD,EAAAF,EAEf,OAAS,CAACI,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKL,CAAO,KAAO,MAAM,CAC9C,MAAMO,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,CAAc,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,CACpD,CACD,CAAA,KACM,CACN,GAAI,CAACR,GAAS,IAAY,MAAA,GAC1B,MAAMU,EAAW,MAAMD,GAAoBT,EAAQ,GAAU,EACzDA,EAAQ,WAAW,SAAS,OAAO,EACtCE,EAAe,wBAAwBQ,CAAQ,aACrCV,EAAQ,WAAW,SAAS,OAAO,EAC9BE,EAAAQ,EACLV,EAAQ,WAAW,SAAS,OAAO,IAC7CE,EAAe,aAAaQ,CAAQ,OAEtC,CAEO,MAAA,GAAGT,CAAa,KAAKC,CAAY,EAAA,CACxC,CAAA,CAEF,CAAA,GAGA,IAAKH,GACLA,EAAa,KACZA,EAAa,CAAC,IAAM,IAAMA,EAAa,CAAC,IAAM,GAAK;AAAA,EAAO,EAC3D,CAAA,EAEA,KAAK;AAAA,CAAI,EAGNY,GAAmB,CAACC,EAAaC,IACtCD,EAAI,QAAQ,aAAc,QAAQC,CAAI,MAAM,EAE7C,SAASC,GACRC,EACS,CACT,OAAKA,EACDA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACjC,OAJgB,MAKxB,CAEA,SAASC,GACRhB,EACgB,CACV,MAAAiB,EAAQ,MAAM,QAAQjB,EAAQ,IAAI,EAAIA,EAAQ,KAAK,CAAC,EAAIA,EAAQ,KAC/D,MAAA,CACN,UAAWc,GAA4BG,GAAO,SAAS,EACvD,MAAOjB,EAAQ,KACf,SAAUA,EAAQ,SAClB,iBAAkB,CAAC,EACnB,MAAO,CAAC,CAAA,CAEV,CAEgB,SAAAkB,GACfC,EACAN,EAC6B,CAC7B,OAAIM,IAAa,KAAaA,EACvBA,EAAS,IAAI,CAACnB,EAASoB,IACzB,OAAOpB,EAAQ,SAAY,SACvB,CACN,KAAMA,EAAQ,KACd,SAAUA,EAAQ,SAClB,QAASW,GAAiBX,EAAQ,QAASa,CAAI,EAC/C,KAAM,OACN,MAAOO,CAAA,EAEE,SAAUpB,EAAQ,QACrB,CACN,QAASgB,GAA0ChB,EAAQ,OAAO,EAClE,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,KACd,KAAM,YACN,MAAOoB,CAAA,EAGF,CAAE,KAAM,YAAa,GAAGpB,CAAQ,CACvC,CACF,CAEgB,SAAAqB,GACfF,EACAN,EAC6B,CAC7B,OAAIM,IAAa,KAAaA,EAClBA,EAAS,QAAQ,CAACpB,EAAc,IACpCA,EAAa,IAAI,CAACC,EAASsB,IAAU,CAC3C,GAAItB,GAAW,KAAa,OAAA,KACtB,MAAAuB,EAAOD,GAAS,EAAI,OAAS,YAE/B,OAAA,OAAOtB,GAAY,SACf,CACN,KAAAuB,EACA,KAAM,OACN,QAASZ,GAAiBX,EAASa,CAAI,EACvC,SAAU,CAAE,MAAO,IAAK,EACxB,MAAO,CAAC,EAAGS,CAAK,CAAA,EAId,SAAUtB,EACN,CACN,QAASgB,GAA0ChB,CAAO,EAC1D,KAAAuB,EACA,KAAM,YACN,MAAO,CAAC,EAAGD,CAAK,CAAA,EAIX,CACN,KAAAC,EACA,QAASvB,EACT,KAAM,YACN,MAAO,CAAC,EAAGsB,CAAK,CAAA,CACjB,CACA,CACD,EACU,OAAQtB,GAAYA,GAAW,IAAI,CAC/C,CAEO,SAASwB,GACfxB,EAC8B,CAC9B,OAAOA,EAAQ,OAAS,WACzB;;;;wLC1JsByB,GAAAC,EAAA,gBAAAC,OAAW,SAAW,oBAAsB,MAAM,UALxErC,GAaKC,EAAAmC,EAAAjC,CAAA,iBARiBgC,GAAAC,EAAA,gBAAAC,OAAW,SAAW,oBAAsB,MAAM,gDAR5D,GAAA,CAAA,OAAAC,EAAS,QAAQ,EAAAC,yrBCmB3BvC,GAEKC,EAAAuC,EAAArC,CAAA,iNAHDkC,EAAQ,CAAA,GAAAI,GAAAJ,CAAA,0DARcA,EAAK,CAAA,CAAA,wJAEbA,EAAQ,CAAA,EAAG,YAAc,eAAe,kFAJ5DrC,GAeQC,EAAAyC,EAAAvC,CAAA,EAdPC,GAQKsC,EAAAF,CAAA,EAPJpC,GAAsCoC,EAAAG,CAAA,kBACtCvC,GAKMoC,EAAAI,CAAA,iDARsBP,EAAc,CAAA,CAAA,kCAEhBA,EAAK,CAAA,CAAA,wBAEbA,EAAQ,CAAA,EAAG,YAAc,eAAe,EAMtDA,EAAQ,CAAA,iOAlBTQ,EAAW,IACJ,MAAAC,CAAa,EAAAP,WAEfQ,GAAc,CACtBC,EAAA,EAAAH,GAAYA,CAAQ,mkBCuEd,IAAAI,EAAAZ,KAAWA,EAAI,CAAA,CAAA,sDAET,2CAEO,qBAET,SAAQa,EAAA,2HANZ,GAAAC,EAAA,GAAAF,KAAAA,EAAAZ,KAAWA,EAAI,CAAA,CAAA,GAAA,oWAVf,IAAAY,EAAAZ,KAAWA,EAAI,CAAA,CAAA,sDAET,8CAEU,sIAJhB,GAAAc,EAAA,GAAAF,KAAAA,EAAAZ,KAAWA,EAAI,CAAA,CAAA,GAAA,oWAdf,IAAAY,EAAAZ,KAAWA,EAAI,CAAA,CAAA,yCACX,SACHA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,aACf,qBACO,8CAGG,mKAPhB,GAAAc,EAAA,GAAAF,KAAAA,EAAAZ,KAAWA,EAAI,CAAA,CAAA,GAAA,oMAEdA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,yNAfrB,IAAAY,EAAAZ,KAAWA,EAAI,CAAA,CAAA,sDAET,qBACO,oFAKG,4HARhB,GAAAc,EAAA,GAAAF,KAAAA,EAAAZ,KAAWA,EAAI,CAAA,CAAA,GAAA,oWAXf,IAAAY,EAAAZ,KAAWA,EAAI,CAAA,CAAA,uEAIN,cAAAA,KAAM,6CAEA,4HANf,GAAAc,EAAA,GAAAF,KAAAA,EAAAZ,KAAWA,EAAI,CAAA,CAAA,GAAA,yPAINc,EAAA,KAAAC,EAAA,cAAAf,KAAM,qJAlBf,IAAAY,EAAAZ,KAAWA,EAAI,CAAA,CAAA,sDAET,gDAIG,eACF,+BAEC,0HATR,GAAAc,EAAA,GAAAF,KAAAA,EAAAZ,KAAWA,EAAI,CAAA,CAAA,GAAA,+aAgDrBrC,GAAwBC,EAAAoD,EAAAlD,CAAA,oGAlDrB,OAAAkC,OAAS,UAAS,EAcbA,OAAS,OAAM,EAWfA,OAAS,QAAO,EAahBA,OAAS,QAAO,EAchBA,OAAS,QAAO,EAUhBA,OAAS,OAAM,0XAzEb,KAAAiB,CAA+D,EAAAf,GAC/D,WAAAgB,CAAU,EAAAhB,GACV,MAAAiB,CAAK,EAAAjB,GACL,OAAAtC,CAAM,EAAAsC,GACN,WAAAkB,CAAU,EAAAlB,GACV,MAAAmB,CAAK,EAAAnB,GACL,KAAAoB,CAAI,EAAApB,GACJ,OAAAqB,CAAM,EAAArB,GACN,OAAAsB,CAAM,EAAAtB,qtECTlBvC,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAA4D,CAAA,o0CCVF9D,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAA4D,CAAA,89BCVF9D,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAA4D,CAAA,g0CCVF9D,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAA4D,CAAA,4pCCaI,OAAAzB,OAAa,UAAS,+DAiBtB,OAAAA,OAAa,OAAM,uFAtBZ0B,GAAAC,EAAA,aAAAC,EAAA5B,OAAa,UAAY,kBAAoB,SAAS,mCAG3CA,EAAQ,CAAA,EAAA,gBAAA,sBADf6B,GAAAF,EAAA,WAAA3B,OAAa,SAAS,4CAiB1B0B,GAAAI,EAAA,aAAAC,EAAA/B,EAAa,CAAA,IAAA,OAAS,eAAiB,MAAM,sBACzC6B,GAAAC,EAAA,WAAA9B,OAAa,MAAM,UAzBpCrC,GAeQC,EAAA+D,EAAA7D,CAAA,2BAERH,GAeQC,EAAAkE,EAAAhE,CAAA,yMA3BK,CAAAkE,GAAAlB,EAAA,GAAAc,KAAAA,EAAA5B,OAAa,UAAY,kBAAoB,qEAGlCA,EAAQ,CAAA,EAAA,+EADf6B,GAAAF,EAAA,WAAA3B,OAAa,SAAS,2HAiB1B,CAAAgC,GAAAlB,EAAA,GAAAiB,KAAAA,EAAA/B,EAAa,CAAA,IAAA,OAAS,eAAiB,yEACnC6B,GAAAC,EAAA,WAAA9B,OAAa,MAAM,wIAhCxB,cAAAiC,CAAgD,EAAA/B,EAChD,CAAA,OAAAgC,EAAS,EAAK,EAAAhC,EACd,CAAA,SAAAiC,EAA6B,MAAM,EAAAjC,EAE1CkC,EAAsC,kBAKzCzB,EAAA,EAAAyB,EAAW,SAAS,EACpBH,EAAcG,CAAQ,UAkBtBzB,EAAA,EAAAyB,EAAW,MAAM,EACjBH,EAAcG,CAAQ,2xBClCd,WAAAC,EAAA,SAAyB,qZAmD5BrC,EAAM,CAAA,GAAAsC,GAAA,IAGPtC,EAAM,CAAA,GAAAI,GAAA,mIALCJ,EAAM,CAAA,EAAG,iBAAmB,cAAc,UAJvDrC,GAYQC,EAAAyC,EAAAvC,CAAA,gEAXGkC,EAAW,CAAA,CAAA,kBAKfA,EAAM,CAAA,uFAGPA,EAAM,CAAA,2GALCA,EAAM,CAAA,EAAG,iBAAmB,yJA9CpC,IAAAuC,EAAS,IACF,MAAApB,CAAa,EAAAjB,EACpBsC,WAEKC,GAAa,CACrB9B,EAAA,EAAA4B,EAAS,EAAI,EACTC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACP7B,EAAA,EAAA4B,EAAS,EAAK,GACZ,oBAGWG,GAAW,CACrB,GAAA,cAAe,UACZ,MAAA,UAAU,UAAU,UAAUvB,CAAK,EACzCsB,SAEM,MAAAE,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQxB,EAEjBwB,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAM,MAGd,SAAS,YAAY,MAAM,EAC3BF,UACQG,EAAK,CACb,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAM,IAKlBN,OAAAA,GAAS,IAAA,CACJG,GAAO,aAAaA,CAAK,ohFC1C/B7E,GAeKC,EAAAC,EAAAC,CAAA,EARJC,GAGCF,EAAAG,CAAA,EACDD,GAGCF,EAAAI,CAAA,uaCuCK+B,EAAa,CAAA,GAAA,CAAK,MAAM,QAAQA,EAAO,CAAA,CAAA,GAAKH,GAAqBG,EAAO,CAAA,CAAA,UAHxEA,EAAS,CAAA,GAAA6C,GAAA7C,CAAA,eAaTA,EAAQ,CAAA,GAAAsC,GAAAtC,CAAA,kEAhBW0B,GAAAvB,EAAA,QAAA2C,EAAA,mBAAA9C,SAAWA,EAAM,CAAA,EAAA,qBAAoBA,EAC5D,CAAA,IAAA,MAAQ,eAAa,gBAAA,UAFvBrC,GAoBKC,EAAAuC,EAAArC,CAAA,4EAhBCkC,EAAS,CAAA,4GAGTA,EAAa,CAAA,GAAA,CAAK,MAAM,QAAQA,EAAO,CAAA,CAAA,GAAKH,GAAqBG,EAAO,CAAA,CAAA,qGAUxEA,EAAQ,CAAA,oGAhBW,CAAAgC,GAAAlB,EAAA,IAAAgC,KAAAA,EAAA,mBAAA9C,SAAWA,EAAM,CAAA,EAAA,qBAAoBA,EAC5D,CAAA,IAAA,MAAQ,eAAa,8LAGRA,EAAY,CAAA,CAAA,qFAAZA,EAAY,CAAA,6IAIlB,KAAAA,EAAS,CAAA,GAAA,SAAS,MAAM,IACpB,SAAAA,KAAQ,QAAQ,MAAM,WAAa,mHADvCc,EAAA,IAAAiC,EAAA,KAAA/C,EAAS,CAAA,GAAA,SAAS,MAAM,KACpBc,EAAA,IAAAiC,EAAA,SAAA/C,KAAQ,QAAQ,MAAM,WAAa,yQAE7CrC,GAEMC,EAAAoF,EAAAlF,CAAA,mLAI8B,OAAAkC,MAAaA,EAAa,CAAA,mGAA1Bc,EAAA,MAAAmC,EAAA,OAAAjD,MAAaA,EAAa,CAAA,uHAnB7DA,EAAI,CAAA,GAAAI,GAAAJ,CAAA,0EAAJA,EAAI,CAAA,2KA1BC,SAAAkD,GACR7E,EAAgD,QAG9C,MAAM,QAAQA,CAAO,GACrBA,EAAQ,MAAO8E,GAAC,OAAYA,EAAE,SAAY,QAAQ,GAAA,CACjD,MAAM,QAAQ9E,CAAO,GAAA,OAAYA,EAAQ,SAAY,SAIhD,SAAA+E,GAAS/E,EAAoC,QACjD,MAAM,QAAQA,CAAO,EACjBA,EAAQ,IAAK8E,GAAMA,EAAE,OAAO,EAAE,KAAK;AAAA,CAAI,EAExC9E,EAAQ,sCAxBL,SAAAgF,CAAiB,EAAAnD,GACjB,iBAAAoD,CAAyB,EAAApD,GACzB,KAAAqD,CAAa,EAAArD,GACb,QAAA7B,CAAgD,EAAA6B,GAChD,SAAAiC,CAA0B,EAAAjC,GAC1B,OAAAsD,CAAuB,EAAAtD,GAEvB,cAAA+B,CAAgD,EAAA/B,GAChD,OAAAD,CAA0B,EAAAC,8UAmBpCS,EAAA,EAAE8C,EAAeP,GAAY7E,CAAO,EAAI+E,GAAS/E,CAAO,EAAI,EAAE,mBAE9DsC,EAAA,EAAE+C,EAAYJ,GAAoBjF,GAAW6E,GAAY7E,CAAO,CAAA,oBAC9DsF,EAAa,CACd,MAAM,QAAQtF,CAAO,GACtBwB,GAAqBxB,CAAO,GAC5BA,EAAQ,QAAQ,OAAO,GAAG,+nCCzClB,CAAA,UAAAgE,EAAA,SAAyB,kcAoD7BrC,EAAM,CAAA,EAAA,+GAHJA,EAAM,CAAA,EAAG,SAAW,WAAW,EAAA,iBAAA,sBAC1BA,EAAM,CAAA,EAAG,sBAAwB,mBAAmB,UAJjErC,GAWQC,EAAAyC,EAAAvC,CAAA,yCAVGkC,EAAW,CAAA,CAAA,0JAEdA,EAAM,CAAA,EAAG,SAAW,WAAW,EAAA,sDAC1BA,EAAM,CAAA,EAAG,sBAAwB,yIA9CzC,IAAAuC,EAAS,IACF,MAAApB,CAAiC,EAAAjB,EAExCsC,WAEKC,GAAa,CACrB9B,EAAA,EAAA4B,EAAS,EAAI,EACTC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACP7B,EAAA,EAAA4B,EAAS,EAAK,GACZ,WAGEqB,EAAiB,IAAA,IAClBzC,EAAK,CACF,MAAA0C,EAAqB1C,EACzB,IAAK9C,GACDA,EAAQ,OAAS,OACV,GAAAA,EAAQ,IAAI,KAAKA,EAAQ,OAAO,MAEjCA,EAAQ,IAAI,KAAKA,EAAQ,QAAQ,MAAM,GAAG,EAEpD,EAAA,KAAK;AAAA;AAAA,CAAM,EAEb,UAAU,UAAU,UAAUwF,CAAkB,EAAE,MAAOC,GAAG,CAC3D,QAAQ,MAAM,gCAAiCA,CAAG,qBAKtCpB,GAAW,CACrB,cAAe,YAClBkB,IACAnB,KAIF,OAAAJ,GAAS,IAAA,CACJG,GAAO,aAAaA,CAAK,sqBCpC7B,aAAAuB,GACA,YAAAC,GACA,sBAAAC,GAGA,KAAAC,GACA,QAAAC,WACc,mFAgSG,MAAAC,EAAAC,EAAS,EAAA,EAAA,CAAC,EAAE,OAAS,OAAS,OAAS,cACjC,MAAAC,EAAAD,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,UAC5B,MAAAE,EAAAF,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,mFA+B7CA,EAAQ,EAAA,EAAC,CAAC,EAAE,4FAtDxBnG,+IALbP,EAQKC,EAAA4G,EAAA1G,CAAA,8dA+JiBkC,EAAW,EAAA,gHAD/BrC,EAEQC,EAAA6G,EAAA3G,CAAA,8DADYkC,EAAW,EAAA,gMA9IxBA,EAAe,EAAA,CAAA,uBAApB,OAAIP,GAAA,kEAyIDO,EAAe,CAAA,GAAAsC,GAAAtC,CAAA,0MAzIbA,EAAe,EAAA,CAAA,oBAApB,OAAIP,GAAA,EAAA,mHAAJ,OAAIA,EAAAiF,EAAA,OAAAjF,GAAA,WAyIDO,EAAe,CAAA,yIAzIlB,OAAIP,GAAA,2QAMOO,EAAoB,EAAA,CAAA,GAAA0B,EAAAiD,EAAA,MAAAC,CAAA,YAAO5E,EAAwB,EAAA,CAAA,6IAD9DrC,EAQKC,EAAA4G,EAAA1G,CAAA,EAPJC,GAAgEyG,EAAAG,CAAA,UAChE5G,GAKAyG,EAAAnE,CAAA,2FANUL,EAAoB,EAAA,CAAA,gDAAOA,EAAwB,EAAA,CAAA,uKAkBtD,IAAAA,OAAY,QACXA,EAAI,EAAA,EAAA,qGAJZrC,EAMKC,EAAA4G,EAAA1G,CAAA,sCAHEgD,EAAA,CAAA,EAAA,YAAA+D,EAAA,IAAA7E,OAAY,4BACXA,EAAI,EAAA,EAAA,mIA2FNA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,QAAM,8GAVD0B,EAAAoD,EAAA,OAAAC,EAAA/E,EAAQ,EAAA,EAAA,QAAQ,MAAM,GAAG,yBAErB0B,EAAAoD,EAAA,WAAAE,EAAA,OAAO,aACd,KACAhF,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,MAAM,UATTrC,EAcGC,EAAAkH,EAAAhH,CAAA,0CAHDkC,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,OACvC,QAAM,KAAAiF,GAAAC,EAAAC,CAAA,EAVDrE,EAAA,CAAA,EAAA,WAAAiE,KAAAA,EAAA/E,EAAQ,EAAA,EAAA,QAAQ,MAAM,oBAElBc,EAAA,CAAA,EAAA,WAAAkE,KAAAA,EAAA,OAAO,aACd,KACAhF,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,wIAnBKA,EAAO,EAAA,EAAC,QAAQ,WACjBA,EAAO,EAAA,EAAC,QAAQ,qBACVA,EAAW,EAAA,QAChBA,EAAO,EAAA,EAAC,QAAQ,0DAIdA,EAAM,EAAA,CAAA,0JAPRA,EAAO,EAAA,EAAC,QAAQ,+BACjBA,EAAO,EAAA,EAAC,QAAQ,uCACVA,EAAW,EAAA,4BAChBA,EAAO,EAAA,EAAC,QAAQ,yPA9BnBA,EAAO,EAAA,EAAC,SAAS,MAAK,sVAchB,QAAAA,MAAQ,qHAKRA,EAAM,EAAA,CAAA,iEALNc,EAAA,CAAA,EAAA,YAAAsE,EAAA,QAAApF,MAAQ,iUAbCA,EAAO,EAAA,EAAC,SAAS,wIAAjBA,EAAO,EAAA,EAAC,SAAS,0MAEzB,QAAAA,MAAQ,qHAKRA,EAAM,EAAA,CAAA,iEALNc,EAAA,CAAA,EAAA,YAAAsE,EAAA,QAAApF,MAAQ,sWAJhBA,EAAO,EAAA,EAAC,OAAS,OAAM,EAwBlBA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,aAAaA,EAAW,EAAA,EAAA,EAaxEA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,YAAc,OAAM,gMAvDjEA,EAAI,EAAA,CAAA,cAaZA,EAAG,CAAA,EAAG,MAAQ,KAAK,EACZ0B,EAAArB,EAAA,aAAAgF,EAAArF,EACX,EAAA,EAAA,eACAsF,GAAuBtF,EAAO,EAAA,CAAA,CAAA,gCAfjB6B,EAAAxB,EAAA,SAAAL,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mCACFA,EAAe,EAAA,CAAA,kEAGnCA,EAAU,CAAA,EAAG,UAAY,SAAS,oBAC9BA,EAAG,CAAA,EAAG,QAAU,MAAM,2BAnBzBA,EAAI,EAAA,EAAA,KAAGH,GAAqBG,EAAO,EAAA,CAAA,EAChDA,EAAO,EAAA,GAAE,QAAQ,UACjB,IAAE,iBAAA,qBACeA,EAAiB,EAAA,CAAA,yBACb,EAAI,mCACMA,EAAe,EAAA,CAAA,EAEhC6B,EAAA2C,EAAA,YAAAxE,QAAa,WAAW,aAC7BH,GAAqBG,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,EACtB6B,EAAA2C,EAAA,UAAAxE,MAAgB,CAAC,oBAJdA,EAAG,CAAA,GAAIA,EAAI,EAAA,IAAK,OAAS,OAAS,OAAO,UAP5DrC,EAuFKC,EAAA4G,EAAA1G,CAAA,EA1EJC,GAyEQyG,EAAAnE,CAAA,kRAxEML,EAAI,EAAA,gDAaZA,EAAG,CAAA,EAAG,MAAQ,uBACP,CAAAgC,GAAAlB,EAAA,CAAA,EAAA,WAAAuE,KAAAA,EAAArF,EACX,EAAA,EAAA,eACAsF,GAAuBtF,EAAO,EAAA,CAAA,uCAfjB6B,EAAAxB,EAAA,SAAAL,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,oDACFA,EAAe,EAAA,CAAA,+DAGnCA,EAAU,CAAA,EAAG,UAAY,SAAS,8BAC9BA,EAAG,CAAA,EAAG,QAAU,MAAM,yCAnBzBA,EAAI,EAAA,EAAA,KAAGH,GAAqBG,EAAO,EAAA,CAAA,EAChDA,EAAO,EAAA,GAAE,QAAQ,UACjB,IAAE,6EACeA,EAAiB,EAAA,CAAA,+CACb,EAAI,yDACMA,EAAe,EAAA,CAAA,wBAEhC6B,EAAA2C,EAAA,YAAAxE,QAAa,WAAW,mCAC7BH,GAAqBG,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,wBACtB6B,EAAA2C,EAAA,UAAAxE,MAAgB,CAAC,oCAJdA,EAAG,CAAA,GAAIA,EAAI,EAAA,IAAK,OAAS,OAAS,OAAO,sHAtC1DA,EAAqB,EAAA,GAAAuF,GAAAvF,CAAA,EAgBpBwF,EAAAxF,QAAe,MAAIyF,GAAAzF,CAAA,OAahBA,EAAQ,EAAA,CAAA,uBAAb,OAAIP,GAAA,oIA8FD,KAAAO,MAAYA,EAAgB,CAAA,sDAIzB,QAAAA,QAAe,SAAWA,MAAS,CAAC,EAAIA,EAAQ,EAAA,EAC/C,SAAAA,EAAS,EAAA,IAAA,OAAS,QAAU,cAC9BA,EAAU,EAAA,qKAvGCA,EAAI,EAAA,EAAA,iBAAA,EACA6B,EAAA6D,EAAA,iBAAA1F,EAAS,EAAA,EAAA,CAAC,EAAE,OAAS,WAAW,EAfnC0B,EAAAiE,EAAA,QAAAC,EAAA,eAAA5F,UAASA,EAAI,EAAA,EAAA,qBAAA,EACd6B,EAAA8D,EAAA,cAAA3F,QAAe,IAAI,EACV6B,EAAA8D,EAAA,uBAAA3F,QAAwB,IAAI,+BAHzDrC,EA8GKC,EAAA+H,EAAA7H,CAAA,yBAhGJC,GA+FK4H,EAAAD,CAAA,yFAxHD1F,EAAqB,EAAA,oHAgBpBA,QAAe,kIAaZA,EAAQ,EAAA,CAAA,oBAAb,OAAIP,GAAA,EAAA,2GAAJ,OAAIA,EAAAiF,EAAA,OAAAjF,GAAA,oDAHYO,EAAI,EAAA,EAAA,0DACA6B,EAAA6D,EAAA,iBAAA1F,EAAS,EAAA,EAAA,CAAC,EAAE,OAAS,WAAW,GAfnC,CAAAgC,GAAAlB,EAAA,CAAA,EAAA,WAAA8E,KAAAA,EAAA,eAAA5F,UAASA,EAAI,EAAA,EAAA,8DACd6B,EAAA8D,EAAA,cAAA3F,QAAe,IAAI,wBACV6B,EAAA8D,EAAA,uBAAA3F,QAAwB,IAAI,aA6GlDc,EAAA,CAAA,EAAA,MAAA+E,EAAA,KAAA7F,MAAYA,EAAgB,CAAA,sGAIzBc,EAAA,CAAA,EAAA,YAAA+E,EAAA,QAAA7F,QAAe,SAAWA,MAAS,CAAC,EAAIA,EAAQ,EAAA,GAC/Cc,EAAA,CAAA,EAAA,YAAA+E,EAAA,SAAA7F,EAAS,EAAA,IAAA,OAAS,QAAU,kCAC9BA,EAAU,EAAA,kFApGf,OAAIP,GAAA,geA3DPO,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAC8F,GAAA9F,CAAA,IAYvDA,EAAoB,CAAA,GAAA+F,GAAA/F,CAAA,8CAalBA,EAAK,CAAA,IAAK,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAKA,EAAe,EAAA,IAAK,KAAI,EA6IzDA,QAAgB,KAAI,sJArJxB0B,EAAAiE,EAAA,QAAAC,EAAAI,GAAAhG,QAAW,SAAW,cAAgB,YAAY,EAAA,iBAAA,uFAC5B6B,EAAA8D,EAAA,wBAAA3F,OAAU,MAAQA,EAAM,CAAA,EAAA,SAAW,CAAC,oDAFlErC,EA4JKC,EAAA+H,EAAA7H,CAAA,EApJJC,GAmJK4H,EAAAD,CAAA,2EA3KD1F,EAAiB,CAAA,GAAIA,EAAU,CAAA,IAAA,MAAQA,EAAK,CAAA,EAAC,OAAS,6GAYtDA,EAAoB,CAAA,uQAKjB,CAAAgC,GAAAlB,EAAA,CAAA,EAAA,QAAA8E,KAAAA,EAAAI,GAAAhG,QAAW,SAAW,cAAgB,YAAY,EAAA,uDAC5B6B,EAAA8D,EAAA,wBAAA3F,OAAU,MAAQA,EAAM,CAAA,EAAA,SAAW,CAAC,6IAtOxD,SAAAiG,GACRzG,EAAoC,KAE/BA,EAAQ,MAAA,GACT,IAAA0B,MAA8B,IAClC,OAAA1B,EAAS,QAASnB,GAAO,CACpBA,EAAQ,OAAS,aACpB6C,EAAW,IAAI7C,EAAQ,QAAQ,SAAS,IAGnC,MAAM,KAAK6C,CAAU,WAsCpBgF,IAAgB,CAEpB,IAAAC,EADQ,iBAAiB,SAAS,IAAI,EACf,iBAAiB,kBAAkB,EAC1DC,SAEID,EAAc,KAChB,OACJC,EAAoB,aAEhB,OACJA,EAAoB,aAEhB,OACJA,EAAoB,iBAGpBA,EAAoB,SAItB,SAAS,KAAK,MAAM,YACnB,2BACAA,EAAoB,IAAI,EA0FjB,SAAAd,GAAuBjH,EAA0B,QACrDA,EAAQ,OAAS,OACbA,EAAQ,QAEfA,EAAQ,OAAS,aACjBA,EAAQ,QAAQ,YAAc,OAE1B,MAAM,QAAQA,EAAQ,QAAQ,KAAK,EACJ,2BAAAA,EAAQ,QAAQ,MAAM,CAAC,EAAE,WAAW,MAAM,GAAG,EAAE,IAAG,CAAA,8BAGzDA,EAAQ,QAAQ,OAAO,WAAW,MAAM,GAAG,EAAE,IAAG,CAAA,IAC1EA,EAAQ,QAAQ,OAAO,WAAa,IAGT,uBAAAA,EAAQ,QAAQ,WAAa,SAAS,6BAnN1D,MAAA8C,EAAK,EAAA,EAAAjB,EACZmG,EAAwC,MAQjC,OAAA7E,CAAoB,EAAAtB,GACpB,eAAAoG,CAAwC,EAAApG,EAE/CqG,EAAW,CAAA,EAEA,eAAAC,EAAgBC,EAAyB,KACnDC,EAAK,CAAA,EACLxF,EAAU,CAAA,EACduF,EAAgB,QAASE,GAAc,CAClC,GAAAJ,EAAYI,CAAc,GAAKA,IAAmB,cAI9C,KAAA,CAAA,KAAAC,GAAM,UAAAC,EAAS,EAAKP,EAAeK,EAAgB,MAAM,EACjED,EAAM,KAAKE,EAAI,EACf1F,EAAW,KAAK2F,EAAS,KAIyB,MAAA,QAAQ,IAAI3F,CAAU,GACvD,QAAS,CAAA2F,EAAWpH,KAAC,CACtCkB,EAAA,GAAA4F,EAAYG,EAAMjH,EAAC,CAAK,EAAAoH,EAAU,QAAON,CAAA,QAmBhC,iBAAAO,CAIR,EAAA5G,EACQ,CAAA,gBAAA6G,EAAkB,EAAK,EAAA7G,EACvB,CAAA,WAAA8G,EAAa,EAAK,EAAA9G,EAClB,CAAA,SAAAmD,EAAW,EAAK,EAAAnD,EAChB,CAAA,kBAAA+G,EAAoB,EAAK,EAAA/G,EACzB,CAAA,qBAAAgH,EAAuB,EAAK,EAAAhH,EAC5B,CAAA,IAAAiH,EAAM,EAAK,EAAAjH,EACX,CAAA,iBAAAoD,EAAmB,EAAK,EAAApD,GACxB,cAAAkH,EAAa,CAAwC,KAAM,IAAI,CAAA,EAAAlH,EAC/D,CAAA,cAAAmH,EAAgB,EAAI,EAAAnH,EACpB,CAAA,kBAAAoH,EAAoB,EAAI,EAAApH,EACxB,CAAA,gBAAAqH,EAAkB,EAAI,EAAArH,EACtB,CAAA,YAAAsH,EAAc,EAAI,EAAAtH,GAClB,WAAAkB,EAAuC,EAAAlB,GACvC,KAAAoB,EAAmB,EAAApB,EACnB,CAAA,OAAAD,EAA6B,QAAQ,EAAAC,EACrC,CAAA,YAAAuH,GAA6B,IAAI,EAAAvH,GACjC,OAAAqB,EAAwB,EAAArB,EACxB,CAAA,WAAAwH,GAAoC,QAAQ,EAAAxH,GAC5C,KAAAhB,EAAY,EAAAgB,EAEnBtC,GAA6B,KAEjCuG,GAAO,IAAA,CACNxD,EAAA,GAAA/C,GAAS,SAAS,cAAc,sBAAsB,CAAA,EACtDsI,WAGG/F,EACAwH,GA4BE,MAAAC,GAAW3D,KAMjBF,GAAY,IAAA,CACXpD,EAAA,GAAAgH,GACCxH,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,GAAG,mBAGnD0H,IAAM,CACf1H,UACC+D,GAAI,EACV,sBAAqB,IAAA,CAChByD,IACHxH,GAAK,SAAS,EAAGA,EAAI,YAAY,SAKhC2H,GACAC,GACAC,GAAwB,GAK5BhE,GAAW,IAAA,CACL7D,GACLA,EAAI,iBAAiB,KAAK,EAAE,QAAS8H,GAAC,CACrCA,EAAE,iBAAiB,QAAUC,GAAC,OACvBtK,EAASsK,EAAE,OACbtK,SACHkK,GAAuBlK,EAAO,GAAG,OACjCmK,GAA2BnK,EAAO,GAAG,EACrC+C,EAAA,GAAAqH,GAAwB,EAAI,kBAevBG,EAAc1I,EAAWpB,EAA0B,CAC3DuJ,GAAS,SAAQ,CAChB,MAAOvJ,EAAQ,MACf,MAAOA,EAAQ,UAIR,SAAA+J,GACR3I,EACApB,EACA+D,EAAuB,CAEnB,GAAAsF,KAAe,SAClBE,GAAS,OAAM,CACd,MAAOvJ,EAAQ,MACf,MAAOA,EAAQ,QACf,MAAO+D,IAAa,kBAGhBiG,EAAe,aAEdC,GAAgBD,EAAgB5I,CAAC,EAChC,CAAA8I,EAAOC,EAAI,EACjB,CAAAF,GAAc,CAAC,EACfA,GAAcA,GAAc,OAAS,CAAC,CAAA,EAGvCV,GAAS,OAAM,CACd,OAAQW,EAAM,MAAOC,GAAK,KAAK,EAC/B,MAAOF,GAAc,IAAKnF,IAAMA,GAAE,OAAO,EACzC,MAAOf,IAAa,UAuBd,SAAAqG,GACRjJ,EAA6B,OAEvB6I,EAAe,CAAA,MACjBK,EAAY,CAAA,EACZC,GAAkC,KAE3B,UAAAtK,KAAWmB,EACjBkI,KAAe,WAClBiB,GAAc,OAGTtK,EAAQ,OAAS,aAAeA,EAAQ,OAAS,UAGnDA,EAAQ,OAASsK,GACpBD,EAAa,KAAKrK,CAAO,GAErBqK,EAAa,OAAS,GACzBL,EAAgB,KAAKK,CAAY,EAElCA,GAAgBrK,CAAO,EACvBsK,GAActK,EAAQ,cAIpBqK,EAAa,OAAS,GACzBL,EAAgB,KAAKK,CAAY,EAG3BL,kFAwCD1H,EAAA,GAAAqH,GAAwB,EAAK,aA8CZG,EAAc1I,EAAGpB,CAAO,UAC3B6J,IAAC,CACTA,EAAE,MAAQ,SACbC,EAAc1I,EAAGpB,CAAO,WAqEd+D,IAAagG,GAAY3I,EAAGD,EAAS,CAAC,EAAG4C,CAAQ,6CAvI1DjC,EAAGyI,u+BAzOXpC,EAAgBP,GAA6B9E,CAAK,CAAA,0CAqG9CA,GAASwG,IAAcpB,IAC7BsB,uCAiBKgB,GAAO1H,EAAOkF,CAAS,IAC3B1F,EAAA,GAAA0F,EAAYlF,CAAK,EACjByG,GAAS,QAAQ,0BAIhBS,EAAkBlH,GAASsH,GAAetH,CAAK,CAAA,26FC5GpC,WAAAnB,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,sNALS,WAAAA,MAAO,YACbc,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAd,MAAO,IAAI,mBACbA,EAAc,EAAA,CAAA,+BACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,gLAQI8I,SACC,GACA,MAAA9I,MAAS,4GAATc,EAAA,CAAA,EAAA,KAAAiI,EAAA,MAAA/I,MAAS,sIAjBdA,EAAc,EAAA,GAAAsC,GAAAtC,CAAA,IAYbA,EAAU,CAAA,GAAAI,GAAAJ,CAAA,0BASR,KAAAA,MAAO,gBACDA,EAAW,CAAA,yEAIhBA,EAAM,EAAA,gFAIIA,EAAc,EAAA,GAAE,SAAW,mKAcpCA,EAAM,EAAA,EAAC,OAAO,cACdA,EAAM,EAAA,EAAC,OAAO,MACN,eAAAA,MAAO,0BACXA,EAAI,EAAA,EACV,KAAAA,MAAO,yPArCfrC,GAuCKC,EAAAuC,EAAArC,CAAA,mDAlDAkC,EAAc,EAAA,qHAYbA,EAAU,CAAA,iHASRc,EAAA,CAAA,EAAA,UAAAkI,EAAA,KAAAhJ,MAAO,8BACDA,EAAW,CAAA,yIAIhBA,EAAM,EAAA,wJAIIA,EAAc,EAAA,GAAE,SAAW,yTAcpCA,EAAM,EAAA,EAAC,OAAO,gCACdA,EAAM,EAAA,EAAC,OAAO,OACNc,EAAA,CAAA,EAAA,UAAAkI,EAAA,eAAAhJ,MAAO,2CACXA,EAAI,EAAA,GACVc,EAAA,CAAA,EAAA,UAAAkI,EAAA,KAAAhJ,MAAO,6PAtDN,yDAIO,qbAzDL,GAAA,CAAA,QAAAiJ,EAAU,EAAE,EAAA/I,GACZ,aAAAgJ,EAAY,EAAA,EAAAhJ,EACZ,CAAA,QAAAiJ,EAAU,EAAI,EAAAjJ,GACd,MAAAiB,EAAK,EAAA,EAAAjB,EACL,CAAA,MAAAkJ,EAAuB,IAAI,EAAAlJ,EAC3B,CAAA,UAAAmJ,EAAgC,MAAS,EAAAnJ,GACzC,MAAAoJ,CAAa,EAAApJ,EACb,CAAA,WAAAqJ,EAAa,EAAI,EAAArJ,GACjB,KAAAhB,CAAY,EAAAgB,EACZ,CAAA,YAAAsJ,EAAc,EAAK,EAAAtJ,EACnB,CAAA,SAAAmD,EAAW,EAAK,EAAAnD,EAChB,CAAA,kBAAA+G,EAAoB,EAAK,EAAA/G,EACzB,CAAA,IAAAiH,EAAM,EAAK,EAAAjH,EACX,CAAA,iBAAAoD,EAAmB,EAAI,EAAApD,EACvB,CAAA,qBAAAgH,EAAuB,EAAK,EAAAhH,EAC5B,CAAA,cAAAmH,EAAgB,EAAI,EAAAnH,EACpB,CAAA,kBAAAoH,EAAoB,EAAI,EAAApH,EACxB,CAAA,OAAAD,EAA6B,QAAQ,EAAAC,EACrC,CAAA,KAAAe,EAA8B,QAAQ,EAAAf,EACtC,CAAA,gBAAAqH,EAAkB,EAAI,EAAArH,EACtB,CAAA,YAAAsH,GAAc,EAAI,EAAAtH,GAClB,iBAAA4G,EAIR,EAAA5G,GACQ,OAAAuJ,CAOT,EAAAvJ,GACS,cAAAkH,GAAa,CAAwC,KAAM,IAAI,CAAA,EAAAlH,EAEtEwJ,GAAM,CAAA,EAOC,CAAA,eAAAC,GAA4C,MAAS,EAAAzJ,EACrD,CAAA,OAAA0J,GAAS,GAAG,EAAA1J,EACZ,CAAA,YAAAuH,GAA6B,IAAI,EAAAvH,GACjC,WAAAkB,CAAuC,EAAAlB,EAqBzB,MAAA2J,GAAA,IAAAJ,EAAO,SAAS,eAAgBE,EAAc,EAyBpDG,GAAA,IAAAL,EAAO,SAAS,SAAUtI,CAAK,KACpC+G,GAAMuB,EAAO,SAAS,SAAUvB,EAAE,MAAM,KAC1CA,GAAMuB,EAAO,SAAS,OAAQvB,EAAE,MAAM,KACrCA,GAAMuB,EAAO,SAAS,QAASvB,EAAE,MAAM,KACvCA,GAAMuB,EAAO,SAAS,QAASvB,EAAE,MAAM,koCA1DjDvH,EAAA,GAAA+I,GACFzI,IAAS,SACNvB,GAAiByB,EAAsBjC,CAAI,EAC3CK,GAAmB4B,EAAoBjC,CAAI,CAAA"}