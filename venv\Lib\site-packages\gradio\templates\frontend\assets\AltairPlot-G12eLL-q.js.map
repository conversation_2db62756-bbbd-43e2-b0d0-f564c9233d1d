{"version": 3, "file": "AltairPlot-G12eLL-q.js", "sources": ["../../../../js/plot/shared/plot_types/altair_utils.ts", "../../../../js/plot/shared/plot_types/AltairPlot.svelte"], "sourcesContent": ["import { colors as color_palette } from \"@gradio/theme\";\nimport { get_next_color } from \"@gradio/utils\";\nimport type { Config, TopLevelSpec as Spec } from \"vega-lite\";\n\nexport function set_config(\n\tspec: Spec,\n\tcomputed_style: CSSStyleDeclaration,\n\tchart_type: string,\n\tcolors: string[]\n): Spec {\n\tlet accentColor = computed_style.getPropertyValue(\"--color-accent\");\n\tlet bodyTextColor = computed_style.getPropertyValue(\"--body-text-color\");\n\tlet borderColorPrimary = computed_style.getPropertyValue(\n\t\t\"--border-color-primary\"\n\t);\n\tlet fontFamily = computed_style.fontFamily;\n\tlet titleWeight = computed_style.getPropertyValue(\n\t\t\"--block-title-text-weight\"\n\t) as \"bold\" | \"normal\" | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900;\n\tconst fontToPxVal = (font: string): number => {\n\t\treturn font.endsWith(\"px\") ? parseFloat(font.slice(0, -2)) : 12;\n\t};\n\tlet textSizeMd = fontToPxVal(computed_style.getPropertyValue(\"--text-md\"));\n\tlet textSizeSm = fontToPxVal(computed_style.getPropertyValue(\"--text-sm\"));\n\tlet config: Config = {\n\t\tautosize: { type: \"fit\", contains: \"padding\" },\n\t\taxis: {\n\t\t\tlabelFont: fontFamily,\n\t\t\tlabelColor: bodyTextColor,\n\t\t\ttitleFont: fontFamily,\n\t\t\ttitleColor: bodyTextColor,\n\t\t\ttickColor: borderColorPrimary,\n\t\t\tlabelFontSize: textSizeSm,\n\t\t\tgridColor: borderColorPrimary,\n\t\t\ttitleFontWeight: \"normal\",\n\t\t\ttitleFontSize: textSizeSm,\n\t\t\tlabelFontWeight: \"normal\",\n\t\t\tdomain: false,\n\t\t\tlabelAngle: 0\n\t\t},\n\t\tlegend: {\n\t\t\tlabelColor: bodyTextColor,\n\t\t\tlabelFont: fontFamily,\n\t\t\ttitleColor: bodyTextColor,\n\t\t\ttitleFont: fontFamily,\n\t\t\ttitleFontWeight: \"normal\",\n\t\t\ttitleFontSize: textSizeSm,\n\t\t\tlabelFontWeight: \"normal\",\n\t\t\toffset: 2\n\t\t},\n\t\ttitle: {\n\t\t\tcolor: bodyTextColor,\n\t\t\tfont: fontFamily,\n\t\t\tfontSize: textSizeMd,\n\t\t\tfontWeight: titleWeight,\n\t\t\tanchor: \"middle\"\n\t\t},\n\t\tview: {\n\t\t\tstroke: borderColorPrimary\n\t\t}\n\t};\n\tspec.config = config;\n\t// @ts-ignore (unsure why the following are not typed in Spec)\n\tlet encoding: any = spec.encoding;\n\t// @ts-ignore\n\tlet layer: any = spec.layer;\n\tswitch (chart_type) {\n\t\tcase \"scatter\":\n\t\t\tspec.config.mark = { stroke: accentColor };\n\t\t\tif (encoding.color && encoding.color.type == \"nominal\") {\n\t\t\t\tencoding.color.scale.range = encoding.color.scale.range.map(\n\t\t\t\t\t(_: string, i: number) => get_color(colors, i)\n\t\t\t\t);\n\t\t\t} else if (encoding.color && encoding.color.type == \"quantitative\") {\n\t\t\t\tencoding.color.scale.range = [\"#eff6ff\", \"#1e3a8a\"];\n\t\t\t\tencoding.color.scale.range.interpolate = \"hsl\";\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"line\":\n\t\t\tspec.config.mark = { stroke: accentColor, cursor: \"crosshair\" };\n\t\t\tlayer.forEach((d: any) => {\n\t\t\t\tif (d.encoding.color) {\n\t\t\t\t\td.encoding.color.scale.range = d.encoding.color.scale.range.map(\n\t\t\t\t\t\t(_: any, i: any) => get_color(colors, i)\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t});\n\t\t\tbreak;\n\t\tcase \"bar\":\n\t\t\tspec.config.mark = { opacity: 0.8, fill: accentColor };\n\t\t\tif (encoding.color) {\n\t\t\t\tencoding.color.scale.range = encoding.color.scale.range.map(\n\t\t\t\t\t(_: any, i: any) => get_color(colors, i)\n\t\t\t\t);\n\t\t\t}\n\t\t\tbreak;\n\t}\n\treturn spec;\n}\n\nfunction get_color(colors: string[], index: number): string {\n\tlet current_color = colors[index % colors.length];\n\n\tif (current_color && current_color in color_palette) {\n\t\treturn color_palette[current_color as keyof typeof color_palette]?.primary;\n\t} else if (!current_color) {\n\t\treturn color_palette[get_next_color(index) as keyof typeof color_palette]\n\t\t\t.primary;\n\t}\n\treturn current_color;\n}\n", "<script lang=\"ts\">\n\t//@ts-nocheck\n\timport { set_config } from \"./altair_utils\";\n\timport { onMount, onDestroy } from \"svelte\";\n\timport type { TopLevelSpec as Spec } from \"vega-lite\";\n\timport vegaEmbed from \"vega-embed\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport type { View } from \"vega\";\n\n\texport let value;\n\texport let target: HTMLDivElement;\n\texport let colors: string[] = [];\n\texport let caption: string;\n\texport let show_actions_button: bool;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t}>;\n\tlet element: HTMLElement;\n\tlet parent_element: HTMLElement;\n\tlet view: View;\n\texport let _selectable: bool;\n\n\tlet computed_style = window.getComputedStyle(target);\n\n\tlet old_spec: Spec;\n\tlet spec_width: number;\n\t$: plot = value?.plot;\n\t$: spec = JSON.parse(plot) as Spec;\n\t$: if (spec && spec.params && !_selectable) {\n\t\tspec.params = spec.params.filter((param) => param.name !== \"brush\");\n\t}\n\t$: if (old_spec !== spec) {\n\t\told_spec = spec;\n\t\tspec_width = spec.width;\n\t}\n\n\t$: if (value.chart) {\n\t\tspec = set_config(spec, computed_style, value.chart as string, colors);\n\t}\n\t$: fit_width_to_parent =\n\t\tspec.encoding?.column?.field ||\n\t\tspec.encoding?.row?.field ||\n\t\tvalue.chart === undefined\n\t\t\t? false\n\t\t\t: true; // vega seems to glitch with width when orientation is set\n\n\tconst get_width = (): number => {\n\t\treturn Math.min(\n\t\t\tparent_element.offsetWidth,\n\t\t\tspec_width || parent_element.offsetWidth\n\t\t);\n\t};\n\tlet resize_callback = (): void => {};\n\tconst renderPlot = (): void => {\n\t\tif (fit_width_to_parent) {\n\t\t\tspec.width = get_width();\n\t\t}\n\t\tvegaEmbed(element, spec, { actions: show_actions_button }).then(\n\t\t\tfunction (result): void {\n\t\t\t\tview = result.view;\n\t\t\t\tresize_callback = () => {\n\t\t\t\t\tview.signal(\"width\", get_width()).run();\n\t\t\t\t};\n\n\t\t\t\tif (!_selectable) return;\n\t\t\t\tconst callback = (event, item): void => {\n\t\t\t\t\tconst brushValue = view.signal(\"brush\");\n\t\t\t\t\tif (brushValue) {\n\t\t\t\t\t\tif (Object.keys(brushValue).length === 0) {\n\t\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\t\tvalue: null,\n\t\t\t\t\t\t\t\tindex: null,\n\t\t\t\t\t\t\t\tselected: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst key = Object.keys(brushValue)[0];\n\t\t\t\t\t\t\tlet range: [number, number] = brushValue[key].map(\n\t\t\t\t\t\t\t\t(x) => x / 1000\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\t\tvalue: brushValue,\n\t\t\t\t\t\t\t\tindex: range,\n\t\t\t\t\t\t\t\tselected: true\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tview.addEventListener(\"mouseup\", callback);\n\t\t\t\tview.addEventListener(\"touchup\", callback);\n\t\t\t}\n\t\t);\n\t};\n\tlet resizeObserver = new ResizeObserver(() => {\n\t\tif (fit_width_to_parent && spec.width !== parent_element.offsetWidth) {\n\t\t\tresize_callback();\n\t\t}\n\t});\n\tonMount(() => {\n\t\trenderPlot();\n\t\tresizeObserver.observe(parent_element);\n\t});\n\tonDestroy(() => {\n\t\tresizeObserver.disconnect();\n\t});\n</script>\n\n<div data-testid={\"altair\"} class=\"altair layout\" bind:this={parent_element}>\n\t<div bind:this={element}></div>\n\t{#if caption}\n\t\t<div class=\"caption layout\">\n\t\t\t{caption}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.altair :global(canvas) {\n\t\tpadding: 6px;\n\t}\n\t.altair :global(.vega-embed) {\n\t\tpadding: 0px !important;\n\t}\n\t.altair :global(.vega-actions) {\n\t\tright: 0px !important;\n\t}\n\t.layout {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.altair {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.caption {\n\t\tfont-size: var(--text-sm);\n\t\tmargin-bottom: 6px;\n\t}\n\t:global(#vg-tooltip-element) {\n\t\tfont-family: var(--font) !important;\n\t\tfont-size: var(--text-xs) !important;\n\t\tbox-shadow: none !important;\n\t\tbackground-color: var(--block-background-fill) !important;\n\t\tborder: 1px solid var(--border-color-primary) !important;\n\t\tcolor: var(--body-text-color) !important;\n\t}\n\t:global(#vg-tooltip-element .key) {\n\t\tcolor: var(--body-text-color-subdued) !important;\n\t}\n</style>\n"], "names": ["set_config", "spec", "computed_style", "chart_type", "colors", "accentColor", "bodyTextColor", "borderColorPrimary", "fontFamily", "titleWeight", "fontToPxVal", "font", "textSizeMd", "textSizeSm", "config", "encoding", "layer", "_", "i", "get_color", "d", "index", "current_color", "color_palette", "get_next_color", "onDestroy", "ctx", "insert", "target", "div", "anchor", "create_if_block", "div1", "append", "div0", "value", "$$props", "caption", "show_actions_button", "gradio", "element", "parent_element", "view", "_selectable", "old_spec", "spec_width", "get_width", "resize_callback", "renderPlot", "fit_width_to_parent", "vegaEmbed", "result", "callback", "event", "item", "brushValue", "key", "range", "x", "resizeObserver", "onMount", "$$value", "$$invalidate", "plot", "param"], "mappings": "qNAIO,SAASA,EACfC,EACAC,EACAC,EACAC,EACO,CACH,IAAAC,EAAcH,EAAe,iBAAiB,gBAAgB,EAC9DI,EAAgBJ,EAAe,iBAAiB,mBAAmB,EACnEK,EAAqBL,EAAe,iBACvC,wBAAA,EAEGM,EAAaN,EAAe,WAC5BO,EAAcP,EAAe,iBAChC,2BAAA,EAEK,MAAAQ,EAAeC,GACbA,EAAK,SAAS,IAAI,EAAI,WAAWA,EAAK,MAAM,EAAG,EAAE,CAAC,EAAI,GAE9D,IAAIC,EAAaF,EAAYR,EAAe,iBAAiB,WAAW,CAAC,EACrEW,EAAaH,EAAYR,EAAe,iBAAiB,WAAW,CAAC,EACrEY,EAAiB,CACpB,SAAU,CAAE,KAAM,MAAO,SAAU,SAAU,EAC7C,KAAM,CACL,UAAWN,EACX,WAAYF,EACZ,UAAWE,EACX,WAAYF,EACZ,UAAWC,EACX,cAAeM,EACf,UAAWN,EACX,gBAAiB,SACjB,cAAeM,EACf,gBAAiB,SACjB,OAAQ,GACR,WAAY,CACb,EACA,OAAQ,CACP,WAAYP,EACZ,UAAWE,EACX,WAAYF,EACZ,UAAWE,EACX,gBAAiB,SACjB,cAAeK,EACf,gBAAiB,SACjB,OAAQ,CACT,EACA,MAAO,CACN,MAAOP,EACP,KAAME,EACN,SAAUI,EACV,WAAYH,EACZ,OAAQ,QACT,EACA,KAAM,CACL,OAAQF,CACT,CAAA,EAEDN,EAAK,OAASa,EAEd,IAAIC,EAAgBd,EAAK,SAErBe,EAAaf,EAAK,MACtB,OAAQE,EAAY,CACnB,IAAK,UACJF,EAAK,OAAO,KAAO,CAAE,OAAQI,CAAY,EACrCU,EAAS,OAASA,EAAS,MAAM,MAAQ,UAC5CA,EAAS,MAAM,MAAM,MAAQA,EAAS,MAAM,MAAM,MAAM,IACvD,CAACE,EAAWC,IAAcC,EAAUf,EAAQc,CAAC,CAAA,EAEpCH,EAAS,OAASA,EAAS,MAAM,MAAQ,iBACnDA,EAAS,MAAM,MAAM,MAAQ,CAAC,UAAW,SAAS,EACzCA,EAAA,MAAM,MAAM,MAAM,YAAc,OAE1C,MACD,IAAK,OACJd,EAAK,OAAO,KAAO,CAAE,OAAQI,EAAa,OAAQ,aAC5CW,EAAA,QAASI,GAAW,CACrBA,EAAE,SAAS,QACZA,EAAA,SAAS,MAAM,MAAM,MAAQA,EAAE,SAAS,MAAM,MAAM,MAAM,IAC3D,CAAC,EAAQF,IAAWC,EAAUf,EAAQc,CAAC,CAAA,EAEzC,CACA,EACD,MACD,IAAK,MACJjB,EAAK,OAAO,KAAO,CAAE,QAAS,GAAK,KAAMI,GACrCU,EAAS,QACZA,EAAS,MAAM,MAAM,MAAQA,EAAS,MAAM,MAAM,MAAM,IACvD,CAACE,EAAQC,IAAWC,EAAUf,EAAQc,CAAC,CAAA,GAGzC,KACF,CACO,OAAAjB,CACR,CAEA,SAASkB,EAAUf,EAAkBiB,EAAuB,CAC3D,IAAIC,EAAgBlB,EAAOiB,EAAQjB,EAAO,MAAM,EAE5C,OAAAkB,GAAiBA,KAAiBC,EAC9BA,EAAcD,CAA2C,GAAG,QACxDA,GACJC,EAAcC,EAAeH,CAAK,CAA+B,EACtE,OAGJ,wMC3GiB,UAAAI,CAAW,EAAA,OAAgB,2EA2GxCC,EAAO,CAAA,CAAA,uDADTC,EAEKC,EAAAC,EAAAC,CAAA,0BADHJ,EAAO,CAAA,CAAA,4CAFLA,EAAO,CAAA,GAAAK,EAAAL,CAAA,oEAFK,QAAQ,sDAA1BC,EAOKC,EAAAI,EAAAF,CAAA,EANJG,EAA8BD,EAAAE,CAAA,oDACzBR,EAAO,CAAA,kJAnGD,MAAAS,CAAK,EAAAC,GACL,OAAAR,CAAsB,EAAAQ,GACtB,OAAAhC,EAAM,EAAA,EAAAgC,GACN,QAAAC,CAAe,EAAAD,GACf,oBAAAE,CAAyB,EAAAF,GACzB,OAAAG,CAET,EAAAH,EACEI,EACAC,EACAC,GACO,YAAAC,CAAiB,EAAAP,EAExBlC,EAAiB,OAAO,iBAAiB0B,CAAM,EAE/CgB,EACAC,QAqBEC,EAAS,IACP,KAAK,IACXL,EAAe,YACfI,GAAcJ,EAAe,WAAW,MAGtCM,EAAe,IAAA,SACbC,EAAU,IAAA,CACXC,QACHhD,EAAK,MAAQ6C,EAAS,EAAA7C,CAAA,EAEvBiD,EAAUV,EAASvC,EAAI,CAAI,QAASqC,EAAmB,EAAI,KAAI,SACpDa,EAAM,IACfT,EAAOS,EAAO,KACdJ,EAAe,IAAA,CACdL,EAAK,OAAO,QAASI,EAAS,GAAI,IAAG,IAGjCH,EAAW,aACVS,EAAQ,CAAIC,GAAOC,KAAI,CACtB,MAAAC,EAAab,EAAK,OAAO,OAAO,KAClCa,EACC,GAAA,OAAO,KAAKA,CAAU,EAAE,SAAW,EACtChB,EAAO,SAAS,SAAQ,CACvB,MAAO,KACP,MAAO,KACP,SAAU,UAGL,MAAAiB,EAAM,OAAO,KAAKD,CAAU,EAAE,CAAC,MACjCE,EAA0BF,EAAWC,CAAG,EAAE,IAC5CE,GAAMA,EAAI,GAAI,EAEhBnB,EAAO,SAAS,SAAQ,CACvB,MAAOgB,EACP,MAAOE,EACP,SAAU,OAKdf,EAAK,iBAAiB,UAAWU,CAAQ,EACzCV,EAAK,iBAAiB,UAAWU,CAAQ,KAIxC,IAAAO,MAAqB,eAAc,IAAA,CAClCV,GAAuBhD,EAAK,QAAUwC,EAAe,aACxDM,MAGFa,EAAO,IAAA,CACNZ,IACAW,EAAe,QAAQlB,CAAc,IAEtChB,EAAS,IAAA,CACRkC,EAAe,WAAU,6CAKVnB,EAAOqB,oDADqCpB,EAAcoB,oTAhFvEC,EAAA,GAAAC,EAAO5B,GAAO,IAAI,wBAClBlC,EAAO,KAAK,MAAM8D,CAAI,CAAA,mBAClB9D,GAAQA,EAAK,SAAW0C,GAC9BmB,EAAA,GAAA7D,EAAK,OAASA,EAAK,OAAO,OAAQ+D,GAAUA,EAAM,OAAS,OAAO,EAAA/D,CAAA,mBAO5DkC,EAAM,YACZlC,EAAOD,EAAWC,EAAMC,EAAgBiC,EAAM,MAAiB/B,CAAM,CAAA,mBAN/DwC,IAAa3C,IACnB6D,EAAA,EAAAlB,EAAW3C,CAAI,EACf4C,EAAa5C,EAAK,yBAMhBgD,EACF,EAAAhD,EAAK,UAAU,QAAQ,OACvBA,EAAK,UAAU,KAAK,OACpBkC,EAAM,QAAU"}