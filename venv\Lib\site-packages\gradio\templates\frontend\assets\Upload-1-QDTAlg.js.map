{"version": 3, "file": "Upload-1-QDTAlg.js", "sources": ["../../../../js/upload/src/UploadProgress.svelte", "../../../../js/upload/src/Upload.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { FileData, type Client } from \"@gradio/client\";\n\timport { onMount, createEventDispatcher, onDestroy } from \"svelte\";\n\n\ttype FileDataWithProgress = FileData & { progress: number };\n\n\texport let upload_id: string;\n\texport let root: string;\n\texport let files: FileData[];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet stream: Awaited<ReturnType<Client[\"stream\"]>>;\n\tlet progress = false;\n\tlet current_file_upload: FileDataWithProgress;\n\tlet file_to_display: FileDataWithProgress;\n\n\tlet files_with_progress: FileDataWithProgress[] = files.map((file) => {\n\t\treturn {\n\t\t\t...file,\n\t\t\tprogress: 0\n\t\t};\n\t});\n\n\tconst dispatch = createEventDispatcher();\n\n\tfunction handleProgress(filename: string, chunk_size: number): void {\n\t\t// Find the corresponding file in the array and update its progress\n\t\tfiles_with_progress = files_with_progress.map((file) => {\n\t\t\tif (file.orig_name === filename) {\n\t\t\t\tfile.progress += chunk_size;\n\t\t\t}\n\t\t\treturn file;\n\t\t});\n\t}\n\n\tfunction getProgress(file: FileDataWithProgress): number {\n\t\treturn (file.progress * 100) / (file.size || 0) || 0;\n\t}\n\n\tonMount(async () => {\n\t\tstream = await stream_handler(\n\t\t\tnew URL(`${root}/upload_progress?upload_id=${upload_id}`)\n\t\t);\n\n\t\tif (stream == null) {\n\t\t\tthrow new Error(\"Event source is not defined\");\n\t\t}\n\t\t// Event listener for progress updates\n\t\tstream.onmessage = async function (event) {\n\t\t\tconst _data = JSON.parse(event.data);\n\t\t\tif (!progress) progress = true;\n\t\t\tif (_data.msg === \"done\") {\n\t\t\t\t// the stream will close itself but is here for clarity; remove .close() in 5.0\n\t\t\t\tstream?.close();\n\t\t\t\tdispatch(\"done\");\n\t\t\t} else {\n\t\t\t\tcurrent_file_upload = _data;\n\t\t\t\thandleProgress(_data.orig_name, _data.chunk_size);\n\t\t\t}\n\t\t};\n\t});\n\tonDestroy(() => {\n\t\t// the stream will close itself but is here for clarity; remove .close() in 5.0\n\t\tif (stream != null || stream != undefined) stream.close();\n\t});\n\n\tfunction calculateTotalProgress(files: FileData[]): number {\n\t\tlet totalProgress = 0;\n\t\tfiles.forEach((file) => {\n\t\t\ttotalProgress += getProgress(file as FileDataWithProgress);\n\t\t});\n\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--upload-progress-width\",\n\t\t\t(totalProgress / files.length).toFixed(2) + \"%\"\n\t\t);\n\n\t\treturn totalProgress / files.length;\n\t}\n\n\t$: calculateTotalProgress(files_with_progress);\n\n\t$: file_to_display = current_file_upload || files_with_progress[0];\n</script>\n\n<div class=\"wrap\" class:progress>\n\t<span class=\"uploading\"\n\t\t>Uploading {files_with_progress.length}\n\t\t{files_with_progress.length > 1 ? \"files\" : \"file\"}...</span\n\t>\n\n\t{#if file_to_display}\n\t\t<div class=\"file\">\n\t\t\t<span>\n\t\t\t\t<div class=\"progress-bar\">\n\t\t\t\t\t<progress\n\t\t\t\t\t\tstyle=\"visibility:hidden;height:0;width:0;\"\n\t\t\t\t\t\tvalue={getProgress(file_to_display)}\n\t\t\t\t\t\tmax=\"100\">{getProgress(file_to_display)}</progress\n\t\t\t\t\t>\n\t\t\t\t</div>\n\t\t\t</span>\n\t\t\t<span class=\"file-name\">\n\t\t\t\t{file_to_display.orig_name}\n\t\t\t</span>\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\toverflow-y: auto;\n\t\ttransition: opacity 0.5s ease-in-out;\n\t\tbackground: var(--block-background-fill);\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmin-height: var(--size-40);\n\t\twidth: var(--size-full);\n\t}\n\n\t.wrap::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: var(--upload-progress-width);\n\t\theight: 100%;\n\t\ttransition: all 0.5s ease-in-out;\n\t\tz-index: 1;\n\t}\n\n\t.uploading {\n\t\tfont-size: var(--text-lg);\n\t\tfont-family: var(--font);\n\t\tz-index: 2;\n\t}\n\n\t.file-name {\n\t\tmargin: var(--spacing-md);\n\t\tfont-size: var(--text-lg);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.file {\n\t\tfont-size: var(--text-md);\n\t\tz-index: 2;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.file progress {\n\t\tdisplay: inline;\n\t\theight: var(--size-1);\n\t\twidth: 100%;\n\t\ttransition: all 0.5s ease-in-out;\n\t\tcolor: var(--color-accent);\n\t\tborder: none;\n\t}\n\n\t.file progress[value]::-webkit-progress-value {\n\t\tbackground-color: var(--color-accent);\n\t\tborder-radius: 20px;\n\t}\n\n\t.file progress[value]::-webkit-progress-bar {\n\t\tbackground-color: var(--border-color-accent);\n\t\tborder-radius: 20px;\n\t}\n\n\t.progress-bar {\n\t\twidth: 14px;\n\t\theight: 14px;\n\t\tborder-radius: 50%;\n\t\tbackground: radial-gradient(\n\t\t\t\tclosest-side,\n\t\t\t\tvar(--block-background-fill) 64%,\n\t\t\t\ttransparent 53% 100%\n\t\t\t),\n\t\t\tconic-gradient(\n\t\t\t\tvar(--color-accent) var(--upload-progress-width),\n\t\t\t\tvar(--border-color-accent) 0\n\t\t\t);\n\t\ttransition: all 0.5s ease-in-out;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick, getContext } from \"svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { prepare_files, type Client } from \"@gradio/client\";\n\timport { _ } from \"svelte-i18n\";\n\timport UploadProgress from \"./UploadProgress.svelte\";\n\n\texport let filetype: string | string[] | null = null;\n\texport let dragging = false;\n\texport let boundedheight = true;\n\texport let center = true;\n\texport let flex = true;\n\texport let file_count: \"single\" | \"multiple\" | \"directory\" = \"single\";\n\texport let disable_click = false;\n\texport let root: string;\n\texport let hidden = false;\n\texport let format: \"blob\" | \"file\" = \"file\";\n\texport let uploading = false;\n\texport let hidden_upload: HTMLInputElement | null = null;\n\texport let show_progress = true;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet upload_id: string;\n\tlet file_data: FileData[];\n\tlet accept_file_types: string | null;\n\tlet use_post_upload_validation: boolean | null = null;\n\n\tconst get_ios = (): boolean => {\n\t\tif (typeof navigator !== \"undefined\") {\n\t\t\tconst userAgent = navigator.userAgent.toLowerCase();\n\t\t\treturn userAgent.indexOf(\"iphone\") > -1 || userAgent.indexOf(\"ipad\") > -1;\n\t\t}\n\t\treturn false;\n\t};\n\n\t$: ios = get_ios();\n\n\tconst dispatch = createEventDispatcher();\n\tconst validFileTypes = [\"image\", \"video\", \"audio\", \"text\", \"file\"];\n\tconst process_file_type = (type: string): string => {\n\t\tif (ios && type.startsWith(\".\")) {\n\t\t\tuse_post_upload_validation = true;\n\t\t\treturn type;\n\t\t}\n\t\tif (ios && type.includes(\"file/*\")) {\n\t\t\treturn \"*\";\n\t\t}\n\t\tif (type.startsWith(\".\") || type.endsWith(\"/*\")) {\n\t\t\treturn type;\n\t\t}\n\t\tif (validFileTypes.includes(type)) {\n\t\t\treturn type + \"/*\";\n\t\t}\n\t\treturn \".\" + type;\n\t};\n\n\t$: if (filetype == null) {\n\t\taccept_file_types = null;\n\t} else if (typeof filetype === \"string\") {\n\t\taccept_file_types = process_file_type(filetype);\n\t} else if (ios && filetype.includes(\"file/*\")) {\n\t\taccept_file_types = \"*\";\n\t} else {\n\t\tfiletype = filetype.map(process_file_type);\n\t\taccept_file_types = filetype.join(\", \");\n\t}\n\n\tfunction updateDragging(): void {\n\t\tdragging = !dragging;\n\t}\n\n\texport function paste_clipboard(): void {\n\t\tnavigator.clipboard.read().then(async (items) => {\n\t\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t\tconst type = items[i].types.find((t) => t.startsWith(\"image/\"));\n\t\t\t\tif (type) {\n\t\t\t\t\titems[i].getType(type).then(async (blob) => {\n\t\t\t\t\t\tconst file = new File(\n\t\t\t\t\t\t\t[blob],\n\t\t\t\t\t\t\t`clipboard.${type.replace(\"image/\", \"\")}`\n\t\t\t\t\t\t);\n\t\t\t\t\t\tawait load_files([file]);\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\texport function open_file_upload(): void {\n\t\tif (disable_click) return;\n\t\tif (hidden_upload) {\n\t\t\thidden_upload.value = \"\";\n\t\t\thidden_upload.click();\n\t\t}\n\t}\n\n\tasync function handle_upload(\n\t\tfile_data: FileData[]\n\t): Promise<(FileData | null)[]> {\n\t\tawait tick();\n\t\tupload_id = Math.random().toString(36).substring(2, 15);\n\t\tuploading = true;\n\t\ttry {\n\t\t\tconst _file_data = await upload(\n\t\t\t\tfile_data,\n\t\t\t\troot,\n\t\t\t\tupload_id,\n\t\t\t\tmax_file_size ?? Infinity\n\t\t\t);\n\t\t\tdispatch(\"load\", file_count === \"single\" ? _file_data?.[0] : _file_data);\n\t\t\tuploading = false;\n\t\t\treturn _file_data || [];\n\t\t} catch (e) {\n\t\t\tdispatch(\"error\", (e as Error).message);\n\t\t\tuploading = false;\n\t\t\treturn [];\n\t\t}\n\t}\n\n\texport async function load_files(\n\t\tfiles: File[] | Blob[]\n\t): Promise<(FileData | null)[] | void> {\n\t\tif (!files.length) {\n\t\t\treturn;\n\t\t}\n\t\tlet _files: File[] = files.map(\n\t\t\t(f) =>\n\t\t\t\tnew File([f], f instanceof File ? f.name : \"file\", { type: f.type })\n\t\t);\n\n\t\tif (ios && use_post_upload_validation) {\n\t\t\t_files = _files.filter((file) => {\n\t\t\t\tif (is_valid_file(file)) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tdispatch(\n\t\t\t\t\t\"error\",\n\t\t\t\t\t`Invalid file type: ${file.name}. Only ${filetype} allowed.`\n\t\t\t\t);\n\t\t\t\treturn false;\n\t\t\t});\n\n\t\t\tif (_files.length === 0) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t}\n\n\t\tfile_data = await prepare_files(_files);\n\t\treturn await handle_upload(file_data);\n\t}\n\n\tfunction is_valid_file(file: File): boolean {\n\t\tif (!filetype) return true;\n\n\t\tconst allowed_types = Array.isArray(filetype) ? filetype : [filetype];\n\n\t\treturn allowed_types.some((type) => {\n\t\t\tconst processed_type = process_file_type(type);\n\n\t\t\tif (processed_type.startsWith(\".\")) {\n\t\t\t\treturn file.name.toLowerCase().endsWith(processed_type.toLowerCase());\n\t\t\t}\n\n\t\t\tif (processed_type === \"*\") {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (processed_type.endsWith(\"/*\")) {\n\t\t\t\tconst [category] = processed_type.split(\"/\");\n\t\t\t\treturn file.type.startsWith(category + \"/\");\n\t\t\t}\n\n\t\t\treturn file.type === processed_type;\n\t\t});\n\t}\n\n\tasync function load_files_from_upload(e: Event): Promise<void> {\n\t\tconst target = e.target as HTMLInputElement;\n\t\tif (!target.files) return;\n\t\tif (format != \"blob\") {\n\t\t\tawait load_files(Array.from(target.files));\n\t\t} else {\n\t\t\tif (file_count === \"single\") {\n\t\t\t\tdispatch(\"load\", target.files[0]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tdispatch(\"load\", target.files);\n\t\t}\n\t}\n\n\tfunction is_valid_mimetype(\n\t\tfile_accept: string | string[] | null,\n\t\tuploaded_file_extension: string,\n\t\tuploaded_file_type: string\n\t): boolean {\n\t\tif (\n\t\t\t!file_accept ||\n\t\t\tfile_accept === \"*\" ||\n\t\t\tfile_accept === \"file/*\" ||\n\t\t\t(Array.isArray(file_accept) &&\n\t\t\t\tfile_accept.some((accept) => accept === \"*\" || accept === \"file/*\"))\n\t\t) {\n\t\t\treturn true;\n\t\t}\n\t\tlet acceptArray: string[];\n\t\tif (typeof file_accept === \"string\") {\n\t\t\tacceptArray = file_accept.split(\",\").map((s) => s.trim());\n\t\t} else if (Array.isArray(file_accept)) {\n\t\t\tacceptArray = file_accept;\n\t\t} else {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn (\n\t\t\tacceptArray.includes(uploaded_file_extension) ||\n\t\t\tacceptArray.some((type) => {\n\t\t\t\tconst [category] = type.split(\"/\").map((s) => s.trim());\n\t\t\t\treturn (\n\t\t\t\t\ttype.endsWith(\"/*\") && uploaded_file_type.startsWith(category + \"/\")\n\t\t\t\t);\n\t\t\t})\n\t\t);\n\t}\n\n\tasync function loadFilesFromDrop(e: DragEvent): Promise<void> {\n\t\tdragging = false;\n\t\tif (!e.dataTransfer?.files) return;\n\t\tconst files_to_load = Array.from(e.dataTransfer.files).filter((file) => {\n\t\t\tconst file_extension = \".\" + file.name.split(\".\").pop();\n\t\t\tif (\n\t\t\t\tfile_extension &&\n\t\t\t\tis_valid_mimetype(accept_file_types, file_extension, file.type)\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (\n\t\t\t\tfile_extension && Array.isArray(filetype)\n\t\t\t\t\t? filetype.includes(file_extension)\n\t\t\t\t\t: file_extension === filetype\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tdispatch(\"error\", `Invalid file type only ${filetype} allowed.`);\n\t\t\treturn false;\n\t\t});\n\n\t\tif (format != \"blob\") {\n\t\t\tawait load_files(files_to_load);\n\t\t} else {\n\t\t\tif (file_count === \"single\") {\n\t\t\t\tdispatch(\"load\", files_to_load[0]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tdispatch(\"load\", files_to_load);\n\t\t}\n\t}\n</script>\n\n{#if filetype === \"clipboard\"}\n\t<button\n\t\tclass:hidden\n\t\tclass:center\n\t\tclass:boundedheight\n\t\tclass:flex\n\t\tstyle:height=\"100%\"\n\t\ttabindex={hidden ? -1 : 0}\n\t\ton:click={paste_clipboard}\n\t>\n\t\t<slot />\n\t</button>\n{:else if uploading && show_progress}\n\t{#if !hidden}\n\t\t<UploadProgress {root} {upload_id} files={file_data} {stream_handler} />\n\t{/if}\n{:else}\n\t<button\n\t\tclass:hidden\n\t\tclass:center\n\t\tclass:boundedheight\n\t\tclass:flex\n\t\tclass:disable_click\n\t\tstyle:height=\"100%\"\n\t\ttabindex={hidden ? -1 : 0}\n\t\ton:drag|preventDefault|stopPropagation\n\t\ton:dragstart|preventDefault|stopPropagation\n\t\ton:dragend|preventDefault|stopPropagation\n\t\ton:dragover|preventDefault|stopPropagation\n\t\ton:dragenter|preventDefault|stopPropagation\n\t\ton:dragleave|preventDefault|stopPropagation\n\t\ton:drop|preventDefault|stopPropagation\n\t\ton:click={open_file_upload}\n\t\ton:drop={loadFilesFromDrop}\n\t\ton:dragenter={updateDragging}\n\t\ton:dragleave={updateDragging}\n\t>\n\t\t<slot />\n\t\t<input\n\t\t\taria-label=\"file upload\"\n\t\t\tdata-testid=\"file-upload\"\n\t\t\ttype=\"file\"\n\t\t\tbind:this={hidden_upload}\n\t\t\ton:change={load_files_from_upload}\n\t\t\taccept={accept_file_types || undefined}\n\t\t\tmultiple={file_count === \"multiple\" || undefined}\n\t\t\twebkitdirectory={file_count === \"directory\" || undefined}\n\t\t\tmozdirectory={file_count === \"directory\" || undefined}\n\t\t/>\n\t</button>\n{/if}\n\n<style>\n\tbutton {\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tflex-grow: 0;\n\t}\n\n\t.hidden :global(svg) {\n\t\tdisplay: none;\n\t}\n\n\t.center {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t.flex {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.disable_click {\n\t\tcursor: default;\n\t}\n\n\tinput {\n\t\tdisplay: none;\n\t}\n</style>\n"], "names": ["onMount", "createEventDispatcher", "onDestroy", "t0_value", "getProgress", "ctx", "t2_value", "progress_1", "progress_1_value_value", "insert", "target", "div1", "anchor", "append", "span0", "div0", "span1", "dirty", "set_data", "t0", "t2", "t1_value", "create_if_block", "div", "span", "t1", "t3", "t3_value", "file", "calculateTotalProgress", "files", "totalProgress", "upload_id", "$$props", "root", "stream_handler", "stream", "progress", "current_file_upload", "file_to_display", "files_with_progress", "dispatch", "handleProgress", "filename", "chunk_size", "$$invalidate", "event", "_data", "tick", "getContext", "attr", "input", "input_accept_value", "button", "current", "create_if_block_2", "is_valid_mimetype", "file_accept", "uploaded_file_extension", "uploaded_file_type", "accept", "acceptArray", "s", "type", "category", "filetype", "dragging", "boundedheight", "center", "flex", "file_count", "disable_click", "hidden", "format", "uploading", "hidden_upload", "show_progress", "max_file_size", "upload", "file_data", "accept_file_types", "use_post_upload_validation", "get_ios", "userAgent", "validFileTypes", "process_file_type", "ios", "updateDragging", "paste_clipboard", "items", "i", "t", "blob", "load_files", "open_file_upload", "handle_upload", "_file_data", "e", "_files", "f", "is_valid_file", "prepare_files", "processed_type", "load_files_from_upload", "loadFilesFromDrop", "files_to_load", "file_extension", "$$value"], "mappings": "oTAEU,CAAA,QAAAA,GAASC,sBAAAA,GAAuB,UAAAC,EAAA,SAAyB,sDAgGlDC,EAAAC,EAAYC,EAAe,CAAA,CAAA,EAAA,WAKvCC,EAAAD,KAAgB,UAAS,gKANjBE,EAAA,MAAAC,EAAAJ,EAAYC,EAAe,CAAA,CAAA,+KALtCI,GAaKC,EAAAC,EAAAC,CAAA,EAZJC,EAQMF,EAAAG,CAAA,EAPLD,EAMKC,EAAAC,CAAA,EALJF,EAIAE,EAAAR,CAAA,gBAGFM,EAEMF,EAAAK,CAAA,iBANQC,EAAA,GAAAd,KAAAA,EAAAC,EAAYC,EAAe,CAAA,CAAA,EAAA,KAAAa,EAAAC,EAAAhB,CAAA,EAD/Bc,EAAA,GAAAT,KAAAA,EAAAJ,EAAYC,EAAe,CAAA,CAAA,gBAMnCY,EAAA,GAAAX,KAAAA,EAAAD,KAAgB,UAAS,KAAAa,EAAAE,EAAAd,CAAA,4CAhBhBe,EAAAhB,KAAoB,OAAM,SACrCA,EAAmB,CAAA,EAAC,OAAS,EAAI,QAAU,eAGxCA,EAAe,CAAA,GAAAiB,GAAAjB,CAAA,wCAJlB,YAAU,0BACwC,KAAG,0HAHxDI,GAsBKC,EAAAa,EAAAX,CAAA,EArBJC,EAGAU,EAAAC,CAAA,qEAFaP,EAAA,GAAAI,KAAAA,EAAAhB,KAAoB,OAAM,KAAAa,EAAAO,EAAAJ,CAAA,cACrChB,EAAmB,CAAA,EAAC,OAAS,EAAI,QAAU,SAAMa,EAAAQ,EAAAC,CAAA,EAG9CtB,EAAe,CAAA,2HAxDX,SAAAD,EAAYwB,EAA0B,QACtCA,EAAK,SAAW,KAAQA,EAAK,MAAQ,IAAM,EA8B3C,SAAAC,GAAuBC,EAAiB,CAC5C,IAAAC,EAAgB,EACpB,OAAAD,EAAM,QAASF,GAAI,CAClBG,GAAiB3B,EAAYwB,CAA4B,IAG1D,SAAS,gBAAgB,MAAM,YAC9B,2BACCG,EAAgBD,EAAM,QAAQ,QAAQ,CAAC,EAAI,GAAG,EAGzCC,EAAgBD,EAAM,8BAvEnB,UAAAE,CAAiB,EAAAC,GACjB,KAAAC,CAAY,EAAAD,GACZ,MAAAH,CAAiB,EAAAG,GACjB,eAAAE,CAAgC,EAAAF,EAEvCG,EACAC,EAAW,GACXC,EACAC,EAEAC,EAA8CV,EAAM,IAAKF,QAExDA,EACH,SAAU,CAAA,IAIN,MAAAa,EAAWxC,cAERyC,EAAeC,EAAkBC,EAAkB,CAE3DC,EAAA,EAAAL,EAAsBA,EAAoB,IAAKZ,IAC1CA,EAAK,YAAce,IACtBf,EAAK,UAAYgB,GAEXhB,KAQT,OAAA5B,GAAO,SAAA,CAKF,GAJJoC,QAAeD,EAAc,IACxB,IAAO,GAAAD,CAAI,8BAA8BF,CAAS,EAAA,CAAA,EAGnDI,GAAU,KACH,MAAA,IAAA,MAAM,6BAA6B,EAG9CA,EAAO,UAAS,eAAmBU,EAAK,CACjC,MAAAC,EAAQ,KAAK,MAAMD,EAAM,IAAI,EAC9BT,GAAQQ,EAAA,EAAER,EAAW,EAAI,EAC1BU,EAAM,MAAQ,QAEjBX,GAAQ,MAAK,EACbK,EAAS,MAAM,IAEfI,EAAA,EAAAP,EAAsBS,CAAK,EAC3BL,EAAeK,EAAM,UAAWA,EAAM,UAAU,MAInD7C,GAAS,IAAA,EAEJkC,GAAU,MAAQA,GAAU,OAAWA,EAAO,8LAiBhDP,GAAuBW,CAAmB,sBAE1CD,EAAkBD,GAAuBE,EAAoB,CAAC,CAAA,26BCjFxD,CAAA,sBAAAvC,GAAuB,KAAA+C,GAAM,WAAAC,EAAA,SAA0B,yPAgTtDC,EAAAC,EAAA,SAAAC,EAAA/C,OAAqB,MAAS,eAC5BA,EAAU,CAAA,IAAK,YAAc,+BACtBA,EAAU,CAAA,IAAK,aAAe,MAAS,uBAC1CA,EAAU,CAAA,IAAK,aAAe,MAAS,iDAvB5CA,EAAM,CAAA,EAAA,GAAQ,CAAC,yKAP1BI,EAgCQC,EAAA2C,EAAAzC,CAAA,yBAXPC,GAUCwC,EAAAF,CAAA,oCALW9C,EAAsB,EAAA,CAAA,4MAXxBA,EAAgB,EAAA,CAAA,aACjBA,EAAiB,EAAA,CAAA,kBACZA,EAAc,EAAA,CAAA,kBACdA,EAAc,EAAA,CAAA,mGASnB,CAAAiD,GAAArC,EAAA,CAAA,EAAA,OAAAmC,KAAAA,EAAA/C,OAAqB,+CACnBA,EAAU,CAAA,IAAK,YAAc,8CACtBA,EAAU,CAAA,IAAK,aAAe,wDACjCA,EAAU,CAAA,IAAK,aAAe,sDAvBnCA,EAAM,CAAA,EAAA,GAAQ,kUAXnBA,EAAM,CAAA,GAAAkD,GAAAlD,CAAA,uEAANA,EAAM,CAAA,0SANDA,EAAM,CAAA,EAAA,GAAQ,CAAC,+IAN1BI,EAUQC,EAAA2C,EAAAzC,CAAA,wCAHGP,EAAe,EAAA,CAAA,uHADfA,EAAM,CAAA,EAAA,GAAQ,+TAOkBA,EAAS,EAAA,wKAATA,EAAS,EAAA,qMAdhD,OAAAA,OAAa,YAAW,EAYnBA,MAAaA,EAAa,EAAA,EAAA,ySAhF1B,SAAAmD,GACRC,EACAC,EACAC,EAA0B,KAGxBF,GACDA,IAAgB,KAChBA,IAAgB,UACf,MAAM,QAAQA,CAAW,GACzBA,EAAY,KAAMG,GAAWA,IAAW,KAAOA,IAAW,QAAQ,QAE5D,OAEJC,EACO,GAAA,OAAAJ,GAAgB,SAC1BI,EAAcJ,EAAY,MAAM,GAAG,EAAE,IAAKK,GAAMA,EAAE,KAAI,CAAA,UAC5C,MAAM,QAAQL,CAAW,EACnCI,EAAcJ,aAEP,UAIPI,EAAY,SAASH,CAAuB,GAC5CG,EAAY,KAAME,GAAI,CACd,KAAA,CAAAC,CAAQ,EAAID,EAAK,MAAM,GAAG,EAAE,IAAKD,GAAMA,EAAE,KAAI,CAAA,SAEnDC,EAAK,SAAS,IAAI,GAAKJ,EAAmB,WAAWK,EAAW,GAAG,wDAtN5D,CAAA,SAAAC,EAAqC,IAAI,EAAAhC,EACzC,CAAA,SAAAiC,EAAW,EAAK,EAAAjC,EAChB,CAAA,cAAAkC,EAAgB,EAAI,EAAAlC,EACpB,CAAA,OAAAmC,EAAS,EAAI,EAAAnC,EACb,CAAA,KAAAoC,EAAO,EAAI,EAAApC,EACX,CAAA,WAAAqC,EAAkD,QAAQ,EAAArC,EAC1D,CAAA,cAAAsC,EAAgB,EAAK,EAAAtC,GACrB,KAAAC,CAAY,EAAAD,EACZ,CAAA,OAAAuC,EAAS,EAAK,EAAAvC,EACd,CAAA,OAAAwC,EAA0B,MAAM,EAAAxC,EAChC,CAAA,UAAAyC,EAAY,EAAK,EAAAzC,EACjB,CAAA,cAAA0C,EAAyC,IAAI,EAAA1C,EAC7C,CAAA,cAAA2C,EAAgB,EAAI,EAAA3C,EACpB,CAAA,cAAA4C,EAA+B,IAAI,EAAA5C,GACnC,OAAA6C,CAAwB,EAAA7C,GACxB,eAAAE,CAAgC,EAAAF,EAEvCD,EACA+C,EACAC,EACAC,GAA6C,WAE3CC,GAAO,IAAA,CACD,GAAA,OAAA,UAAc,IAAW,CAC7B,MAAAC,EAAY,UAAU,UAAU,YAAW,EAC1C,OAAAA,EAAU,QAAQ,QAAQ,EAAK,IAAKA,EAAU,QAAQ,MAAM,WAE7D,IAKF1C,EAAWxC,KACXmF,GAAc,CAAI,QAAS,QAAS,QAAS,OAAQ,MAAM,EAC3DC,EAAqBtB,GACtBuB,GAAOvB,EAAK,WAAW,GAAG,GAC7BkB,GAA6B,GACtBlB,GAEJuB,GAAOvB,EAAK,SAAS,QAAQ,EACzB,IAEJA,EAAK,WAAW,GAAG,GAAKA,EAAK,SAAS,IAAI,EACtCA,EAEJqB,GAAe,SAASrB,CAAI,EACxBA,EAAO,KAER,IAAMA,WAcLwB,IAAc,CACtB1C,EAAA,GAAAqB,GAAYA,CAAQ,WAGLsB,IAAe,CAC9B,UAAU,UAAU,KAAO,EAAA,WAAYC,GAAK,SAClCC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAAC,CAC5B,MAAA3B,EAAO0B,EAAMC,CAAC,EAAE,MAAM,KAAMC,GAAMA,EAAE,WAAW,QAAQ,CAAA,KACzD5B,EAAI,CACP0B,EAAMC,CAAC,EAAE,QAAQ3B,CAAI,EAAE,KAAI,MAAQ6B,GAAI,OAChChE,EAAI,IAAO,KAAI,CACnBgE,CAAI,EAAA,aACQ7B,EAAK,QAAQ,SAAU,EAAE,CAAA,EAAA,EAEjC,MAAA8B,GAAYjE,CAAI,CAAA,uBAQXkE,IAAgB,CAC3BvB,GACAI,QACHA,EAAc,MAAQ,GAAEA,CAAA,EACxBA,EAAc,MAAK,GAIN,eAAAoB,GACdhB,EAAqB,OAEf/B,GAAI,EACVH,EAAA,GAAAb,EAAY,KAAK,OAAS,EAAA,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,CAAA,EACtDa,EAAA,EAAA6B,EAAY,EAAI,YAETsB,EAAU,MAASlB,EACxBC,EACA7C,EACAF,EACA6C,GAAiB,GAAQ,EAE1B,OAAApC,EAAS,OAAQ6B,IAAe,SAAW0B,IAAa,CAAC,EAAIA,CAAU,EACvEnD,EAAA,EAAA6B,EAAY,EAAK,EACVsB,GAAU,CAAA,QACTC,EAAC,CACT,OAAAxD,EAAS,QAAUwD,EAAY,OAAO,EACtCpD,EAAA,EAAA6B,EAAY,EAAK,MAKG,eAAAmB,EACrB/D,EAAsB,CAEjB,GAAA,CAAAA,EAAM,kBAGPoE,EAAiBpE,EAAM,IACzBqE,GACI,IAAA,KAAM,CAAAA,CAAC,EAAGA,aAAa,KAAOA,EAAE,KAAO,OAAM,CAAI,KAAMA,EAAE,IAAI,CAAA,CAAA,EAG/D,OAAAb,GAAOL,KACViB,EAASA,EAAO,OAAQtE,GACnBwE,GAAcxE,CAAI,EACd,IAERa,EACC,QAAO,sBACeb,EAAK,IAAI,UAAUqC,CAAQ,WAAA,EAE3C,KAGJiC,EAAO,SAAW,YAKvBnB,EAAS,MAASsB,GAAcH,CAAM,CAAA,EACzB,MAAAH,GAAchB,CAAS,GAG5B,SAAAqB,GAAcxE,EAAU,CAC3B,OAAAqC,GAEiB,MAAM,QAAQA,CAAQ,EAAIA,EAAQ,CAAIA,CAAQ,GAE/C,KAAMF,GAAI,OACxBuC,EAAiBjB,EAAkBtB,CAAI,KAEzCuC,EAAe,WAAW,GAAG,SACzB1E,EAAK,KAAK,YAAW,EAAG,SAAS0E,EAAe,YAAW,CAAA,EAG/D,GAAAA,IAAmB,UACf,MAGJA,EAAe,SAAS,IAAI,EAAA,CACxB,KAAA,CAAAtC,CAAQ,EAAIsC,EAAe,MAAM,GAAG,EACpC,OAAA1E,EAAK,KAAK,WAAWoC,EAAW,GAAG,SAGpCpC,EAAK,OAAS0E,IApBA,GAwBR,eAAAC,GAAuBN,EAAQ,OACvCvF,EAASuF,EAAE,OACZ,GAAAvF,EAAO,MACR,GAAA+D,GAAU,OACP,MAAAoB,EAAW,MAAM,KAAKnF,EAAO,KAAK,CAAA,OAEpC,GAAA4D,IAAe,SAAQ,CAC1B7B,EAAS,OAAQ/B,EAAO,MAAM,CAAC,CAAA,SAGhC+B,EAAS,OAAQ/B,EAAO,KAAK,GAsChB,eAAA8F,GAAkBP,EAAY,IAC5CpD,EAAA,GAAAqB,EAAW,EAAK,GACX+B,EAAE,cAAc,MAAK,OACpB,MAAAQ,EAAgB,MAAM,KAAKR,EAAE,aAAa,KAAK,EAAE,OAAQrE,GAAI,OAC5D8E,EAAiB,IAAM9E,EAAK,KAAK,MAAM,GAAG,EAAE,MAQjD,OANA8E,GACAlD,GAAkBwB,EAAmB0B,EAAgB9E,EAAK,IAAI,IAK9D8E,GAAkB,MAAM,QAAQzC,CAAQ,EACrCA,EAAS,SAASyC,CAAc,EAChCA,IAAmBzC,GAEf,IAERxB,EAAS,QAAO,0BAA4BwB,CAAQ,WAAA,EAC7C,MAGJ,GAAAQ,GAAU,OACP,MAAAoB,EAAWY,CAAa,OAE1B,GAAAnC,IAAe,SAAQ,CAC1B7B,EAAS,OAAQgE,EAAc,CAAC,CAAA,SAGjChE,EAAS,OAAQgE,CAAa,8QA+CnB9B,EAAagC,orBArPnB1C,GAAY,KAClBpB,EAAA,GAAAmC,EAAoB,IAAI,EACP,OAAAf,GAAa,cAC9Be,EAAoBK,EAAkBpB,CAAQ,CAAA,EACpCqB,GAAOrB,EAAS,SAAS,QAAQ,EAC3CpB,EAAA,GAAAmC,EAAoB,GAAG,GAEvBnC,EAAA,EAAAoB,EAAWA,EAAS,IAAIoB,CAAiB,CAAA,EACzCxC,EAAA,GAAAmC,EAAoBf,EAAS,KAAK,IAAI,CAAA,KA7BtCpB,EAAA,GAAEyC,EAAMJ,GAAO,CAAA"}