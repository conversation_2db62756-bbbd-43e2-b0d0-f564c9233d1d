#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced News Chatbot - Version simplifiée pour test
Ce script lance directement le chatbot avec une interface Gradio
"""

import os
import sys
import json
import time
import logging
import threading
import schedule
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Imports des bibliothèques
import feedparser
import requests
from bs4 import BeautifulSoup
import nltk
from sentence_transformers import SentenceTransformer
import faiss
import gradio as gr
import numpy as np
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline

# Configuration simple
class SimpleConfig:
    def __init__(self):
        self.BASE_DIR = './news_data'
        self.CATEGORIES = ['world', 'technology', 'business']
        self.UPDATE_INTERVAL_MINUTES = 10
        self.EMBEDDING_MODEL = 'all-MiniLM-L6-v2'
        self.LLM_MODEL = 'Qwen/Qwen2-1.5B-Instruct'
        self.CHUNK_SIZE = 500
        self.MAX_ARTICLES_PER_SOURCE = 20
        
        # Sources RSS simplifiées
        self.RSS_FEEDS = {
            'world': [
                'http://feeds.bbci.co.uk/news/world/rss.xml',
                'https://rss.cnn.com/rss/edition.rss'
            ],
            'technology': [
                'https://feeds.feedburner.com/TechCrunch',
                'https://www.wired.com/feed/rss'
            ],
            'business': [
                'https://feeds.bloomberg.com/markets/news.rss',
                'https://www.reuters.com/business/finance/rss'
            ]
        }

# Collecteur de données simplifié
class SimpleNewsCollector:
    def __init__(self, config):
        self.config = config
        self.cache = {}
        
    def fetch_rss_articles(self, feed_url, category):
        """Collecte les articles d'un flux RSS"""
        try:
            print(f"📡 Collecte RSS: {feed_url}")
            feed = feedparser.parse(feed_url)
            articles = []
            
            for entry in feed.entries[:self.config.MAX_ARTICLES_PER_SOURCE]:
                article = {
                    'title': entry.get('title', 'No Title'),
                    'description': entry.get('description', ''),
                    'link': entry.get('link', ''),
                    'pub_date': entry.get('published', ''),
                    'source': feed.feed.get('title', 'Unknown'),
                    'category': category
                }
                articles.append(article)
            
            print(f"✅ Collecté {len(articles)} articles de {category}")
            return articles
            
        except Exception as e:
            print(f"❌ Erreur RSS {feed_url}: {e}")
            return []
    
    def collect_all_news(self):
        """Collecte toutes les actualités"""
        all_articles = []
        
        for category, feeds in self.config.RSS_FEEDS.items():
            for feed_url in feeds:
                articles = self.fetch_rss_articles(feed_url, category)
                all_articles.extend(articles)
        
        print(f"📚 Total collecté: {len(all_articles)} articles")
        return all_articles

# Processeur de données simplifié
class SimpleDataProcessor:
    def __init__(self, config):
        self.config = config
        self.embedding_model = SentenceTransformer(config.EMBEDDING_MODEL)
        
    def clean_text(self, text):
        """Nettoie le texte"""
        if not text:
            return ""
        
        soup = BeautifulSoup(text, 'html.parser')
        text = soup.get_text()
        text = ' '.join(text.split())
        return text
    
    def chunk_text(self, text, max_length=None):
        """Découpe le texte en chunks"""
        max_length = max_length or self.config.CHUNK_SIZE
        
        try:
            from nltk.tokenize import sent_tokenize
            sentences = sent_tokenize(text)
        except:
            sentences = text.split('. ')
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + sentence) <= max_length:
                current_chunk += sentence + " "
            else:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def process_articles(self, articles):
        """Traite les articles pour la recherche"""
        processed_chunks = []
        
        for article in articles:
            title = self.clean_text(article.get('title', ''))
            description = self.clean_text(article.get('description', ''))
            
            full_text = f"{title}. {description}"
            chunks = self.chunk_text(full_text)
            
            for i, chunk in enumerate(chunks):
                if chunk.strip():
                    processed_chunk = {
                        'content': chunk,
                        'title': title,
                        'source': article.get('source', 'Unknown'),
                        'link': article.get('link', ''),
                        'category': article.get('category', 'unknown'),
                        'chunk_index': i
                    }
                    processed_chunks.append(processed_chunk)
        
        return processed_chunks

# Gestionnaire du modèle Qwen
class QwenLLMManager:
    def __init__(self, config):
        self.config = config
        self.tokenizer = None
        self.model = None
        self.pipeline = None
        self.is_loaded = False

    def load_model(self):
        """Charge le modèle Qwen avec fallback vers GPT-2"""
        try:
            print("🧠 Chargement du modèle Qwen2-1.5B-Instruct...")

            # Essayer de charger Qwen
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.LLM_MODEL,
                trust_remote_code=True
            )

            self.model = AutoModelForCausalLM.from_pretrained(
                self.config.LLM_MODEL,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )

            # Créer le pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                max_new_tokens=400,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                device=0 if torch.cuda.is_available() else -1
            )

            self.is_loaded = True
            print("✅ Modèle Qwen chargé avec succès!")
            return True

        except Exception as e:
            print(f"⚠️ Erreur avec Qwen ({e}), fallback vers GPT-2...")
            return self._load_fallback_model()

    def _load_fallback_model(self):
        """Charge GPT-2 comme modèle de fallback"""
        try:
            from transformers import GPT2LMHeadModel, GPT2Tokenizer

            print("🔄 Chargement de GPT-2 comme fallback...")

            self.tokenizer = GPT2Tokenizer.from_pretrained('gpt2')
            self.tokenizer.pad_token = self.tokenizer.eos_token
            self.model = GPT2LMHeadModel.from_pretrained('gpt2')

            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                max_new_tokens=300,
                temperature=0.8,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                device=-1
            )

            self.is_loaded = True
            print("✅ Modèle GPT-2 chargé comme fallback!")
            return True

        except Exception as e:
            print(f"❌ Erreur critique lors du chargement des modèles: {e}")
            return False

    def create_prompt(self, question, context_chunks):
        """Crée un prompt optimisé pour le modèle"""

        # Construire le contexte à partir des chunks
        context_text = ""
        sources = []

        for i, chunk in enumerate(context_chunks[:3], 1):
            context_text += f"\n[Article {i}]\n"
            context_text += f"Titre: {chunk['title']}\n"
            context_text += f"Catégorie: {chunk['category']}\n"
            context_text += f"Source: {chunk['source']}\n"
            context_text += f"Contenu: {chunk['content']}\n"

            sources.append({
                'title': chunk['title'],
                'source': chunk['source'],
                'link': chunk.get('link', ''),
                'category': chunk['category']
            })

        # Prompt optimisé en français
        prompt = f"""Tu es un assistant expert en actualités qui répond de manière précise et informative en français.

INSTRUCTIONS:
- Réponds UNIQUEMENT basé sur les articles fournis ci-dessous
- Sois factuel, précis et informatif
- Structure ta réponse de manière claire
- Cite les sources quand pertinent
- Si l'information n'est pas dans les articles, dis-le clairement
- Utilise un ton professionnel mais accessible
- Limite ta réponse à 200-300 mots maximum

ARTICLES DE RÉFÉRENCE:{context_text}

QUESTION DE L'UTILISATEUR: {question}

RÉPONSE DÉTAILLÉE:"""

        return prompt, sources

    def generate_response(self, question, context_chunks):
        """Génère une réponse avec le modèle LLM"""
        if not self.is_loaded:
            return "❌ Le modèle n'est pas encore chargé.", []

        if not context_chunks:
            return "❌ Aucune information trouvée dans la base de données pour répondre à votre question.", []

        try:
            # Créer le prompt
            prompt, sources = self.create_prompt(question, context_chunks)

            # Générer la réponse
            response = self.pipeline(prompt, max_new_tokens=400, do_sample=True, temperature=0.7)

            # Extraire seulement la partie générée
            generated_text = response[0]['generated_text']

            # Extraire la réponse après "RÉPONSE DÉTAILLÉE:"
            if "RÉPONSE DÉTAILLÉE:" in generated_text:
                answer = generated_text.split("RÉPONSE DÉTAILLÉE:")[-1].strip()
            else:
                answer = generated_text.strip()

            # Nettoyer la réponse
            answer = self._clean_response(answer)

            return answer, sources

        except Exception as e:
            print(f"❌ Erreur génération: {e}")
            return f"❌ Erreur lors de la génération de la réponse: {str(e)}", []

    def _clean_response(self, response):
        """Nettoie la réponse générée"""
        # Supprimer les répétitions et artefacts
        lines = response.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if line and not line.startswith('[') and not line.startswith('QUESTION'):
                cleaned_lines.append(line)

        cleaned_response = '\n'.join(cleaned_lines)

        # Limiter la longueur
        if len(cleaned_response) > 1000:
            cleaned_response = cleaned_response[:1000] + "..."

        return cleaned_response

# Chatbot avec Qwen
class EnhancedChatbot:
    def __init__(self, config, collector, processor):
        self.config = config
        self.collector = collector
        self.processor = processor
        self.llm_manager = QwenLLMManager(config)
        self.index = None
        self.chunks = []
        self.query_count = 0
        self.model_loaded = False
        
    def build_index(self):
        """Construit l'index de recherche"""
        print("🔄 Construction de l'index...")
        
        # Collecter les articles
        articles = self.collector.collect_all_news()
        if not articles:
            print("⚠️ Aucun article collecté")
            return False
        
        # Traiter les articles
        self.chunks = self.processor.process_articles(articles)
        if not self.chunks:
            print("⚠️ Aucun chunk généré")
            return False
        
        # Générer les embeddings
        print("🧠 Génération des embeddings...")
        texts = [chunk['content'] for chunk in self.chunks]
        embeddings = self.processor.embedding_model.encode(texts)
        
        # Créer l'index FAISS
        dimension = embeddings.shape[1]
        self.index = faiss.IndexFlatL2(dimension)
        self.index.add(embeddings.astype('float32'))
        
        print(f"✅ Index construit avec {len(self.chunks)} chunks")

        # Charger le modèle LLM si pas encore fait
        if not self.model_loaded:
            print("🤖 Chargement du modèle LLM...")
            self.model_loaded = self.llm_manager.load_model()

        return True
    
    def search(self, query, k=3):
        """Recherche dans l'index"""
        if not self.index or not self.chunks:
            return []
        
        # Générer l'embedding de la requête
        query_embedding = self.processor.embedding_model.encode([query])
        
        # Rechercher
        distances, indices = self.index.search(query_embedding.astype('float32'), k)
        
        # Retourner les résultats
        results = []
        for i, idx in enumerate(indices[0]):
            if idx < len(self.chunks):
                chunk = self.chunks[idx].copy()
                chunk['score'] = float(distances[0][i])
                results.append(chunk)
        
        return results
    
    def answer_question(self, question):
        """Répond à une question avec Qwen"""
        self.query_count += 1

        if not self.index:
            return "❌ L'index n'est pas encore construit. Veuillez attendre..."

        if not self.model_loaded:
            return "❌ Le modèle LLM n'est pas encore chargé. Veuillez attendre..."

        # Rechercher les chunks pertinents
        results = self.search(question, k=5)  # Plus de contexte pour Qwen

        if not results:
            # Réponse générale si aucun contexte trouvé
            general_response = self._generate_general_response(question)
            return f"🤖 **Réponse générale:**\n\n{general_response}\n\n*Note: Aucun article spécifique trouvé dans la base de données pour cette question.*"

        # Générer la réponse avec Qwen
        answer, sources = self.llm_manager.generate_response(question, results)

        # Formater la réponse finale
        formatted_response = f"🤖 **Réponse de l'assistant:**\n\n{answer}\n\n"

        # Ajouter les sources
        if sources:
            formatted_response += "📚 **Sources consultées:**\n"
            for i, source in enumerate(sources, 1):
                formatted_response += f"{i}. **{source['title']}** ({source['category']})\n"
                formatted_response += f"   *{source['source']}*"
                if source['link']:
                    formatted_response += f" - [Lire l'article]({source['link']})"
                formatted_response += "\n"

        formatted_response += f"\n*Requête #{self.query_count} - Basé sur {len(self.chunks)} articles indexés*"

        return formatted_response

    def _generate_general_response(self, question):
        """Génère une réponse générale quand aucun contexte n'est trouvé"""
        general_prompt = f"""Tu es un assistant expert. Réponds brièvement à cette question en français de manière informative et utile, même sans contexte spécifique:

Question: {question}

Réponse (maximum 100 mots):"""

        try:
            if self.llm_manager.is_loaded:
                response = self.llm_manager.pipeline(general_prompt, max_new_tokens=150, temperature=0.8)
                generated = response[0]['generated_text']

                if "Réponse (maximum 100 mots):" in generated:
                    answer = generated.split("Réponse (maximum 100 mots):")[-1].strip()
                else:
                    answer = generated.strip()

                return self.llm_manager._clean_response(answer)
            else:
                return "Je ne peux pas répondre à cette question car elle ne correspond à aucun article dans ma base de données d'actualités."
        except:
            return "Je ne peux pas répondre à cette question car elle ne correspond à aucun article dans ma base de données d'actualités."

# Interface Gradio
def create_interface(chatbot):
    """Crée l'interface Gradio"""
    
    def chat_response(message, history):
        if not message.strip():
            return "Veuillez poser une question."
        return chatbot.answer_question(message)
    
    def rebuild_index():
        success = chatbot.build_index()
        if success:
            return "✅ Index reconstruit avec succès!"
        else:
            return "❌ Erreur lors de la reconstruction de l'index"
    
    with gr.Blocks(title="🤖 Enhanced News Chatbot avec Qwen", theme=gr.themes.Soft()) as interface:
        gr.Markdown("""
        # 🤖 Enhanced News Chatbot avec Qwen2

        Chatbot intelligent alimenté par **Qwen2-1.5B-Instruct** qui répond à vos questions
        sur l'actualité avec des réponses générées et contextualisées.

        **Fonctionnalités:**
        - 🧠 **IA Avancée**: Réponses générées par Qwen2
        - 📰 **Actualités en temps réel**: Base de données mise à jour
        - 🔍 **Recherche intelligente**: Trouve les informations pertinentes
        - 📚 **Sources citées**: Liens vers les articles originaux

        **Exemples de questions:**
        - "Résume-moi les dernières nouvelles en technologie"
        - "Que se passe-t-il dans le monde actuellement ?"
        - "Quelles sont les tendances en business ?"
        - "Explique-moi les actualités récentes"
        """)
        
        with gr.Tab("💬 Chat"):
            chatbot_interface = gr.ChatInterface(
                chat_response,
                title="Assistant IA alimenté par Qwen2 - Posez vos questions sur l'actualité",
                examples=[
                    "Résume-moi les dernières nouvelles en technologie",
                    "Que se passe-t-il dans le monde actuellement ?",
                    "Explique-moi les tendances en business",
                    "Quelles sont les actualités importantes aujourd'hui ?",
                    "Parle-moi des innovations récentes",
                    "Que dois-je savoir sur l'actualité mondiale ?"
                ]
            )
        
        with gr.Tab("⚙️ Contrôle"):
            gr.Markdown("### 🔧 Gestion du système")

            with gr.Row():
                rebuild_btn = gr.Button("🔄 Reconstruire l'index", variant="primary")
                model_status = gr.Button("🤖 Statut du modèle", variant="secondary")

            status_output = gr.Textbox(label="Statut du système", interactive=False, lines=3)

            def get_model_status():
                if chatbot.model_loaded:
                    model_name = chatbot.llm_manager.config.LLM_MODEL
                    return f"✅ Modèle chargé: {model_name}\n📊 Index: {len(chatbot.chunks)} chunks\n🔢 Requêtes traitées: {chatbot.query_count}"
                else:
                    return "❌ Modèle non chargé"

            rebuild_btn.click(rebuild_index, outputs=status_output)
            model_status.click(get_model_status, outputs=status_output)
    
    return interface

def main():
    """Fonction principale"""
    print("🤖 Enhanced News Chatbot avec Qwen")
    print("=" * 50)

    # Initialisation
    config = SimpleConfig()
    collector = SimpleNewsCollector(config)
    processor = SimpleDataProcessor(config)
    chatbot = EnhancedChatbot(config, collector, processor)
    
    # Construction initiale de l'index
    print("🚀 Construction initiale de l'index...")
    if not chatbot.build_index():
        print("❌ Impossible de construire l'index initial")
        return
    
    # Création de l'interface
    interface = create_interface(chatbot)
    
    # Lancement
    print("\n🎨 Lancement de l'interface Gradio...")
    print("🌐 L'interface va s'ouvrir dans votre navigateur")
    
    interface.launch(
        share=True,
        server_name="0.0.0.0",
        server_port=7860,
        debug=True
    )

if __name__ == "__main__":
    main()
