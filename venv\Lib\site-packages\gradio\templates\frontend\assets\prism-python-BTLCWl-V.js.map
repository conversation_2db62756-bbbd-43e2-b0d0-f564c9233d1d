{"version": 3, "file": "prism-python-BTLCWl-V.js", "sources": ["../../../../node_modules/.pnpm/prismjs@1.29.0/node_modules/prismjs/prism.js", "../../../../node_modules/.pnpm/prismjs@1.29.0/node_modules/prismjs/components/prism-python.js"], "sourcesContent": ["\n/* **********************************************\n     Begin prism-core.js\n********************************************** */\n\n/// <reference lib=\"WebWorker\"/>\n\nvar _self = (typeof window !== 'undefined')\n\t? window   // if in browser\n\t: (\n\t\t(typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope)\n\t\t\t? self // if in worker\n\t\t\t: {}   // if in node js\n\t);\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> <PERSON> <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = (function (_self) {\n\n\t// Private helper vars\n\tvar lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n\tvar uniqueId = 0;\n\n\t// The grammar object for plaintext\n\tvar plainTextGrammar = {};\n\n\n\tvar _ = {\n\t\t/**\n\t\t * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n\t\t * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n\t\t * additional languages or plugins yourself.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n\t\t *\n\t\t * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.manual = true;\n\t\t * // add a new <script> to load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tmanual: _self.Prism && _self.Prism.manual,\n\t\t/**\n\t\t * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses\n\t\t * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your\n\t\t * own worker, you don't want it to do this.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not add its own listeners to the worker.\n\t\t *\n\t\t * You obviously have to change this value before Prism executes. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.disableWorkerMessageHandler = true;\n\t\t * // Load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tdisableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n\n\t\t/**\n\t\t * A namespace for utility methods.\n\t\t *\n\t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n\t\t * change or disappear at any time.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t */\n\t\tutil: {\n\t\t\tencode: function encode(tokens) {\n\t\t\t\tif (tokens instanceof Token) {\n\t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n\t\t\t\t} else if (Array.isArray(tokens)) {\n\t\t\t\t\treturn tokens.map(encode);\n\t\t\t\t} else {\n\t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the name of the type of the given value.\n\t\t\t *\n\t\t\t * @param {any} o\n\t\t\t * @returns {string}\n\t\t\t * @example\n\t\t\t * type(null)      === 'Null'\n\t\t\t * type(undefined) === 'Undefined'\n\t\t\t * type(123)       === 'Number'\n\t\t\t * type('foo')     === 'String'\n\t\t\t * type(true)      === 'Boolean'\n\t\t\t * type([1, 2])    === 'Array'\n\t\t\t * type({})        === 'Object'\n\t\t\t * type(String)    === 'Function'\n\t\t\t * type(/abc+/)    === 'RegExp'\n\t\t\t */\n\t\t\ttype: function (o) {\n\t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n\t\t\t *\n\t\t\t * @param {Object} obj\n\t\t\t * @returns {number}\n\t\t\t */\n\t\t\tobjId: function (obj) {\n\t\t\t\tif (!obj['__id']) {\n\t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n\t\t\t\t}\n\t\t\t\treturn obj['__id'];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Creates a deep clone of the given object.\n\t\t\t *\n\t\t\t * The main intended use of this function is to clone language definitions.\n\t\t\t *\n\t\t\t * @param {T} o\n\t\t\t * @param {Record<number, any>} [visited]\n\t\t\t * @returns {T}\n\t\t\t * @template T\n\t\t\t */\n\t\t\tclone: function deepClone(o, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar clone; var id;\n\t\t\t\tswitch (_.util.type(o)) {\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\tfor (var key in o) {\n\t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n\t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tcase 'Array':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n\t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn o;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n\t\t\t *\n\t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @returns {string}\n\t\t\t */\n\t\t\tgetLanguage: function (element) {\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar m = lang.exec(element.className);\n\t\t\t\t\tif (m) {\n\t\t\t\t\t\treturn m[1].toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn 'none';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Sets the Prism `language-xxxx` class of the given element.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} language\n\t\t\t * @returns {void}\n\t\t\t */\n\t\t\tsetLanguage: function (element, language) {\n\t\t\t\t// remove all `language-xxxx` classes\n\t\t\t\t// (this might leave behind a leading space)\n\t\t\t\telement.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n\t\t\t\t// add the new `language-xxxx` class\n\t\t\t\t// (using `classList` will automatically clean up spaces for us)\n\t\t\t\telement.classList.add('language-' + language);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the script element that is currently executing.\n\t\t\t *\n\t\t\t * This does __not__ work for line script element.\n\t\t\t *\n\t\t\t * @returns {HTMLScriptElement | null}\n\t\t\t */\n\t\t\tcurrentScript: function () {\n\t\t\t\tif (typeof document === 'undefined') {\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\tif ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */) {\n\t\t\t\t\treturn /** @type {any} */ (document.currentScript);\n\t\t\t\t}\n\n\t\t\t\t// IE11 workaround\n\t\t\t\t// we'll get the src of the current script by parsing IE11's error stack trace\n\t\t\t\t// this will not work for inline scripts\n\n\t\t\t\ttry {\n\t\t\t\t\tthrow new Error();\n\t\t\t\t} catch (err) {\n\t\t\t\t\t// Get file src url from stack. Specifically works with the format of stack traces in IE.\n\t\t\t\t\t// A stack will look like this:\n\t\t\t\t\t//\n\t\t\t\t\t// Error\n\t\t\t\t\t//    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n\t\t\t\t\t//    at Global code (http://localhost/components/prism-core.js:606:1)\n\n\t\t\t\t\tvar src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n\t\t\t\t\tif (src) {\n\t\t\t\t\t\tvar scripts = document.getElementsByTagName('script');\n\t\t\t\t\t\tfor (var i in scripts) {\n\t\t\t\t\t\t\tif (scripts[i].src == src) {\n\t\t\t\t\t\t\t\treturn scripts[i];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns whether a given class is active for `element`.\n\t\t\t *\n\t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n\t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n\t\t\t * given class is just the given class with a `no-` prefix.\n\t\t\t *\n\t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n\t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n\t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n\t\t\t *\n\t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n\t\t\t * version of it, the class is considered active.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} className\n\t\t\t * @param {boolean} [defaultActivation=false]\n\t\t\t * @returns {boolean}\n\t\t\t */\n\t\t\tisActive: function (element, className, defaultActivation) {\n\t\t\t\tvar no = 'no-' + className;\n\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar classList = element.classList;\n\t\t\t\t\tif (classList.contains(className)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (classList.contains(no)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn !!defaultActivation;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tlanguages: {\n\t\t\t/**\n\t\t\t * The grammar for plain, unformatted text.\n\t\t\t */\n\t\t\tplain: plainTextGrammar,\n\t\t\tplaintext: plainTextGrammar,\n\t\t\ttext: plainTextGrammar,\n\t\t\ttxt: plainTextGrammar,\n\n\t\t\t/**\n\t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n\t\t\t *\n\t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n\t\t\t * will be overwritten at its original position.\n\t\t\t *\n\t\t\t * ## Best practices\n\t\t\t *\n\t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n\t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n\t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n\t\t\t *\n\t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n\t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n\t\t\t *\n\t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n\t\t\t * @param {Grammar} redef The new tokens to append.\n\t\t\t * @returns {Grammar} The new language created.\n\t\t\t * @public\n\t\t\t * @example\n\t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n\t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n\t\t\t *     // at its original position\n\t\t\t *     'comment': { ... },\n\t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n\t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n\t\t\t * });\n\t\t\t */\n\t\t\textend: function (id, redef) {\n\t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n\t\t\t\tfor (var key in redef) {\n\t\t\t\t\tlang[key] = redef[key];\n\t\t\t\t}\n\n\t\t\t\treturn lang;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n\t\t\t *\n\t\t\t * ## Usage\n\t\t\t *\n\t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n\t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n\t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n\t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n\t\t\t * this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.markup.style = {\n\t\t\t *     // token\n\t\t\t * };\n\t\t\t * ```\n\t\t\t *\n\t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n\t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n\t\t\t *     'style': {\n\t\t\t *         // token\n\t\t\t *     }\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Special cases\n\t\t\t *\n\t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n\t\t\t * will be ignored.\n\t\t\t *\n\t\t\t * This behavior can be used to insert tokens after `before`:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n\t\t\t *     'comment': Prism.languages.markup.comment,\n\t\t\t *     // tokens after 'comment'\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Limitations\n\t\t\t *\n\t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n\t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n\t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n\t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n\t\t\t *\n\t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n\t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n\t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n\t\t\t *\n\t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n\t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * var oldMarkup = Prism.languages.markup;\n\t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n\t\t\t *\n\t\t\t * assert(oldMarkup !== Prism.languages.markup);\n\t\t\t * assert(newMarkup === Prism.languages.markup);\n\t\t\t * ```\n\t\t\t *\n\t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n\t\t\t * object to be modified.\n\t\t\t * @param {string} before The key to insert before.\n\t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n\t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n\t\t\t * object to be modified.\n\t\t\t *\n\t\t\t * Defaults to `Prism.languages`.\n\t\t\t * @returns {Grammar} The new grammar object.\n\t\t\t * @public\n\t\t\t */\n\t\t\tinsertBefore: function (inside, before, insert, root) {\n\t\t\t\troot = root || /** @type {any} */ (_.languages);\n\t\t\t\tvar grammar = root[inside];\n\t\t\t\t/** @type {Grammar} */\n\t\t\t\tvar ret = {};\n\n\t\t\t\tfor (var token in grammar) {\n\t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n\t\t\t\t\t\tif (token == before) {\n\t\t\t\t\t\t\tfor (var newToken in insert) {\n\t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n\t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n\t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n\t\t\t\t\t\t\tret[token] = grammar[token];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar old = root[inside];\n\t\t\t\troot[inside] = ret;\n\n\t\t\t\t// Update references in other language definitions\n\t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n\t\t\t\t\tif (value === old && key != inside) {\n\t\t\t\t\t\tthis[key] = ret;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn ret;\n\t\t\t},\n\n\t\t\t// Traverse a language definition with Depth First Search\n\t\t\tDFS: function DFS(o, callback, type, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar objId = _.util.objId;\n\n\t\t\t\tfor (var i in o) {\n\t\t\t\t\tif (o.hasOwnProperty(i)) {\n\t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n\t\t\t\t\t\tvar property = o[i];\n\t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n\t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, null, visited);\n\t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, i, visited);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tplugins: {},\n\n\t\t/**\n\t\t * This is the most high-level function in Prism’s API.\n\t\t * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n\t\t * each one of them.\n\t\t *\n\t\t * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n\t\t *\n\t\t * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAll: function (async, callback) {\n\t\t\t_.highlightAllUnder(document, async, callback);\n\t\t},\n\n\t\t/**\n\t\t * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n\t\t * {@link Prism.highlightElement} on each one of them.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-highlightall`\n\t\t * 2. `before-all-elements-highlight`\n\t\t * 3. All hooks of {@link Prism.highlightElement} for each element.\n\t\t *\n\t\t * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n\t\t * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAllUnder: function (container, async, callback) {\n\t\t\tvar env = {\n\t\t\t\tcallback: callback,\n\t\t\t\tcontainer: container,\n\t\t\t\tselector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n\t\t\t};\n\n\t\t\t_.hooks.run('before-highlightall', env);\n\n\t\t\tenv.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n\n\t\t\t_.hooks.run('before-all-elements-highlight', env);\n\n\t\t\tfor (var i = 0, element; (element = env.elements[i++]);) {\n\t\t\t\t_.highlightElement(element, async === true, env.callback);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Highlights the code inside a single element.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-sanity-check`\n\t\t * 2. `before-highlight`\n\t\t * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n\t\t * 4. `before-insert`\n\t\t * 5. `after-highlight`\n\t\t * 6. `complete`\n\t\t *\n\t\t * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n\t\t * the element's language.\n\t\t *\n\t\t * @param {Element} element The element containing the code.\n\t\t * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n\t\t * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n\t\t * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n\t\t * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n\t\t *\n\t\t * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n\t\t * asynchronous highlighting to work. You can build your own bundle on the\n\t\t * [Download page](https://prismjs.com/download.html).\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n\t\t * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightElement: function (element, async, callback) {\n\t\t\t// Find language\n\t\t\tvar language = _.util.getLanguage(element);\n\t\t\tvar grammar = _.languages[language];\n\n\t\t\t// Set language on the element, if not present\n\t\t\t_.util.setLanguage(element, language);\n\n\t\t\t// Set language on the parent, for styling\n\t\t\tvar parent = element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre') {\n\t\t\t\t_.util.setLanguage(parent, language);\n\t\t\t}\n\n\t\t\tvar code = element.textContent;\n\n\t\t\tvar env = {\n\t\t\t\telement: element,\n\t\t\t\tlanguage: language,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tcode: code\n\t\t\t};\n\n\t\t\tfunction insertHighlightedCode(highlightedCode) {\n\t\t\t\tenv.highlightedCode = highlightedCode;\n\n\t\t\t\t_.hooks.run('before-insert', env);\n\n\t\t\t\tenv.element.innerHTML = env.highlightedCode;\n\n\t\t\t\t_.hooks.run('after-highlight', env);\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t}\n\n\t\t\t_.hooks.run('before-sanity-check', env);\n\n\t\t\t// plugins may change/add the parent/element\n\t\t\tparent = env.element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n\t\t\t\tparent.setAttribute('tabindex', '0');\n\t\t\t}\n\n\t\t\tif (!env.code) {\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t_.hooks.run('before-highlight', env);\n\n\t\t\tif (!env.grammar) {\n\t\t\t\tinsertHighlightedCode(_.util.encode(env.code));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (async && _self.Worker) {\n\t\t\t\tvar worker = new Worker(_.filename);\n\n\t\t\t\tworker.onmessage = function (evt) {\n\t\t\t\t\tinsertHighlightedCode(evt.data);\n\t\t\t\t};\n\n\t\t\t\tworker.postMessage(JSON.stringify({\n\t\t\t\t\tlanguage: env.language,\n\t\t\t\t\tcode: env.code,\n\t\t\t\t\timmediateClose: true\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tinsertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns a string with the HTML produced.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-tokenize`\n\t\t * 2. `after-tokenize`\n\t\t * 3. `wrap`: On each {@link Token}.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @param {string} language The name of the language definition passed to `grammar`.\n\t\t * @returns {string} The highlighted HTML.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n\t\t */\n\t\thighlight: function (text, grammar, language) {\n\t\t\tvar env = {\n\t\t\t\tcode: text,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tlanguage: language\n\t\t\t};\n\t\t\t_.hooks.run('before-tokenize', env);\n\t\t\tif (!env.grammar) {\n\t\t\t\tthrow new Error('The language \"' + env.language + '\" has no grammar.');\n\t\t\t}\n\t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n\t\t\t_.hooks.run('after-tokenize', env);\n\t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n\t\t},\n\n\t\t/**\n\t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns an array with the tokenized code.\n\t\t *\n\t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n\t\t *\n\t\t * This method could be useful in other contexts as well, as a very crude parser.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * let code = `var foo = 0;`;\n\t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n\t\t * tokens.forEach(token => {\n\t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n\t\t *         console.log(`Found numeric literal: ${token.content}`);\n\t\t *     }\n\t\t * });\n\t\t */\n\t\ttokenize: function (text, grammar) {\n\t\t\tvar rest = grammar.rest;\n\t\t\tif (rest) {\n\t\t\t\tfor (var token in rest) {\n\t\t\t\t\tgrammar[token] = rest[token];\n\t\t\t\t}\n\n\t\t\t\tdelete grammar.rest;\n\t\t\t}\n\n\t\t\tvar tokenList = new LinkedList();\n\t\t\taddAfter(tokenList, tokenList.head, text);\n\n\t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n\t\t\treturn toArray(tokenList);\n\t\t},\n\n\t\t/**\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thooks: {\n\t\t\tall: {},\n\n\t\t\t/**\n\t\t\t * Adds the given callback to the list of callbacks for the given hook.\n\t\t\t *\n\t\t\t * The callback will be invoked when the hook it is registered for is run.\n\t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n\t\t\t *\n\t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n\t\t\t * @public\n\t\t\t */\n\t\t\tadd: function (name, callback) {\n\t\t\t\tvar hooks = _.hooks.all;\n\n\t\t\t\thooks[name] = hooks[name] || [];\n\n\t\t\t\thooks[name].push(callback);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t\t\t *\n\t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t\t\t * @public\n\t\t\t */\n\t\t\trun: function (name, env) {\n\t\t\t\tvar callbacks = _.hooks.all[name];\n\n\t\t\t\tif (!callbacks || !callbacks.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n\t\t\t\t\tcallback(env);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tToken: Token\n\t};\n\t_self.Prism = _;\n\n\n\t// Typescript note:\n\t// The following can be used to import the Token type in JSDoc:\n\t//\n\t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n\t/**\n\t * Creates a new token.\n\t *\n\t * @param {string} type See {@link Token#type type}\n\t * @param {string | TokenStream} content See {@link Token#content content}\n\t * @param {string|string[]} [alias] The alias(es) of the token.\n\t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n\t * @class\n\t * @global\n\t * @public\n\t */\n\tfunction Token(type, content, alias, matchedStr) {\n\t\t/**\n\t\t * The type of the token.\n\t\t *\n\t\t * This is usually the key of a pattern in a {@link Grammar}.\n\t\t *\n\t\t * @type {string}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.type = type;\n\t\t/**\n\t\t * The strings or tokens contained by this token.\n\t\t *\n\t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n\t\t *\n\t\t * @type {string | TokenStream}\n\t\t * @public\n\t\t */\n\t\tthis.content = content;\n\t\t/**\n\t\t * The alias(es) of the token.\n\t\t *\n\t\t * @type {string|string[]}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.alias = alias;\n\t\t// Copy of the full string this token was created from\n\t\tthis.length = (matchedStr || '').length | 0;\n\t}\n\n\t/**\n\t * A token stream is an array of strings and {@link Token Token} objects.\n\t *\n\t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n\t * them.\n\t *\n\t * 1. No adjacent strings.\n\t * 2. No empty strings.\n\t *\n\t *    The only exception here is the token stream that only contains the empty string and nothing else.\n\t *\n\t * @typedef {Array<string | Token>} TokenStream\n\t * @global\n\t * @public\n\t */\n\n\t/**\n\t * Converts the given token or token stream to an HTML representation.\n\t *\n\t * The following hooks will be run:\n\t * 1. `wrap`: On each {@link Token}.\n\t *\n\t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n\t * @param {string} language The name of current language.\n\t * @returns {string} The HTML representation of the token or token stream.\n\t * @memberof Token\n\t * @static\n\t */\n\tToken.stringify = function stringify(o, language) {\n\t\tif (typeof o == 'string') {\n\t\t\treturn o;\n\t\t}\n\t\tif (Array.isArray(o)) {\n\t\t\tvar s = '';\n\t\t\to.forEach(function (e) {\n\t\t\t\ts += stringify(e, language);\n\t\t\t});\n\t\t\treturn s;\n\t\t}\n\n\t\tvar env = {\n\t\t\ttype: o.type,\n\t\t\tcontent: stringify(o.content, language),\n\t\t\ttag: 'span',\n\t\t\tclasses: ['token', o.type],\n\t\t\tattributes: {},\n\t\t\tlanguage: language\n\t\t};\n\n\t\tvar aliases = o.alias;\n\t\tif (aliases) {\n\t\t\tif (Array.isArray(aliases)) {\n\t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n\t\t\t} else {\n\t\t\t\tenv.classes.push(aliases);\n\t\t\t}\n\t\t}\n\n\t\t_.hooks.run('wrap', env);\n\n\t\tvar attributes = '';\n\t\tfor (var name in env.attributes) {\n\t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n\t\t}\n\n\t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n\t};\n\n\t/**\n\t * @param {RegExp} pattern\n\t * @param {number} pos\n\t * @param {string} text\n\t * @param {boolean} lookbehind\n\t * @returns {RegExpExecArray | null}\n\t */\n\tfunction matchPattern(pattern, pos, text, lookbehind) {\n\t\tpattern.lastIndex = pos;\n\t\tvar match = pattern.exec(text);\n\t\tif (match && lookbehind && match[1]) {\n\t\t\t// change the match to remove the text matched by the Prism lookbehind group\n\t\t\tvar lookbehindLength = match[1].length;\n\t\t\tmatch.index += lookbehindLength;\n\t\t\tmatch[0] = match[0].slice(lookbehindLength);\n\t\t}\n\t\treturn match;\n\t}\n\n\t/**\n\t * @param {string} text\n\t * @param {LinkedList<string | Token>} tokenList\n\t * @param {any} grammar\n\t * @param {LinkedListNode<string | Token>} startNode\n\t * @param {number} startPos\n\t * @param {RematchOptions} [rematch]\n\t * @returns {void}\n\t * @private\n\t *\n\t * @typedef RematchOptions\n\t * @property {string} cause\n\t * @property {number} reach\n\t */\n\tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n\t\tfor (var token in grammar) {\n\t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar patterns = grammar[token];\n\t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n\t\t\tfor (var j = 0; j < patterns.length; ++j) {\n\t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar patternObj = patterns[j];\n\t\t\t\tvar inside = patternObj.inside;\n\t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n\t\t\t\tvar greedy = !!patternObj.greedy;\n\t\t\t\tvar alias = patternObj.alias;\n\n\t\t\t\tif (greedy && !patternObj.pattern.global) {\n\t\t\t\t\t// Without the global flag, lastIndex won't work\n\t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n\t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n\t\t\t\t}\n\n\t\t\t\t/** @type {RegExp} */\n\t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n\t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n\t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n\t\t\t\t\tcurrentNode !== tokenList.tail;\n\t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n\t\t\t\t) {\n\n\t\t\t\t\tif (rematch && pos >= rematch.reach) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar str = currentNode.value;\n\n\t\t\t\t\tif (tokenList.length > text.length) {\n\t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (str instanceof Token) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n\t\t\t\t\tvar match;\n\n\t\t\t\t\tif (greedy) {\n\t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n\t\t\t\t\t\tif (!match || match.index >= text.length) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar from = match.index;\n\t\t\t\t\t\tvar to = match.index + match[0].length;\n\t\t\t\t\t\tvar p = pos;\n\n\t\t\t\t\t\t// find the node that contains the match\n\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\twhile (from >= p) {\n\t\t\t\t\t\t\tcurrentNode = currentNode.next;\n\t\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// adjust pos (and p)\n\t\t\t\t\t\tp -= currentNode.value.length;\n\t\t\t\t\t\tpos = p;\n\n\t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n\t\t\t\t\t\tif (currentNode.value instanceof Token) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// find the last node which is affected by this match\n\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\tvar k = currentNode;\n\t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n\t\t\t\t\t\t\tk = k.next\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tremoveCount++;\n\t\t\t\t\t\t\tp += k.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tremoveCount--;\n\n\t\t\t\t\t\t// replace with the new match\n\t\t\t\t\t\tstr = text.slice(pos, p);\n\t\t\t\t\t\tmatch.index -= pos;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// eslint-disable-next-line no-redeclare\n\t\t\t\t\tvar from = match.index;\n\t\t\t\t\tvar matchStr = match[0];\n\t\t\t\t\tvar before = str.slice(0, from);\n\t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n\t\t\t\t\tvar reach = pos + str.length;\n\t\t\t\t\tif (rematch && reach > rematch.reach) {\n\t\t\t\t\t\trematch.reach = reach;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeFrom = currentNode.prev;\n\n\t\t\t\t\tif (before) {\n\t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n\t\t\t\t\t\tpos += before.length;\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n\t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n\t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n\t\t\t\t\tif (after) {\n\t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (removeCount > 1) {\n\t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n\t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n\t\t\t\t\t\t/** @type {RematchOptions} */\n\t\t\t\t\t\tvar nestedRematch = {\n\t\t\t\t\t\t\tcause: token + ',' + j,\n\t\t\t\t\t\t\treach: reach\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n\t\t\t\t\t\t// the reach might have been extended because of the rematching\n\t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n\t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @typedef LinkedListNode\n\t * @property {T} value\n\t * @property {LinkedListNode<T> | null} prev The previous node.\n\t * @property {LinkedListNode<T> | null} next The next node.\n\t * @template T\n\t * @private\n\t */\n\n\t/**\n\t * @template T\n\t * @private\n\t */\n\tfunction LinkedList() {\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar head = { value: null, prev: null, next: null };\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar tail = { value: null, prev: head, next: null };\n\t\thead.next = tail;\n\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.head = head;\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.tail = tail;\n\t\tthis.length = 0;\n\t}\n\n\t/**\n\t * Adds a new node with the given value to the list.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {T} value\n\t * @returns {LinkedListNode<T>} The added node.\n\t * @template T\n\t */\n\tfunction addAfter(list, node, value) {\n\t\t// assumes that node != list.tail && values.length >= 0\n\t\tvar next = node.next;\n\n\t\tvar newNode = { value: value, prev: node, next: next };\n\t\tnode.next = newNode;\n\t\tnext.prev = newNode;\n\t\tlist.length++;\n\n\t\treturn newNode;\n\t}\n\t/**\n\t * Removes `count` nodes after the given node. The given node will not be removed.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {number} count\n\t * @template T\n\t */\n\tfunction removeRange(list, node, count) {\n\t\tvar next = node.next;\n\t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n\t\t\tnext = next.next;\n\t\t}\n\t\tnode.next = next;\n\t\tnext.prev = node;\n\t\tlist.length -= i;\n\t}\n\t/**\n\t * @param {LinkedList<T>} list\n\t * @returns {T[]}\n\t * @template T\n\t */\n\tfunction toArray(list) {\n\t\tvar array = [];\n\t\tvar node = list.head.next;\n\t\twhile (node !== list.tail) {\n\t\t\tarray.push(node.value);\n\t\t\tnode = node.next;\n\t\t}\n\t\treturn array;\n\t}\n\n\n\tif (!_self.document) {\n\t\tif (!_self.addEventListener) {\n\t\t\t// in Node.js\n\t\t\treturn _;\n\t\t}\n\n\t\tif (!_.disableWorkerMessageHandler) {\n\t\t\t// In worker\n\t\t\t_self.addEventListener('message', function (evt) {\n\t\t\t\tvar message = JSON.parse(evt.data);\n\t\t\t\tvar lang = message.language;\n\t\t\t\tvar code = message.code;\n\t\t\t\tvar immediateClose = message.immediateClose;\n\n\t\t\t\t_self.postMessage(_.highlight(code, _.languages[lang], lang));\n\t\t\t\tif (immediateClose) {\n\t\t\t\t\t_self.close();\n\t\t\t\t}\n\t\t\t}, false);\n\t\t}\n\n\t\treturn _;\n\t}\n\n\t// Get current script and highlight\n\tvar script = _.util.currentScript();\n\n\tif (script) {\n\t\t_.filename = script.src;\n\n\t\tif (script.hasAttribute('data-manual')) {\n\t\t\t_.manual = true;\n\t\t}\n\t}\n\n\tfunction highlightAutomaticallyCallback() {\n\t\tif (!_.manual) {\n\t\t\t_.highlightAll();\n\t\t}\n\t}\n\n\tif (!_.manual) {\n\t\t// If the document state is \"loading\", then we'll use DOMContentLoaded.\n\t\t// If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n\t\t// DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n\t\t// might take longer one animation frame to execute which can create a race condition where only some plugins have\n\t\t// been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n\t\t// See https://github.com/PrismJS/prism/issues/2102\n\t\tvar readyState = document.readyState;\n\t\tif (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n\t\t\tdocument.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n\t\t} else {\n\t\t\tif (window.requestAnimationFrame) {\n\t\t\t\twindow.requestAnimationFrame(highlightAutomaticallyCallback);\n\t\t\t} else {\n\t\t\t\twindow.setTimeout(highlightAutomaticallyCallback, 16);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn _;\n\n}(_self));\n\nif (typeof module !== 'undefined' && module.exports) {\n\tmodule.exports = Prism;\n}\n\n// hack for components to work correctly in node.js\nif (typeof global !== 'undefined') {\n\tglobal.Prism = Prism;\n}\n\n// some additional documentation/types\n\n/**\n * The expansion of a simple `RegExp` literal to support additional properties.\n *\n * @typedef GrammarToken\n * @property {RegExp} pattern The regular expression of the token.\n * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n * @property {boolean} [greedy=false] Whether the token is greedy.\n * @property {string|string[]} [alias] An optional alias or list of aliases.\n * @property {Grammar} [inside] The nested grammar of this token.\n *\n * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n *\n * This can be used to make nested and even recursive language definitions.\n *\n * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n * each another.\n * @global\n * @public\n */\n\n/**\n * @typedef Grammar\n * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n * @global\n * @public\n */\n\n/**\n * A function which will invoked after an element was successfully highlighted.\n *\n * @callback HighlightCallback\n * @param {Element} element The element successfully highlighted.\n * @returns {void}\n * @global\n * @public\n */\n\n/**\n * @callback HookCallback\n * @param {Object<string, any>} env The environment variables of the hook.\n * @returns {void}\n * @global\n * @public\n */\n\n\n/* **********************************************\n     Begin prism-markup.js\n********************************************** */\n\nPrism.languages.markup = {\n\t'comment': {\n\t\tpattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n\t\tgreedy: true\n\t},\n\t'prolog': {\n\t\tpattern: /<\\?[\\s\\S]+?\\?>/,\n\t\tgreedy: true\n\t},\n\t'doctype': {\n\t\t// https://www.w3.org/TR/xml/#NT-doctypedecl\n\t\tpattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'internal-subset': {\n\t\t\t\tpattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: null // see below\n\t\t\t},\n\t\t\t'string': {\n\t\t\t\tpattern: /\"[^\"]*\"|'[^']*'/,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t'punctuation': /^<!|>$|[[\\]]/,\n\t\t\t'doctype-tag': /^DOCTYPE/i,\n\t\t\t'name': /[^\\s<>'\"]+/\n\t\t}\n\t},\n\t'cdata': {\n\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\tgreedy: true\n\t},\n\t'tag': {\n\t\tpattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'tag': {\n\t\t\t\tpattern: /^<\\/?[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /^<\\/?/,\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t},\n\t\t\t'special-attr': [],\n\t\t\t'attr-value': {\n\t\t\t\tpattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^(\\s*)[\"']|[\"']$/,\n\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\t'punctuation': /\\/?>/,\n\t\t\t'attr-name': {\n\t\t\t\tpattern: /[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t},\n\t'entity': [\n\t\t{\n\t\t\tpattern: /&[\\da-z]{1,8};/i,\n\t\t\talias: 'named-entity'\n\t\t},\n\t\t/&#x?[\\da-f]{1,8};/i\n\t]\n};\n\nPrism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n\tPrism.languages.markup['entity'];\nPrism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n// Plugin to make entity title show the real entity, idea by Roman Komarov\nPrism.hooks.add('wrap', function (env) {\n\n\tif (env.type === 'entity') {\n\t\tenv.attributes['title'] = env.content.replace(/&amp;/, '&');\n\t}\n});\n\nObject.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n\t/**\n\t * Adds an inlined language to markup.\n\t *\n\t * An example of an inlined language is CSS with `<style>` tags.\n\t *\n\t * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addInlined('style', 'css');\n\t */\n\tvalue: function addInlined(tagName, lang) {\n\t\tvar includedCdataInside = {};\n\t\tincludedCdataInside['language-' + lang] = {\n\t\t\tpattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\t\tincludedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n\n\t\tvar inside = {\n\t\t\t'included-cdata': {\n\t\t\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\t\t\tinside: includedCdataInside\n\t\t\t}\n\t\t};\n\t\tinside['language-' + lang] = {\n\t\t\tpattern: /[\\s\\S]+/,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\n\t\tvar def = {};\n\t\tdef[tagName] = {\n\t\t\tpattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () { return tagName; }), 'i'),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: inside\n\t\t};\n\n\t\tPrism.languages.insertBefore('markup', 'cdata', def);\n\t}\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n\t/**\n\t * Adds an pattern to highlight languages embedded in HTML attributes.\n\t *\n\t * An example of an inlined language is CSS with `style` attributes.\n\t *\n\t * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addAttribute('style', 'css');\n\t */\n\tvalue: function (attrName, lang) {\n\t\tPrism.languages.markup.tag.inside['special-attr'].push({\n\t\t\tpattern: RegExp(\n\t\t\t\t/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n\t\t\t\t'i'\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'attr-name': /^[^\\s=]+/,\n\t\t\t\t'attr-value': {\n\t\t\t\t\tpattern: /=[\\s\\S]+/,\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'value': {\n\t\t\t\t\t\t\tpattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n\t\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\t\talias: [lang, 'language-' + lang],\n\t\t\t\t\t\t\tinside: Prism.languages[lang]\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t/\"|'/\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n});\n\nPrism.languages.html = Prism.languages.markup;\nPrism.languages.mathml = Prism.languages.markup;\nPrism.languages.svg = Prism.languages.markup;\n\nPrism.languages.xml = Prism.languages.extend('markup', {});\nPrism.languages.ssml = Prism.languages.xml;\nPrism.languages.atom = Prism.languages.xml;\nPrism.languages.rss = Prism.languages.xml;\n\n\n/* **********************************************\n     Begin prism-css.js\n********************************************** */\n\n(function (Prism) {\n\n\tvar string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n\n\tPrism.languages.css = {\n\t\t'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n\t\t'atrule': {\n\t\t\tpattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n\t\t\tinside: {\n\t\t\t\t'rule': /^@[\\w-]+/,\n\t\t\t\t'selector-function-argument': {\n\t\t\t\t\tpattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'selector'\n\t\t\t\t},\n\t\t\t\t'keyword': {\n\t\t\t\t\tpattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t}\n\t\t\t\t// See rest below\n\t\t\t}\n\t\t},\n\t\t'url': {\n\t\t\t// https://drafts.csswg.org/css-values-3/#urls\n\t\t\tpattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^url/i,\n\t\t\t\t'punctuation': /^\\(|\\)$/,\n\t\t\t\t'string': {\n\t\t\t\t\tpattern: RegExp('^' + string.source + '$'),\n\t\t\t\t\talias: 'url'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'selector': {\n\t\t\tpattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': {\n\t\t\tpattern: string,\n\t\t\tgreedy: true\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'important': /!important\\b/i,\n\t\t'function': {\n\t\t\tpattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'punctuation': /[(){};:,]/\n\t};\n\n\tPrism.languages.css['atrule'].inside.rest = Prism.languages.css;\n\n\tvar markup = Prism.languages.markup;\n\tif (markup) {\n\t\tmarkup.tag.addInlined('style', 'css');\n\t\tmarkup.tag.addAttribute('style', 'css');\n\t}\n\n}(Prism));\n\n\n/* **********************************************\n     Begin prism-clike.js\n********************************************** */\n\nPrism.languages.clike = {\n\t'comment': [\n\t\t{\n\t\t\tpattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^\\\\:])\\/\\/.*/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t}\n\t],\n\t'string': {\n\t\tpattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n\t\tgreedy: true\n\t},\n\t'class-name': {\n\t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n\t\tlookbehind: true,\n\t\tinside: {\n\t\t\t'punctuation': /[.\\\\]/\n\t\t}\n\t},\n\t'keyword': /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n\t'boolean': /\\b(?:false|true)\\b/,\n\t'function': /\\b\\w+(?=\\()/,\n\t'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n\t'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n\t'punctuation': /[{}[\\];(),.:]/\n};\n\n\n/* **********************************************\n     Begin prism-javascript.js\n********************************************** */\n\nPrism.languages.javascript = Prism.languages.extend('clike', {\n\t'class-name': [\n\t\tPrism.languages.clike['class-name'],\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n\t\t\tlookbehind: true\n\t\t}\n\t],\n\t'keyword': [\n\t\t{\n\t\t\tpattern: /((?:^|\\})\\s*)catch\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t],\n\t// Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n\t'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n\t'number': {\n\t\tpattern: RegExp(\n\t\t\t/(^|[^\\w$])/.source +\n\t\t\t'(?:' +\n\t\t\t(\n\t\t\t\t// constant\n\t\t\t\t/NaN|Infinity/.source +\n\t\t\t\t'|' +\n\t\t\t\t// binary integer\n\t\t\t\t/0[bB][01]+(?:_[01]+)*n?/.source +\n\t\t\t\t'|' +\n\t\t\t\t// octal integer\n\t\t\t\t/0[oO][0-7]+(?:_[0-7]+)*n?/.source +\n\t\t\t\t'|' +\n\t\t\t\t// hexadecimal integer\n\t\t\t\t/0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source +\n\t\t\t\t'|' +\n\t\t\t\t// decimal bigint\n\t\t\t\t/\\d+(?:_\\d+)*n/.source +\n\t\t\t\t'|' +\n\t\t\t\t// decimal number (integer or float) but no bigint\n\t\t\t\t/(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source\n\t\t\t) +\n\t\t\t')' +\n\t\t\t/(?![\\w$])/.source\n\t\t),\n\t\tlookbehind: true\n\t},\n\t'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n});\n\nPrism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\n\nPrism.languages.insertBefore('javascript', 'keyword', {\n\t'regex': {\n\t\tpattern: RegExp(\n\t\t\t// lookbehind\n\t\t\t// eslint-disable-next-line regexp/no-dupe-characters-character-class\n\t\t\t/((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source +\n\t\t\t// Regex pattern:\n\t\t\t// There are 2 regex patterns here. The RegExp set notation proposal added support for nested character\n\t\t\t// classes if the `v` flag is present. Unfortunately, nested CCs are both context-free and incompatible\n\t\t\t// with the only syntax, so we have to define 2 different regex patterns.\n\t\t\t/\\//.source +\n\t\t\t'(?:' +\n\t\t\t/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source +\n\t\t\t'|' +\n\t\t\t// `v` flag syntax. This supports 3 levels of nested character classes.\n\t\t\t/(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source +\n\t\t\t')' +\n\t\t\t// lookahead\n\t\t\t/(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source\n\t\t),\n\t\tlookbehind: true,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'regex-source': {\n\t\t\t\tpattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'language-regex',\n\t\t\t\tinside: Prism.languages.regex\n\t\t\t},\n\t\t\t'regex-delimiter': /^\\/|\\/$/,\n\t\t\t'regex-flags': /^[a-z]+$/,\n\t\t}\n\t},\n\t// This must be declared before keyword because we use \"function\" inside the look-forward\n\t'function-variable': {\n\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n\t\talias: 'function'\n\t},\n\t'parameter': [\n\t\t{\n\t\t\tpattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t}\n\t],\n\t'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n});\n\nPrism.languages.insertBefore('javascript', 'string', {\n\t'hashbang': {\n\t\tpattern: /^#!.*/,\n\t\tgreedy: true,\n\t\talias: 'comment'\n\t},\n\t'template-string': {\n\t\tpattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'template-punctuation': {\n\t\t\t\tpattern: /^`|`$/,\n\t\t\t\talias: 'string'\n\t\t\t},\n\t\t\t'interpolation': {\n\t\t\t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation-punctuation': {\n\t\t\t\t\t\tpattern: /^\\$\\{|\\}$/,\n\t\t\t\t\t\talias: 'punctuation'\n\t\t\t\t\t},\n\t\t\t\t\trest: Prism.languages.javascript\n\t\t\t\t}\n\t\t\t},\n\t\t\t'string': /[\\s\\S]+/\n\t\t}\n\t},\n\t'string-property': {\n\t\tpattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n\t\tlookbehind: true,\n\t\tgreedy: true,\n\t\talias: 'property'\n\t}\n});\n\nPrism.languages.insertBefore('javascript', 'operator', {\n\t'literal-property': {\n\t\tpattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n\t\tlookbehind: true,\n\t\talias: 'property'\n\t},\n});\n\nif (Prism.languages.markup) {\n\tPrism.languages.markup.tag.addInlined('script', 'javascript');\n\n\t// add attribute support for all DOM events.\n\t// https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n\tPrism.languages.markup.tag.addAttribute(\n\t\t/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,\n\t\t'javascript'\n\t);\n}\n\nPrism.languages.js = Prism.languages.javascript;\n\n\n/* **********************************************\n     Begin prism-file-highlight.js\n********************************************** */\n\n(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined') {\n\t\treturn;\n\t}\n\n\t// https://developer.mozilla.org/en-US/docs/Web/API/Element/matches#Polyfill\n\tif (!Element.prototype.matches) {\n\t\tElement.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\n\t}\n\n\tvar LOADING_MESSAGE = 'Loading…';\n\tvar FAILURE_MESSAGE = function (status, message) {\n\t\treturn '✖ Error ' + status + ' while fetching file: ' + message;\n\t};\n\tvar FAILURE_EMPTY_MESSAGE = '✖ Error: File does not exist or is empty';\n\n\tvar EXTENSIONS = {\n\t\t'js': 'javascript',\n\t\t'py': 'python',\n\t\t'rb': 'ruby',\n\t\t'ps1': 'powershell',\n\t\t'psm1': 'powershell',\n\t\t'sh': 'bash',\n\t\t'bat': 'batch',\n\t\t'h': 'c',\n\t\t'tex': 'latex'\n\t};\n\n\tvar STATUS_ATTR = 'data-src-status';\n\tvar STATUS_LOADING = 'loading';\n\tvar STATUS_LOADED = 'loaded';\n\tvar STATUS_FAILED = 'failed';\n\n\tvar SELECTOR = 'pre[data-src]:not([' + STATUS_ATTR + '=\"' + STATUS_LOADED + '\"])'\n\t\t+ ':not([' + STATUS_ATTR + '=\"' + STATUS_LOADING + '\"])';\n\n\t/**\n\t * Loads the given file.\n\t *\n\t * @param {string} src The URL or path of the source file to load.\n\t * @param {(result: string) => void} success\n\t * @param {(reason: string) => void} error\n\t */\n\tfunction loadFile(src, success, error) {\n\t\tvar xhr = new XMLHttpRequest();\n\t\txhr.open('GET', src, true);\n\t\txhr.onreadystatechange = function () {\n\t\t\tif (xhr.readyState == 4) {\n\t\t\t\tif (xhr.status < 400 && xhr.responseText) {\n\t\t\t\t\tsuccess(xhr.responseText);\n\t\t\t\t} else {\n\t\t\t\t\tif (xhr.status >= 400) {\n\t\t\t\t\t\terror(FAILURE_MESSAGE(xhr.status, xhr.statusText));\n\t\t\t\t\t} else {\n\t\t\t\t\t\terror(FAILURE_EMPTY_MESSAGE);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\txhr.send(null);\n\t}\n\n\t/**\n\t * Parses the given range.\n\t *\n\t * This returns a range with inclusive ends.\n\t *\n\t * @param {string | null | undefined} range\n\t * @returns {[number, number | undefined] | undefined}\n\t */\n\tfunction parseRange(range) {\n\t\tvar m = /^\\s*(\\d+)\\s*(?:(,)\\s*(?:(\\d+)\\s*)?)?$/.exec(range || '');\n\t\tif (m) {\n\t\t\tvar start = Number(m[1]);\n\t\t\tvar comma = m[2];\n\t\t\tvar end = m[3];\n\n\t\t\tif (!comma) {\n\t\t\t\treturn [start, start];\n\t\t\t}\n\t\t\tif (!end) {\n\t\t\t\treturn [start, undefined];\n\t\t\t}\n\t\t\treturn [start, Number(end)];\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tPrism.hooks.add('before-highlightall', function (env) {\n\t\tenv.selector += ', ' + SELECTOR;\n\t});\n\n\tPrism.hooks.add('before-sanity-check', function (env) {\n\t\tvar pre = /** @type {HTMLPreElement} */ (env.element);\n\t\tif (pre.matches(SELECTOR)) {\n\t\t\tenv.code = ''; // fast-path the whole thing and go to complete\n\n\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_LOADING); // mark as loading\n\n\t\t\t// add code element with loading message\n\t\t\tvar code = pre.appendChild(document.createElement('CODE'));\n\t\t\tcode.textContent = LOADING_MESSAGE;\n\n\t\t\tvar src = pre.getAttribute('data-src');\n\n\t\t\tvar language = env.language;\n\t\t\tif (language === 'none') {\n\t\t\t\t// the language might be 'none' because there is no language set;\n\t\t\t\t// in this case, we want to use the extension as the language\n\t\t\t\tvar extension = (/\\.(\\w+)$/.exec(src) || [, 'none'])[1];\n\t\t\t\tlanguage = EXTENSIONS[extension] || extension;\n\t\t\t}\n\n\t\t\t// set language classes\n\t\t\tPrism.util.setLanguage(code, language);\n\t\t\tPrism.util.setLanguage(pre, language);\n\n\t\t\t// preload the language\n\t\t\tvar autoloader = Prism.plugins.autoloader;\n\t\t\tif (autoloader) {\n\t\t\t\tautoloader.loadLanguages(language);\n\t\t\t}\n\n\t\t\t// load file\n\t\t\tloadFile(\n\t\t\t\tsrc,\n\t\t\t\tfunction (text) {\n\t\t\t\t\t// mark as loaded\n\t\t\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_LOADED);\n\n\t\t\t\t\t// handle data-range\n\t\t\t\t\tvar range = parseRange(pre.getAttribute('data-range'));\n\t\t\t\t\tif (range) {\n\t\t\t\t\t\tvar lines = text.split(/\\r\\n?|\\n/g);\n\n\t\t\t\t\t\t// the range is one-based and inclusive on both ends\n\t\t\t\t\t\tvar start = range[0];\n\t\t\t\t\t\tvar end = range[1] == null ? lines.length : range[1];\n\n\t\t\t\t\t\tif (start < 0) { start += lines.length; }\n\t\t\t\t\t\tstart = Math.max(0, Math.min(start - 1, lines.length));\n\t\t\t\t\t\tif (end < 0) { end += lines.length; }\n\t\t\t\t\t\tend = Math.max(0, Math.min(end, lines.length));\n\n\t\t\t\t\t\ttext = lines.slice(start, end).join('\\n');\n\n\t\t\t\t\t\t// add data-start for line numbers\n\t\t\t\t\t\tif (!pre.hasAttribute('data-start')) {\n\t\t\t\t\t\t\tpre.setAttribute('data-start', String(start + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// highlight code\n\t\t\t\t\tcode.textContent = text;\n\t\t\t\t\tPrism.highlightElement(code);\n\t\t\t\t},\n\t\t\t\tfunction (error) {\n\t\t\t\t\t// mark as failed\n\t\t\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_FAILED);\n\n\t\t\t\t\tcode.textContent = error;\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\t});\n\n\tPrism.plugins.fileHighlight = {\n\t\t/**\n\t\t * Executes the File Highlight plugin for all matching `pre` elements under the given container.\n\t\t *\n\t\t * Note: Elements which are already loaded or currently loading will not be touched by this method.\n\t\t *\n\t\t * @param {ParentNode} [container=document]\n\t\t */\n\t\thighlight: function highlight(container) {\n\t\t\tvar elements = (container || document).querySelectorAll(SELECTOR);\n\n\t\t\tfor (var i = 0, element; (element = elements[i++]);) {\n\t\t\t\tPrism.highlightElement(element);\n\t\t\t}\n\t\t}\n\t};\n\n\tvar logged = false;\n\t/** @deprecated Use `Prism.plugins.fileHighlight.highlight` instead. */\n\tPrism.fileHighlight = function () {\n\t\tif (!logged) {\n\t\t\tconsole.warn('Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead.');\n\t\t\tlogged = true;\n\t\t}\n\t\tPrism.plugins.fileHighlight.highlight.apply(this, arguments);\n\t};\n\n}());\n", "Prism.languages.python = {\n\t'comment': {\n\t\tpattern: /(^|[^\\\\])#.*/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'string-interpolation': {\n\t\tpattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'interpolation': {\n\t\t\t\t// \"{\" <expression> <optional \"!s\", \"!r\", or \"!a\"> <optional \":\" format specifier> \"}\"\n\t\t\t\tpattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'format-spec': {\n\t\t\t\t\t\tpattern: /(:)[^:(){}]+(?=\\}$)/,\n\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t},\n\t\t\t\t\t'conversion-option': {\n\t\t\t\t\t\tpattern: /![sra](?=[:}]$)/,\n\t\t\t\t\t\talias: 'punctuation'\n\t\t\t\t\t},\n\t\t\t\t\trest: null\n\t\t\t\t}\n\t\t\t},\n\t\t\t'string': /[\\s\\S]+/\n\t\t}\n\t},\n\t'triple-quoted-string': {\n\t\tpattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n\t\tgreedy: true,\n\t\talias: 'string'\n\t},\n\t'string': {\n\t\tpattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n\t\tgreedy: true\n\t},\n\t'function': {\n\t\tpattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n\t\tlookbehind: true\n\t},\n\t'class-name': {\n\t\tpattern: /(\\bclass\\s+)\\w+/i,\n\t\tlookbehind: true\n\t},\n\t'decorator': {\n\t\tpattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n\t\tlookbehind: true,\n\t\talias: ['annotation', 'punctuation'],\n\t\tinside: {\n\t\t\t'punctuation': /\\./\n\t\t}\n\t},\n\t'keyword': /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n\t'builtin': /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n\t'boolean': /\\b(?:False|None|True)\\b/,\n\t'number': /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n\t'operator': /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n\t'punctuation': /[{}[\\];(),.:]/\n};\n\nPrism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = Prism.languages.python;\n\nPrism.languages.py = Prism.languages.python;\n"], "names": ["_self", "Prism", "lang", "uniqueId", "plainTextGrammar", "_", "encode", "tokens", "Token", "o", "obj", "deepClone", "visited", "clone", "id", "key", "v", "i", "element", "m", "language", "err", "src", "scripts", "className", "defaultActivation", "no", "classList", "redef", "inside", "before", "insert", "root", "grammar", "ret", "token", "newToken", "old", "value", "DFS", "callback", "type", "objId", "property", "propertyType", "async", "container", "env", "parent", "code", "insertHighlightedCode", "highlightedCode", "worker", "evt", "text", "rest", "tokenList", "LinkedList", "addAfter", "matchGrammar", "toArray", "name", "hooks", "callbacks", "content", "alias", "matchedStr", "stringify", "s", "e", "aliases", "attributes", "matchPattern", "pattern", "pos", "lookbehind", "match", "lookbehindLength", "startNode", "startPos", "rematch", "patterns", "j", "patternObj", "greedy", "flags", "currentNode", "str", "removeCount", "from", "to", "p", "k", "matchStr", "after", "reach", "removeFrom", "<PERSON><PERSON><PERSON><PERSON>", "wrapped", "nested<PERSON><PERSON><PERSON>", "head", "tail", "list", "node", "next", "newNode", "count", "array", "message", "immediateClose", "script", "highlightAutomatically<PERSON>allback", "readyState", "module", "global", "tagName", "includedCdataInside", "def", "attrName", "string", "markup", "LOADING_MESSAGE", "FAILURE_MESSAGE", "status", "FAILURE_EMPTY_MESSAGE", "EXTENSIONS", "STATUS_ATTR", "STATUS_LOADING", "STATUS_LOADED", "STATUS_FAILED", "SELECTOR", "loadFile", "success", "error", "xhr", "parseRange", "range", "start", "comma", "end", "pre", "extension", "autoloader", "lines", "elements", "logged"], "mappings": "+EAOA,IAAIA,EAAS,OAAO,OAAW,IAC5B,OAEA,OAAO,kBAAsB,KAAe,gBAAgB,kBAC1D,KACA,CAAE,EAGP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQA,IAAIC,EAAS,SAAUD,EAAO,CAG7B,IAAIE,EAAO,0CACPC,EAAW,EAGXC,EAAmB,CAAA,EAGnBC,EAAI,CAsBP,OAAQL,EAAM,OAASA,EAAM,MAAM,OAsBnC,4BAA6BA,EAAM,OAASA,EAAM,MAAM,4BAWxD,KAAM,CACL,OAAQ,SAASM,EAAOC,EAAQ,CAC/B,OAAIA,aAAkBC,EACd,IAAIA,EAAMD,EAAO,KAAMD,EAAOC,EAAO,OAAO,EAAGA,EAAO,KAAK,EACxD,MAAM,QAAQA,CAAM,EACvBA,EAAO,IAAID,CAAM,EAEjBC,EAAO,QAAQ,KAAM,OAAO,EAAE,QAAQ,KAAM,MAAM,EAAE,QAAQ,UAAW,GAAG,CAElF,EAkBD,KAAM,SAAUE,EAAG,CAClB,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAC,EAAE,MAAM,EAAG,EAAE,CACpD,EAQD,MAAO,SAAUC,EAAK,CACrB,OAAKA,EAAI,MACR,OAAO,eAAeA,EAAK,OAAQ,CAAE,MAAO,EAAEP,CAAQ,CAAE,EAElDO,EAAI,IACX,EAYD,MAAO,SAASC,EAAUF,EAAGG,EAAS,CACrCA,EAAUA,GAAW,GAErB,IAAIC,EAAWC,EACf,OAAQT,EAAE,KAAK,KAAKI,CAAC,EAAC,CACrB,IAAK,SAEJ,GADAK,EAAKT,EAAE,KAAK,MAAMI,CAAC,EACfG,EAAQE,CAAE,EACb,OAAOF,EAAQE,CAAE,EAElBD,EAA4C,CAAE,EAC9CD,EAAQE,CAAE,EAAID,EAEd,QAASE,KAAON,EACXA,EAAE,eAAeM,CAAG,IACvBF,EAAME,CAAG,EAAIJ,EAAUF,EAAEM,CAAG,EAAGH,CAAO,GAIxC,OAA2BC,EAE5B,IAAK,QAEJ,OADAC,EAAKT,EAAE,KAAK,MAAMI,CAAC,EACfG,EAAQE,CAAE,EACNF,EAAQE,CAAE,GAElBD,EAAQ,CAAA,EACRD,EAAQE,CAAE,EAAID,EAE2BJ,EAAK,QAAQ,SAAUO,EAAGC,EAAG,CACrEJ,EAAMI,CAAC,EAAIN,EAAUK,EAAGJ,CAAO,CACtC,CAAO,EAE0BC,GAE5B,QACC,OAAOJ,CACR,CACD,EAUD,YAAa,SAAUS,EAAS,CAC/B,KAAOA,GAAS,CACf,IAAIC,EAAIjB,EAAK,KAAKgB,EAAQ,SAAS,EACnC,GAAIC,EACH,OAAOA,EAAE,CAAC,EAAE,cAEbD,EAAUA,EAAQ,aAClB,CACD,MAAO,MACP,EASD,YAAa,SAAUA,EAASE,EAAU,CAGzCF,EAAQ,UAAYA,EAAQ,UAAU,QAAQ,OAAOhB,EAAM,IAAI,EAAG,EAAE,EAIpEgB,EAAQ,UAAU,IAAI,YAAcE,CAAQ,CAC5C,EASD,cAAe,UAAY,CAC1B,GAAI,OAAO,SAAa,IACvB,OAAO,KAER,GAAI,kBAAmB,SACtB,OAA2B,SAAS,cAOrC,GAAI,CACH,MAAM,IAAI,KACV,OAAQC,EAAK,CAQb,IAAIC,GAAO,qCAAqC,KAAKD,EAAI,KAAK,GAAK,IAAI,CAAC,EACxE,GAAIC,EAAK,CACR,IAAIC,EAAU,SAAS,qBAAqB,QAAQ,EACpD,QAASN,KAAKM,EACb,GAAIA,EAAQN,CAAC,EAAE,KAAOK,EACrB,OAAOC,EAAQN,CAAC,CAGlB,CACD,OAAO,IACP,CACD,EAqBD,SAAU,SAAUC,EAASM,EAAWC,EAAmB,CAG1D,QAFIC,EAAK,MAAQF,EAEVN,GAAS,CACf,IAAIS,EAAYT,EAAQ,UACxB,GAAIS,EAAU,SAASH,CAAS,EAC/B,MAAO,GAER,GAAIG,EAAU,SAASD,CAAE,EACxB,MAAO,GAERR,EAAUA,EAAQ,aAClB,CACD,MAAO,CAAC,CAACO,CACT,CACD,EASD,UAAW,CAIV,MAAOrB,EACP,UAAWA,EACX,KAAMA,EACN,IAAKA,EA8BL,OAAQ,SAAUU,EAAIc,EAAO,CAC5B,IAAI1B,EAAOG,EAAE,KAAK,MAAMA,EAAE,UAAUS,CAAE,CAAC,EAEvC,QAASC,KAAOa,EACf1B,EAAKa,CAAG,EAAIa,EAAMb,CAAG,EAGtB,OAAOb,CACP,EA6ED,aAAc,SAAU2B,EAAQC,EAAQC,EAAQC,EAAM,CACrDA,EAAOA,GAA4B3B,EAAE,UACrC,IAAI4B,EAAUD,EAAKH,CAAM,EAErBK,EAAM,CAAA,EAEV,QAASC,KAASF,EACjB,GAAIA,EAAQ,eAAeE,CAAK,EAAG,CAElC,GAAIA,GAASL,EACZ,QAASM,KAAYL,EAChBA,EAAO,eAAeK,CAAQ,IACjCF,EAAIE,CAAQ,EAAIL,EAAOK,CAAQ,GAM7BL,EAAO,eAAeI,CAAK,IAC/BD,EAAIC,CAAK,EAAIF,EAAQE,CAAK,EAE3B,CAGF,IAAIE,EAAML,EAAKH,CAAM,EACrB,OAAAG,EAAKH,CAAM,EAAIK,EAGf7B,EAAE,UAAU,IAAIA,EAAE,UAAW,SAAUU,EAAKuB,EAAO,CAC9CA,IAAUD,GAAOtB,GAAOc,IAC3B,KAAKd,CAAG,EAAImB,EAElB,CAAK,EAEMA,CACP,EAGD,IAAK,SAASK,EAAI9B,EAAG+B,EAAUC,EAAM7B,EAAS,CAC7CA,EAAUA,GAAW,GAErB,IAAI8B,EAAQrC,EAAE,KAAK,MAEnB,QAASY,KAAKR,EACb,GAAIA,EAAE,eAAeQ,CAAC,EAAG,CACxBuB,EAAS,KAAK/B,EAAGQ,EAAGR,EAAEQ,CAAC,EAAGwB,GAAQxB,CAAC,EAEnC,IAAI0B,EAAWlC,EAAEQ,CAAC,EACd2B,EAAevC,EAAE,KAAK,KAAKsC,CAAQ,EAEnCC,IAAiB,UAAY,CAAChC,EAAQ8B,EAAMC,CAAQ,CAAC,GACxD/B,EAAQ8B,EAAMC,CAAQ,CAAC,EAAI,GAC3BJ,EAAII,EAAUH,EAAU,KAAM5B,CAAO,GAC3BgC,IAAiB,SAAW,CAAChC,EAAQ8B,EAAMC,CAAQ,CAAC,IAC9D/B,EAAQ8B,EAAMC,CAAQ,CAAC,EAAI,GAC3BJ,EAAII,EAAUH,EAAUvB,EAAGL,CAAO,EAEnC,CAEF,CACD,EAED,QAAS,CAAE,EAcX,aAAc,SAAUiC,EAAOL,EAAU,CACxCnC,EAAE,kBAAkB,SAAUwC,EAAOL,CAAQ,CAC7C,EAiBD,kBAAmB,SAAUM,EAAWD,EAAOL,EAAU,CACxD,IAAIO,EAAM,CACT,SAAUP,EACV,UAAWM,EACX,SAAU,kGACd,EAEGzC,EAAE,MAAM,IAAI,sBAAuB0C,CAAG,EAEtCA,EAAI,SAAW,MAAM,UAAU,MAAM,MAAMA,EAAI,UAAU,iBAAiBA,EAAI,QAAQ,CAAC,EAEvF1C,EAAE,MAAM,IAAI,gCAAiC0C,CAAG,EAEhD,QAAS9B,EAAI,EAAGC,EAAUA,EAAU6B,EAAI,SAAS9B,GAAG,GACnDZ,EAAE,iBAAiBa,EAAS2B,IAAU,GAAME,EAAI,QAAQ,CAEzD,EA8BD,iBAAkB,SAAU7B,EAAS2B,EAAOL,EAAU,CAErD,IAAIpB,EAAWf,EAAE,KAAK,YAAYa,CAAO,EACrCe,EAAU5B,EAAE,UAAUe,CAAQ,EAGlCf,EAAE,KAAK,YAAYa,EAASE,CAAQ,EAGpC,IAAI4B,EAAS9B,EAAQ,cACjB8B,GAAUA,EAAO,SAAS,YAAW,IAAO,OAC/C3C,EAAE,KAAK,YAAY2C,EAAQ5B,CAAQ,EAGpC,IAAI6B,EAAO/B,EAAQ,YAEf6B,EAAM,CACT,QAAS7B,EACT,SAAUE,EACV,QAASa,EACT,KAAMgB,CACV,EAEG,SAASC,EAAsBC,EAAiB,CAC/CJ,EAAI,gBAAkBI,EAEtB9C,EAAE,MAAM,IAAI,gBAAiB0C,CAAG,EAEhCA,EAAI,QAAQ,UAAYA,EAAI,gBAE5B1C,EAAE,MAAM,IAAI,kBAAmB0C,CAAG,EAClC1C,EAAE,MAAM,IAAI,WAAY0C,CAAG,EAC3BP,GAAYA,EAAS,KAAKO,EAAI,OAAO,CACrC,CAUD,GARA1C,EAAE,MAAM,IAAI,sBAAuB0C,CAAG,EAGtCC,EAASD,EAAI,QAAQ,cACjBC,GAAUA,EAAO,SAAS,YAAW,IAAO,OAAS,CAACA,EAAO,aAAa,UAAU,GACvFA,EAAO,aAAa,WAAY,GAAG,EAGhC,CAACD,EAAI,KAAM,CACd1C,EAAE,MAAM,IAAI,WAAY0C,CAAG,EAC3BP,GAAYA,EAAS,KAAKO,EAAI,OAAO,EACrC,MACA,CAID,GAFA1C,EAAE,MAAM,IAAI,mBAAoB0C,CAAG,EAE/B,CAACA,EAAI,QAAS,CACjBG,EAAsB7C,EAAE,KAAK,OAAO0C,EAAI,IAAI,CAAC,EAC7C,MACA,CAED,GAAIF,GAAS7C,EAAM,OAAQ,CAC1B,IAAIoD,EAAS,IAAI,OAAO/C,EAAE,QAAQ,EAElC+C,EAAO,UAAY,SAAUC,EAAK,CACjCH,EAAsBG,EAAI,IAAI,CACnC,EAEID,EAAO,YAAY,KAAK,UAAU,CACjC,SAAUL,EAAI,SACd,KAAMA,EAAI,KACV,eAAgB,EAChB,CAAA,CAAC,CACN,MACIG,EAAsB7C,EAAE,UAAU0C,EAAI,KAAMA,EAAI,QAASA,EAAI,QAAQ,CAAC,CAEvE,EAsBD,UAAW,SAAUO,EAAMrB,EAASb,EAAU,CAC7C,IAAI2B,EAAM,CACT,KAAMO,EACN,QAASrB,EACT,SAAUb,CACd,EAEG,GADAf,EAAE,MAAM,IAAI,kBAAmB0C,CAAG,EAC9B,CAACA,EAAI,QACR,MAAM,IAAI,MAAM,iBAAmBA,EAAI,SAAW,mBAAmB,EAEtE,OAAAA,EAAI,OAAS1C,EAAE,SAAS0C,EAAI,KAAMA,EAAI,OAAO,EAC7C1C,EAAE,MAAM,IAAI,iBAAkB0C,CAAG,EAC1BvC,EAAM,UAAUH,EAAE,KAAK,OAAO0C,EAAI,MAAM,EAAGA,EAAI,QAAQ,CAC9D,EA0BD,SAAU,SAAUO,EAAMrB,EAAS,CAClC,IAAIsB,EAAOtB,EAAQ,KACnB,GAAIsB,EAAM,CACT,QAASpB,KAASoB,EACjBtB,EAAQE,CAAK,EAAIoB,EAAKpB,CAAK,EAG5B,OAAOF,EAAQ,IACf,CAED,IAAIuB,EAAY,IAAIC,EACpB,OAAAC,EAASF,EAAWA,EAAU,KAAMF,CAAI,EAExCK,EAAaL,EAAME,EAAWvB,EAASuB,EAAU,KAAM,CAAC,EAEjDI,EAAQJ,CAAS,CACxB,EAOD,MAAO,CACN,IAAK,CAAE,EAcP,IAAK,SAAUK,EAAMrB,EAAU,CAC9B,IAAIsB,EAAQzD,EAAE,MAAM,IAEpByD,EAAMD,CAAI,EAAIC,EAAMD,CAAI,GAAK,CAAA,EAE7BC,EAAMD,CAAI,EAAE,KAAKrB,CAAQ,CACzB,EAWD,IAAK,SAAUqB,EAAMd,EAAK,CACzB,IAAIgB,EAAY1D,EAAE,MAAM,IAAIwD,CAAI,EAEhC,GAAI,GAACE,GAAa,CAACA,EAAU,QAI7B,QAAS9C,EAAI,EAAGuB,EAAWA,EAAWuB,EAAU9C,GAAG,GAClDuB,EAASO,CAAG,CAEb,CACD,EAED,MAAOvC,CACT,EACCR,EAAM,MAAQK,EAmBd,SAASG,EAAMiC,EAAMuB,EAASC,EAAOC,EAAY,CAUhD,KAAK,KAAOzB,EASZ,KAAK,QAAUuB,EAQf,KAAK,MAAQC,EAEb,KAAK,QAAUC,GAAc,IAAI,OAAS,CAC1C,CA8BD1D,EAAM,UAAY,SAAS2D,EAAU1D,EAAGW,EAAU,CACjD,GAAI,OAAOX,GAAK,SACf,OAAOA,EAER,GAAI,MAAM,QAAQA,CAAC,EAAG,CACrB,IAAI2D,EAAI,GACR,OAAA3D,EAAE,QAAQ,SAAU4D,EAAG,CACtBD,GAAKD,EAAUE,EAAGjD,CAAQ,CAC9B,CAAI,EACMgD,CACP,CAED,IAAIrB,EAAM,CACT,KAAMtC,EAAE,KACR,QAAS0D,EAAU1D,EAAE,QAASW,CAAQ,EACtC,IAAK,OACL,QAAS,CAAC,QAASX,EAAE,IAAI,EACzB,WAAY,CAAE,EACd,SAAUW,CACb,EAEMkD,EAAU7D,EAAE,MACZ6D,IACC,MAAM,QAAQA,CAAO,EACxB,MAAM,UAAU,KAAK,MAAMvB,EAAI,QAASuB,CAAO,EAE/CvB,EAAI,QAAQ,KAAKuB,CAAO,GAI1BjE,EAAE,MAAM,IAAI,OAAQ0C,CAAG,EAEvB,IAAIwB,EAAa,GACjB,QAASV,KAAQd,EAAI,WACpBwB,GAAc,IAAMV,EAAO,MAAQd,EAAI,WAAWc,CAAI,GAAK,IAAI,QAAQ,KAAM,QAAQ,EAAI,IAG1F,MAAO,IAAMd,EAAI,IAAM,WAAaA,EAAI,QAAQ,KAAK,GAAG,EAAI,IAAMwB,EAAa,IAAMxB,EAAI,QAAU,KAAOA,EAAI,IAAM,GACtH,EASC,SAASyB,EAAaC,EAASC,EAAKpB,EAAMqB,EAAY,CACrDF,EAAQ,UAAYC,EACpB,IAAIE,EAAQH,EAAQ,KAAKnB,CAAI,EAC7B,GAAIsB,GAASD,GAAcC,EAAM,CAAC,EAAG,CAEpC,IAAIC,EAAmBD,EAAM,CAAC,EAAE,OAChCA,EAAM,OAASC,EACfD,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAE,MAAMC,CAAgB,CAC1C,CACD,OAAOD,CACP,CAgBD,SAASjB,EAAaL,EAAME,EAAWvB,EAAS6C,EAAWC,EAAUC,EAAS,CAC7E,QAAS7C,KAASF,EACjB,GAAI,GAACA,EAAQ,eAAeE,CAAK,GAAK,CAACF,EAAQE,CAAK,GAIpD,KAAI8C,EAAWhD,EAAQE,CAAK,EAC5B8C,EAAW,MAAM,QAAQA,CAAQ,EAAIA,EAAW,CAACA,CAAQ,EAEzD,QAASC,EAAI,EAAGA,EAAID,EAAS,OAAQ,EAAEC,EAAG,CACzC,GAAIF,GAAWA,EAAQ,OAAS7C,EAAQ,IAAM+C,EAC7C,OAGD,IAAIC,EAAaF,EAASC,CAAC,EACvBrD,EAASsD,EAAW,OACpBR,EAAa,CAAC,CAACQ,EAAW,WAC1BC,EAAS,CAAC,CAACD,EAAW,OACtBlB,EAAQkB,EAAW,MAEvB,GAAIC,GAAU,CAACD,EAAW,QAAQ,OAAQ,CAEzC,IAAIE,EAAQF,EAAW,QAAQ,SAAU,EAAC,MAAM,WAAW,EAAE,CAAC,EAC9DA,EAAW,QAAU,OAAOA,EAAW,QAAQ,OAAQE,EAAQ,GAAG,CAClE,CAKD,QAFIZ,EAAUU,EAAW,SAAWA,EAG/BG,EAAcR,EAAU,KAAMJ,EAAMK,EACxCO,IAAgB9B,EAAU,MAItB,EAAAwB,GAAWN,GAAOM,EAAQ,OAH9BN,GAAOY,EAAY,MAAM,OAAQA,EAAcA,EAAY,KAC1D,CAMD,IAAIC,EAAMD,EAAY,MAEtB,GAAI9B,EAAU,OAASF,EAAK,OAE3B,OAGD,GAAI,EAAAiC,aAAe/E,GAInB,KAAIgF,EAAc,EACdZ,EAEJ,GAAIQ,EAAQ,CAEX,GADAR,EAAQJ,EAAaC,EAASC,EAAKpB,EAAMqB,CAAU,EAC/C,CAACC,GAASA,EAAM,OAAStB,EAAK,OACjC,MAGD,IAAImC,EAAOb,EAAM,MACbc,EAAKd,EAAM,MAAQA,EAAM,CAAC,EAAE,OAC5Be,EAAIjB,EAIR,IADAiB,GAAKL,EAAY,MAAM,OAChBG,GAAQE,GACdL,EAAcA,EAAY,KAC1BK,GAAKL,EAAY,MAAM,OAOxB,GAJAK,GAAKL,EAAY,MAAM,OACvBZ,EAAMiB,EAGFL,EAAY,iBAAiB9E,EAChC,SAID,QACKoF,EAAIN,EACRM,IAAMpC,EAAU,OAASmC,EAAID,GAAM,OAAOE,EAAE,OAAU,UACtDA,EAAIA,EAAE,KAENJ,IACAG,GAAKC,EAAE,MAAM,OAEdJ,IAGAD,EAAMjC,EAAK,MAAMoB,EAAKiB,CAAC,EACvBf,EAAM,OAASF,CACrB,SACME,EAAQJ,EAAaC,EAAS,EAAGc,EAAKZ,CAAU,EAC5C,CAACC,EACJ,SAKF,IAAIa,EAAOb,EAAM,MACbiB,EAAWjB,EAAM,CAAC,EAClB9C,EAASyD,EAAI,MAAM,EAAGE,CAAI,EAC1BK,EAAQP,EAAI,MAAME,EAAOI,EAAS,MAAM,EAExCE,EAAQrB,EAAMa,EAAI,OAClBP,GAAWe,EAAQf,EAAQ,QAC9BA,EAAQ,MAAQe,GAGjB,IAAIC,EAAaV,EAAY,KAEzBxD,IACHkE,EAAatC,EAASF,EAAWwC,EAAYlE,CAAM,EACnD4C,GAAO5C,EAAO,QAGfmE,EAAYzC,EAAWwC,EAAYR,CAAW,EAE9C,IAAIU,EAAU,IAAI1F,EAAM2B,EAAON,EAASxB,EAAE,SAASwF,EAAUhE,CAAM,EAAIgE,EAAU5B,EAAO4B,CAAQ,EAOhG,GANAP,EAAc5B,EAASF,EAAWwC,EAAYE,CAAO,EAEjDJ,GACHpC,EAASF,EAAW8B,EAAaQ,CAAK,EAGnCN,EAAc,EAAG,CAKpB,IAAIW,EAAgB,CACnB,MAAOhE,EAAQ,IAAM+C,EACrB,MAAOa,CACd,EACMpC,EAAaL,EAAME,EAAWvB,EAASqD,EAAY,KAAMZ,EAAKyB,CAAa,EAGvEnB,GAAWmB,EAAc,MAAQnB,EAAQ,QAC5CA,EAAQ,MAAQmB,EAAc,MAE/B,EACD,CACD,EAEF,CAeD,SAAS1C,GAAa,CAErB,IAAI2C,EAAO,CAAE,MAAO,KAAM,KAAM,KAAM,KAAM,MAExCC,EAAO,CAAE,MAAO,KAAM,KAAMD,EAAM,KAAM,MAC5CA,EAAK,KAAOC,EAGZ,KAAK,KAAOD,EAEZ,KAAK,KAAOC,EACZ,KAAK,OAAS,CACd,CAWD,SAAS3C,EAAS4C,EAAMC,EAAMjE,EAAO,CAEpC,IAAIkE,EAAOD,EAAK,KAEZE,EAAU,CAAE,MAAOnE,EAAO,KAAMiE,EAAM,KAAMC,GAChD,OAAAD,EAAK,KAAOE,EACZD,EAAK,KAAOC,EACZH,EAAK,SAEEG,CACP,CASD,SAASR,EAAYK,EAAMC,EAAMG,EAAO,CAEvC,QADIF,EAAOD,EAAK,KACPtF,EAAI,EAAGA,EAAIyF,GAASF,IAASF,EAAK,KAAMrF,IAChDuF,EAAOA,EAAK,KAEbD,EAAK,KAAOC,EACZA,EAAK,KAAOD,EACZD,EAAK,QAAUrF,CACf,CAMD,SAAS2C,EAAQ0C,EAAM,CAGtB,QAFIK,EAAQ,CAAA,EACRJ,EAAOD,EAAK,KAAK,KACdC,IAASD,EAAK,MACpBK,EAAM,KAAKJ,EAAK,KAAK,EACrBA,EAAOA,EAAK,KAEb,OAAOI,CACP,CAGD,GAAI,CAAC3G,EAAM,SACV,OAAKA,EAAM,mBAKNK,EAAE,6BAENL,EAAM,iBAAiB,UAAW,SAAUqD,EAAK,CAChD,IAAIuD,EAAU,KAAK,MAAMvD,EAAI,IAAI,EAC7BnD,EAAO0G,EAAQ,SACf3D,EAAO2D,EAAQ,KACfC,EAAiBD,EAAQ,eAE7B5G,EAAM,YAAYK,EAAE,UAAU4C,EAAM5C,EAAE,UAAUH,CAAI,EAAGA,CAAI,CAAC,EACxD2G,GACH7G,EAAM,MAAK,CAEZ,EAAE,EAAK,GAGFK,EAIR,IAAIyG,EAASzG,EAAE,KAAK,cAAa,EAE7ByG,IACHzG,EAAE,SAAWyG,EAAO,IAEhBA,EAAO,aAAa,aAAa,IACpCzG,EAAE,OAAS,KAIb,SAAS0G,GAAiC,CACpC1G,EAAE,QACNA,EAAE,aAAY,CAEf,CAED,GAAI,CAACA,EAAE,OAAQ,CAOd,IAAI2G,EAAa,SAAS,WACtBA,IAAe,WAAaA,IAAe,eAAiBF,GAAUA,EAAO,MAChF,SAAS,iBAAiB,mBAAoBC,CAA8B,EAExE,OAAO,sBACV,OAAO,sBAAsBA,CAA8B,EAE3D,OAAO,WAAWA,EAAgC,EAAE,CAGtD,CAED,OAAO1G,CAER,EAAEL,CAAK,EAE8BiH,EAAO,UAC3CA,EAAA,QAAiBhH,GAId,OAAOiH,EAAW,MACrBA,EAAO,MAAQjH,GAyDhBA,EAAM,UAAU,OAAS,CACxB,QAAW,CACV,QAAS,8BACT,OAAQ,EACR,EACD,OAAU,CACT,QAAS,iBACT,OAAQ,EACR,EACD,QAAW,CAEV,QAAS,uHACT,OAAQ,GACR,OAAQ,CACP,kBAAmB,CAClB,QAAS,6BACT,WAAY,GACZ,OAAQ,GACR,OAAQ,IACR,EACD,OAAU,CACT,QAAS,kBACT,OAAQ,EACR,EACD,YAAe,eACf,cAAe,YACf,KAAQ,YACR,CACD,EACD,MAAS,CACR,QAAS,4BACT,OAAQ,EACR,EACD,IAAO,CACN,QAAS,uHACT,OAAQ,GACR,OAAQ,CACP,IAAO,CACN,QAAS,iBACT,OAAQ,CACP,YAAe,QACf,UAAa,cACb,CACD,EACD,eAAgB,CAAE,EAClB,aAAc,CACb,QAAS,qCACT,OAAQ,CACP,YAAe,CACd,CACC,QAAS,KACT,MAAO,aACP,EACD,CACC,QAAS,mBACT,WAAY,EACZ,CACD,CACD,CACD,EACD,YAAe,OACf,YAAa,CACZ,QAAS,YACT,OAAQ,CACP,UAAa,cACb,CACD,CAED,CACD,EACD,OAAU,CACT,CACC,QAAS,kBACT,MAAO,cACP,EACD,oBACA,CACF,EAEAA,EAAM,UAAU,OAAO,IAAO,OAAO,YAAY,EAAE,OAAO,OACzDA,EAAM,UAAU,OAAO,OACxBA,EAAM,UAAU,OAAO,QAAW,OAAO,iBAAiB,EAAE,OAASA,EAAM,UAAU,OAGrFA,EAAM,MAAM,IAAI,OAAQ,SAAU8C,EAAK,CAElCA,EAAI,OAAS,WAChBA,EAAI,WAAW,MAAWA,EAAI,QAAQ,QAAQ,QAAS,GAAG,EAE5D,CAAC,EAED,OAAO,eAAe9C,EAAM,UAAU,OAAO,IAAK,aAAc,CAY/D,MAAO,SAAoBkH,EAASjH,EAAM,CACzC,IAAIkH,EAAsB,CAAA,EAC1BA,EAAoB,YAAclH,CAAI,EAAI,CACzC,QAAS,oCACT,WAAY,GACZ,OAAQD,EAAM,UAAUC,CAAI,CAC/B,EACEkH,EAAoB,MAAW,uBAE/B,IAAIvF,EAAS,CACZ,iBAAkB,CACjB,QAAS,4BACT,OAAQuF,CACR,CACJ,EACEvF,EAAO,YAAc3B,CAAI,EAAI,CAC5B,QAAS,UACT,OAAQD,EAAM,UAAUC,CAAI,CAC/B,EAEE,IAAImH,EAAM,CAAA,EACVA,EAAIF,CAAO,EAAI,CACd,QAAS,OAAO,wFAAwF,OAAO,QAAQ,MAAO,UAAY,CAAE,OAAOA,CAAU,CAAA,EAAG,GAAG,EACnK,WAAY,GACZ,OAAQ,GACR,OAAQtF,CACX,EAEE5B,EAAM,UAAU,aAAa,SAAU,QAASoH,CAAG,CACnD,CACF,CAAC,EACD,OAAO,eAAepH,EAAM,UAAU,OAAO,IAAK,eAAgB,CAYjE,MAAO,SAAUqH,EAAUpH,EAAM,CAChCD,EAAM,UAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK,CACtD,QAAS,OACR,aAAa,OAAS,MAAQqH,EAAW,IAAM,iDAAiD,OAChG,GACA,EACD,WAAY,GACZ,OAAQ,CACP,YAAa,WACb,aAAc,CACb,QAAS,WACT,OAAQ,CACP,MAAS,CACR,QAAS,yCACT,WAAY,GACZ,MAAO,CAACpH,EAAM,YAAcA,CAAI,EAChC,OAAQD,EAAM,UAAUC,CAAI,CAC5B,EACD,YAAe,CACd,CACC,QAAS,KACT,MAAO,aACP,EACD,KACA,CACD,CACD,CACD,CACJ,CAAG,CACD,CACF,CAAC,EAEDD,EAAM,UAAU,KAAOA,EAAM,UAAU,OACvCA,EAAM,UAAU,OAASA,EAAM,UAAU,OACzCA,EAAM,UAAU,IAAMA,EAAM,UAAU,OAEtCA,EAAM,UAAU,IAAMA,EAAM,UAAU,OAAO,SAAU,CAAA,CAAE,EACzDA,EAAM,UAAU,KAAOA,EAAM,UAAU,IACvCA,EAAM,UAAU,KAAOA,EAAM,UAAU,IACvCA,EAAM,UAAU,IAAMA,EAAM,UAAU,IAOrC,SAAUA,EAAO,CAEjB,IAAIsH,EAAS,8EAEbtH,EAAM,UAAU,IAAM,CACrB,QAAW,mBACX,OAAU,CACT,QAAS,OAAO,aAAe,sBAAsB,OAAS,IAAMsH,EAAO,OAAS,MAAQ,kBAAkB,MAAM,EACpH,OAAQ,CACP,KAAQ,WACR,6BAA8B,CAC7B,QAAS,4FACT,WAAY,GACZ,MAAO,UACP,EACD,QAAW,CACV,QAAS,yCACT,WAAY,EACZ,CAED,CACD,EACD,IAAO,CAEN,QAAS,OAAO,eAAiBA,EAAO,OAAS,IAAM,8BAA8B,OAAS,OAAQ,GAAG,EACzG,OAAQ,GACR,OAAQ,CACP,SAAY,QACZ,YAAe,UACf,OAAU,CACT,QAAS,OAAO,IAAMA,EAAO,OAAS,GAAG,EACzC,MAAO,KACP,CACD,CACD,EACD,SAAY,CACX,QAAS,OAAO,oDAAuDA,EAAO,OAAS,eAAe,EACtG,WAAY,EACZ,EACD,OAAU,CACT,QAASA,EACT,OAAQ,EACR,EACD,SAAY,CACX,QAAS,oFACT,WAAY,EACZ,EACD,UAAa,gBACb,SAAY,CACX,QAAS,kCACT,WAAY,EACZ,EACD,YAAe,WACjB,EAECtH,EAAM,UAAU,IAAI,OAAU,OAAO,KAAOA,EAAM,UAAU,IAE5D,IAAIuH,EAASvH,EAAM,UAAU,OACzBuH,IACHA,EAAO,IAAI,WAAW,QAAS,KAAK,EACpCA,EAAO,IAAI,aAAa,QAAS,KAAK,EAGvC,EAACvH,CAAK,EAOPA,EAAM,UAAU,MAAQ,CACvB,QAAW,CACV,CACC,QAAS,kCACT,WAAY,GACZ,OAAQ,EACR,EACD,CACC,QAAS,mBACT,WAAY,GACZ,OAAQ,EACR,CACD,EACD,OAAU,CACT,QAAS,iDACT,OAAQ,EACR,EACD,aAAc,CACb,QAAS,2FACT,WAAY,GACZ,OAAQ,CACP,YAAe,OACf,CACD,EACD,QAAW,6GACX,QAAW,qBACX,SAAY,cACZ,OAAU,4DACV,SAAY,+CACZ,YAAe,eAChB,EAOAA,EAAM,UAAU,WAAaA,EAAM,UAAU,OAAO,QAAS,CAC5D,aAAc,CACbA,EAAM,UAAU,MAAM,YAAY,EAClC,CACC,QAAS,0GACT,WAAY,EACZ,CACD,EACD,QAAW,CACV,CACC,QAAS,uBACT,WAAY,EACZ,EACD,CACC,QAAS,mdACT,WAAY,EACZ,CACD,EAED,SAAY,oGACZ,OAAU,CACT,QAAS,OACR,aAAa,OACb,OAGC,eAAe,OACf,IAEA,0BAA0B,OAC1B,IAEA,4BAA4B,OAC5B,IAEA,sCAAsC,OACtC,IAEA,gBAAgB,OAChB,IAEA,oFAAoF,QAErF,IACA,YAAY,MACZ,EACD,WAAY,EACZ,EACD,SAAY,2FACb,CAAC,EAEDA,EAAM,UAAU,WAAW,YAAY,EAAE,CAAC,EAAE,QAAU,uEAEtDA,EAAM,UAAU,aAAa,aAAc,UAAW,CACrD,MAAS,CACR,QAAS,OAGR,0DAA0D,OAK1D,KAAK,OACL,MACA,iEAAiE,OACjE,IAEA,qIAAqI,OACrI,IAEA,kEAAkE,MAClE,EACD,WAAY,GACZ,OAAQ,GACR,OAAQ,CACP,eAAgB,CACf,QAAS,4BACT,WAAY,GACZ,MAAO,iBACP,OAAQA,EAAM,UAAU,KACxB,EACD,kBAAmB,UACnB,cAAe,UACf,CACD,EAED,oBAAqB,CACpB,QAAS,gMACT,MAAO,UACP,EACD,UAAa,CACZ,CACC,QAAS,sIACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,EACD,CACC,QAAS,qFACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,EACD,CACC,QAAS,kEACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,EACD,CACC,QAAS,8eACT,WAAY,GACZ,OAAQA,EAAM,UAAU,UACxB,CACD,EACD,SAAY,2BACb,CAAC,EAEDA,EAAM,UAAU,aAAa,aAAc,SAAU,CACpD,SAAY,CACX,QAAS,QACT,OAAQ,GACR,MAAO,SACP,EACD,kBAAmB,CAClB,QAAS,2EACT,OAAQ,GACR,OAAQ,CACP,uBAAwB,CACvB,QAAS,QACT,MAAO,QACP,EACD,cAAiB,CAChB,QAAS,mEACT,WAAY,GACZ,OAAQ,CACP,4BAA6B,CAC5B,QAAS,YACT,MAAO,aACP,EACD,KAAMA,EAAM,UAAU,UACtB,CACD,EACD,OAAU,SACV,CACD,EACD,kBAAmB,CAClB,QAAS,4EACT,WAAY,GACZ,OAAQ,GACR,MAAO,UACP,CACF,CAAC,EAEDA,EAAM,UAAU,aAAa,aAAc,WAAY,CACtD,mBAAoB,CACnB,QAAS,oFACT,WAAY,GACZ,MAAO,UACP,CACF,CAAC,EAEGA,EAAM,UAAU,SACnBA,EAAM,UAAU,OAAO,IAAI,WAAW,SAAU,YAAY,EAI5DA,EAAM,UAAU,OAAO,IAAI,aAC1B,yNAAyN,OACzN,YACF,GAGAA,EAAM,UAAU,GAAKA,EAAM,UAAU,WAOpC,UAAY,CAEZ,GAAI,OAAOA,EAAU,KAAe,OAAO,SAAa,IACvD,OAII,QAAQ,UAAU,UACtB,QAAQ,UAAU,QAAU,QAAQ,UAAU,mBAAqB,QAAQ,UAAU,uBAGtF,IAAIwH,EAAkB,WAClBC,EAAkB,SAAUC,EAAQf,EAAS,CAChD,MAAO,WAAae,EAAS,yBAA2Bf,CAC1D,EACKgB,EAAwB,2CAExBC,EAAa,CAChB,GAAM,aACN,GAAM,SACN,GAAM,OACN,IAAO,aACP,KAAQ,aACR,GAAM,OACN,IAAO,QACP,EAAK,IACL,IAAO,OACT,EAEKC,EAAc,kBACdC,EAAiB,UACjBC,EAAgB,SAChBC,EAAgB,SAEhBC,EAAW,sBAAwBJ,EAAc,KAAOE,EAAgB,YAC9DF,EAAc,KAAOC,EAAiB,MASpD,SAASI,EAAS7G,EAAK8G,EAASC,EAAO,CACtC,IAAIC,EAAM,IAAI,eACdA,EAAI,KAAK,MAAOhH,EAAK,EAAI,EACzBgH,EAAI,mBAAqB,UAAY,CAChCA,EAAI,YAAc,IACjBA,EAAI,OAAS,KAAOA,EAAI,aAC3BF,EAAQE,EAAI,YAAY,EAEpBA,EAAI,QAAU,IACjBD,EAAMX,EAAgBY,EAAI,OAAQA,EAAI,UAAU,CAAC,EAEjDD,EAAMT,CAAqB,EAIjC,EACEU,EAAI,KAAK,IAAI,CACb,CAUD,SAASC,EAAWC,EAAO,CAC1B,IAAIrH,EAAI,wCAAwC,KAAKqH,GAAS,EAAE,EAChE,GAAIrH,EAAG,CACN,IAAIsH,EAAQ,OAAOtH,EAAE,CAAC,CAAC,EACnBuH,EAAQvH,EAAE,CAAC,EACXwH,EAAMxH,EAAE,CAAC,EAEb,OAAKuH,EAGAC,EAGE,CAACF,EAAO,OAAOE,CAAG,CAAC,EAFlB,CAACF,EAAO,MAAS,EAHjB,CAACA,EAAOA,CAAK,CAMrB,CAED,CAEDxI,EAAM,MAAM,IAAI,sBAAuB,SAAU8C,EAAK,CACrDA,EAAI,UAAY,KAAOmF,CACzB,CAAE,EAEDjI,EAAM,MAAM,IAAI,sBAAuB,SAAU8C,EAAK,CACrD,IAAI6F,EAAqC7F,EAAI,QAC7C,GAAI6F,EAAI,QAAQV,CAAQ,EAAG,CAC1BnF,EAAI,KAAO,GAEX6F,EAAI,aAAad,EAAaC,CAAc,EAG5C,IAAI9E,EAAO2F,EAAI,YAAY,SAAS,cAAc,MAAM,CAAC,EACzD3F,EAAK,YAAcwE,EAEnB,IAAInG,EAAMsH,EAAI,aAAa,UAAU,EAEjCxH,EAAW2B,EAAI,SACnB,GAAI3B,IAAa,OAAQ,CAGxB,IAAIyH,GAAa,WAAW,KAAKvH,CAAG,GAAK,CAAG,CAAA,MAAM,GAAG,CAAC,EACtDF,EAAWyG,EAAWgB,CAAS,GAAKA,CACpC,CAGD5I,EAAM,KAAK,YAAYgD,EAAM7B,CAAQ,EACrCnB,EAAM,KAAK,YAAY2I,EAAKxH,CAAQ,EAGpC,IAAI0H,EAAa7I,EAAM,QAAQ,WAC3B6I,GACHA,EAAW,cAAc1H,CAAQ,EAIlC+G,EACC7G,EACA,SAAUgC,EAAM,CAEfsF,EAAI,aAAad,EAAaE,CAAa,EAG3C,IAAIQ,EAAQD,EAAWK,EAAI,aAAa,YAAY,CAAC,EACrD,GAAIJ,EAAO,CACV,IAAIO,EAAQzF,EAAK,MAAM,WAAW,EAG9BmF,EAAQD,EAAM,CAAC,EACfG,EAAMH,EAAM,CAAC,GAAK,KAAOO,EAAM,OAASP,EAAM,CAAC,EAE/CC,EAAQ,IAAKA,GAASM,EAAM,QAChCN,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAIA,EAAQ,EAAGM,EAAM,MAAM,CAAC,EACjDJ,EAAM,IAAKA,GAAOI,EAAM,QAC5BJ,EAAM,KAAK,IAAI,EAAG,KAAK,IAAIA,EAAKI,EAAM,MAAM,CAAC,EAE7CzF,EAAOyF,EAAM,MAAMN,EAAOE,CAAG,EAAE,KAAK;AAAA,CAAI,EAGnCC,EAAI,aAAa,YAAY,GACjCA,EAAI,aAAa,aAAc,OAAOH,EAAQ,CAAC,CAAC,CAEjD,CAGDxF,EAAK,YAAcK,EACnBrD,EAAM,iBAAiBgD,CAAI,CAC3B,EACD,SAAUoF,EAAO,CAEhBO,EAAI,aAAad,EAAaG,CAAa,EAE3ChF,EAAK,YAAcoF,CACnB,CACL,CACG,CACH,CAAE,EAEDpI,EAAM,QAAQ,cAAgB,CAQ7B,UAAW,SAAmB6C,EAAW,CAGxC,QAFIkG,GAAYlG,GAAa,UAAU,iBAAiBoF,CAAQ,EAEvDjH,EAAI,EAAGC,EAAUA,EAAU8H,EAAS/H,GAAG,GAC/ChB,EAAM,iBAAiBiB,CAAO,CAE/B,CACH,EAEC,IAAI+H,EAAS,GAEbhJ,EAAM,cAAgB,UAAY,CAC5BgJ,IACJ,QAAQ,KAAK,yFAAyF,EACtGA,EAAS,IAEVhJ,EAAM,QAAQ,cAAc,UAAU,MAAM,KAAM,SAAS,CAC7D,CAEA,uCCz5DA,MAAM,UAAU,OAAS,CACxB,QAAW,CACV,QAAS,eACT,WAAY,GACZ,OAAQ,EACR,EACD,uBAAwB,CACvB,QAAS,sEACT,OAAQ,GACR,OAAQ,CACP,cAAiB,CAEhB,QAAS,sFACT,WAAY,GACZ,OAAQ,CACP,cAAe,CACd,QAAS,sBACT,WAAY,EACZ,EACD,oBAAqB,CACpB,QAAS,kBACT,MAAO,aACP,EACD,KAAM,IACN,CACD,EACD,OAAU,SACV,CACD,EACD,uBAAwB,CACvB,QAAS,uCACT,OAAQ,GACR,MAAO,QACP,EACD,OAAU,CACT,QAAS,mDACT,OAAQ,EACR,EACD,SAAY,CACX,QAAS,4CACT,WAAY,EACZ,EACD,aAAc,CACb,QAAS,mBACT,WAAY,EACZ,EACD,UAAa,CACZ,QAAS,2BACT,WAAY,GACZ,MAAO,CAAC,aAAc,aAAa,EACnC,OAAQ,CACP,YAAe,IACf,CACD,EACD,QAAW,wNACX,QAAW,shBACX,QAAW,0BACX,OAAU,mJACV,SAAY,uDACZ,YAAe,eAChB,EAEA,MAAM,UAAU,OAAO,sBAAsB,EAAE,OAAO,cAAiB,OAAO,KAAO,MAAM,UAAU,OAErG,MAAM,UAAU,GAAK,MAAM,UAAU", "x_google_ignoreList": [0, 1]}