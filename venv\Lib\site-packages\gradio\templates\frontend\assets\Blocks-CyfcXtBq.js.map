{"version": 3, "mappings": ";o0BAAAA,GAiBKC,EAAAC,EAAAC,CAAA,EALJC,GAICF,EAAAG,CAAA,uzBChBFL,GAiBKC,EAAAC,EAAAC,CAAA,EALJC,GAICF,EAAAG,CAAA,g1BChBFL,GAiBKC,EAAAC,EAAAC,CAAA,EALJC,GAICF,EAAAG,CAAA,uGCWK,MAAMC,WAAmB,KAAM,CACrC,YAAYC,EAAiB,CAC5B,MAAMA,CAAO,EACb,KAAK,KAAO,YACb,CACD,CAEsB,eAAAC,GACrBC,EACAC,EACkB,CACd,UAAO,kBAAoB,KACxB,UAAIJ,GAAW,6BAA6B,EAE/C,IAAAK,EACAC,EACAC,EACgB,CACf,IAAAC,EAEJ,GAAI,OAAOL,GAAS,UAAYA,EAAK,IACpCK,EAAML,EAAK,YACD,OAAOA,GAAS,SACpBK,EAAAL,MAEA,WAAI,MAAM,kCAAkC,EAG7C,MAAAM,EAAW,MAAM,MAAMD,CAAG,EACzBH,EAAA,MAAMI,EAAS,OACtBH,EAAcG,EAAS,QAAQ,IAAI,cAAc,GAAK,GACtDF,EAAWE,EAAS,QAAQ,IAAI,qBAAqB,GAAK,EAe3D,CAEM,MAAAC,EAAO,IAAI,KAAK,CAACL,CAAI,EAAGE,EAAU,CAAE,KAAMD,CAAA,CAAa,EAGvDK,EAAiB,MAAM,MAAM,iCAAkC,CACpE,OAAQ,OACR,KAAMD,EACN,QAAS,CACR,eAAgBA,EAAK,KACrB,mBAAoB,gBACrB,EACA,EAGG,IAACC,EAAe,GAAI,CACvB,GACCA,EAAe,QAAQ,IAAI,cAAc,GAAG,SAAS,kBAAkB,EACtE,CACK,MAAAC,EAAQ,MAAMD,EAAe,OACnC,MAAM,IAAIX,GAAW,kBAAkBY,EAAM,KAAK,EAAE,CACrD,CACM,UAAIZ,GAAW,gBAAgB,CACtC,CAIO,OADQ,MAAMW,EAAe,MAErC,CAcO,SAASE,GAAKC,EAAoC,CACnDA,EAAA,iBAAiB,QAASC,CAAW,EAE1C,eAAeA,EAAYC,EAAkC,CACtD,MAAAjB,EAAOiB,EAAM,eAEb,CAACC,CAAW,EAAIlB,EAAK,OACzBmB,GAAMA,GAAG,UAAY,UAAYA,EAAE,UAAU,SAAS,kBAAkB,GAG1E,GAAID,EAAa,CAYP,IAAAE,EAAT,SAAuBC,EAA2C,CACjEA,EAAoB,MAAM,QAAU,IACpC,WAAW,IAAM,CAChBA,EAAoB,MAAM,QAAU,KAClC,GAAI,GAfRJ,EAAM,yBAAyB,EAE/B,MAAMK,EAAYJ,EAAY,cAAe,UAAU,KAAK,EACtDK,EAAqB,MAAM,KAChCL,EAAY,UACX,CAAC,EAEY,MAAMM,GAAkBF,CAAS,GAEpCF,EAAcG,CAAkB,CAQ7C,CACD,CAEO,OACN,SAAgB,CACVR,EAAA,oBAAoB,QAASC,CAAW,CAC9C,EAEF,CAEA,eAAeQ,GAAkBC,EAAiC,CACjE,IAAIC,EAAS,GACb,GAAI,cAAe,UACZ,gBAAU,UAAU,UAAUD,CAAK,EAChCC,EAAA,OACH,CACA,MAAAC,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQF,EAEjBE,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEb,cAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAO,EAEZ,IACH,SAAS,YAAY,MAAM,EAClBD,EAAA,SACDb,EAAO,CACf,QAAQ,MAAMA,CAAK,EACVa,EAAA,UACR,CACDC,EAAS,OAAO,CACjB,CACD,CAEO,OAAAD,CACR,CAEa,MAAAE,GAAeC,GAA4B,CACvD,MAAMC,EAAQ,KAAK,MAAMD,EAAU,IAAI,EACjCE,EAAU,KAAK,MAAOF,EAAU,KAAQ,EAAE,EAC1CG,EAAoB,KAAK,MAAMH,CAAO,EAAI,GAC1CI,EAAiB,GAAGF,EAAU,GAAK,IAAM,EAAE,GAAGA,CAAO,GACrDG,EAAiB,GACtBF,EAAoB,GAAK,IAAM,EAChC,GAAGA,CAAiB,GAEpB,OAAIF,EAAQ,EACJ,GAAGA,CAAK,IAAIG,CAAc,IAAIC,CAAc,GAE7C,GAAGH,CAAO,IAAIG,CAAc,EACpC,EAiBO,MAAMC,EAA4D,CAaxE,YACCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAuBC,GAAsBA,EAC7CC,EACAC,EACC,CAbe,oBAAAC,GAAgB,KAAK,IAAI,EAczC,KAAKC,GAAMZ,EACX,KAAK,MAAQE,EACb,KAAK,QAAUC,EACf,KAAKU,GAAMZ,EACX,KAAK,cAAgBK,EAErB,KAAK,KAAOC,EACZ,KAAK,KAAOH,EACZ,KAAK,WAAaC,EAClB,KAAK,OAASI,EAEd,KAAK,gBAAkBC,CACxB,CApCAE,GAIAC,GAkCA,SAA4BC,EAAe9C,EAAmB,CACvD,MAAAe,EAAI,IAAI,YAAY,SAAU,CACnC,QAAS,GACT,OAAQ,CAAE,KAAAf,EAAM,GAAI,KAAK4C,GAAK,MAAOE,CAAW,EAChD,EACI,KAAAD,GAAI,cAAc9B,CAAC,CACzB,CACD,CAEA,SAAS4B,GAERI,EACAC,EAA4C,YACb,CAC/B,OAAO,KAAK,gBAAiB,CAC5B,KAAAD,EACA,QAAS,KAAK,OAAO,QAAQ,KAC7B,QAAAC,CAAA,CACA,CACF,CCjQO,SAASC,GAAKtC,EAAM,CAAE,KAAAuC,EAAM,GAAAC,CAAI,EAAEC,EAAS,GAAI,CACrD,MAAMC,EAAQ,iBAAiB1C,CAAI,EAC7B2C,EAAYD,EAAM,YAAc,OAAS,GAAKA,EAAM,UACpD,CAACE,EAAIC,CAAE,EAAIH,EAAM,gBAAgB,MAAM,GAAG,EAAE,IAAI,UAAU,EAC1DI,EAAKP,EAAK,KAAQA,EAAK,MAAQK,EAAMJ,EAAG,OAASA,EAAG,KAAOI,GAC3DG,EAAKR,EAAK,IAAOA,EAAK,OAASM,EAAML,EAAG,QAAUA,EAAG,IAAMK,GAC3D,CAAE,MAAAG,EAAQ,EAAG,SAAAC,EAAY,GAAM,KAAK,KAAK,CAAC,EAAI,IAAK,OAAAC,EAASC,EAAQ,EAAKV,EAC/E,MAAO,CACN,MAAAO,EACA,SAAUI,GAAYH,CAAQ,EAAIA,EAAS,KAAK,KAAKH,EAAKA,EAAKC,EAAKA,CAAE,CAAC,EAAIE,EAC3E,OAAAC,EACA,IAAK,CAACG,EAAGC,IAAM,CACd,MAAMzB,EAAIyB,EAAIR,EACRS,EAAID,EAAIP,EACRS,EAAKH,EAAKC,EAAIf,EAAK,MAASC,EAAG,MAC/BiB,EAAKJ,EAAKC,EAAIf,EAAK,OAAUC,EAAG,OACtC,MAAO,cAAcG,CAAS,cAAcd,CAAC,OAAO0B,CAAC,aAAaC,CAAE,KAAKC,CAAE,IAC3E,CACH,CACA,kdC7B+B,QAAAC,EAAS,SAAgB,kpBA8DjD,OAAAC,OAAS,UAAS,EAEbA,OAAS,OAAM,EAEfA,OAAS,QAAO,iHAMOA,EAAI,4HAXdA,EAAI,mDAWFA,EAAI,kDACLA,EAAI,qDAFFA,EAAI,8EASVA,EAAI,gIASVA,EAAI,2DACYA,EAAwB,oCAtCpCA,EAAI,0FAGRA,EAAO,YAJvB/E,GAyCKC,EAAA+E,EAAA7E,CAAA,EA/BJC,GAQK4E,EAAAC,CAAA,6BAEL7E,GAKK4E,EAAAE,CAAA,EAJJ9E,GAA2C8E,EAAAC,CAAA,kBAC3C/E,GAEK8E,EAAAE,CAAA,cADGL,EAAO,WAIhB3E,GAQQ4E,EAAAK,CAAA,EADPjF,GAAqCiF,EAAAC,CAAA,UAGtClF,GAGC4E,EAAAO,CAAA,2BAZUR,EAAa,yOAlBAA,EAAI,uDAWMA,EAAI,oCAAZA,EAAI,+DAErBA,EAAO,mCADSA,EAAI,yEAFFA,EAAI,uEASVA,EAAI,iEASVA,EAAI,+EACYA,EAAwB,wDAtCpCA,EAAI,kEAGRA,EAAO,+CAGXS,EAAAC,GAAAT,EAAAU,GAAA,UAAU,IAAK,MAAO,GAAG,wDACxBC,EAAAC,GAAAZ,EAAAU,GAAA,UAAU,GAAG,2FAvDd,SAAAnF,EAAU,EAAE,EAAAsF,GACZ,KAAAnF,CAA0B,EAAAmF,GAC1B,GAAApD,CAAU,EAAAoD,EACV,UAAAxB,EAA0B,EAAE,EAAAwB,EAC5B,SAAAC,EAAU,EAAI,EAAAD,EAEnB,MAAAE,EAAmBC,GAAmB,KAEjC,QAAAA,GAAY,QAAIA,EAAM,SAAS,IAAI,EAAE,SAAW,SAAS,YACzD,OACF,KAITC,GAAU,QAAQ,mCAAqC7E,EAAI,CACtD,WAAYA,GACX2E,EAAgB3E,EAAK,aAAa,MAAM,KAC3CA,EAAK,aAAa,SAAU,QAAQ,EACpCA,EAAK,aAAa,MAAO,qBAAqB,KAU3C,MAAA8E,EAAWC,cAERC,GAAa,CACrBF,EAAS,QAASzD,CAAE,EAGrBqC,GAAO,KACFT,IAAa,MAChB,gBACC+B,KACE/B,EAAW,gRAfb9D,EAAU0F,GAAU,SAAS1F,CAAO,mBAEtC8F,EAAA,EAAEC,EAAUR,CAAO,iBACjBO,EAAA,EAAAhC,EAAWA,GAAY,IAAI,iBAgB3BgC,EAAA,EAAAE,EAA8B,GAAAlC,GAAY,CAAC,+xCC7B7CrE,GAEKC,EAAAuG,EAAArG,CAAA,6NAFgBsG,EAAAC,GAAAF,EAAAG,EAAAjD,GAAA,UAAU,GAAG,wIAD5BqB,EAAQ,eAA8CA,EAAE,mBAA7D,OAAI6B,GAAA,6JADP5G,GAMKC,EAAAuG,EAAArG,CAAA,+EALG4E,EAAQ,0JAAb,OAAI6B,GAAA,uHAVG,SAAAC,GAAcC,EAAyB,CAC3CA,EAAU,OAAS,GAClB,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,yBAP1B,SAAAC,EAAQ,IAAAlB,uHAEhBgB,GAAcE,CAAQ,osCCP1B/G,GAkBKC,EAAAC,EAAAC,CAAA,EARJC,GAOGF,EAAA8G,CAAA,EANF5G,GAEC4G,EAAAC,CAAA,EACD7G,GAEC4G,EAAAE,CAAA,6XCfO,uBAAAf,EAAA,SAAqC,2KAU1B;AAAA,GAEnB,oBACEpB,EAAI;;;;;;;;;wMALR/E,GAwBKC,EAAAuG,EAAArG,CAAA,EAvBJC,GAAgBoG,EAAAW,CAAA,UAChB/G,GAKGoG,EAAAY,CAAA,UAHFhH,GAEMgH,EAAAC,CAAA,kBAEPjH,GAeGoG,EAAAc,CAAA,YAGJtH,GAEQC,EAAAoF,EAAAlF,CAAA,6EAvBJ4E,EAAI,wIAVD,MAAAmB,EAAWC,SAEN,KAAAtD,CAAY,EAAAgD,EA6BA,MAAA0B,EAAA,IAAArB,EAAS,OAAO,wMCnCxC,MAAesB,GAAA,wnECCL,uBAAArB,EAAA,SAAqC,gPA0B5CnG,GAA4CC,EAAAuG,EAAArG,CAAA,YAC5CH,GAAiDC,EAAAwH,EAAAtH,CAAA,2EAGmB,GAAC,gPAAf,IAAAuH,EAAA3C,KAAY,GAAC4C,GAAA,0EAjBnD;AAAA,GAEjB,mBACE5C,EAAI,6EAccA,EAAS,SAAQ,eAAa,yEAlBzCyC,EAAQ,GAAAI,GAAAC,EAAA,MAAAC,CAAA,uQADnB9H,GAuBIC,EAAA8H,EAAA5H,CAAA,EAtBHC,GAA4B2H,EAAAF,CAAA,UAC5BzH,GAKK2H,EAAA5C,CAAA,UAHJ/E,GAEK+E,EAAAF,CAAA,kBAEN7E,GAcM2H,EAAAC,CAAA,uBAJL5H,GAGG4H,EAAAP,CAAA,EAFFrH,GAAoCqH,EAAAQ,CAAA,iCAAsC7H,GACzEqH,EAAAS,CAAA,YAKJlI,GAEQC,EAAAoF,EAAAlF,CAAA,6EAtBJ4E,EAAI,2EAccA,EAAS,IAA0BA,KAAY,+OAzBzD,KAAAlC,CAAY,EAAAgD,GACZ,UAAAsC,CAAiB,EAAAtC,EAEtB,MAAAK,EAAWC,KAgBCoB,EAAA,IAAArB,EAAS,QAAW,sBAAsB,EAAI,GAYzCkC,EAAA,IAAAlC,EAAS,OAAO,kVCpCjC,SAASmC,GACfvG,EACApB,EACA4H,EAAoC,KACyB,CAC7D,OAAI5H,IAAS,OACL4H,IAAS,KAAO,OAAS,KAE7BxG,IAAU,MAAQwG,IAAS,KACvB,OAEJ5H,IAAS,UAAYA,IAAS,MAC1B4H,IAAS,KAAOxG,EAAQ,IAAMA,EAAQ,IACnCpB,IAAS,SACZ4H,IAAS,KAAO,WAAWxG,CAAK,EAAIA,EACjCpB,IAAS,WAAaA,GAAQ,OACpC4H,IAAS,MACZxG,EAAQ,OAAOA,CAAK,EACbA,IAAU,OAAS,OAAS,SACzBwG,IAAS,MAAQA,IAAS,OAC7BxG,EAEDA,IAAU,OACPpB,IAAS,aACXoB,EAAA,KAAK,UAAUA,CAAK,EACrBA,GACGpB,EAAK,WAAW,WAAW,EAE9B,IAAMoB,EAAQ,IAGlBwG,IAAS,KACLxG,IAAU,GAAK,KAAO,KAAK,MAAMA,CAAK,EACnC,OAAOA,GAAU,SACvBA,IAAU,GACNwG,IAAS,KAAO,OAAS,OAE1BxG,GAEJwG,IAAS,SACZxG,EAAQyG,GAAmBzG,CAAK,GAE7BwG,IAAS,OACZxG,EAAQ0G,GAAqC1G,CAAK,GAE5C2G,GAA+B3G,CAAK,EAC5C,CAEO,SAAS4G,GAAgCC,EAAmB,CAClE,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MAClCA,EAAI,eAAe,KAAK,GAAKA,EAAI,eAAe,MAAM,GAExD,OAAOA,EAAI,MAAS,UACpBA,EAAI,OAAS,MACbA,EAAI,KAAK,QAAU,kBAEZ,SAIV,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MACtC,QAASC,KAAOD,EACf,GAAI,OAAOA,EAAIC,CAAG,GAAM,UACVF,GAAgCC,EAAIC,CAAG,CAAC,EAE7C,SAKJ,QACR,CAEA,SAASL,GAAmBI,EAAe,CACtC,cAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,GACVA,EAAI,MAAM,QAAU,kBAEb,CAAE,KAAMA,EAAI,MAGjB,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACE,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCF,EAAAG,CAAK,EAAIP,GAAmBM,CAAI,EACrC,CACA,EACS,OAAOF,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIL,GAAmBI,EAAIC,CAAG,CAAC,EACtC,EAEKD,EACR,CAEA,SAASH,GAAqCG,EAAe,CACxD,cAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,GACVA,EAAI,MAAM,QAAU,kBAEb,gBAAgBA,EAAI,GAAG,MAG5B,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACE,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCF,EAAAG,CAAK,EAAIN,GAAqCK,CAAI,EACvD,CACA,EACS,OAAOF,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIJ,GAAqCG,EAAIC,CAAG,CAAC,EACxD,EAEKD,EACR,CAEA,SAASF,GAA+BE,EAAkB,CACzD,IAAII,EAAa,KAAK,UAAUJ,EAAK,CAACC,EAAK9G,IACtCA,IAAU,KACN,eAGP,OAAOA,GAAU,UACjBA,EAAM,WAAW,cAAc,GAC/BA,EAAM,SAAS,GAAG,EAEX,WAAWA,CAAK,GAEjBA,CACP,EACD,MAAMkH,EAAQ,oCACDD,IAAW,QAAQC,EAAO,CAACC,EAAO3B,IAAO,eAAeA,CAAE,GAAG,EAC1E,MAAM4B,EAAY,kBACX,OAAAH,EAAW,QAAQG,EAAW,MAAM,CAC5C,0mBChI8E,GAAC,oDAevD,IAAAC,GAAApE,EAAW,GAAAA,EAAG,WAAQ,OAAK,kDAA3BqE,EAAA,GAAAD,QAAApE,EAAW,GAAAA,EAAG,WAAQ,OAAK,KAAAsE,GAAA,EAAAF,CAAA,kCADT,IAAAA,EAAApE,KAAY,KAAI,SAAMA,EAAqB,IAAIA,EAAiB,KAAK,MAAIuE,GAAA,qFAAzEF,EAAA,GAAAD,OAAApE,KAAY,KAAI,KAAAsE,GAAA,EAAAF,CAAA,EAAMpE,EAAqB,IAAIA,EAAiB,KAAK,sIAAK;AAAA,YACzG,4DAOHwE,EAAAlB,GAAgBtD,EAAiB,GAAEA,EAAY,QAAM,IAAI,6JAHpD/E,GAAuBC,EAAAgI,EAAA9H,CAAA,EAAAH,GAI9BC,EAAA+H,EAAA7H,CAAA,kBADEiJ,EAAA,GAAAG,OAAAlB,GAAgBtD,EAAiB,GAAEA,EAAY,QAAM,IAAI,OAAAsE,GAAAG,EAAAD,CAAA,oIALHvJ,GAExDC,EAAAqF,EAAAnF,CAAA,wDAVC4E,EAAgB,KAAK,QAAUA,EAAA,GAC9BA,EAAA,GACA,IAAMA,EAAC,IAAG,KAAG,mBAeyBA,EAAK,YAC9CA,EAAS,4BAbH,OAAAA,OAAqB,SAAQ0E,+CAG9B1E,EAAqB,IAAIA,EAAgB,IAAI,OAAM2E,6JAQ3C,2CAC2B,eAAO,IAAE,eAGlD;AAAA,eACD,8SAzBD1J,GAAgBC,EAAA0J,EAAAxJ,CAAA,YAChBH,GAyBKC,EAAAuG,EAAArG,CAAA,EAxBJC,GAiBGoG,EAAAY,CAAA,EAhBFhH,GAIAgH,EAAAa,CAAA,kBACA7H,GAGAgH,EAAAY,CAAA,0CASD5H,GAKGoG,EAAAc,CAAA,wEArBCvC,EAAgB,KAAK,QAAUA,EAAA,GAC9BA,EAAA,GACA,IAAMA,EAAC,IAAG,KAAG,KAAAsE,GAAAG,EAAAD,CAAA,8IAeyBxE,EAAK,QAAAsE,GAAAO,EAAAC,CAAA,cAC9C9E,EAAS,QAAAsE,GAAAS,EAAAC,CAAA,qGASI,EAAK,yEADtB/J,GAEKC,EAAAuG,EAAArG,CAAA,uIArCIoJ,EAAAxE,KAAiB,OAAM,qBAAgBA,EAAgB,GAAC,QAAU,GAACiF,GAAA,OAIrEjF,EAAgB,yBAArB,OAAI6B,GAAA,2BA8BF7B,EAAU,IAAA4C,GAAA,qGAnCT;AAAA,UACG,eAAyB,YAAU,gBAAwC,GACpF,uKAEiB5C,EAAU,YAP3B/E,GAKIC,EAAAgK,EAAA9J,CAAA,EAJHC,GAEK6J,EAAA9E,CAAA,2DAINnF,GA8BKC,EAAAmF,EAAAjF,CAAA,sGAjCK,CAAA+J,GAAAd,EAAA,IAAAG,OAAAxE,KAAiB,OAAM,KAAAsE,GAAAG,EAAAD,CAAA,EAAgBxE,EAAgB,GAAC,QAAU,2DAIpEA,EAAgB,sBAArB,OAAI6B,GAAA,qHAAJ,8BADc7B,EAAU,IA+BtBA,EAAU,uOA5CH,WAAAoF,CAAmB,EAAAtE,GACnB,iBAAAuE,CAAqB,EAAAvE,GACrB,WAAAwE,CAAe,EAAAxE,GACf,iBAAAyE,CAAkD,EAAAzE,q+BCQ5Dd,EAAS,wCAATA,EAAS,sIADqBA,EAAI,4NAZxB,KAAAwF,CAAY,EAAA1E,EACnBlE,EAAY,gBAEPR,GAAI,CACZ,UAAU,UAAU,UAAUoJ,CAAI,EAClClE,EAAA,EAAA1E,EAAY,SAAS,EACrB,gBACC0E,EAAA,EAAA1E,EAAY,MAAM,GAChB,wiBCmBiB6I,EAAY,yFAGtBA,EAAY,4EAJrBxK,GAEKC,EAAAgF,EAAA9E,CAAA,yBACLH,GAEKC,EAAAkF,EAAAhF,CAAA,EADJC,GAA0B+E,EAAAsF,CAAA,gLAVRC,EAAU,yFAGpBA,EAAU,4EAJnB1K,GAEKC,EAAAgF,EAAA9E,CAAA,yBACLH,GAEKC,EAAAkF,EAAAhF,CAAA,EADJC,GAAwB+E,EAAAsF,CAAA,gLAVNE,EAAU,yFAGpBA,EAAU,4EAJnB3K,GAEKC,EAAAgF,EAAA9E,CAAA,yBACLH,GAEKC,EAAAkF,EAAAhF,CAAA,EADJC,GAAwB+E,EAAAsF,CAAA,uLALrB,OAAA1F,OAAqB,SAAQ,EAOxBA,OAAqB,aAAY,EAOjCA,OAAqB,OAAM,wGAftC/E,GAuBMC,EAAAsK,EAAApK,CAAA,ojBA7BF,IAAAwK,GAAa,4BACbD,GAAa,0BACbF,GAAe,wCAJR,iBAAAF,CAAkD,EAAAzE,ufCQ1D;AAAA,GAEF,oBAAoBd,EAAQ,6EAF7B/E,GAGIC,EAAA2K,EAAAzK,CAAA,UADHC,GAAmCwK,EAAAtF,CAAA,4BAAfP,EAAQ,8CALRwE,EAAA,IAAMxE,EAAQ,gCAFhC;AAAA,GAEF,gGAFD/E,GAGIC,EAAA2K,EAAAzK,CAAA,UADHC,GAAyCwK,EAAAtF,CAAA,kBAArB8D,EAAA,GAAAG,OAAA,IAAMxE,EAAQ,KAAAsE,GAAAG,EAAAD,CAAA,+DAH/BxE,EAAK,GAAA4C,kNALE,aAAAkD,EAA0B,IAAI,EAAAhF,EAC9B,UAAAiF,EAA0B,IAAI,EAAAjF,GAC9B,MAAAkF,CAAc,EAAAlF,8jDCwCUd,EAAgB,mHAAhBA,EAAgB,2JAFhB,SAAAA,KAAW,qGAAXqE,EAAA,OAAA4B,EAAA,SAAAjG,KAAW,w/BAuGE,GAC1C,wDALHwE,EAAAlB,GACItD,EAAa,IACbA,EAAY,SACZ,MAAK,SACC2C,EAAA3C,EAAI,IAAAA,EAAoB,UAAS,GAACkG,GAAA,iBAN8E;AAAA,QACvH,gFACH7B,EAAA,OAAAG,OAAAlB,GACItD,EAAa,IACbA,EAAY,SACZ,MAAK,OAAAsE,GAAAG,EAAAD,CAAA,EACCxE,EAAI,IAAAA,EAAoB,UAAS,sIAPZmG,EAAAnG,KAAW,SAAQ,SAA8C,YAUlG,UACc,UAAc,YACSoG,EAAApG,KAAW,SAAQ,0BAhBnC,KAAAA,OAAgB,sBAKrBA,EAAmB,yBAAxB,OAAI6B,GAAA,8GADP,eAAa,MAAC7B,EAAI,QAAC,OAAK,aAAqB,8CAA4C,aAAI;AAAA,YAC5F,2CAQC;AAAA,EACX,aAAK;AAAA,gBACS,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAI,QAAC,OAAK,aAAqB,YAAU,6GAlBlE/E,EAoBMC,EAAAsK,EAAApK,EAAA,EAnBLC,EAEKmK,EAAAtF,CAAA,sBAEL7E,EAcKmK,EAAApF,CAAA,EAbJ/E,EAYqE+E,EAAAsF,CAAA,4NAhBnDrB,GAAA,UAAAgC,GAAA,KAAArG,OAAgB,0CAIfA,EAAI,KAAO,CAAAmF,GAAAd,GAAA,OAAA8B,OAAAnG,KAAW,SAAQ,KAAAsE,GAAAgC,EAAAH,CAAA,iBACpCnG,EAAmB,sBAAxB,OAAI6B,GAAA,sHAAJ,2BAWe7B,EAAI,KAAO,CAAAmF,GAAAd,GAAA,OAAA+B,OAAApG,KAAW,SAAQ,KAAAsE,GAAAiC,EAAAH,CAAA,wIA3DvBI,EAAAxG,MAAc,IAAG,SACtCA,EAAS,8BAFrB;AAAA,gBACa,MAACA,EAAC,SAAC,kBAAgB,aAAmB;AAAA,cACxC,aAAW,oBAAkB,MAACA,EAAC,SAAC;AAAA,OACvC,kMAIwB,aAAkB,MAACA,EAAQ,QAAC,mBAAwB,2DAAjCA,EAAQ,oEAGzCA,EAAgB,0CAAhBA,EAAgB,2CADpBwE,EAAAxE,KAAW,SAAQ,mCAAtB,IAAE,aAAqB,GAAC,kDADc/E,EAEvCC,EAAAqF,EAAAnF,CAAA,+BADIiJ,EAAA,MAAAG,OAAAxE,KAAW,SAAQ,KAAAsE,GAAAG,EAAAD,CAAA,2CAepBxE,EAAc,WAAIwG,EAAAlD,GACnBtD,EAAa,IACbA,EAAY,SACZ,IAAG,wBALT;AAAA,GACF,0BACwB,IAAE,aAKnB,IAAE,kDANT/E,EAMOC,EAAAqF,EAAAnF,CAAA,wDALE4E,EAAc,SAAAsE,GAAAG,EAAAD,CAAA,EAAIH,EAAA,OAAAmC,OAAAlD,GACnBtD,EAAa,IACbA,EAAY,SACZ,IAAG,OAAAsE,GAAAmC,EAAAD,CAAA,uDAXFxG,EAAc,aAAWA,EAAS,4BAH1C;AAAA,KACC,0BAEsB,WAAS,aAC1B,IAAE,kHAHP/E,EAGKC,EAAAgI,EAAA9H,CAAA,gCACJH,EAEIC,EAAA+H,EAAA7H,CAAA,0BAJE4E,EAAc,SAAAsE,GAAAG,EAAAD,CAAA,kBAAWxE,EAAS,SAAAsE,GAAAmC,EAAAD,CAAA,iHAJnCxG,EAAe,IAAC,SAASA,EAAS,0OANlCmG,GAAAnG,MAAYA,EAAI,6BAXH,YAAAA,MAAS,SAAS,aAIlCA,EAAa,0BAAlB,OAAI6B,GAAA,qBAQM,IAAA6E,EAAA1G,OAAa,MAAI2G,GAAA3G,CAAA,yBACYA,EAAK,GAAA4G,6BAGpC5G,EAAmB,yBAAxB,OAAI6B,GAAA,8GAbC;AAAA,CACV,2CAKE;AAAA,qCACkC,mBAC5B,GAAC,aAAkB,GAAC,eAC2D;AAAA,qCACnD,YAEE,MAAW,2CAsBrC;AAAA;AAAA;AAAA;AAAA,CAIZ,wJA3CG5G,EA6CMC,EAAAsK,EAAApK,CAAA,EA5CLC,EAEKmK,EAAAtF,CAAA,sBACL7E,EAwCKmK,EAAApF,CAAA,EAvCJ/E,EAsCA+E,EAAAsF,CAAA,iEA/B+BrK,EAE7BqK,EAAAnF,CAAA,4JAZgB8D,EAAA,SAAAgC,EAAA,KAAArG,MAAS,qCAIzBA,EAAa,uBAAlB,OAAI6B,GAAA,kHAAJ,WAOQ,CAAAsD,GAAAd,EAAA,QAAA8B,QAAAnG,MAAYA,EAAI,SAAAsE,GAAAgC,EAAAH,CAAA,EACdnG,OAAa,yIAIfA,EAAmB,sBAAxB,OAAI6B,GAAA,kHAAJ,gLA9C4B,eAAa,2EAGhB,WAAS,MAAC7B,EAAQ,QAAC,kBAAgB,2DAAzBA,EAAQ,qDAK7CwE,EAAAxE,EAAA,IACOA,MAAiB,IACjB,SACAsD,GACDtD,EAAqB,IAAGA,EAAiB,IAAGA,EAAa,IACzDA,MAAY,KACZ,4BAPC;AAAA,GACR,iCAQM,GAAC,4BANI/E,EAMLC,EAAAqF,EAAAnF,CAAA,0BARLiJ,EAAA,OAAAG,OAAAxE,EAAA,IACOA,MAAiB,IACjB,KAAEsE,GAAAG,EAAAD,CAAA,kBACFlB,GACDtD,EAAqB,IAAGA,EAAiB,IAAGA,EAAa,IACzDA,MAAY,KACZ,iGAZoC6G,GAAA7G,MAAYA,EAAI,uBAiBxB8G,EAAA9G,KAAW,SAAQ,yBAxBjC,YAAAA,MAAa,SAAS,UAK1BA,EAAa,KAAA0E,GAAA,EAGpBqC,EAAA/G,OAAa,MAAI2E,GAAA3E,CAAA,OAGnBA,EAAmB,yBAAxB,OAAI6B,GAAA,gJARoC,iBAAe,0CAErD,SAAO,eAAoC;AAAA;AAAA,iBAElC,mBAA2B,GAAC,aAAkB,GAAC,eACO;AAAA,iBACtD,2CACT,GAAC,2CAaJ;AAAA,YACO,mBAAuB,IAAE,aAAqB,GAAC,MACtD;AAAA;AAAA,CAEJ,yCAAoC,UAAQ,ySA7BzC5G,EA+BMC,EAAAsK,EAAApK,EAAA,EA9BLC,EAEKmK,EAAAtF,CAAA,sBACL7E,EA0BKmK,EAAApF,CAAA,EAzBJ/E,EAwB4C+E,EAAAsF,CAAA,EAxBvCrK,EAAmCqK,EAAAxC,CAAA,SAAe7H,EAErDqK,EAAAzC,CAAA,+BAES5H,EACTqK,EAAAsB,CAAA,6CACS3L,EACTqK,EAAAuB,CAAA,sEAcI5L,EAAsDqK,EAAAwB,CAAA,8BAGjE7L,EAAoCqK,EAAAyB,CAAA,+CA3Bb9C,GAAA,SAAAgC,GAAA,KAAArG,MAAa,uBAOS,CAAAmF,GAAAd,GAAA,QAAAwC,QAAA7G,MAAYA,EAAI,SAAAsE,GAAA8C,EAAAP,CAAA,EACjD7G,OAAa,4EAGfA,EAAmB,sBAAxB,OAAI6B,GAAA,sHAAJ,QAagC,CAAAsD,GAAAd,GAAA,OAAAyC,OAAA9G,KAAW,SAAQ,KAAAsE,GAAA+C,EAAAP,CAAA,8MAjClD9G,EAAK,qEAKL,OAAAA,OAAqB,SAAQ,EAmCxBA,OAAqB,aAAY,EAiDjCA,OAAqB,OAAM,+HA1FtC/E,EAmHKC,EAAAuG,EAAArG,CAAA,wcA1IO,WAAAkM,CAAsB,EAAAxG,GACtB,iBAAAyG,CAAwB,EAAAzG,GACxB,KAAAhD,CAAY,EAAAgD,GACZ,SAAA0G,CAAuB,EAAA1G,GACvB,oBAAA2G,CAAwB,EAAA3G,GACxB,MAAAkF,CAAc,EAAAlF,GACd,SAAA4G,CAAuB,EAAA5G,GACvB,iBAAAyE,CAAkD,EAAAzE,EAEzD6G,EACAC,EACAC,EAGAC,EAAgBL,EAAoB,KAAMM,GAC7CpE,GAAgCoE,EAAM,aAAa,GAEhDC,GAAmB,QAAS,OAAQ,QAAS,OAAO,EACpDC,EAAuBR,EAAoB,OAC7CM,GAA6BC,EAAgB,SAASD,EAAM,SAAS,6CAgBpDJ,EAAWO,qDAmCXN,EAAOM,qDAkDPL,EAAcK,gpDCnIjB,KAAAC,EAAM,SAAgB,0TAqKhB,YAAAnI,MAAW,SAAS,aAG/BA,EAAW,yBAAhB,OAAI6B,GAAA,4MALR5G,GAaMC,EAAAsK,EAAApK,CAAA,EAZLC,EAEKmK,EAAAtF,CAAA,sBACL7E,EAQKmK,EAAApF,CAAA,uFAVciE,EAAA,KAAAgC,EAAA,KAAArG,MAAW,iCAGtBA,EAAW,sBAAhB,OAAI6B,GAAA,qHAAJ,6KAvBgB,YAAA7B,MAAS,SAAS,IAM7B,IAAA2C,EAAA3C,OAAa,MAAIiF,GAAAjF,CAAA,OACjBA,EAAS,yBAAd,OAAI6B,GAAA,8GAJD;AAAA;AAAA,kCAEuB,mBAA2B,GAAC,MAAC7B,EAAU,QAAC,GAAC,eACa;AAAA,MAClF,yLATF/E,GAkBMC,EAAAsK,EAAApK,CAAA,EAjBLC,EAEKmK,EAAAtF,CAAA,sBACL7E,EAaKmK,EAAApF,CAAA,EAZJ/E,EAWa+E,EAAAsF,CAAA,SATerK,EAC1BqK,EAAAnF,CAAA,kIANgB8D,EAAA,KAAAgC,EAAA,KAAArG,MAAS,qCAK8BA,EAAU,IAC5DA,OAAa,wEACbA,EAAS,sBAAd,OAAI6B,GAAA,qHAAJ,wMA5BgB7B,EAAgB,MAQ3B,IAAA2C,EAAA3C,OAAa,MAAI2E,GAAA3E,CAAA,OACtBA,EAAS,yBAAd,OAAI6B,GAAA,gJANuC,iBAAe,0CAErD;AAAA;AAAA,iBAES,mBAA2B,GAAC,MAAC7B,EAAU,QAAC,GAAC,eACa;AAAA,CACtE,6OAXG/E,GAmBMC,EAAAsK,EAAApK,CAAA,EAlBLC,EAEKmK,EAAAtF,CAAA,sBACL7E,EAcKmK,EAAApF,CAAA,EAbJ/E,EAYc+E,EAAAsF,CAAA,EAZTrK,EAAmCqK,EAAAxC,CAAA,SAAe7H,EAErDqK,EAAAzC,CAAA,SAES5H,EACTqK,EAAAsB,CAAA,gJARgBhH,EAAgB,8BAOMA,EAAU,IAC3CA,OAAa,wEAClBA,EAAS,sBAAd,OAAI6B,GAAA,qHAAJ,4JAoCwC7B,EAAQ,aAA8C,UACrFA,EAAI,aAAG,UACF,UAAc,cACeA,EAAQ,0CAH1C,eAAa,MAACA,EAAU,QAAC,OAAK,aAAU,8CAA4C,aAAK;AAAA,WAC1F,aAAM,GAAC,aAAK;AAAA,gBACP,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAU,QAAC,OAAK,aAAU,YAAU,0DAH1D/E,GAG+DC,EAAAwK,EAAAtK,CAAA,iJAC/DH,GAAKC,EAAAiI,EAAA/H,CAAA,oBAJc4E,EAAU,kBAAOA,EAAQ,SAAAsE,GAAAmC,EAAAD,CAAA,gBACvCxG,EAAI,SAAAsE,GAAA8C,EAAAP,CAAA,YAEa7G,EAAU,kBAAOA,EAAQ,SAAAsE,GAAAiC,EAAAH,CAAA,yEArBvB,aAAkB,MAACpG,EAAQ,QAAC,mBAAwB,0DAAjCA,EAAQ,uEAMvC;AAAA,QACX,gEAFFA,EAAQ,aAEAA,EAAI,aADJA,EAAI,KAAAuE,GAAA,iBAJV;AAAA,sBACc,mBACG;AAAA,KACpB,aAAU,GAAC,sBAEG;AAAA,OACZ,2DALetJ,GAGbC,EAAAqF,EAAAnF,CAAA,6EADH4E,EAAQ,SAAAsE,GAAA8D,EAAAC,CAAA,EACArI,EAAI,2EACJA,EAAI,SAAAsE,GAAAgC,EAAAH,CAAA,yFA1Ba,WAAS,MAACnG,EAAQ,QAAC,kBAAgB,0DAAzBA,EAAQ,8DAK/CA,EAAI,iBAAsCA,EAAQ,4BAHjD;AAAA,QACK,mBACE;AAAA,CACT,aAAM,aAAW,mBAAuB,IAAE,aAAU,GAAC,MAAM;AAAA;AAAA,CAE3D,qFAJO/E,GAIAC,EAAA+H,EAAA7H,CAAA,uBAFUC,EAA0C4H,EAAAC,CAAA,oDAA1DlD,EAAI,SAAAsE,GAAA8D,EAAAC,CAAA,gBAAsCrI,EAAQ,SAAAsE,GAAAgE,EAAAC,CAAA,8FAhB5C,OAAAvI,OAAqB,SAAQ,EAqBxBA,OAAqB,aAAY,EAoBjCA,OAAqB,OAAM,kZA1ClB,qIAFrB/E,GA6DKC,EAAAuG,EAAArG,CAAA,6MAjLO,aAAAoN,CAA0B,EAAA1H,GAC1B,WAAA2H,CAAkB,EAAA3H,GAClB,KAAAhD,CAAY,EAAAgD,GACZ,iBAAAyE,CAAkD,EAAAzE,GAClD,SAAA4G,CAAuB,EAAA5G,EAE9B6G,EACAe,EACAd,EACAe,GAEO,UAAAC,EAAS,IAAA9H,iBAEL+H,GAAQ,QAKd,MADa,YAAM/K,EAAO,0BAA0B,GAClC,WAIvBgL,EACAC,EAAS,GACTC,EAAS,GACTC,EAAW,YAENC,EAAgBC,EAAe5F,EAA0B,CAC3D,MAAAuC,MAAe0C,EAAaW,EAAK,QAAQ,EAAE,QAAQ,GAMnDrK,EAJ0BqK,EAAK,KAAK,OACxCC,GAAC,OAAYA,EAAM,GAAW,EAI9B,IAAK,CAAArB,EAAOhE,IAAK,CACb,GAAA+E,EAAehD,CAAQ,GACpB,MAAAuD,EAAaP,EAAehD,CAAQ,EAAE,WAAW/B,CAAK,MACvDsF,eAGCC,EAAaD,EAAW,eACxBE,EAAcF,EAAW,YAAY,KACvC,GAAA9F,IAAS,KACA,WAAA+F,CAAU,IAAIhG,GACzByE,EACAwB,EACA,IAAI,IAEK,GAAAhG,IAAS,KACL,aAAA+F,CAAU,KAAKhG,GAC5ByE,EACAwB,EACA,IAAI,IAEK,GAAAhG,IAAS,OACL,aAAAD,GACbyE,EACAwB,EACA,MAAM,IAIG,WAAAjG,GAAgByE,EAAiB,OAAWxE,CAAI,MAE5D,OAAQ6F,GAAa,OAAAA,EAAM,GAAW,EACtC,KAAK;AAAA,CAAK,KACRtK,EAAM,CACL,GAAAyE,IAAS,cACFzE,CAAM;AAAA,EACN,GAAAyE,IAAS;EACNzE,CAAM;AAAA,GACT,GAAAyE,IAAS;EACPzE,CAAM;AAAA,EAGhB,OAAAyE,IAAS,KACL,GAED;AAAA,EAGRxD,GAAO,UAEN+I,SADmBD,KACG,oBAClBW,EAAyBZ,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,GAEvBM,EAAyBb,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,GAEvBO,EAA2Bd,EAAU,IAAKO,GAC7CD,EAAgBC,EAAM,MAAM,GAEzBQ,EAAsBf,EAAU,IAClCO,GAASX,EAAaW,EAAK,QAAQ,EAAE,UAAY,EAAE,EAErD7H,EAAA,EAAAyH,EAAYS,EAAa,KAAKL,EAAMpF,KAAK,CACxC,KAAAoF,EACA,SAAUQ,EAAU5F,CAAK,MAE1BzC,EAAA,EAAA0H,EAAYS,EAAa,KAAKN,EAAMpF,KAAK,CACxC,KAAAoF,EACA,SAAUQ,EAAU5F,CAAK,MAE1BzC,EAAA,EAAA2H,EAAcS,EAAe,KAAKP,EAAMpF,KAAK,CAC5C,KAAAoF,EACA,SAAUQ,EAAU5F,CAAK,YAGpBoE,GAAI,MAEVO,EAAmBf,EAAY,SAAS,8CAYtBA,EAAWO,qDAqBXN,EAAOM,qDAoBPS,EAAST,88BC5K7B,MAAe0B,GAAA,wpCCAAC,GAAA,0yBCAAC,GAAA,+yECeC,WACL,yDAFR,IAAAC,EAAA/J,MAAoB,SAAW,QAAU,WAAYqI,EAAArI,KAAiB,OAAM,+BAA5B,MAAI,eAAwB;AAAA,WACrE,0DADPqE,EAAA,GAAA0F,OAAA/J,MAAoB,SAAW,QAAU,SAAMsE,GAAA0F,EAAAD,CAAA,EAAM1F,EAAA,GAAAgE,OAAArI,KAAiB,OAAM,KAAAsE,GAAA8D,EAAAC,CAAA,sGAWrDrI,EAAC,mDAAtB/E,GAA8BC,EAAAqF,EAAAnF,CAAA,wCAGgC4E,EAAU,GACtEA,OACC,KAAI,8DAFuDA,EAAU,GACtEA,OACC,KAAI,KAAAsE,GAAA,EAAAF,CAAA,kCAF8B,IAAAA,EAAApE,KAAY,KAAI,kDAAhBqE,EAAA,GAAAD,OAAApE,KAAY,KAAI,KAAAsE,GAAA,EAAAF,CAAA,0DAMfpE,EAAK,YAC3CA,EAAS,cAXLA,EAAgB,GAAC,OAAS,GAAC0E,GAAA1E,CAAA,kBAIzB,OAAAA,OAAqB,SAAQ2E,oIAKtB,wCACwB,eAAO,IAAE,eAG/C;AAAA,eACD,2JAjBD1J,GAAgBC,EAAA0J,EAAAxJ,CAAA,YAChBH,GAiBKC,EAAAuG,EAAArG,CAAA,EAhBJC,GASGoG,EAAAY,CAAA,yBALFhH,GAIAgH,EAAA9B,CAAA,sBAEDlF,GAKGoG,EAAAc,CAAA,0DAdGvC,EAAgB,GAAC,OAAS,6HAUQA,EAAK,QAAAsE,GAAAgC,EAAAH,CAAA,cAC3CnG,EAAS,QAAAsE,GAAAO,EAAAC,CAAA,wGASI,EAAK,yEADtB7J,GAEKC,EAAAuG,EAAArG,CAAA,wKAhCQ4E,EAAgB,GAAC,OAAS,EAACuE,6BAOjCvE,EAAgB,yBAArB,OAAI6B,GAAA,2BAsBF7B,EAAU,IAAA4C,GAAA,kHA9BT;AAAA,UACG,6KAMQ5C,EAAU,YAV3B/E,GAQIC,EAAAgK,EAAA9J,CAAA,EAPHC,GAEK6J,EAAA9E,CAAA,gCAONnF,GAsBKC,EAAAmF,EAAAjF,CAAA,gLArBG4E,EAAgB,sBAArB,OAAI6B,GAAA,qHAAJ,8BADc7B,EAAU,IAuBtBA,EAAU,oOAvCH,WAAAoF,CAAmB,EAAAtE,GACnB,iBAAAuE,CAAqB,EAAAvE,GACrB,WAAAwE,CAAe,EAAAxE,GACf,iBAAAyE,CAAkD,EAAAzE,onCCJ7CM,0BAAuB,OAAgB,6OAiGlDpB,EAAS,0eAEc,KAAAA,MAAYA,EAAI,mDAYjCA,EAAK,yBAAV,OAAI6B,GAAA,0DAWF,OAAA7B,KAAU,OAAM,mCAmGdA,EAAY,yBAAjB,OAAI6B,GAAA;sVA3HR5G,EAEKC,EAAAgF,EAAA9E,CAAA,wBAELH,EA8JKC,EAAAsF,EAAApF,CAAA,EA7JJC,EAKKmF,EAAAJ,CAAA,SACL/E,EAsJKmF,EAAAL,CAAA,EArJJ9E,EAWK8E,EAAAE,CAAA,4JAtBoBgE,EAAA,IAAA4F,EAAA,KAAAjK,MAAYA,EAAI,0BAYjCA,EAAK,sBAAV,OAAI6B,GAAA,qHAAJ,0JA8GI7B,EAAY,sBAAjB,OAAI6B,GAAA,gHAAJ,OAAIA,EAAAqI,EAAA,OAAArI,GAAA,oEAAJ,OAAIA,GAAA,uMAvGF7B,EAAQ,kHADCA,EAAG,MAAA6C,EAAAsH,EAAA,MAAAC,CAAA,6CAHbvH,EAAAwH,EAAA,QAAAC,EAAA,YAAAtK,OAAqBA,EAAQ,IAAG,eAAiB,iBAAe,0BAFjE/E,EAOIC,EAAAmP,EAAAjP,CAAA,EAFHC,EAAuBgP,EAAAF,CAAA,+DAHvB9F,EAAA,IAAAiG,OAAA,YAAAtK,OAAqBA,EAAQ,IAAG,eAAiB,iBAAe,kHA6C5D,OAAAA,EAAoB,cAAYA,MAAoB,aAAYuK,wEAkBhEvK,EAAQ,IAAA2G,GAAA3G,CAAA,gHAqBR,IAAAwK,EAAAxK,MAAoB,QAAMiF,GAAAjF,CAAA,yEAzBf;AAAA;AAAA;AAAA,OAIhB,eASQ;AAAA;AAAA;AAAA,OAGR,wBAOQ;AAAA;AAAA,OAER,2FAxCD/E,EAWGC,EAAAmH,EAAAjH,CAAA,0CAIHH,EA6CGC,EAAAqH,EAAAnH,CAAA,oMAzCG4E,EAAQ,uHAqBRA,MAAoB,gRArEpBqI,EAAArI,KAAU,OAAM,wHAcR,WAAAA,MAAYA,EAAI,iDAhB7B,wBACuB,kBACpB,GAAC,aAAkB,GAAC,sBAGrB;AAAA,wBAEe,MAACA,EAAgB;AAAA,eAElC;;qVAbD/E,EA4BKC,EAAAuG,EAAArG,CAAA,EA3BJC,EAOGoG,EAAAY,CAAA,SAHoBhH,EAEtBgH,EAAA9B,CAAA,8BAEDlF,EAIGoG,EAAAc,CAAA,kDAUHlH,EAIGoG,EAAAgJ,CAAA,WAEJxP,EAIGC,EAAAwP,EAAAtP,CAAA,gBA3BG,CAAA+J,GAAAd,EAAA,KAAAgE,OAAArI,KAAU,OAAM,KAAAsE,GAAA8D,EAAAC,CAAA,mBAKHrI,EAAgB,sHASrBqE,EAAA,IAAAsG,EAAA,WAAA3K,MAAYA,EAAI,8KAuBvB,yDAEN,0FATqE;AAAA,QAEpE,kBAAyCA,EAAgB,QAAO;AAAA,gBACxD,eAES,MAAI,MACpB,2CACF,sCAHQ6C,EAAA+H,EAAA,OAAAC,EAAA7K,MAAoB,SAAW8K,GAAUC,EAAO,uEAFvD9P,EAAgEC,EAAAqF,EAAAnF,CAAA,kBACxDH,EAGPC,EAAA0P,EAAAxP,CAAA,qCAJwC4E,EAAgB,IAElDqE,EAAA,IAAAwG,OAAA7K,MAAoB,SAAW8K,GAAUC,qGAcnC;AAAA,oCACe,eAOX,WAAS,MACzB,IAAE,EAPIlI,EAAA+H,EAAA,OAAAC,EAAA7K,MAAoB,SACvB8K,GAAUE,GACVhL,MAAoB,aACnB+K,GAAUC,GACVC,EAAS,iFALchQ,EAQ3BC,EAAA0P,EAAAxP,CAAA,0BAPMiJ,EAAA,IAAAwG,OAAA7K,MAAoB,SACvB8K,GAAUE,GACVhL,MAAoB,aACnB+K,GAAUC,GACVC,uOAWLhQ,EAA8BC,EAAAuG,EAAArG,CAAA,WAC9BH,EAAwCC,EAAAwH,EAAAtH,CAAA,sGAY9BuH,EAAA3C,OAAa,MAAI4G,GAAA,2BATU,GAAM,gBAAM;AAAA;AAAA,QAEjD,+CAA2B;AAAA,QAC3B,uCAAgB;AAAA,cACV,sCAAgB,gBAAc,uCAAiB;AAAA,mBAC1C,2CAAqB;AAAA,QAChC,sCAAgB;AAAA,mBACL,sCAAgB,OAAK,uCAAiB;AAAA;AAAA,aAE5C,eAES;AAAA,QACd,eAAoC,WAAS,MAAI,GAClD,wNADUqE,EAAS,8DAZahQ,EAAMC,EAAAgQ,EAAA9P,CAAA,WAAMH,EAAMC,EAAAiQ,EAAA/P,CAAA,WAEjDH,EAA2BC,EAAAkQ,EAAAhQ,CAAA,WAC3BH,EAAgBC,EAAAoH,EAAAlH,CAAA,WACVH,EAAgBC,EAAAmQ,EAAAjQ,CAAA,WAAcH,EAAiBC,EAAAoQ,EAAAlQ,CAAA,WAC1CH,EAAqBC,EAAAqQ,EAAAnQ,CAAA,WAChCH,EAAgBC,EAAAsQ,EAAApQ,CAAA,WACLH,EAAgBC,EAAAuQ,EAAArQ,CAAA,WAAKH,EAAiBC,EAAAwQ,EAAAtQ,CAAA,gCAKjDH,EAAiDC,EAAA0P,EAAAxP,CAAA,0BAHvC4E,OAAa,4OAAI;AAAA,iBAElB,oGAgBD,uBACcA,EAAI,GAAC,gBACzB,IAAMA,EAAU,IAAC,UAChB,oJAUgBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,sBACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,oFAMgBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,mBACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,kMA7BJ/E,EAiCKC,EAAAuG,EAAArG,CAAA,mHA9BkB4E,EAAI,GAAC,gBACzB,IAAMA,EAAU,IAAC,UAChB,sLAUgBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,iCACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,2FAMgBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,8BACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,iQA9BAA,EAAU,IAAC,UAAYA,EAAI,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,GAAA0E,GAAA1E,CAAA,uEAArEA,EAAU,IAAC,UAAYA,EAAI,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,yMA9H1EA,EAAI,IAAA4C,GAAA5C,CAAA,yEAAJA,EAAI,iLAvEF,MAAA+K,GACL,mEACKD,GACL,uEACKG,GACL,+DACKD,GAAqB,sCAsBvB,IAAA5F,GAAa,0BAnCN,aAAAoD,CAA0B,EAAA1H,GAC1B,KAAAhD,CAAY,EAAAgD,GACZ,IAAA6K,CAA+C,EAAA7K,GAC/C,SAAA0G,CAAuB,EAAA1G,GACvB,UAAA8K,CAAwB,EAAA9K,GACxB,SAAA4G,CAAuB,EAAA5G,EAU9BsC,EAAYoF,EAAa,OAC3BlB,GAAeA,EAAW,QAAQ,EAClC,OAEExJ,IAAS,KACZA,EAAO,SAAS,SAAW,KAAO,SAAS,KAAO,SAAS,UAEvDA,EAAK,SAAS,GAAG,IACrBA,GAAQ,SAGE,UAAA8K,EAAS,IAAA9H,EAChByE,EAAqD,eAEnDsG,EAAK,EACT,SAAUjC,EAAM,GAChB,aAAcC,EAAU,GACxB,OAAQC,EAAI,kBAKCjB,GAAQ,QAKd,MADa,YAAM/K,EAAO,MAAM,GACd,sBAGZgO,GAAW,QACV,MAASH,EAAI,eAIzBI,EAKAC,EAEJnD,EAAQ,EAAG,KAAMnN,GAAI,CACpB4F,EAAA,EAAAyK,EAAOrQ,CAAI,IAGZoQ,EAAW,EAAG,KAAMG,GAAW,CAC9B3K,EAAA,EAAA0K,EAAUC,CAAW,IAGhB,MAAA9K,EAAWC,KAEjBrB,GAAO,KACN,SAAS,KAAK,MAAM,SAAW,SAC3B,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,OAGlC,SAAS,KAAK,MAAM,SAAW,0CAwBV,MAAAyC,EAAA0J,GAAA5K,EAAA,EAAAiE,EAAmB2G,CAAQ,EA6E5B7I,EAAA,IAAAlC,EAAS,QAAW,sBAAsB,EAAI,szCChL7DnB,EAAY,GAACA,EAAS,GAACA,EAAS,GAAC,OAAS,CAAC,EAAE,QAAQ,EACtD,SAAQ,kCADT,GAAC,yDADH/E,GAGAC,EAAAqF,EAAAnF,CAAA,sCAFI4E,EAAY,GAACA,EAAS,GAACA,EAAS,GAAC,OAAS,CAAC,EAAE,QAAQ,EACtD,SAAQ,KAAAsE,GAAAG,EAAAD,CAAA,oDALT2B,EAAAnG,KAAU,OAAM,WAEdA,EAAS,GAAC,OAAS,GAAC4C,GAAA5C,CAAA,kHAHF,GACrB,eAAkB,GACpB,8NALD/E,GAA4CC,EAAAuG,EAAArG,CAAA,YAC5CH,GAAgDC,EAAAmH,EAAAjH,CAAA,YAChDH,GAUGC,EAAAqH,EAAAnH,CAAA,EATFC,GAEMkH,EAAAhC,CAAA,yDADH8D,EAAA,GAAA8B,OAAAnG,KAAU,OAAM,KAAAsE,GAAAgC,EAAAH,CAAA,EAEdnG,EAAS,GAAC,OAAS,8SAR3B/E,GAgBKC,EAAAuG,EAAArG,CAAA,qMApBO,UAAAwN,EAAS,IAAA9H,GACT,aAAA0H,CAA0B,EAAA1H,4XCDzB,MAAAqL,GAAYC,GAAIC,EAAM,8gBCGrBC,sBAAmB,OAAyB,6WAqDrDtM,EAAW,6EAPTA,EAAU,gOAOZA,EAAW,gSAPTA,EAAU,qUAOZA,EAAW,4bAnDJ,KAAAlC,CAAY,EAAAgD,GACZ,UAAAyL,CAAqC,EAAAzL,GACrC,OAAA5F,CAAmB,EAAA4F,GACnB,WAAA0L,CAAqB,EAAA1L,GACrB,SAAA2L,CAAmC,EAAA3L,GACnC,MAAA/D,CAAU,EAAA+D,GACV,OAAA4L,CAAc,EAAA5L,GACd,QAAA6L,CAAe,EAAA7L,GACf,aAAA8L,CAAsB,EAAA9L,GACtB,IAAA+L,CAAW,EAAA/L,QAEhBgM,EAAC,CAAIpP,EAAYgF,EAAWqK,IAAM,IACnC,YAAY,eAAiB,OAAM,CAAI,GAAArP,EAAI,KAAMgF,EAAG,MAAOqK,CAAC,IAExD,SAAAC,EACRT,EAAyC,QAErB,IAAO,MAAMA,GAChC,UAAUU,EAASC,EAA2B,OAEvCT,EAAQ,IAAOQ,EAAO,GAAIC,CAAI,EAC9BC,EAAQ,OAAO,KAAKV,EAAS,GAAG,KAAK,EAElC,SAAAW,EAAOD,EAAa,iBACXE,EAAa,CACvB,MAAAC,EAAKR,EAAED,EAAKM,EAAOE,CAAQ,EACjCnS,EAAO,cAAcoS,CAAE,GAGzB,OAAAH,EAAM,QAASJ,GAAC,CACfT,GAAkB,KAAI,IAAOiB,GAAKd,EAAUM,EAAGK,EAAOL,CAAC,MAGjDN,WAOJe,EAAaR,EAAKT,CAAS,4CAKtBE,EAAQvE,gvXCpDV,CAAAnI,WAASqB,yBAAuB,WAAAqM,EAAA,SAA0B,0HAuF3DC,EAAAC,GAAA3N,KAAK,QAAQ,EAAW,MAAA4N,EAAA5N,SAAM,mBAAnC,OAAI6B,GAAA,2LAAC6L,EAAAC,GAAA3N,KAAK,QAAQ,sFAAlB,OAAI6B,GAAA,gLAEE7B,EAAK,IACA,UAAAA,MAAM,sBAEb,GAAAA,MAAM,4OAHJA,EAAK,KACAqE,EAAA,IAAAwJ,EAAA,UAAA7N,MAAM,gCAEbqE,EAAA,IAAAwJ,EAAA,GAAA7N,MAAM,gOANR2C,EAAA3C,KAAK,UAAYA,EAAK,YAAS,QAAM4C,GAAA5C,CAAA,wEAArCA,KAAK,UAAYA,EAAK,YAAS,yNAb/B,KAAAA,KAAK,EAAE,EACD,WAAAA,KAAK,SAAS,GAGf,oBAAaA,EAAK,UAASA,EAAK,SAAM,SAClC,aAAAA,KAAK,EAAE,kBACN,iBAAkBA,EAAK,UAASA,EAAI,GAAC,MAAM,cAAY,kBAElEA,KAAK,4CAGDA,EAAY,2IATL,OAAAA,KAAK,WAAQ,SAAb8N,EAAA,SAAA9N,KAAK,UACRA,EAAI,GAAC,MAAM,QAAK,iBAAhBA,EAAI,GAAC,MAAM,mKAHlBqE,EAAA,QAAArE,KAAK,EAAE,EACDqE,EAAA,cAAArE,KAAK,SAAS,QAGf,oBAAaA,EAAK,UAASA,EAAK,SAAM,SAClC,aAAAA,KAAK,EAAE,uBACN,iBAAkBA,EAAK,UAASA,EAAI,GAAC,MAAM,cAAY,uBAElEqE,EAAA,GAAA0J,GAAA/N,KAAK,KAAK,wDAGNA,EAAY,8DATLgO,EAAA,SAAAhO,KAAK,8CACRA,EAAI,GAAC,MAAM,2IAtEZ,KAAAlC,CAAY,EAAAgD,GAEZ,KAAAzE,CAAmB,EAAAyE,EACnB,QAAAmN,EAAwB,IAAI,EAAAnN,GAC5B,OAAA5F,CAAmB,EAAA4F,GACnB,WAAA0L,CAAqB,EAAA1L,GACrB,QAAAjD,CAAe,EAAAiD,GACf,WAAA/C,CAAmB,EAAA+C,GACnB,cAAA9C,CAA4B,EAAA8C,GAC5B,OAAA3C,CAAc,EAAA2C,EAEnB,MAAAK,EAAWC,SACb8M,EAAiB,GAErBnO,GAAO,KACNoB,EAAS,QAAS9E,EAAK,EAAE,EAEd,UAAA8R,KAASD,EACnB/M,EAAS,QAASgN,EAAM,EAAE,aAI1BhN,EAAS,UAAW9E,EAAK,EAAE,EAEhB,UAAA8R,KAASD,EACnB/M,EAAS,QAASgN,EAAM,EAAE,KAe7BV,GAAW,YAAaQ,CAAM,gFA6BfG,EAAA,aAAA/R,EAAK,SAAQU,CAAA,IAAbV,EAAK,SAAQU,+CAChBV,EAAK,MAAM,MAAKU,CAAA,IAAhBV,EAAK,MAAM,MAAKU,2WAxC3BuE,IAAEjF,EAAK,SACPA,EAAK,UACLA,EAAK,SAAS,OAAQ0Q,GAAC,CAChB,MAAAsB,EAAahS,EAAK,OAAS,uBAC5BgS,GACJH,EAAkB,KAAKnB,CAAC,EAElBsB,qBAMJhS,EAAK,OAAS,SACbA,EAAK,UAAU,MAAOiS,GAAC,CAAMA,EAAE,MAAM,OAAO,EAC/ChN,EAAA,EAAAjF,EAAK,MAAM,QAAU,GAAKA,CAAA,EAE1BiF,EAAA,EAAAjF,EAAK,MAAM,QAAU,GAAIA,CAAA,mBAK3BiF,EAAA,EAAEiN,EAAY,IAAO9Q,GACrBpB,EAAK,GACLnB,EACAsR,EACA3O,EACAC,EACAC,EACAC,EACAmO,GACAhO,EACAqQ,EAAc,omCCrEC,sBAAApN,IAAuB,OAAgB,+EAoBjDpB,EAAQ,wLAARA,EAAQ,8RAhBH,SAAAyO,CAAa,EAAA3N,GACb,KAAAhD,CAAS,EAAAgD,GACT,OAAA5F,CAAW,EAAA4F,GACX,WAAA0L,CAAe,EAAA1L,GACf,QAAAjD,CAAY,EAAAiD,GACZ,WAAA/C,CAAmB,EAAA+C,EACnB,eAAA9C,EAA+B,IAAI,EAAA8C,GACnC,OAAA3C,CAAc,EAAA2C,EAEnB,MAAAK,EAAWC,KACjB,OAAArB,GAAO,KACNoB,EAAS,OAAO,qmCChBlB,MAAeuN,GAAA,0yCC+BR,SAASC,IAAkD,CAC3D,MAAAC,EAAQC,GAAkC,EAAE,EAE5CC,EAAsC,GACtCC,EAAuC,GACvCC,MAAsB,IACtBC,MAAqB,IAErBC,MAAuB,IACvBC,EAAqD,GAE3D,SAASC,EAAO,CACf,SAAArJ,EACA,OAAAsJ,EACA,MAAAC,EAAQ,GACR,KAAAC,EACA,SAAAC,EAAW,KACX,IAAAC,EAAM,KACN,QAAAjU,EAAU,KACV,SAAAkU,CAAA,EAUQ,CACF,MAAAC,EAAUZ,EAAWhJ,CAAQ,EAC7B6J,EAASd,EAAU/I,CAAQ,EAC3B8J,EAAcV,EAAUpJ,CAAQ,EAEhC+J,EAAoBH,EAAQ,IAAKjS,GAAO,CACzC,IAAAqS,EAEJ,MAAMC,EAAgBhB,EAAgB,IAAItR,CAAE,GAAK,EAG7C,GAAAmS,IAAgB,WAAaR,IAAW,UAAW,CACtD,IAAIY,EAAYD,EAAgB,EAEhChB,EAAgB,IAAItR,EAAIuS,EAAY,EAAI,EAAIA,CAAS,EAExCF,EAAAE,EAAY,EAAI,UAAYZ,CAG/B,MAAAQ,IAAgB,WAAaR,IAAW,UACrCU,EAAA,UAGHF,IAAgB,WAAaR,IAAW,WACrCU,EAAA,UACGf,EAAA,IAAItR,EAAIsS,EAAgB,CAAC,GAE5BD,EAAAV,EAGP,OACN,GAAA3R,EACA,eAAgB8R,EAChB,WAAYD,EACZ,IAAAE,EACA,OAAQM,EACR,QAAAvU,EACA,SAAAkU,CAAA,CACD,CACA,EAEME,EAAA,QAASlS,GAAO,CACtB,MAAMsS,EAAgBf,EAAe,IAAIvR,CAAE,GAAK,EAG5C,GAAAmS,IAAgB,WAAaR,IAAW,UAAW,CACtD,IAAIY,EAAYD,EAAgB,EAChCf,EAAe,IAAIvR,EAAIuS,EAAY,EAAI,EAAIA,CAAS,EACnCf,EAAA,IAAIxR,EAAI2R,CAAM,CACrB,MAAAQ,IAAgB,WAAaR,IAAW,WACnCJ,EAAA,IAAIvR,EAAIsS,EAAgB,CAAC,EACvBd,EAAA,IAAIxR,EAAI2R,CAAM,GAE/BH,EAAiB,OAAOxR,CAAE,CAC3B,CACA,EAEKkR,EAAA,OAAQe,IACKG,EAAA,QACjB,CAAC,CACA,GAAApS,EACA,eAAAwS,EACA,WAAAC,EACA,IAAAV,EACA,OAAAJ,EACA,QAAA7T,EACA,SAAAkU,CAAA,IACK,CACLC,EAAQjS,CAAE,EAAI,CACb,MAAA4R,EACA,WAAAa,EACA,eAAAD,EACA,IAAKT,EACL,QAASjU,EACT,SAAAkU,EACA,OAAAL,EACA,SAAAtJ,CAAA,CAEF,GAGM4J,EACP,EACDR,EAAUpJ,CAAQ,EAAIsJ,CACvB,CAES,SAAAe,EAASrM,EAAe6L,EAAkBD,EAAyB,CAC3Eb,EAAU/K,CAAK,EAAI6L,EACnBb,EAAWhL,CAAK,EAAI4L,CACrB,CAEO,OACN,OAAAP,EACA,SAAAgB,EACA,UAAWxB,EAAM,UACjB,kBAAkB/M,EAAW,CAC5B,OAAOsN,EAAUtN,CAAC,CACnB,EACA,sBAAuB,CACf,OAAAqN,CACR,EAEF,CC/IA,IAAImB,GAAyC,GAMtC,SAASC,IAwBd,CACG,IAAAC,EAEAC,EAAkC3B,GAAS,EAAE,EAC7C4B,EAAyB,GACzBb,EACAD,EACAe,EACAC,EACAC,EACHjC,KACD,MAAMkC,EAAwChC,KAC9C,IAAIiC,EAA+B,GAC/BnF,EACAoF,EAAuD,GACvDC,EAEJ,SAASC,EAAc,CACtB,IAAKC,EACL,WAAAC,EACA,OAAAC,EACA,aAAA5I,EACA,KAAA1K,EACA,QAAAuT,CAAA,EAUQ,CACF1F,EAAAuF,EACNI,EAAmBR,CAAW,EAEhBA,EAAAK,EACdvB,MAAa,IACbD,MAAc,IACdU,GAAkB,GAClBK,MAAsB,IACtBH,MAAqB,IAErBI,EAAe,GAEHK,EAAA,CACX,GAAII,EAAO,GACX,KAAM,SACN,MAAO,CAAE,YAAa,GAAO,MAAOC,EAAQ,YAAc,EAAI,IAAK,EACnE,UAAW,GACX,SAAU,KACV,UAAW,KACX,mBAAoB,GACpB,IAAK,MAGNF,EAAW,KAAKH,CAAS,EAEZxI,EAAA,QAAS+I,GAAQ,CAC7BX,EAAe,SAASW,EAAI,GAAIA,EAAI,OAAQA,EAAI,OAAO,EACvDA,EAAI,YAAcC,GACjBD,EAAI,GACJ,CAAC,CAACA,EAAI,WACNA,EAAI,OAAO,OACXA,EAAI,QAAQ,QAEbE,GAAmBF,EAAI,QAASA,EAAI,GAAId,CAAW,EAChCiB,GAAAH,EAAK3B,EAAQD,CAAO,EACvC,EAEDa,EAAW,IAAIC,CAAW,EAERC,EAAAiB,GAAuBR,EAAYrT,CAAI,EAEzD6S,EAAeQ,EAAW,OACzB,CAACS,EAAKtD,KACDsD,EAAAtD,EAAE,EAAE,EAAIA,EACLsD,GAER,CAAC,GAGFC,EAAYT,EAAQtT,CAAI,EAAE,KAAK,IAAM,CACpC+S,EAAa,IAAIG,CAAS,EAC1B,CACF,CAKA,SAASc,EAAgB,CACxB,UAAAC,EACA,WAAAZ,EACA,OAAAC,EACA,KAAAtT,EACA,aAAA0K,CAAA,EAOQ,CACemJ,GAAuBR,EAAYrT,CAAI,EAC7C,QAAQ,CAACiP,EAAGiF,IAAM,CAClBtB,EAAA,IAAIsB,EAAGjF,CAAC,EACxB,EAED0D,EAAc,GAEDjI,EAAA,QAAS+I,GAAQ,CAC7BX,EAAe,SAASW,EAAI,GAAIA,EAAI,OAAQA,EAAI,OAAO,EACvDA,EAAI,YAAcC,GACjBD,EAAI,GACJ,CAAC,CAACA,EAAI,WACNA,EAAI,OAAO,OACXA,EAAI,QAAQ,QAEbE,GAAmBF,EAAI,QAASA,EAAI,GAAId,CAAW,EAChCiB,GAAAH,EAAK3B,EAAQD,CAAO,EACvC,EAEDa,EAAW,IAAIC,CAAW,EAEtB,IAAAwB,EAAkBtB,EAAaS,EAAO,EAAE,EACxCc,EAAwC,GACtC,MAAAC,EAA2B5F,GAAmC,CACnE2F,EAAqB,KAAK3F,CAAS,EAC/BA,EAAU,UACHA,EAAA,SAAS,QAAS4B,GAAU,CACrCgE,EAAwBhE,CAAK,EAC7B,CACF,EAEDgE,EAAwBF,CAAe,EACvCX,EAAmBY,CAAoB,EAEhC,eAAQvB,CAAY,EAAE,QAAQ,CAAC,CAACjT,EAAI6O,CAAS,IAAM,CACrD,IAAAM,EAAM,OAAOnP,CAAE,EACf6O,EAAU,cAAgBwF,IAC7B,OAAOpB,EAAa9D,CAAG,EACnB0D,EAAe,IAAI1D,CAAG,GACzB0D,EAAe,OAAO1D,CAAG,EAE3B,CACA,EAEUsE,EAAA,QAAS7C,GAAM,CACZqC,EAAArC,EAAE,EAAE,EAAIA,EACNiC,EAAA,IAAIjC,EAAE,GAAIA,CAAC,EAC1B,EACG2D,EAAgB,SACHA,EAAA,OAAO,SACtBA,EAAgB,OAAO,SAAU,QAAQA,CAAe,CACzD,EAAItB,EAAaS,EAAO,EAAE,GAG3BS,EAAYT,EAAQtT,EAAMmU,EAAgB,MAAM,EAAE,KAAK,IAAM,CAC5DpB,EAAa,IAAIG,CAAS,EAC1B,CACF,CAEe,eAAAa,EACdxV,EACAyB,EACAmQ,EACyB,CACnB,MAAAxB,EAAWkE,EAAatU,EAAK,EAAE,EAE5B,OAAAoQ,EAAA,WAAa,MAAMiE,EAAgB,IAC3CjE,EAAS,oBAAsBA,EAAS,IACpC,YACLA,EAAS,OAASwB,EAEdxB,EAAS,OAAS,YACrBA,EAAS,MAAM,cAAgB2F,GAC9B3F,EAAS,KACTA,EAAS,mBACT3O,EACAgT,EACArE,EAAS,MAAM,UACd,sBAGCgE,EAAYhE,EAAS,EAAE,IAC1BA,EAAS,MAAM,gBAAkB,OAAO,KAAKgE,EAAYhE,EAAS,EAAE,CAAC,GAGtEA,EAAS,MAAM,YAAc4F,GAC5B5F,EAAS,GACTA,EAAS,MAAM,YACfA,EAAS,MAAM,MACfmD,EACAD,CAAA,EAGDlD,EAAS,MAAM,OAAS6F,GACvB7F,EAAS,GACTA,EAAS,MAAM,WACfd,CAAA,EAIAc,EAAS,KAAO,MAChBsE,EAAuBtE,EAAS,GAAG,IAAM,SAEzCA,EAAS,MAAM,MAAQsE,EAAuBtE,EAAS,GAAG,GAG5C8D,EAAA,IAAI9D,EAAS,GAAIA,CAAQ,EAEpCpQ,EAAK,WACCoQ,EAAA,SAAW,MAAM,QAAQ,IACjCpQ,EAAK,SAAS,IAAK0Q,GAAM8E,EAAY9E,EAAGjP,EAAM2O,CAAQ,CAAC,IAIlDA,CACR,CAEA,IAAI8F,EAAmB,GACnBC,EAAyB3D,GAAS,EAAK,EAE3C,SAASyC,EAAmBH,EAAmC,CACnDA,EAAA,QAAS7C,GAAM,CACrBA,EAAE,KAAO,OACZyC,EAAuBzC,EAAE,GAAG,EAAIA,EAAE,MAAM,MACzC,CACA,CACF,CAEA,SAASmE,GAAc,CACT5B,EAAA,OAAQO,GAAW,CAC/B,QAASvP,EAAI,EAAGA,EAAIwO,GAAgB,OAAQxO,IAC3C,QAAS6Q,EAAI,EAAGA,EAAIrC,GAAgBxO,CAAC,EAAE,OAAQ6Q,IAAK,CACnD,MAAMtD,EAASiB,GAAgBxO,CAAC,EAAE6Q,CAAC,EACnC,GAAI,CAACtD,EAAQ,SACP,MAAA3C,EAAWkE,EAAavB,EAAO,EAAE,EACvC,GAAI,CAAC3C,EAAU,SACX,IAAAkG,EACAvD,EAAO,iBAAiB,IAAiBuD,EAAA,IAAI,IAAIvD,EAAO,KAAK,EACxDA,EAAO,iBAAiB,IACpBuD,EAAA,IAAI,IAAIvD,EAAO,KAAK,EACxB,MAAM,QAAQA,EAAO,KAAK,EAAeuD,EAAA,CAAC,GAAGvD,EAAO,KAAK,EACzDA,EAAO,QAAU,KAAkBuD,EAAA,KACnC,OAAOvD,EAAO,OAAU,SACpBuD,EAAA,CAAE,GAAGvD,EAAO,OACpBuD,EAAYvD,EAAO,MACf3C,EAAA,MAAM2C,EAAO,IAAI,EAAIuD,CAC/B,CAEM,OAAAvB,CAAA,CACP,EAEDf,GAAkB,GACCkC,EAAA,GACnBC,EAAuB,IAAI,EAAK,CACjC,CAEA,SAASI,EAAaC,EAAgD,CAChEA,IACLxC,GAAgB,KAAKwC,CAAO,EAEvBN,IACeA,EAAA,GACnBC,EAAuB,IAAI,EAAI,EAC/B,sBAAsBC,CAAK,GAE7B,CAEA,SAASK,EAASpV,EAAgC,CAC3C,MAAAqV,EAAOxC,EAAe,IAAI7S,CAAE,EAClC,OAAKqV,EAGDA,EAAK,SAAS,UACVA,EAAK,SAAS,YAEfA,EAAK,MAAM,MALV,IAMT,CAEO,OACN,OAAQlC,EACR,QAASL,EACT,aAAAoC,EACA,SAAAE,EACA,eAAAlC,EACA,kBAAmB4B,EACnB,cAAe,IAAItF,IAClB,sBAAsB,IAAM+D,EAAc,GAAG/D,CAAI,CAAC,EACnD,gBAAA4E,CAAA,CAEF,CAGO,MAAMkB,GAE2B,OAAO,eAC9C,gBAAkB,CAAC,CACpB,EAAE,YAUK,SAASxB,GACfyB,EACAC,EACAC,EACAC,EACsD,CACtD,GAAI,CAACH,EAAe,YAEpB,MAAMjG,EAAOkG,EAAaC,IAAiB,EAAIC,IAAkB,EAC7D,IACH,OAAO,IAAIJ,GACV,YACA,yBAAyBC,CAAM;AAAA;AAAA,YAEtBjG,CAAI,0DAENvQ,EAAG,CACX,eAAQ,MAAM,mCAAmC,EACjD,QAAQ,MAAMA,CAAC,EACR,IACR,CACD,CAUgB,SAAAgV,GACf4B,EACAC,EACA9C,EACY,CACZ,OAAA6C,EAAQ,QAAQ,CAAC,CAAC3V,EAAI6V,CAAO,IAAM,CAC7B/C,EAAW9S,CAAE,IACN8S,EAAA9S,CAAE,EAAI,IAGjB8S,EAAW9S,CAAE,IAAI6V,CAAO,GACxB,CAAC/C,EAAW9S,CAAE,IAAI6V,CAAO,EAAE,SAASD,CAAK,EAEzC9C,EAAW9S,CAAE,EAAE6V,CAAO,EAAE,KAAKD,CAAK,EAElC9C,EAAW9S,CAAE,EAAE6V,CAAO,EAAI,CAACD,CAAK,CACjC,CACA,EAEM9C,CACR,CASgB,SAAAkB,GACfH,EACA3B,EACAD,EAC6B,CAC7B,OAAA4B,EAAI,OAAO,QAASiC,GAAU5D,EAAO,IAAI4D,CAAK,CAAC,EAC/CjC,EAAI,QAAQ,QAASkC,GAAW9D,EAAQ,IAAI8D,CAAM,CAAC,EAC5C,CAAC7D,EAAQD,CAAO,CACxB,CAOA,SAAS+D,GAAqB3W,EAAqB,CAEhD,aAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,GAC1CA,IAAU,IACVA,IAAU,GACV,CAACA,CAEH,CAWO,SAASsV,GACf3U,EACAiW,EACA5W,EACA6S,EACAD,EACU,CACV,OAAIgE,IAAqB,GACjB,GACGA,IAAqB,GACxB,GAEP,GAAA/D,EAAO,IAAIlS,CAAE,GACZ,CAACiS,EAAQ,IAAIjS,CAAE,GAAKgW,GAAqB3W,CAAK,EAMjD,CAWgB,SAAAuV,GACf5U,EACAkW,EACAjI,EACkB,CAClB,OAAKiI,EAGEA,EAAW,OAAO,CAAChC,EAAKiC,KAC1BjC,EAAAiC,CAAE,EAAI,SAAU3G,KACfA,EAAK,SAAW,IACnBA,EAAOA,EAAK,CAAC,GAEC,MAAMvB,EAAI,iBAAiBjO,EAAImW,EAAI3G,CAAI,GAGhD0E,GACL,CAAqB,GAXhB,EAYT,CAWO,SAASQ,GACfzW,EACAmY,EACAhW,EACAqT,EACA4C,EAKC,CACG,IAAAC,MACC,IACDrY,IAAS,WAAaoY,GACxBA,EAAgC,QAAStV,GAAiB,CACtD,GAAAuV,EAAsB,IAAIvV,CAAI,EACjC,OAEGwV,MAEJ,MAAMC,EAAqB/C,EAAW,KAAM,GAAM,EAAE,OAAS1S,CAAI,EAC7DyV,IACHD,EAAKzF,GAAe,CACnB,QAAS1Q,EACT,KAAAW,EACA,GAAIyV,EAAmB,mBACvB,QAAS,UACT,EACqBF,EAAA,IAAIvV,EAAMwV,EAAG,SAAS,EAC7C,CACA,EAGF,MAAMA,EAAKzF,GAAe,CACzB,QAAS1Q,EACT,KAAMnC,EACN,GAAImY,EACJ,QAAS,YACT,EAEM,OACN,UAAWG,EAAG,UACd,KAAMA,EAAG,KACT,mBACCD,EAAsB,KAAO,EAAIA,EAAwB,OAE5D,CAQgB,SAAArC,GACfR,EACArT,EAC+C,CAC3C,IAAA4S,MAAoE,IAE7D,OAAAS,EAAA,QAAS7C,GAAM,CACnB,MAAE,UAAA/B,EAAW,mBAAAwH,CAAA,EAAuB3B,GACzC9D,EAAE,KACFA,EAAE,mBACFxQ,EACAqT,CAAA,EAKD,GAFAT,EAAgB,IAAIpC,EAAE,oBAAsBA,EAAE,KAAM/B,CAAS,EAEzDwH,EACH,SAAW,CAACtV,EAAM0V,CAAiB,IAAKJ,EACvBrD,EAAA,IAAIjS,EAAM0V,CAAiB,CAE7C,CACA,EAEMzD,CACR,yZC5kBU,MAAAvI,EAAA,SAAoB,0DAqlBpBnI,EAAK,4EAQDA,EAAQ,sFAOHA,EAAG,IAAC,OAAO,qBAClBA,EAAG,qBAJDA,EAAY,8FAJZA,EAAQ,uKAOHA,EAAG,IAAC,OAAO,oCAClBA,EAAG,2HAyBVwE,EAAAxE,MAAG,0BAA0B,iBAlB1BA,EAAQ,IAAAiF,GAAAjF,CAAA,4FAmBF0O,EAAI,GAAA7L,GAAAC,EAAA,MAAAC,CAAA,EAAOF,GAAAC,EAAA,MAAAsR,EAAApU,MAAG,aAAa,sMApBvC/E,GAsBQC,EAAAmZ,EAAAjZ,CAAA,yBATPC,GAQGgZ,EAAAzJ,CAAA,kBADFvP,GAAyCuP,EAAA9H,CAAA,UAnBrC9C,EAAQ,0DAkBXqE,EAAA,WAAAG,OAAAxE,MAAG,0BAA0B,OAAAsE,GAAAG,EAAAD,CAAA,EACTH,EAAA,WAAA+P,OAAApU,MAAG,aAAa,iEAZnC+J,EAAA/J,MAAG,oBAAoB,4HACdyC,EAAQ,GAAAI,GAAAC,EAAA,MAAAC,CAAA,EAAOF,GAAAC,EAAA,MAAAsR,EAAApU,MAAG,aAAa,iHAP1C/E,GAQQC,EAAAoF,EAAAlF,CAAA,kBADPC,GAA6CiF,EAAAwC,CAAA,YAE9C7H,GAAWC,EAAAuG,EAAArG,CAAA,0CAHTiJ,EAAA,WAAA0F,OAAA/J,MAAG,oBAAoB,OAAAsE,GAAA0F,EAAAD,CAAA,EACC1F,EAAA,WAAA+P,OAAApU,MAAG,aAAa,kQAqB7C/E,GAQKC,EAAAuG,EAAArG,CAAA,wTAgBS4E,EAAQ,uTAZtB/E,GA0BKC,EAAAmF,EAAAjF,CAAA,EAtBJC,GAKCgF,EAAAH,CAAA,UACD7E,GAeKgF,EAAAD,CAAA,gGAbQJ,EAAQ,2WAkBMA,EAAkB,4OA/FzCA,EAAkB,IAAA2G,GAAA3G,CAAA,IAOjBA,EAAQ,KAAIA,EAAG,IAAC,QAAM4G,GAAA5G,CAAA,IAevBA,EAAW,IAAAuE,GAAAvE,CAAA,IA2BZA,EAAoB,KAAA0E,GAAA1E,CAAA,EAepBsU,EAAAtU,OAAoBA,EAAQ,KAAA2E,GAAA3E,CAAA,IA8B5BA,EAAQ,KAAA4C,GAAA5C,CAAA,oMAxF0BA,EAAQ,GAAG,IAAM,MAAM,wDAD1BA,EAAQ,GAAG,OAAS,MAAM,uDAA9D/E,GA0CKC,EAAAkF,EAAAhF,CAAA,EAzCJC,GAcK+E,EAAAF,CAAA,iIApBAF,EAAkB,6DAOjBA,EAAQ,KAAIA,EAAG,IAAC,6IADgBA,EAAQ,GAAG,IAAM,MAAM,EAgBxDA,EAAW,yFAjBmBA,EAAQ,GAAG,OAAS,MAAM,EA4CzDA,EAAoB,uHAepBA,OAAoBA,EAAQ,uHA8B5BA,EAAQ,oTArgBN,MAAAuU,GAAmB,aAKnBC,GAAgC,GAChCC,GAAmC,GA2ZhC,SAAAC,GAAcnY,EAAY,CAC3B,iBAAYA,yDAtjBpBoY,SAEW,KAAA7W,CAAY,EAAAgD,GACZ,WAAAqQ,CAA2B,EAAArQ,GAC3B,OAAAsQ,CAAkB,EAAAtQ,GAClB,aAAA0H,CAA0B,EAAA1H,EAC1B,OAAA8T,EAAQ,QAAQ,EAAA9T,GAChB,OAAA5F,CAAmB,EAAA4F,GACnB,WAAA/C,CAAmB,EAAA+C,EACnB,UAAA+T,EAAW,EAAI,EAAA/T,EACf,aAAAgU,EAAc,EAAI,EAAAhU,EAClB,oBAAAiU,EAAqB,EAAK,EAAAjU,GAC1B,SAAAkU,CAAiB,EAAAlU,GACjB,WAAA0L,CAAqB,EAAA1L,GACrB,IAAA6K,CAA+C,EAAA7K,GAC/C,SAAA0G,CAAuB,EAAA1G,GACvB,QAAAjD,CAAe,EAAAiD,GACf,GAAAmU,CAAiB,EAAAnU,EACjB,aAAAoU,EAAc,EAAK,EAAApU,GACnB,MAAAqU,CAAc,EAAArU,GACd,SAAA4G,CAAuB,EAAA5G,EAGjC,aAAQsU,EACR,QAAA/B,EACA,aAAAT,EACA,SAAAE,EACA,eAAAlC,EACA,kBAAAyE,EACA,cAAApE,EACA,gBAAAa,GACGxB,GAAiB,sFAiBjB,IAAAxR,OAAa,gBAAgB,OAAO,SAAS,MAAM,EACnDwW,GAAmBxW,GAAO,IAAI,MAAM,IAAM,OAAS+V,EACnDU,EAAuBzW,GAAO,IAAI,MAAM,IAAM,gBAAkB+V,EAC3D,SAAAW,GAAqBzU,EAAgB,CAC7CO,EAAA,GAAAiU,EAAuB,EAAK,EAC5BjU,EAAA,GAAAgU,GAAmBvU,CAAO,EACtB,IAAAjC,MAAa,gBAAgB,OAAO,SAAS,MAAM,EACnDiC,EACHjC,EAAO,IAAI,OAAQ,KAAK,EAExBA,EAAO,OAAO,MAAM,EAErB,QAAQ,aAAa,KAAM,GAAI,IAAMA,EAAO,SAAQ,OAEjD8J,GAAS,GAEF,iBAAA6M,GAAkB,EAAK,EAAA3U,iBACnB4U,GAAcha,EAAWqK,EAAgB,CACjD,MAAA4J,EAAUnH,EAAa,KAAM+I,IAAQA,GAAI,IAAMxL,CAAQ,EAAG,QAE1D4P,EAAeja,GAAM,IAAK,CAAAqB,GAAY8E,OAE1C,GAAI8N,EAAQ9N,EAAC,EACb,KAAM,kBACN,MAAO,MAIT+Q,EAAa+C,CAAY,QAEnBxN,GAAI,QAEJ0K,GAAO,GAEbnX,GAAM,QAAS,CAAAqB,GAAY8E,KAAS,WAE3B9E,IAAU,UACjBA,KAAU,MACVA,GAAM,WAAa,SAEP,UAAA6Y,GAAYhD,CAAY,IAAK,OAAO,QAAQ7V,EAAK,EACxD6Y,KAAe,YAGlB/C,GAAQ,KAAI,CACX,GAAIlD,EAAQ9N,EAAC,EACb,KAAM+T,GACN,MAAOhD,SAKVC,GAAQ,KACP,IAAIlD,EAAQ9N,EAAC,EACb,KAAM,QACN,MAAA9E,EAAA,KAIH6V,EAAaC,EAAO,QAEd1K,GAAI,EAGP,IAAA0N,OAA6D,IAE7D7T,GAAQ,GACH,SAAA8T,GACRta,EACAuK,EACApK,EACA2D,EAA0B,GAC1ByB,GAAU,GAAI,QAGb,QAAAvF,EACA,SAAAuK,EACA,KAAApK,EACA,KAAMoa,GACN,SAAAzW,EACA,QAAAyB,aAIciV,GACfxa,EACAG,EAA0B,MAE1BqG,GAAQ,CAAI8T,GAAYta,KAAaG,CAAI,KAAMqG,EAAQ,GAGpD,IAAA+T,MAEAE,GAAiB,GACrB,SAAS,iBAAiB,mBAAkB,WACvC,SAAS,kBAAoB,WAChCA,GAAiB,YAMbC,GAAoBC,EAAG,4BAA4B,EACnDC,GAAuBD,EAAG,6BAA6B,EACvDE,GAA2BF,EAAG,wBAAwB,EAGtDG,GACL,iEAAiE,KAChE,UAAU,SAAS,EAEjB,IAAAC,GAA2B,GAC3BC,GAAwB,GAGnB,SAAAC,GACRC,EACAC,EAA4B,KAC5BC,EAAsB,KAAI,KAEtBC,EAAM,gBACDC,IAAK,CACbD,IAEGE,EACHF,EAASxB,EAAkB,UAAW2B,IAAQ,CACxCA,KACJC,GAAiBP,EAAWC,EAAYC,CAAU,EAClDE,QAIFG,GAAiBP,EAAWC,EAAYC,CAAU,EAIrC,eAAAK,GACdP,EACAC,EAA4B,KAC5BC,EAAsB,KAAI,KAEtBrF,EAAM/I,EAAa,KAAM+I,GAAQA,EAAI,KAAOmF,CAAS,EAEnD,MAAAQ,GAAiBtG,EAAe,kBAAkB8F,CAAS,OACjE1U,GAAWA,GAAS,SAAU,SAAA+D,KAAeA,IAAa2Q,CAAS,IAC/DQ,KAAmB,WAAaA,KAAmB,gBACtD3F,EAAI,gBAAkB,QAGnB4F,GAAO,CACV,SAAUT,EACV,KAAY,cAAQ,IAAInF,EAAI,OAAO,IAAK7T,GAAOoV,EAASpV,CAAE,IAC1D,WAAY6T,EAAI,oBAAsBqF,EAAa,KACvC,WAAAD,GAGTpF,EAAI,YACPA,EACE,YACA4F,GAAQ,KAAK,OACN,cAAQ,IAAI5F,EAAI,QAAQ,IAAK7T,GAAOoV,EAASpV,CAAE,MAGtD,KAAMqP,GAAY,CACdwE,EAAI,YACP4F,GAAQ,KAAOpK,EACfqK,GAAmB7F,EAAK4F,EAAO,GAE/BzB,GAAc3I,EAAG2J,CAAS,IAGnBnF,EAAI,MAAM,QAAUA,EAAI,cAC5B,QAAQ,IACbA,EAAI,QAAQ,IAAG,MAAQxL,GAAQ,CACxB,MAAAsR,GAAaxB,GAAW,IAAI9P,CAAQ,EAC1C,OAAAsR,IAAY,OAAM,EACXA,MAIL9F,EAAI,YACP6F,GAAmB7F,EAAK4F,EAAO,WAIxBC,GAAmB7F,EAAiB4F,GAAgB,CACxD5F,EAAI,eAAiB,OACnBA,EAAI,iBAAiB+F,GAAgBH,EAAO,EACvC5F,EAAI,eAAiB,WAC/B+F,GAAgBH,EAAO,EACb5F,EAAI,eAAiB,gBAC1BA,EAAI,gBAGRA,EAAI,YAAc4F,GAFlBG,GAAgBH,EAAO,GAOX,eAAAG,GAAgBH,EAAgB,CAC1C5B,QACH3M,GAAS,IAAOA,GAAW,KAAK,MAAM,KAAK,UAAUuO,CAAO,SAGzDE,OAEHA,GAAa1L,EAAI,OAChBwL,EAAQ,SACRA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,UAAU,QAEX1a,GAAC,MAETuF,GAAQ,CAAI8T,GAAY,OAAOrZ,EAAC,EAAG,EAAU,OAAO,KAAMuF,EAAQ,GAClE4O,EAAe,OAAM,CACpB,OAAQ,QACR,WACA,IAAK,EACL,MAAO,GACP,eAAgB,OAEjB2G,GAAWC,CAAe,SAI3B3B,GAAW,IAAIa,EAAWW,EAAU,EAEnB,gBAAA7b,MAAW6b,GACvB7b,GAAQ,OAAS,OACpBic,GAAYjc,EAAO,EACTA,GAAQ,OAAS,SAC3Bkc,GAAclc,EAAO,EACXA,GAAQ,OAAS,SAC3Bmc,GAAqBnc,EAAO,EAClBA,GAAQ,OAAS,OAC3Boc,GAAWpc,EAAO,EAIX,SAAAic,GAAYjc,GAAgB,OAC5B,KAAAE,GAAM,SAAAqK,EAAQ,EAAKvK,GACvB+V,EAAI,iBAAmBA,EAAI,cAC9BA,EAAI,gBAAkB,GACtB+F,GAAgB/F,EAAI,WAAW,GAEhCA,EAAI,gBAAkB,GACtBmE,GAAcha,GAAMqK,EAAQ,EAC5BwR,GAAWC,CAAe,EAGlB,SAAAE,GAAclc,GAAsB,CACpC,WAAAE,IAASF,OACbsV,GAA+BpV,GAAK,WACpCmc,GAA4Bnc,GAAK,OACjCoc,GAA8Bpc,GAAK,aACnCqW,GAAYrW,GAAK,UAEjBqc,GAAc,GAClBvP,EAAa,QAAS,CAAA+I,GAAK1P,KAAC,CACvB0P,GAAI,cAAgBQ,IACvBgG,GAAe,KAAKlW,EAAC,IAGvBkW,GAAe,QAAO,EAAG,QAASlW,IAAC,CAClC2G,EAAa,OAAO3G,GAAG,CAAC,IAEzBiW,GAAc,QAASvG,IAAG,CACzB/I,EAAa,KAAK+I,EAAG,IAGtBO,EAAe,CACd,WAAYhB,GACZ,OAAQ+G,GACF,KAAA/Z,EACQ,aAAA0K,EACH,UAAAuJ,KAIJ,SAAA6F,GAAWI,GAAe,OAC1B,IAAAC,GAAK,SAAAlS,GAAU,MAAAmS,GAAO,SAAA5Y,GAAU,QAAAyB,EAAO,EAAKiX,GACpD1W,EAAA,GAAAU,GACC,CAAA8T,GAAYmC,GAAKlS,GAAUmS,GAAO5Y,GAAUyB,EAAO,EAChD,GAAAiB,EAAA,GAII,SAAA2V,GAAqBnc,GAAsB,OAC3C,SAAAuK,GAAQ,GAAKsJ,EAAM,EAAK7T,GAoD5B,GAlDJoV,EAAe,OAAM,IACjBvB,GACH,OAAQA,GAAO,MACf,SAAUA,GAAO,cACjB,SAAAtJ,KAEDwR,GAAWC,CAAe,GAExBjB,IACD/O,IAAa,MACb6H,GAAO,WAAa,QACpBA,GAAO,UAAY,GACnBA,GAAO,MAAQ,QACfA,GAAO,IAAMmF,KAEb+B,GAA2B,QAC3BvU,GAAQ,CACP8T,GAAYI,GAAmBnQ,GAAU,SAAS,EAC/C,GAAA/D,EAAA,IAIH,CAAAwU,IACDF,IACAjH,GAAO,MAAQ,QACfA,GAAO,IAAMoF,KAEb+B,GAAwB,QACxBxU,GAAQ,CACP8T,GAAYM,GAAsBrQ,GAAU,SAAS,EAClD,GAAA/D,EAAA,IAIDqN,GAAO,QAAU,aACpBA,GAAO,mBAAmB,QAAS3R,IAAE,CACpC8K,EACE,OAAQ+I,IAAQA,GAAI,QAAQ,KAAI,EAAG1E,GAAKsL,EAAC,IAAMtL,KAAQnP,EAAE,CACzD,UAAS6T,IAAG,CACZkF,GAA2BlF,GAAI,GAAI4F,EAAQ,UAAU,MAGxD3O,EAAa,QAAO,MAAQ+I,IAAG,CAC1BA,GAAI,gBAAkBxL,IACzB0Q,GAA2BlF,GAAI,GAAI4F,EAAQ,UAAU,KAMpD9H,GAAO,QAAUiH,IAAoBL,GACxC,OAAO,qBACNjU,GAAQ,CACP8T,GAAYO,GAA0BtQ,GAAU,OAAO,EACpD,GAAA/D,MAEF,GACHyU,GAA2BlF,EAAI,GAAI4F,EAAQ,WAAYP,CAAU,EACjEX,GAAiB,WACP5G,GAAO,QAAU,QAAO,CAC9B,GAAAA,GAAO,QAAO,CACX,MAAA+I,GAAW/I,GAAO,QAAQ,QAC/BkF,GACC,CAAA4D,GAAGE,KAAMA,EAAC,OAEZrW,GAAQ,CACP8T,GACCsC,GACArS,GACA,QACAsJ,GAAO,SACPA,GAAO,OAAO,EAEZ,GAAArN,KAGLwG,EAAa,IAAG,MAAQ+I,IAAG,CAEzBA,GAAI,gBAAkBxL,IACrB,CAAAwL,GAAI,yBAELkF,GAA2BlF,GAAI,GAAI4F,EAAQ,UAAU,gBAQlDmB,GAAc1D,EAA2B2D,EAAmB,CAChE,GAAA/Q,IAAa,kBAGXgR,EAAc,IAAO,IAAG,iCACIhR,CAAQ,oBAEtCoN,IAAU,QAAaA,EAAM,OAAS,GACzC4D,EAAe,aAAa,IAAI,QAAS5D,CAAK,EAE/C4D,EAAe,aAAa,IAAI,cAAeD,CAAW,EAC1D,OAAO,KAAKC,EAAe,WAAY,QAAQ,EAGvC,SAAAC,GAAmBhc,EAA6B,OAClDoQ,EAAMpQ,EAAE,YACduF,GAAWA,GAAS,OAAQ0W,GAAMA,EAAE,KAAO7L,CAAG,GAGzC,MAAA7L,GAAmBC,GAAmB,GACxCA,GAAI,IAAQ,IAAIA,EAAM,SAAS,IAAI,EAAE,SAAW,SAAS,uBAE9C0X,IAAY,CACtB1D,SACmB,IAAOjC,GAAa,uBAClBiC,CAAE;AAAA,4DAGF,QAGnB9M,GAAI,UAENyC,EAAI1P,EAAO,qBAAqB,GAAG,EAE9B2G,EAAI,EAAGA,EAAI+I,EAAE,OAAQ/I,IAAC,CACxB,MAAAoL,EAAUrC,EAAE/I,CAAC,EAAE,aAAa,QAAQ,EACpC+W,EAAQhO,EAAE/I,CAAC,EAAE,aAAa,MAAM,EAGlCb,GAAgB4X,CAAK,GAAK3L,IAAY,UACzCrC,EAAE/I,CAAC,EAAE,aAAa,SAAU,QAAQ,EAItC2G,EAAa,QAAS+I,GAAG,CACpBA,EAAI,QAAQ,KAAMA,GAAQA,EAAI,CAAC,IAAM,MAAM,GAC9CkF,GAA2BlF,EAAI,EAAE,IAI/B,CAAAkE,KAEJva,EAAO,iBAAiB,cAAgBuB,GAAQ,CAC1C,IAAAiY,GAAcjY,CAAC,EAAa,gBAAM,oBAAoB,EACnD,SAAAiB,EAAI,KAAAmb,GAAM,MAAA9b,EAAK,EAAKN,EAAE,OAC9BmW,EAAgB,KAAAlV,EAAI,KAAAmb,GAAM,MAAA9b,EAAK,MAEhC7B,EAAO,iBAAiB,SAAWuB,GAAQ,CACrC,IAAAiY,GAAcjY,CAAC,EAAa,gBAAM,oBAAoB,EAEnD,SAAAiB,EAAI,MAAAnB,GAAO,KAAAb,EAAI,EAAKe,EAAE,OAE1B,GAAAF,KAAU,QAAO,OACZ,MAAAqY,GAAO,YAAA2D,EAAW,EAAK7c,GAC/B4c,GAAc1D,GAAO2D,EAAW,CACtB,MAAAhc,KAAU,SAAWA,KAAU,eACzCyF,GAAQ,CAAI8T,GAAYpa,MAAUa,EAAK,KAAMyF,EAAQ,GAC3CzF,IAAS,eACnBuc,GAAcpb,EAAI,WAAYhC,EAAI,EAErBqd,EAASrb,CAAE,IAAInB,EAAK,GAE3B,QAASyc,IAAM,CACpB,sBAAqB,KACpBvC,GAA2BuC,GAAQtb,EAAIhC,EAAI,QAM/C4F,EAAA,GAAAmU,GAAkB,EAAI,GAKd,SAAAqD,GACRpb,EACA2R,EACA3T,EAAmB,CAEnBA,EAAK,OAAS2T,EACduD,EAAY,EAEV,GAAAlV,EACA,KAAM,iBACN,MAAOhC,CAAA,IAKD,SAAA6b,GAAW0B,EAAiC,KAChDpG,EAAO,GAKX,OAAO,QAAQoG,CAAQ,EAAE,QAAO,EAAGvb,GAAIkT,EAAc,KAChD,IAAAtJ,GAAakB,EAAa,KAC5B+I,IAAQA,GAAI,IAAMX,GAAe,QAAQ,EAEvCtJ,KAAe,SAGnBsJ,GAAe,iBAAmBtJ,GAAW,iBAC7CsJ,GAAe,cAAgBtJ,GAAW,cAC1CuL,EAAQ,KAAI,CACX,GAAI,SAASnV,EAAE,EACf,KAAM,iBACN,MAAOkT,cAIH1B,EAAmB0B,EAAe,uBAClCsI,EAAqB,MAAM,KAAKhK,CAAgB,EAAE,IAAG,EACxDxR,GAAIyb,EAAc,MAElB,GAAAzb,GACA,KAAM,UACN,MAAOyb,KAAmB,aAK7BvG,EAAY,IAAKC,EAAO,GAAKqG,CAAkB,iBAoC3C1D,IAAsBF,EAAgB,WA6BzCE,GAAqB,EAAI,EACzBlU,EAAA,GAAAiU,EAAuB,EAAK,WAe3BC,GAAqB,EAAK,MAMfjZ,GAAK,CACfiZ,GAAqB,EAAK,OAC1B5M,GAAS,IACTtH,EAAA,GAAAiU,EAAuBhZ,EAAM,OAAO,oBAAoB,mxBA3mBzD0U,EAAa,CACf,WAAAE,EACA,OAAAC,EACA,aAAA5I,EACA,KAAA1K,EACA,IAAA6N,EACA,QACC,aAAAuJ,CAAA,yBAKD5T,EAAA,GAAA6T,IAAUiE,CAAQ,mBAmdhB7B,GAAWC,CAAe", "names": ["insert", "target", "svg", "anchor", "append", "path", "ShareError", "message", "uploadToHuggingFace", "data", "type", "blob", "contentType", "filename", "url", "response", "file", "uploadResponse", "error", "copy", "node", "handle_copy", "event", "copy_button", "e", "copy_feedback", "_copy_sucess_button", "copy_text", "copy_sucess_button", "copy_to_clipboard", "value", "copied", "textArea", "format_time", "seconds", "hours", "minutes", "seconds_remainder", "padded_minutes", "padded_seconds", "Gradio", "id", "el", "theme", "version", "root", "autoscroll", "max_file_size", "i18n", "x", "client", "virtual_component_loader", "_load_component", "#id", "#el", "event_name", "name", "variant", "flip", "from", "to", "params", "style", "transform", "ox", "oy", "dx", "dy", "delay", "duration", "easing", "cubicOut", "is_function", "t", "u", "y", "sx", "sy", "onMount", "ctx", "div5", "div0", "div3", "div1", "div2", "button", "span", "div4", "div5_intro", "create_in_transition", "fade", "div5_outro", "create_out_transition", "$$props", "visible", "is_external_url", "link", "DOMPurify", "dispatch", "createEventDispatcher", "close_message", "$$invalidate", "display", "timer_animation_duration", "div", "stop_animation", "create_animation", "rect", "i", "scroll_to_top", "_messages", "messages", "g", "path0", "path1", "h1", "p0", "code0", "p1", "click_handler", "api_logo", "p", "if_block", "create_if_block", "attr", "img", "img_src_value", "h2", "span1", "span0", "br", "api_count", "click_handler_1", "represent_value", "lang", "simplify_file_data", "replace_file_data_with_file_function", "stringify_except_file_function", "is_potentially_nested_file_data", "obj", "key", "item", "index", "jsonString", "regex", "match", "regexNone", "t_value", "dirty", "set_data", "create_if_block_3", "t1_value", "t1", "create_if_block_2", "create_if_block_1", "hr", "t6", "t6_value", "t8", "t8_value", "create_if_block_4", "h4", "current", "is_running", "endpoint_returns", "js_returns", "current_language", "code", "bash_install", "pre", "js_install", "py_install", "h3", "api_name", "fn_index", "named", "endpointdetail_changes", "create_if_block_8", "t4_value", "t17_value", "copybutton_changes", "t4", "t17", "t3_value", "t3", "if_block0", "create_if_block_6", "create_if_block_5", "t7_value", "t14_value", "if_block1", "span2", "span3", "span4", "span5", "t7", "t14", "dependency", "dependency_index", "space_id", "endpoint_parameters", "username", "python_code", "js_code", "bash_post_code", "has_file_path", "param", "blob_components", "blob_examples", "$$value", "tick", "t2", "t2_value", "t5", "t5_value", "dependencies", "short_root", "python_code_text", "bash_code", "api_calls", "get_info", "endpoints_info", "py_zipped", "js_zipped", "bash_zipped", "format_api_call", "call", "d", "param_info", "param_name", "python_type", "py_api_calls", "js_api_calls", "bash_api_calls", "api_names", "python", "javascript", "bash", "t0_value", "t0", "apibanner_changes", "each_blocks", "img_1", "img_1_src_value", "li", "li_class_value", "create_if_block_7", "if_block2", "p2", "p3", "recordingsnippet_changes", "a", "a_href_value", "py_docs", "js_docs", "spaces_docs_suffix", "bash_docs", "br0", "br1", "strong", "code1", "code2", "code3", "code4", "code5", "code6", "app", "root_node", "langs", "get_js_info", "info", "js_info", "js_api_info", "language", "formatter", "get", "format", "binding_callbacks", "component", "theme_mode", "instance", "gradio", "elem_id", "elem_classes", "_id", "s", "v", "wrap", "_target", "args", "props", "report", "propargs", "ev", "bind", "_component", "setContext", "each_value", "ensure_array_like", "get_key", "render_changes", "rendercomponent_props", "get_spread_object", "rendercomponent_changes", "parent", "filtered_children", "child", "$$self", "valid_node", "c", "gradio_class", "load_component", "rootNode", "logo", "create_loading_status_store", "store", "writable", "fn_inputs", "fn_outputs", "pending_outputs", "pending_inputs", "inputs_to_update", "fn_status", "update", "status", "queue", "size", "position", "eta", "progress", "outputs", "inputs", "last_status", "outputs_to_update", "new_status", "pending_count", "new_count", "queue_position", "queue_size", "register", "pending_updates", "create_components", "_component_map", "target_map", "_target_map", "constructor_map", "instance_map", "loading_status", "layout_store", "_components", "keyed_component_values", "_rootNode", "create_layout", "_app", "components", "layout", "options", "store_keyed_values", "dep", "process_frontend_fn", "create_target_meta", "get_inputs_outputs", "preload_all_components", "acc", "walk_layout", "rerender_layout", "render_id", "k", "current_element", "all_current_children", "add_to_current_children", "get_component", "determine_interactivity", "process_server_fn", "update_scheduled", "update_scheduled_store", "flush", "j", "new_value", "update_value", "updates", "get_data", "comp", "AsyncFunction", "source", "backend_fn", "input_length", "output_length", "targets", "fn_id", "trigger", "input", "output", "has_no_default_value", "interactive_prop", "server_fns", "fn", "class_id", "example_components", "example_component_map", "_c", "matching_component", "example_component", "img_alt_value", "footer", "if_block4", "MESSAGE_QUOTE_RE", "SHOW_DUPLICATE_MESSAGE_ON_ETA", "SHOW_MOBILE_QUEUE_WARNING_ON_ETA", "isCustomEvent", "setupi18n", "title", "show_api", "show_footer", "control_page_title", "app_mode", "js", "fill_height", "ready", "_layout", "scheduled_updates", "api_docs_visible", "api_recorder_visible", "set_api_docs_visible", "render_complete", "handle_update", "meta_updates", "update_key", "submit_map", "new_message", "_error_id", "add_new_message", "user_left_page", "DUPLICATE_MESSAGE", "$_", "MOBILE_QUEUE_WARNING", "MOBILE_RECONNECT_MESSAGE", "is_mobile_device", "showed_duplicate_message", "showed_mobile_warning", "wait_then_trigger_api_call", "dep_index", "trigger_id", "event_data", "_unsub", "unsub", "$scheduled_updates", "updating", "trigger_api_call", "current_status", "payload", "trigger_prediction", "submission", "make_prediction", "set_status", "$loading_status", "handle_data", "handle_render", "handle_status_update", "handle_log", "render_layout", "_dependencies", "deps_to_remove", "msg", "log", "level", "_", "_message", "b", "trigger_share", "description", "discussion_url", "handle_error_close", "m", "handle_mount", "_link", "prop", "update_status", "$targets", "dep_id", "statuses", "additional_updates", "pending_status", "$_layout"], "ignoreList": [4], "sources": ["../../../../js/icons/src/Error.svelte", "../../../../js/icons/src/Info.svelte", "../../../../js/icons/src/Warning.svelte", "../../../../js/utils/src/utils.ts", "../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/animate/index.js", "../../../../js/statustracker/static/ToastContent.svelte", "../../../../js/statustracker/static/Toast.svelte", "../../../../js/core/src/api_docs/img/clear.svelte", "../../../../js/core/src/api_docs/NoApi.svelte", "../../../../js/core/src/api_docs/img/api-logo.svg", "../../../../js/core/src/api_docs/ApiBanner.svelte", "../../../../js/core/src/api_docs/utils.ts", "../../../../js/core/src/api_docs/ParametersSnippet.svelte", "../../../../js/core/src/api_docs/CopyButton.svelte", "../../../../js/core/src/api_docs/InstallSnippet.svelte", "../../../../js/core/src/api_docs/EndpointDetail.svelte", "../../../../js/core/src/api_docs/CodeSnippet.svelte", "../../../../js/core/src/api_docs/RecordingSnippet.svelte", "../../../../js/core/src/api_docs/img/python.svg", "../../../../js/core/src/api_docs/img/javascript.svg", "../../../../js/core/src/api_docs/img/bash.svg", "../../../../js/core/src/api_docs/ResponseSnippet.svelte", "../../../../js/core/src/api_docs/ApiDocs.svelte", "../../../../js/core/src/api_docs/ApiRecorder.svelte", "../../../../js/core/src/gradio_helper.ts", "../../../../js/core/src/RenderComponent.svelte", "../../../../js/core/src/Render.svelte", "../../../../js/core/src/MountComponents.svelte", "../../../../js/core/src/images/logo.svg", "../../../../js/core/src/stores.ts", "../../../../js/core/src/init.ts", "../../../../js/core/src/Blocks.svelte"], "sourcesContent": ["<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z\"\n\t/>\n</svg>\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z\"\n\t/>\n</svg>\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\n\t/>\n</svg>\n", "import type { ActionReturn } from \"svelte/action\";\nimport type { Client } from \"@gradio/client\";\nimport type { ComponentType, SvelteComponent } from \"svelte\";\n\nexport interface SelectData {\n\trow_value?: any[];\n\tindex: number | [number, number];\n\tvalue: any;\n\tselected?: boolean;\n}\n\nexport interface LikeData {\n\tindex: number | [number, number];\n\tvalue: any;\n\tliked?: boolean;\n}\n\nexport interface KeyUpData {\n\tkey: string;\n\tinput_value: string;\n}\n\nexport interface ShareData {\n\tdescription: string;\n\ttitle?: string;\n}\n\nexport class ShareError extends Error {\n\tconstructor(message: string) {\n\t\tsuper(message);\n\t\tthis.name = \"ShareError\";\n\t}\n}\n\nexport async function uploadToHuggingFace(\n\tdata: string | { url?: string; path?: string },\n\ttype: \"base64\" | \"url\"\n): Promise<string> {\n\tif (window.__gradio_space__ == null) {\n\t\tthrow new ShareError(\"Must be on Spaces to share.\");\n\t}\n\tlet blob: Blob;\n\tlet contentType: string;\n\tlet filename: string;\n\tif (type === \"url\") {\n\t\tlet url: string;\n\n\t\tif (typeof data === \"object\" && data.url) {\n\t\t\turl = data.url;\n\t\t} else if (typeof data === \"string\") {\n\t\t\turl = data;\n\t\t} else {\n\t\t\tthrow new Error(\"Invalid data format for URL type\");\n\t\t}\n\n\t\tconst response = await fetch(url);\n\t\tblob = await response.blob();\n\t\tcontentType = response.headers.get(\"content-type\") || \"\";\n\t\tfilename = response.headers.get(\"content-disposition\") || \"\";\n\t} else {\n\t\tlet dataurl: string;\n\n\t\tif (typeof data === \"object\" && data.path) {\n\t\t\tdataurl = data.path;\n\t\t} else if (typeof data === \"string\") {\n\t\t\tdataurl = data;\n\t\t} else {\n\t\t\tthrow new Error(\"Invalid data format for base64 type\");\n\t\t}\n\n\t\tblob = dataURLtoBlob(dataurl);\n\t\tcontentType = dataurl.split(\";\")[0].split(\":\")[1];\n\t\tfilename = \"file.\" + contentType.split(\"/\")[1];\n\t}\n\n\tconst file = new File([blob], filename, { type: contentType });\n\n\t// Send file to endpoint\n\tconst uploadResponse = await fetch(\"https://huggingface.co/uploads\", {\n\t\tmethod: \"POST\",\n\t\tbody: file,\n\t\theaders: {\n\t\t\t\"Content-Type\": file.type,\n\t\t\t\"X-Requested-With\": \"XMLHttpRequest\"\n\t\t}\n\t});\n\n\t// Check status of response\n\tif (!uploadResponse.ok) {\n\t\tif (\n\t\t\tuploadResponse.headers.get(\"content-type\")?.includes(\"application/json\")\n\t\t) {\n\t\t\tconst error = await uploadResponse.json();\n\t\t\tthrow new ShareError(`Upload failed: ${error.error}`);\n\t\t}\n\t\tthrow new ShareError(`Upload failed.`);\n\t}\n\n\t// Return response if needed\n\tconst result = await uploadResponse.text();\n\treturn result;\n}\n\nfunction dataURLtoBlob(dataurl: string): Blob {\n\tvar arr = dataurl.split(\",\"),\n\t\tmime = (arr[0].match(/:(.*?);/) as RegExpMatchArray)[1],\n\t\tbstr = atob(arr[1]),\n\t\tn = bstr.length,\n\t\tu8arr = new Uint8Array(n);\n\twhile (n--) {\n\t\tu8arr[n] = bstr.charCodeAt(n);\n\t}\n\treturn new Blob([u8arr], { type: mime });\n}\n\nexport function copy(node: HTMLDivElement): ActionReturn {\n\tnode.addEventListener(\"click\", handle_copy);\n\n\tasync function handle_copy(event: MouseEvent): Promise<void> {\n\t\tconst path = event.composedPath() as HTMLButtonElement[];\n\n\t\tconst [copy_button] = path.filter(\n\t\t\t(e) => e?.tagName === \"BUTTON\" && e.classList.contains(\"copy_code_button\")\n\t\t);\n\n\t\tif (copy_button) {\n\t\t\tevent.stopImmediatePropagation();\n\n\t\t\tconst copy_text = copy_button.parentElement!.innerText.trim();\n\t\t\tconst copy_sucess_button = Array.from(\n\t\t\t\tcopy_button.children\n\t\t\t)[1] as HTMLDivElement;\n\n\t\t\tconst copied = await copy_to_clipboard(copy_text);\n\n\t\t\tif (copied) copy_feedback(copy_sucess_button);\n\n\t\t\tfunction copy_feedback(_copy_sucess_button: HTMLDivElement): void {\n\t\t\t\t_copy_sucess_button.style.opacity = \"1\";\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t_copy_sucess_button.style.opacity = \"0\";\n\t\t\t\t}, 2000);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"click\", handle_copy);\n\t\t}\n\t};\n}\n\nasync function copy_to_clipboard(value: string): Promise<boolean> {\n\tlet copied = false;\n\tif (\"clipboard\" in navigator) {\n\t\tawait navigator.clipboard.writeText(value);\n\t\tcopied = true;\n\t} else {\n\t\tconst textArea = document.createElement(\"textarea\");\n\t\ttextArea.value = value;\n\n\t\ttextArea.style.position = \"absolute\";\n\t\ttextArea.style.left = \"-999999px\";\n\n\t\tdocument.body.prepend(textArea);\n\t\ttextArea.select();\n\n\t\ttry {\n\t\t\tdocument.execCommand(\"copy\");\n\t\t\tcopied = true;\n\t\t} catch (error) {\n\t\t\tconsole.error(error);\n\t\t\tcopied = false;\n\t\t} finally {\n\t\t\ttextArea.remove();\n\t\t}\n\t}\n\n\treturn copied;\n}\n\nexport const format_time = (seconds: number): string => {\n\tconst hours = Math.floor(seconds / 3600);\n\tconst minutes = Math.floor((seconds % 3600) / 60);\n\tconst seconds_remainder = Math.round(seconds) % 60;\n\tconst padded_minutes = `${minutes < 10 ? \"0\" : \"\"}${minutes}`;\n\tconst padded_seconds = `${\n\t\tseconds_remainder < 10 ? \"0\" : \"\"\n\t}${seconds_remainder}`;\n\n\tif (hours > 0) {\n\t\treturn `${hours}:${padded_minutes}:${padded_seconds}`;\n\t}\n\treturn `${minutes}:${padded_seconds}`;\n};\n\ninterface Args {\n\tapi_url: string;\n\tname: string;\n\tid?: string;\n\tvariant: \"component\" | \"example\" | \"base\";\n}\n\ntype component_loader = (args: Args) => {\n\tname: \"string\";\n\tcomponent: {\n\t\tdefault: ComponentType<SvelteComponent>;\n\t};\n};\n\nexport type I18nFormatter = any;\nexport class Gradio<T extends Record<string, any> = Record<string, any>> {\n\t#id: number;\n\ttheme: string;\n\tversion: string;\n\ti18n: I18nFormatter;\n\t#el: HTMLElement;\n\troot: string;\n\tautoscroll: boolean;\n\tmax_file_size: number | null;\n\tclient: Client;\n\t_load_component?: component_loader;\n\tload_component = _load_component.bind(this);\n\n\tconstructor(\n\t\tid: number,\n\t\tel: HTMLElement,\n\t\ttheme: string,\n\t\tversion: string,\n\t\troot: string,\n\t\tautoscroll: boolean,\n\t\tmax_file_size: number | null,\n\t\ti18n: I18nFormatter = (x: string): string => x,\n\t\tclient: Client,\n\t\tvirtual_component_loader?: component_loader\n\t) {\n\t\tthis.#id = id;\n\t\tthis.theme = theme;\n\t\tthis.version = version;\n\t\tthis.#el = el;\n\t\tthis.max_file_size = max_file_size;\n\n\t\tthis.i18n = i18n;\n\t\tthis.root = root;\n\t\tthis.autoscroll = autoscroll;\n\t\tthis.client = client;\n\n\t\tthis._load_component = virtual_component_loader;\n\t}\n\n\tdispatch<E extends keyof T>(event_name: E, data?: T[E]): void {\n\t\tconst e = new CustomEvent(\"gradio\", {\n\t\t\tbubbles: true,\n\t\t\tdetail: { data, id: this.#id, event: event_name }\n\t\t});\n\t\tthis.#el.dispatchEvent(e);\n\t}\n}\n\nfunction _load_component(\n\tthis: Gradio,\n\tname: string,\n\tvariant: \"component\" | \"example\" | \"base\" = \"component\"\n): ReturnType<component_loader> {\n\treturn this._load_component!({\n\t\tname,\n\t\tapi_url: this.client.config?.root!,\n\t\tvariant\n\t});\n}\n", "import { cubicOut } from '../easing/index.js';\nimport { is_function } from '../internal/index.js';\n\n/**\n * The flip function calculates the start and end position of an element and animates between them, translating the x and y values.\n * `flip` stands for [First, Last, Invert, Play](https://aerotwist.com/blog/flip-your-animations/).\n *\n * https://svelte.dev/docs/svelte-animate#flip\n * @param {Element} node\n * @param {{ from: DOMRect; to: DOMRect }} fromTo\n * @param {import('./public.js').FlipParams} params\n * @returns {import('./public.js').AnimationConfig}\n */\nexport function flip(node, { from, to }, params = {}) {\n\tconst style = getComputedStyle(node);\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst [ox, oy] = style.transformOrigin.split(' ').map(parseFloat);\n\tconst dx = from.left + (from.width * ox) / to.width - (to.left + ox);\n\tconst dy = from.top + (from.height * oy) / to.height - (to.top + oy);\n\tconst { delay = 0, duration = (d) => Math.sqrt(d) * 120, easing = cubicOut } = params;\n\treturn {\n\t\tdelay,\n\t\tduration: is_function(duration) ? duration(Math.sqrt(dx * dx + dy * dy)) : duration,\n\t\teasing,\n\t\tcss: (t, u) => {\n\t\t\tconst x = u * dx;\n\t\t\tconst y = u * dy;\n\t\t\tconst sx = t + (u * from.width) / to.width;\n\t\t\tconst sy = t + (u * from.height) / to.height;\n\t\t\treturn `transform: ${transform} translate(${x}px, ${y}px) scale(${sx}, ${sy});`;\n\t\t}\n\t};\n}\n", "<script lang=\"ts\">\n\timport { Error, Info, Warning } from \"@gradio/icons\";\n\timport DOMPurify from \"dompurify\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport type { ToastMessage } from \"./types\";\n\n\texport let message = \"\";\n\texport let type: ToastMessage[\"type\"];\n\texport let id: number;\n\texport let duration: number | null = 10;\n\texport let visible = true;\n\n\tconst is_external_url = (link: string | null): boolean => {\n\t\ttry {\n\t\t\treturn !!link && new URL(link, location.href).origin !== location.origin;\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t};\n\n\tDOMPurify.addHook(\"afterSanitizeAttributes\", function (node) {\n\t\tif (\"target\" in node) {\n\t\t\tif (is_external_url(node.getAttribute(\"href\"))) {\n\t\t\t\tnode.setAttribute(\"target\", \"_blank\");\n\t\t\t\tnode.setAttribute(\"rel\", \"noopener noreferrer\");\n\t\t\t}\n\t\t}\n\t});\n\n\t$: message = DOMPurify.sanitize(message);\n\n\t$: display = visible;\n\t$: duration = duration || null;\n\n\tconst dispatch = createEventDispatcher();\n\n\tfunction close_message(): void {\n\t\tdispatch(\"close\", id);\n\t}\n\n\tonMount(() => {\n\t\tif (duration !== null) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tclose_message();\n\t\t\t}, duration * 1000);\n\t\t}\n\t});\n\n\t$: timer_animation_duration = `${duration || 0}s`;\n</script>\n\n<!-- TODO: fix-->\n<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n<div\n\tclass=\"toast-body {type}\"\n\trole=\"alert\"\n\tdata-testid=\"toast-body\"\n\tclass:hidden={!display}\n\ton:click|stopPropagation\n\ton:keydown|stopPropagation\n\tin:fade={{ duration: 200, delay: 100 }}\n\tout:fade={{ duration: 200 }}\n>\n\t<div class=\"toast-icon {type}\">\n\t\t{#if type === \"warning\"}\n\t\t\t<Warning />\n\t\t{:else if type === \"info\"}\n\t\t\t<Info />\n\t\t{:else if type === \"error\"}\n\t\t\t<Error />\n\t\t{/if}\n\t</div>\n\n\t<div class=\"toast-details {type}\">\n\t\t<div class=\"toast-title {type}\">{type}</div>\n\t\t<div class=\"toast-text {type}\">\n\t\t\t{@html message}\n\t\t</div>\n\t</div>\n\n\t<button\n\t\ton:click={close_message}\n\t\tclass=\"toast-close {type}\"\n\t\ttype=\"button\"\n\t\taria-label=\"Close\"\n\t\tdata-testid=\"toast-close\"\n\t>\n\t\t<span aria-hidden=\"true\">&#215;</span>\n\t</button>\n\n\t<div\n\t\tclass=\"timer {type}\"\n\t\tstyle={`animation-duration: ${timer_animation_duration};`}\n\t/>\n</div>\n\n<style>\n\t.toast-body {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tright: 0;\n\t\tleft: 0;\n\t\talign-items: center;\n\t\tmargin: var(--size-6) var(--size-4);\n\t\tmargin: auto;\n\t\tborder-radius: var(--container-radius);\n\t\toverflow: hidden;\n\t\tpointer-events: auto;\n\t}\n\n\t.toast-body.error {\n\t\tborder: 1px solid var(--color-red-700);\n\t\tbackground: var(--color-red-50);\n\t}\n\t:global(.dark) .toast-body.error {\n\t\tborder: 1px solid var(--color-red-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-body.warning {\n\t\tborder: 1px solid var(--color-yellow-700);\n\t\tbackground: var(--color-yellow-50);\n\t}\n\t:global(.dark) .toast-body.warning {\n\t\tborder: 1px solid var(--color-yellow-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-body.info {\n\t\tborder: 1px solid var(--color-grey-700);\n\t\tbackground: var(--color-grey-50);\n\t}\n\t:global(.dark) .toast-body.info {\n\t\tborder: 1px solid var(--color-grey-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-sm);\n\t\ttext-transform: capitalize;\n\t}\n\n\t.toast-title.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-title.error {\n\t\tcolor: var(--color-red-50);\n\t}\n\n\t.toast-title.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\t:global(.dark) .toast-title.warning {\n\t\tcolor: var(--color-yellow-50);\n\t}\n\n\t.toast-title.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\t:global(.dark) .toast-title.info {\n\t\tcolor: var(--color-grey-50);\n\t}\n\n\t.toast-close {\n\t\tmargin: 0 var(--size-3);\n\t\tborder-radius: var(--size-3);\n\t\tpadding: 0px var(--size-1-5);\n\t\tfont-size: var(--size-5);\n\t\tline-height: var(--size-5);\n\t}\n\n\t.toast-close.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-close.error {\n\t\tcolor: var(--color-red-500);\n\t}\n\n\t.toast-close.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\t:global(.dark) .toast-close.warning {\n\t\tcolor: var(--color-yellow-500);\n\t}\n\n\t.toast-close.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\t:global(.dark) .toast-close.info {\n\t\tcolor: var(--color-grey-500);\n\t}\n\n\t.toast-text {\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.toast-text.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-text.error {\n\t\tcolor: var(--color-red-50);\n\t}\n\n\t.toast-text.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .toast-text.warning {\n\t\tcolor: var(--color-yellow-50);\n\t}\n\n\t.toast-text.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .toast-text.info {\n\t\tcolor: var(--color-grey-50);\n\t}\n\n\t.toast-details {\n\t\tmargin: var(--size-3) var(--size-3) var(--size-3) 0;\n\t\twidth: 100%;\n\t}\n\n\t.toast-icon {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tposition: relative;\n\t\tflex-shrink: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tpadding: var(--size-1);\n\t\tpadding-left: calc(var(--size-1) - 1px);\n\t\twidth: 35px;\n\t\theight: 35px;\n\t}\n\n\t.toast-icon.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\n\t:global(.dark) .toast-icon.error {\n\t\tcolor: var(--color-red-500);\n\t}\n\n\t.toast-icon.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .toast-icon.warning {\n\t\tcolor: var(--color-yellow-500);\n\t}\n\n\t.toast-icon.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .toast-icon.info {\n\t\tcolor: var(--color-grey-500);\n\t}\n\n\t@keyframes countdown {\n\t\tfrom {\n\t\t\ttransform: scaleX(1);\n\t\t}\n\t\tto {\n\t\t\ttransform: scaleX(0);\n\t\t}\n\t}\n\n\t.timer {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\ttransform-origin: 0 0;\n\t\tanimation: countdown 10s linear forwards;\n\t\twidth: 100%;\n\t\theight: var(--size-1);\n\t}\n\n\t.timer.error {\n\t\tbackground: var(--color-red-700);\n\t}\n\n\t:global(.dark) .timer.error {\n\t\tbackground: var(--color-red-500);\n\t}\n\n\t.timer.warning {\n\t\tbackground: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .timer.warning {\n\t\tbackground: var(--color-yellow-500);\n\t}\n\n\t.timer.info {\n\t\tbackground: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .timer.info {\n\t\tbackground: var(--color-grey-500);\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.toast-text :global(a) {\n\t\ttext-decoration: underline;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { flip } from \"svelte/animate\";\n\timport type { ToastMessage } from \"./types\";\n\timport ToastContent from \"./ToastContent.svelte\";\n\n\texport let messages: ToastMessage[] = [];\n\n\t$: scroll_to_top(messages);\n\n\tfunction scroll_to_top(_messages: ToastMessage[]): void {\n\t\tif (_messages.length > 0) {\n\t\t\tif (\"parentIFrame\" in window) {\n\t\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<div class=\"toast-wrap\">\n\t{#each messages as { type, message, id, duration, visible } (id)}\n\t\t<div animate:flip={{ duration: 300 }} style:width=\"100%\">\n\t\t\t<ToastContent {type} {message} {duration} {visible} on:close {id} />\n\t\t</div>\n\t{/each}\n</div>\n\n<style>\n\t.toast-wrap {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: var(--size-4);\n\t\tright: var(--size-4);\n\n\t\tflex-direction: column;\n\t\talign-items: end;\n\t\tgap: var(--size-2);\n\t\tz-index: var(--layer-top);\n\t\twidth: calc(100% - var(--size-8));\n\t}\n\n\t@media (--screen-sm) {\n\t\t.toast-wrap {\n\t\t\twidth: calc(var(--size-96) + var(--size-10));\n\t\t}\n\t}\n</style>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 5 5\"\n\tversion=\"1.1\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\txml:space=\"preserve\"\n\tstyle=\"fill:currentColor;fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;\"\n>\n\t<g>\n\t\t<path\n\t\t\td=\"M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z\"\n\t\t/>\n\t</g>\n</svg>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport Clear from \"./img/clear.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let root: string;\n</script>\n\n<div class=\"wrap prose\">\n\t<h1>API Docs</h1>\n\t<p class=\"attention\">\n\t\tNo API Routes found for\n\t\t<code>\n\t\t\t{root}\n\t\t</code>\n\t</p>\n\t<p>\n\t\tTo expose an API endpoint of your app in this page, set the <code>\n\t\t\tapi_name\n\t\t</code>\n\t\tparameter of the event listener.\n\t\t<br />\n\t\tFor more information, visit the\n\t\t<a href=\"https://gradio.app/sharing_your_app/#api-page\" target=\"_blank\">\n\t\t\tAPI Page guide\n\t\t</a>\n\t\t. To hide the API documentation button and this page, set\n\t\t<code>show_api=False</code>\n\t\tin the\n\t\t<code>Blocks.launch()</code>\n\t\tmethod.\n\t</p>\n</div>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\t.wrap {\n\t\tpadding: var(--size-6);\n\t}\n\n\t.attention {\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.attention code {\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\t}\n</style>\n", "export default \"data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport api_logo from \"./img/api-logo.svg\";\n\timport Clear from \"./img/clear.svelte\";\n\timport { BaseButton } from \"@gradio/button\";\n\n\texport let root: string;\n\texport let api_count: number;\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<h2>\n\t<img src={api_logo} alt=\"\" />\n\t<div class=\"title\">\n\t\tAPI documentation\n\t\t<div class=\"url\">\n\t\t\t{root}\n\t\t</div>\n\t</div>\n\t<span class=\"counts\">\n\t\t<BaseButton\n\t\t\tsize=\"sm\"\n\t\t\tvariant=\"secondary\"\n\t\t\telem_id=\"start-api-recorder\"\n\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t>\n\t\t\t<div class=\"loading-dot self-baseline\"></div>\n\t\t\t<p class=\"self-baseline btn-text\">API Recorder</p>\n\t\t</BaseButton>\n\t\t<p>\n\t\t\t<span class=\"url\">{api_count}</span> API endpoint{#if api_count > 1}s{/if}<br\n\t\t\t/>\n\t\t</p>\n\t</span>\n</h2>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\th2 {\n\t\tdisplay: flex;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tgap: var(--size-4);\n\t}\n\n\th2 img {\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\tdisplay: inline-block;\n\t}\n\n\t.url {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: normal;\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\n\t\th2 img {\n\t\t\twidth: var(--size-5);\n\t\t}\n\t}\n\n\t.counts {\n\t\tmargin-top: auto;\n\t\tmargin-right: var(--size-8);\n\t\tmargin-bottom: auto;\n\t\tmargin-left: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-light);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 12px;\n\t\theight: 12px;\n\t\tborder-radius: 6px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.3rem;\n\t}\n\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\t.btn-text {\n\t\tfont-size: var(--text-lg);\n\t}\n</style>\n", "// eslint-disable-next-line complexity\nexport function represent_value(\n\tvalue: string,\n\ttype: string | undefined,\n\tlang: \"js\" | \"py\" | \"bash\" | null = null\n): string | null | number | boolean | Record<string, unknown> {\n\tif (type === undefined) {\n\t\treturn lang === \"py\" ? \"None\" : null;\n\t}\n\tif (value === null && lang === \"py\") {\n\t\treturn \"None\";\n\t}\n\tif (type === \"string\" || type === \"str\") {\n\t\treturn lang === null ? value : '\"' + value + '\"';\n\t} else if (type === \"number\") {\n\t\treturn lang === null ? parseFloat(value) : value;\n\t} else if (type === \"boolean\" || type == \"bool\") {\n\t\tif (lang === \"py\") {\n\t\t\tvalue = String(value);\n\t\t\treturn value === \"true\" ? \"True\" : \"False\";\n\t\t} else if (lang === \"js\" || lang === \"bash\") {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === \"true\";\n\t} else if (type === \"List[str]\") {\n\t\tvalue = JSON.stringify(value);\n\t\treturn value;\n\t} else if (type.startsWith(\"Literal['\")) {\n\t\t// a literal of strings\n\t\treturn '\"' + value + '\"';\n\t}\n\t// assume object type\n\tif (lang === null) {\n\t\treturn value === \"\" ? null : JSON.parse(value);\n\t} else if (typeof value === \"string\") {\n\t\tif (value === \"\") {\n\t\t\treturn lang === \"py\" ? \"None\" : \"null\";\n\t\t}\n\t\treturn value;\n\t}\n\tif (lang === \"bash\") {\n\t\tvalue = simplify_file_data(value);\n\t}\n\tif (lang === \"py\") {\n\t\tvalue = replace_file_data_with_file_function(value);\n\t}\n\treturn stringify_except_file_function(value);\n}\n\nexport function is_potentially_nested_file_data(obj: any): boolean {\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tif (obj.hasOwnProperty(\"url\") && obj.hasOwnProperty(\"meta\")) {\n\t\t\tif (\n\t\t\t\ttypeof obj.meta === \"object\" &&\n\t\t\t\tobj.meta !== null &&\n\t\t\t\tobj.meta._type === \"gradio.FileData\"\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t}\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tfor (let key in obj) {\n\t\t\tif (typeof obj[key] === \"object\") {\n\t\t\t\tlet result = is_potentially_nested_file_data(obj[key]);\n\t\t\t\tif (result) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction simplify_file_data(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn { path: obj.url };\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = simplify_file_data(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = simplify_file_data(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction replace_file_data_with_file_function(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn `handle_file('${obj.url}')`;\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = replace_file_data_with_file_function(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = replace_file_data_with_file_function(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction stringify_except_file_function(obj: any): string {\n\tlet jsonString = JSON.stringify(obj, (key, value) => {\n\t\tif (value === null) {\n\t\t\treturn \"UNQUOTEDNone\";\n\t\t}\n\t\tif (\n\t\t\ttypeof value === \"string\" &&\n\t\t\tvalue.startsWith(\"handle_file(\") &&\n\t\t\tvalue.endsWith(\")\")\n\t\t) {\n\t\t\treturn `UNQUOTED${value}`; // Flag the special strings\n\t\t}\n\t\treturn value;\n\t});\n\tconst regex = /\"UNQUOTEDhandle_file\\(([^)]*)\\)\"/g;\n\tjsonString = jsonString.replace(regex, (match, p1) => `handle_file(${p1})`);\n\tconst regexNone = /\"UNQUOTEDNone\"/g;\n\treturn jsonString.replace(regexNone, \"None\");\n}\n", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\timport { represent_value } from \"./utils\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot\" />\n\t</div>\n\tAccepts {endpoint_returns.length} parameter{#if endpoint_returns.length != 1}s{/if}:\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, python_type, component, parameter_name, parameter_has_default, parameter_default }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p style=\"white-space: nowrap; overflow-x: auto;\">\n\t\t\t\t<span class=\"code\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{current_language !== \"bash\" && parameter_name\n\t\t\t\t\t\t? parameter_name\n\t\t\t\t\t\t: \"[\" + i + \"]\"}</span\n\t\t\t\t>\n\t\t\t\t<span class=\"code highlight\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{#if parameter_has_default && parameter_default === null}&nbsp;|\n\t\t\t\t\t\t\tNone{/if}{:else}{js_returns[i].type || \"any\"}{/if}</span\n\t\t\t\t>\n\t\t\t\t{#if !parameter_has_default || current_language == \"bash\"}<span\n\t\t\t\t\t\tstyle=\"font-weight:bold\">Required</span\n\t\t\t\t\t>{:else}<span> Default: </span><span\n\t\t\t\t\t\tclass=\"code\"\n\t\t\t\t\t\tstyle=\"font-size: var(--text-sm);\"\n\t\t\t\t\t\t>{represent_value(parameter_default, python_type.type, \"py\")}</span\n\t\t\t\t\t>{/if}\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe input value that is provided in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->.\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t\tmargin-bottom: 12px;\n\t}\n\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tdisplay: inline;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t\tmargin-top: var(--size-1);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-right: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseButton } from \"@gradio/button\";\n\texport let code: string;\n\tlet copy_text = \"copy\";\n\n\tfunction copy(): void {\n\t\tnavigator.clipboard.writeText(code);\n\t\tcopy_text = \"copied!\";\n\t\tsetTimeout(() => {\n\t\t\tcopy_text = \"copy\";\n\t\t}, 1500);\n\t}\n</script>\n\n<BaseButton size=\"sm\" on:click={copy}>\n\t{copy_text}\n</BaseButton>\n", "<script lang=\"ts\">\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\n\tlet py_install = \"pip install gradio_client\";\n\tlet js_install = \"npm i -D @gradio/client\";\n\tlet bash_install = \"curl --version\";\n</script>\n\n<Block border_mode=\"contrast\">\n\t<code>\n\t\t{#if current_language === \"python\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={py_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {py_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={js_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {js_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={bash_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {bash_install}</pre>\n\t\t\t</div>\n\t\t{/if}\n\t</code>\n</Block>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let api_name: string | null = null;\n\texport let fn_index: number | null = null;\n\texport let named: boolean;\n</script>\n\n{#if named}\n\t<h3>\n\t\tapi_name:\n\t\t<span class=\"post\">{\"/\" + api_name}</span>\n\t</h3>\n{:else}\n\t<h3>\n\t\tfn_index:\n\t\t<span class=\"post\">{fn_index}</span>\n\t</h3>\n{/if}\n\n<style>\n\th3 {\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.post {\n\t\tmargin-right: var(--size-2);\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport Copy<PERSON>utton from \"./CopyButton.svelte\";\n\timport { represent_value, is_potentially_nested_file_data } from \"./utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport EndpointDetail from \"./EndpointDetail.svelte\";\n\n\tinterface EndpointParameter {\n\t\tlabel: string;\n\t\ttype: string;\n\t\tpython_type: { type: string };\n\t\tcomponent: string;\n\t\texample_input: string;\n\t\tserializer: string;\n\t}\n\n\texport let dependency: Dependency;\n\texport let dependency_index: number;\n\texport let root: string;\n\texport let space_id: string | null;\n\texport let endpoint_parameters: any;\n\texport let named: boolean;\n\texport let username: string | null;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\n\tlet python_code: HTMLElement;\n\tlet js_code: HTMLElement;\n\tlet bash_post_code: HTMLElement;\n\tlet bash_get_code: HTMLElement;\n\n\tlet has_file_path = endpoint_parameters.some((param: EndpointParameter) =>\n\t\tis_potentially_nested_file_data(param.example_input)\n\t);\n\tlet blob_components = [\"Audio\", \"File\", \"Image\", \"Video\"];\n\tlet blob_examples: any[] = endpoint_parameters.filter(\n\t\t(param: EndpointParameter) => blob_components.includes(param.component)\n\t);\n</script>\n\n<div class=\"container\">\n\t{#if named}\n\t\t<EndpointDetail {named} api_name={dependency.api_name} />\n\t{:else}\n\t\t<EndpointDetail {named} fn_index={dependency_index} />\n\t{/if}\n\t{#if current_language === \"python\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client{#if has_file_path}, handle_file{/if}\n\nclient = Client(<span class=\"token string\">\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\nresult = client.<span class=\"highlight\">predict</span\n\t\t\t\t\t\t>(<!--\n-->{#each endpoint_parameters as { python_type, example_input, parameter_name, parameter_has_default, parameter_default }, i}<!--\n        -->\n\t\t{parameter_name\n\t\t\t\t\t\t\t\t? parameter_name + \"=\"\n\t\t\t\t\t\t\t\t: \"\"}<span\n\t\t\t\t\t\t\t\t>{represent_value(\n\t\t\t\t\t\t\t\t\tparameter_has_default ? parameter_default : example_input,\n\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t>,{/each}<!--\n\n\t\t-->\n\t\tapi_name=<span class=\"api-name\">\"/{dependency.api_name}\"</span><!--\n\t\t-->\n)\n<span class=\"highlight\">print</span>(result)</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"javascript\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n{#each blob_examples as { component, example_input }, i}<!--\n-->\nconst response_{i} = await fetch(\"{example_input.url}\");\nconst example{component} = await response_{i}.blob();\n\t\t\t\t\t\t{/each}<!--\n-->\nconst client = await Client.connect(<span class=\"token string\"\n\t\t\t\t\t\t\t>\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\nconst result = await client.predict({#if named}<span class=\"api-name\"\n\t\t\t\t\t\t\t\t>\"/{dependency.api_name}\"</span\n\t\t\t\t\t\t\t>{:else}{dependency_index}{/if}, &lbrace; <!--\n-->{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}<!--\n\t\t-->{#if blob_components.includes(component)}<!--\n\t-->\n\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: example{component}</span\n\t\t\t\t\t\t\t\t>, <!--\n\t\t--><span class=\"desc\"><!--\n\t\t--></span\n\t\t\t\t\t\t\t\t><!--\n\t\t-->{:else}<!--\n\t-->\t\t\n\t\t<span class=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: {represent_value(\n\t\t\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t\t>, <!--\n--><!--\n-->{/if}\n\t\t\t\t\t\t{/each}\n&rbrace;);\n\nconsole.log(result.data);\n</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"bash\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_post_code?.innerText}></CopyButton>\n\t\t\t\t</div>\n\n\t\t\t\t<div bind:this={bash_post_code}>\n\t\t\t\t\t<pre>curl -X POST {root}call/{dependency.api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"}\n  \"data\": [{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}\n\t\t\t\t\t\t\t<!-- \n-->{represent_value(\n\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t\t)}{#if i < endpoint_parameters.length - 1},\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/each}\n]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}'  \\\n  | read EVENT_ID; curl -N {root}call/{dependency.api_name}/$EVENT_ID</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{/if}\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Dependency, Payload } from \"../types\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { represent_value } from \"./utils\";\n\timport { onMount, tick } from \"svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let short_root: string;\n\texport let root: string;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\texport let username: string | null;\n\n\tlet python_code: HTMLElement;\n\tlet python_code_text: string;\n\tlet js_code: HTMLElement;\n\tlet bash_code: HTMLElement;\n\n\texport let api_calls: Payload[] = [];\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(root + \"info/?all_endpoints=true\");\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\n\tlet endpoints_info: any;\n\tlet py_zipped: { call: string; api_name: string }[] = [];\n\tlet js_zipped: { call: string; api_name: string }[] = [];\n\tlet bash_zipped: { call: string; api_name: string }[] = [];\n\n\tfunction format_api_call(call: Payload, lang: \"py\" | \"js\" | \"bash\"): string {\n\t\tconst api_name = `/${dependencies[call.fn_index].api_name}`;\n\t\t// If an input is undefined (distinct from null) then it corresponds to a State component.\n\t\tlet call_data_excluding_state = call.data.filter(\n\t\t\t(d) => typeof d !== \"undefined\"\n\t\t);\n\n\t\tconst params = call_data_excluding_state\n\t\t\t.map((param, index) => {\n\t\t\t\tif (endpoints_info[api_name]) {\n\t\t\t\t\tconst param_info = endpoints_info[api_name].parameters[index];\n\t\t\t\t\tif (!param_info) {\n\t\t\t\t\t\treturn undefined;\n\t\t\t\t\t}\n\t\t\t\t\tconst param_name = param_info.parameter_name;\n\t\t\t\t\tconst python_type = param_info.python_type.type;\n\t\t\t\t\tif (lang === \"py\") {\n\t\t\t\t\t\treturn `  ${param_name}=${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"js\") {\n\t\t\t\t\t\treturn `    ${param_name}: ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"bash\") {\n\t\t\t\t\t\treturn `    ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn `  ${represent_value(param as string, undefined, lang)}`;\n\t\t\t})\n\t\t\t.filter((d) => typeof d !== \"undefined\")\n\t\t\t.join(\",\\n\");\n\t\tif (params) {\n\t\t\tif (lang === \"py\") {\n\t\t\t\treturn `${params},\\n`;\n\t\t\t} else if (lang === \"js\") {\n\t\t\t\treturn `{\\n${params},\\n}`;\n\t\t\t} else if (lang === \"bash\") {\n\t\t\t\treturn `\\n${params}\\n`;\n\t\t\t}\n\t\t}\n\t\tif (lang === \"py\") {\n\t\t\treturn \"\";\n\t\t}\n\t\treturn \"\\n\";\n\t}\n\n\tonMount(async () => {\n\t\tconst data = await get_info();\n\t\tendpoints_info = data[\"named_endpoints\"];\n\t\tlet py_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"py\")\n\t\t);\n\t\tlet js_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"js\")\n\t\t);\n\t\tlet bash_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"bash\")\n\t\t);\n\t\tlet api_names: string[] = api_calls.map(\n\t\t\t(call) => dependencies[call.fn_index].api_name || \"\"\n\t\t);\n\t\tpy_zipped = py_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tjs_zipped = js_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tbash_zipped = bash_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\n\t\tawait tick();\n\n\t\tpython_code_text = python_code.innerText;\n\t});\n</script>\n\n<div class=\"container\">\n\t<!-- <EndpointDetail {named} api_name={dependency.api_name} /> -->\n\t<Block border_mode={\"focus\"}>\n\t\t{#if current_language === \"python\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code_text} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client, file\n\nclient = Client(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\n{#each py_zipped as { call, api_name }}<!--\n-->\nclient.<span class=\"highlight\"\n\t\t\t\t\t\t\t\t>predict(\n{call}  api_name=<span class=\"api-name\">\"/{api_name}\"</span>\n)\n</span>{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n\nconst app = await Client.connect(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\n\t\t\t\t\t{#each js_zipped as { call, api_name }}<!--\n\t\t\t\t\t-->\nawait client.predict(<span\n\t\t\t\t\t\t\t\tclass=\"api-name\">\n  \"/{api_name}\"</span\n\t\t\t\t\t\t\t>{#if call},\n\t\t\t\t\t\t\t{/if}{call});\n\t\t\t\t\t\t{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={bash_code}>\n\t\t\t\t\t{#each bash_zipped as { call, api_name }}\n\t\t\t\t\t\t<pre>curl -X POST {short_root}call/{api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"} \n\t\"data\": [{call}]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}' \\\n  | read EVENT_ID; curl -N {short_root}call/{api_name}/$EVENT_ID</pre>\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{/if}\n\t</Block>\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot toggle-right\" />\n\t</div>\n\tReturns {#if endpoint_returns.length > 1}\n\t\t{current_language == \"python\" ? \"tuple\" : \"list\"} of {endpoint_returns.length}\n\t\telements{:else}\n\t\t1 element{/if}\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, type, python_type, component, serializer }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p>\n\t\t\t\t{#if endpoint_returns.length > 1}\n\t\t\t\t\t<span class=\"code\">[{i}]</span>\n\t\t\t\t{/if}\n\t\t\t\t<span class=\"code highlight\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{:else}{js_returns[\n\t\t\t\t\t\t\ti\n\t\t\t\t\t\t].type}{/if}</span\n\t\t\t\t>\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe output value that appears in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->.\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t}\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tmargin-right: 10px;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-left: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\t/* eslint-disable */\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport NoApi from \"./NoApi.svelte\";\n\timport type { Client } from \"@gradio/client\";\n\timport type { Payload } from \"../types\";\n\n\timport ApiBanner from \"./ApiBanner.svelte\";\n\timport { BaseButton as Button } from \"@gradio/button\";\n\timport ParametersSnippet from \"./ParametersSnippet.svelte\";\n\timport InstallSnippet from \"./InstallSnippet.svelte\";\n\timport CodeSnippet from \"./CodeSnippet.svelte\";\n\timport RecordingSnippet from \"./RecordingSnippet.svelte\";\n\n\timport python from \"./img/python.svg\";\n\timport javascript from \"./img/javascript.svg\";\n\timport bash from \"./img/bash.svg\";\n\timport ResponseSnippet from \"./ResponseSnippet.svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let root: string;\n\texport let app: Awaited<ReturnType<typeof Client.connect>>;\n\texport let space_id: string | null;\n\texport let root_node: ComponentMeta;\n\texport let username: string | null;\n\n\tconst js_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-js-client\";\n\tconst py_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-python-client\";\n\tconst bash_docs =\n\t\t\"https://www.gradio.app/guides/querying-gradio-apps-with-curl\";\n\tconst spaces_docs_suffix = \"#connecting-to-a-hugging-face-space\";\n\n\tlet api_count = dependencies.filter(\n\t\t(dependency) => dependency.show_api\n\t).length;\n\n\tif (root === \"\") {\n\t\troot = location.protocol + \"//\" + location.host + location.pathname;\n\t}\n\tif (!root.endsWith(\"/\")) {\n\t\troot += \"/\";\n\t}\n\n\texport let api_calls: Payload[] = [];\n\tlet current_language: \"python\" | \"javascript\" | \"bash\" = \"python\";\n\n\tconst langs = [\n\t\t[\"python\", python],\n\t\t[\"javascript\", javascript],\n\t\t[\"bash\", bash]\n\t] as const;\n\n\tlet is_running = false;\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(root + \"info\");\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\tasync function get_js_info(): Promise<Record<string, any>> {\n\t\tlet js_api_info = await app.view_api();\n\t\treturn js_api_info;\n\t}\n\n\tlet info: {\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t};\n\n\tlet js_info: Record<string, any>;\n\n\tget_info().then((data) => {\n\t\tinfo = data;\n\t});\n\n\tget_js_info().then((js_api_info) => {\n\t\tjs_info = js_api_info;\n\t});\n\n\tconst dispatch = createEventDispatcher();\n\n\tonMount(() => {\n\t\tdocument.body.style.overflow = \"hidden\";\n\t\tif (\"parentIFrame\" in window) {\n\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t}\n\t\treturn () => {\n\t\t\tdocument.body.style.overflow = \"auto\";\n\t\t};\n\t});\n</script>\n\n{#if info}\n\t{#if api_count}\n\t\t<div class=\"banner-wrap\">\n\t\t\t<ApiBanner on:close root={space_id || root} {api_count} />\n\t\t</div>\n\n\t\t<div class=\"docs-wrap\">\n\t\t\t<div class=\"client-doc\">\n\t\t\t\t<p style=\"font-size: var(--text-lg);\">\n\t\t\t\t\tChoose a language to see the code snippets for interacting with the\n\t\t\t\t\tAPI.\n\t\t\t\t</p>\n\t\t\t</div>\n\t\t\t<div class=\"endpoint\">\n\t\t\t\t<div class=\"snippets\">\n\t\t\t\t\t{#each langs as [language, img]}\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\tclass=\"snippet\n\t\t\t\t\t\t{current_language === language ? 'current-lang' : 'inactive-lang'}\"\n\t\t\t\t\t\t\ton:click={() => (current_language = language)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<img src={img} alt=\"\" />\n\t\t\t\t\t\t\t{language}\n\t\t\t\t\t\t</li>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t\t{#if api_calls.length}\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<p\n\t\t\t\t\t\t\tid=\"num-recorded-api-calls\"\n\t\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 10px 0px;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🪄 Recorded API Calls <span class=\"api-count\"\n\t\t\t\t\t\t\t\t>[{api_calls.length}]</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</p>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tHere is the code snippet to replay the most recently recorded API\n\t\t\t\t\t\t\tcalls using the {current_language}\n\t\t\t\t\t\t\tclient.\n\t\t\t\t\t\t</p>\n\n\t\t\t\t\t\t<RecordingSnippet\n\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t{api_calls}\n\t\t\t\t\t\t\t{dependencies}\n\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\tshort_root={space_id || root}\n\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tNote: Some API calls only affect the UI, so when using the\n\t\t\t\t\t\t\tclients, the desired result may be achieved with only a subset of\n\t\t\t\t\t\t\tthe recorded calls.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<p\n\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 30px 0px 10px;\"\n\t\t\t\t\t>\n\t\t\t\t\t\tAPI Documentation\n\t\t\t\t\t</p>\n\t\t\t\t{:else}\n\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t{#if current_language == \"python\" || current_language == \"javascript\"}\n\t\t\t\t\t\t\t1. Install the\n\t\t\t\t\t\t\t<span style=\"text-transform:capitalize\">{current_language}</span>\n\t\t\t\t\t\t\tclient (<a\n\t\t\t\t\t\t\t\thref={current_language == \"python\" ? py_docs : js_docs}\n\t\t\t\t\t\t\t\ttarget=\"_blank\">docs</a\n\t\t\t\t\t\t\t>) if you don't already have it installed.\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t1. Confirm that you have cURL installed on your system.\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</p>\n\n\t\t\t\t\t<InstallSnippet {current_language} />\n\n\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t2. Find the API endpoint below corresponding to your desired\n\t\t\t\t\t\tfunction in the app. Copy the code snippet, replacing the\n\t\t\t\t\t\tplaceholder values with your own input data.\n\t\t\t\t\t\t{#if space_id}If this is a private Space, you may need to pass your\n\t\t\t\t\t\t\tHugging Face token as well (<a\n\t\t\t\t\t\t\t\thref={current_language == \"python\"\n\t\t\t\t\t\t\t\t\t? py_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t: current_language == \"javascript\"\n\t\t\t\t\t\t\t\t\t\t? js_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t\t: bash_docs}\n\t\t\t\t\t\t\t\tclass=\"underline\"\n\t\t\t\t\t\t\t\ttarget=\"_blank\">read more</a\n\t\t\t\t\t\t\t>).{/if}\n\n\t\t\t\t\t\tOr use the\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"loading-dot\"></div>\n\t\t\t\t\t\t\t<p class=\"self-baseline\">API Recorder</p>\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\tto automatically generate your API requests.\n\t\t\t\t\t\t{#if current_language == \"bash\"}<br />&nbsp;<br />Making a\n\t\t\t\t\t\t\tprediction and getting a result requires\n\t\t\t\t\t\t\t<strong>2 requests</strong>: a\n\t\t\t\t\t\t\t<code>POST</code>\n\t\t\t\t\t\t\tand a <code>GET</code> request. The <code>POST</code> request\n\t\t\t\t\t\t\treturns an <code>EVENT_ID</code>, which is used in the second\n\t\t\t\t\t\t\t<code>GET</code> request to fetch the results. In these snippets,\n\t\t\t\t\t\t\twe've used <code>awk</code> and <code>read</code> to parse the\n\t\t\t\t\t\t\tresults, combining these two requests into one command for ease of\n\t\t\t\t\t\t\tuse. {#if username !== null}\n\t\t\t\t\t\t\t\tNote: connecting to an authenticated app requires an additional\n\t\t\t\t\t\t\t\trequest.{/if} See\n\t\t\t\t\t\t\t<a href={bash_docs} target=\"_blank\">curl docs</a>.\n\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t<!-- <span\n\t\t\t\t\t\t\tid=\"api-recorder\"\n\t\t\t\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t\t>🪄 API Recorder</span\n\t\t\t\t\t\t> to automatically generate your API requests! -->\n\t\t\t\t\t</p>\n\t\t\t\t{/if}\n\n\t\t\t\t{#each dependencies as dependency, dependency_index}\n\t\t\t\t\t{#if dependency.show_api && info.named_endpoints[\"/\" + dependency.api_name]}\n\t\t\t\t\t\t<div class=\"endpoint-container\">\n\t\t\t\t\t\t\t<CodeSnippet\n\t\t\t\t\t\t\t\tnamed={true}\n\t\t\t\t\t\t\t\tendpoint_parameters={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t{dependency}\n\t\t\t\t\t\t\t\t{dependency_index}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t{space_id}\n\t\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<ParametersSnippet\n\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t.parameters}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<ResponseSnippet\n\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].returns}\n\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t.returns}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t{:else}\n\t\t<NoApi {root} on:close />\n\t{/if}\n{/if}\n\n<style>\n\t.banner-wrap {\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tpadding: var(--size-4) var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t@media (--screen-md) {\n\t\t.banner-wrap {\n\t\t\tfont-size: var(--text-xl);\n\t\t}\n\t}\n\n\t.docs-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t}\n\n\t.endpoint {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-6);\n\t\tpadding-top: var(--size-1);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.client-doc {\n\t\tpadding-top: var(--size-6);\n\t\tpadding-right: var(--size-6);\n\t\tpadding-left: var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.library {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-size: var(--text-md);\n\t\ttext-decoration: none;\n\t}\n\n\t.snippets {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.snippet {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-1) var(--size-1-5);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t\ttext-transform: capitalize;\n\t}\n\n\t.current-lang {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-lang {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-lang:hover,\n\t.inactive-lang:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.snippet img {\n\t\tmargin-right: var(--size-1-5);\n\t\twidth: var(--size-3);\n\t}\n\n\t.header {\n\t\tmargin-top: var(--size-6);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.endpoint-container {\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t\tborder: 1px solid var(--body-text-color);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-3);\n\t\tpadding-top: 0;\n\t}\n\n\ta {\n\t\ttext-decoration: underline;\n\t}\n\n\tp.padded {\n\t\tpadding: 15px 0px;\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t#api-recorder {\n\t\tborder: 1px solid var(--color-accent);\n\t\tbackground-color: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-2);\n\t\tborder-radius: var(--size-1);\n\t\tcursor: pointer;\n\t}\n\n\tcode {\n\t\tfont-size: var(--text-md);\n\t}\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.25rem;\n\t}\n\t:global(.docs-wrap .sm.secondary) {\n\t\tpadding-top: 1px;\n\t\tpadding-bottom: 1px;\n\t}\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.api-count {\n\t\tfont-weight: bold;\n\t\tcolor: #fd7b00;\n\t\talign-self: baseline;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-md);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Payload, Dependency } from \"../types\";\n\timport { BaseButton as <PERSON><PERSON> } from \"@gradio/button\";\n\n\texport let api_calls: Payload[] = [];\n\texport let dependencies: Dependency[];\n</script>\n\n<div id=\"api-recorder\">\n\t<Button size=\"sm\" variant=\"secondary\">\n\t\t<div class=\"loading-dot self-baseline\"></div>\n\t\t<p class=\"self-baseline\">Recording API Calls:</p>\n\t\t<p class=\"self-baseline api-section\">\n\t\t\t<span class=\"api-count\">\n\t\t\t\t[{api_calls.length}]\n\t\t\t</span>\n\t\t\t{#if api_calls.length > 0}\n\t\t\t\t<span class=\"api-name\"\n\t\t\t\t\t>/{dependencies[api_calls[api_calls.length - 1].fn_index]\n\t\t\t\t\t\t.api_name}</span\n\t\t\t\t>\n\t\t\t{/if}\n\t\t</p>\n\t</Button>\n</div>\n\n<style>\n\t.api-name {\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t\tcolor: #fd7b00;\n\t}\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tanimation: loading-dot 2s infinite linear;\n\t\tanimation-delay: 0.25s;\n\t\tmargin-left: 0.25rem;\n\t\tmargin-right: 0.5rem;\n\t}\n\t:global(.docs-wrap .sm.secondary) {\n\t\tpadding-top: 1px;\n\t\tpadding-bottom: 1px;\n\t}\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t@keyframes loading-dot {\n\t\t0% {\n\t\t\tbox-shadow: 9999px 0 0 -1px;\n\t\t}\n\t\t50% {\n\t\t\tbox-shadow: 9999px 0 0 2px;\n\t\t}\n\t\t100% {\n\t\t\tbox-shadow: 9999px 0 0 -1px;\n\t\t}\n\t}\n\t.api-count {\n\t\tfont-weight: bold;\n\t\tcolor: #fd7b00;\n\t\talign-self: baseline;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\t.api-section {\n\t\tmargin-left: 4px;\n\t}\n</style>\n", "import { format } from \"svelte-i18n\";\nimport { get } from \"svelte/store\";\nexport { Gradio } from \"@gradio/utils\";\n\nexport const formatter = get(format);\n\nexport type I18nFormatter = typeof formatter;\n", "<svelte:options immutable={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"./gradio_helper\";\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport type { SvelteComponent, ComponentType } from \"svelte\";\n\t// @ts-ignore\n\timport { bind, binding_callbacks } from \"svelte/internal\";\n\n\texport let root: string;\n\texport let component: ComponentMeta[\"component\"];\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let instance: ComponentMeta[\"instance\"];\n\texport let value: any;\n\texport let gradio: Gradio;\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let _id: number;\n\n\tconst s = (id: number, p: string, v: any): CustomEvent =>\n\t\tnew CustomEvent(\"prop_change\", { detail: { id, prop: p, value: v } });\n\n\tfunction wrap(\n\t\tcomponent: ComponentType<SvelteComponent>\n\t): ComponentType<SvelteComponent> {\n\t\tconst ProxiedMyClass = new Proxy(component, {\n\t\t\tconstruct(_target, args: Record<string, any>[]) {\n\t\t\t\t//@ts-ignore\n\t\t\t\tconst instance = new _target(...args);\n\t\t\t\tconst props = Object.keys(instance.$$.props);\n\n\t\t\t\tfunction report(props: string) {\n\t\t\t\t\treturn function (propargs: any) {\n\t\t\t\t\t\tconst ev = s(_id, props, propargs);\n\t\t\t\t\t\ttarget.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tprops.forEach((v) => {\n\t\t\t\t\tbinding_callbacks.push(() => bind(instance, v, report(v)));\n\t\t\t\t});\n\n\t\t\t\treturn instance;\n\t\t\t}\n\t\t});\n\n\t\treturn ProxiedMyClass;\n\t}\n\n\tconst _component = wrap(component);\n</script>\n\n<svelte:component\n\tthis={_component}\n\tbind:this={instance}\n\tbind:value\n\ton:prop_change\n\t{elem_id}\n\t{elem_classes}\n\t{target}\n\t{...$$restProps}\n\t{theme_mode}\n\t{root}\n\t{gradio}\n>\n\t<slot />\n</svelte:component>\n", "<script lang=\"ts\">\n\timport { Gradio, formatter } from \"./gradio_helper\";\n\timport { onMount, createEventDispatcher, setContext } from \"svelte\";\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport type { Client } from \"@gradio/client\";\n\timport RenderComponent from \"./RenderComponent.svelte\";\n\timport { load_component } from \"virtual:component-loader\";\n\n\texport let root: string;\n\n\texport let node: ComponentMeta;\n\texport let parent: string | null = null;\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let version: string;\n\texport let autoscroll: boolean;\n\texport let max_file_size: number | null;\n\texport let client: Client;\n\n\tconst dispatch = createEventDispatcher<{ mount: number; destroy: number }>();\n\tlet filtered_children: ComponentMeta[] = [];\n\n\tonMount(() => {\n\t\tdispatch(\"mount\", node.id);\n\n\t\tfor (const child of filtered_children) {\n\t\t\tdispatch(\"mount\", child.id);\n\t\t}\n\n\t\treturn () => {\n\t\t\tdispatch(\"destroy\", node.id);\n\n\t\t\tfor (const child of filtered_children) {\n\t\t\t\tdispatch(\"mount\", child.id);\n\t\t\t}\n\t\t};\n\t});\n\n\t$: node.children =\n\t\tnode.children &&\n\t\tnode.children.filter((v) => {\n\t\t\tconst valid_node = node.type !== \"statustracker\";\n\t\t\tif (!valid_node) {\n\t\t\t\tfiltered_children.push(v);\n\t\t\t}\n\t\t\treturn valid_node;\n\t\t});\n\n\tsetContext(\"BLOCK_KEY\", parent);\n\n\t$: {\n\t\tif (node.type === \"form\") {\n\t\t\tif (node.children?.every((c) => !c.props.visible)) {\n\t\t\t\tnode.props.visible = false;\n\t\t\t} else {\n\t\t\t\tnode.props.visible = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: gradio_class = new Gradio<Record<string, any>>(\n\t\tnode.id,\n\t\ttarget,\n\t\ttheme_mode,\n\t\tversion,\n\t\troot,\n\t\tautoscroll,\n\t\tmax_file_size,\n\t\tformatter,\n\t\tclient,\n\t\tload_component\n\t);\n</script>\n\n<RenderComponent\n\t_id={node.id}\n\tcomponent={node.component}\n\tbind:instance={node.instance}\n\tbind:value={node.props.value}\n\telem_id={(\"elem_id\" in node.props && node.props.elem_id) ||\n\t\t`component-${node.id}`}\n\telem_classes={(\"elem_classes\" in node.props && node.props.elem_classes) || []}\n\t{target}\n\t{...node.props}\n\t{theme_mode}\n\t{root}\n\tgradio={gradio_class}\n>\n\t{#if node.children && node.children.length}\n\t\t{#each node.children as _node (_node.id)}\n\t\t\t<svelte:self\n\t\t\t\tnode={_node}\n\t\t\t\tcomponent={_node.component}\n\t\t\t\t{target}\n\t\t\t\tid={_node.id}\n\t\t\t\t{root}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:destroy\n\t\t\t\ton:mount\n\t\t\t\t{max_file_size}\n\t\t\t\t{client}\n\t\t\t/>\n\t\t{/each}\n\t{/if}\n</RenderComponent>\n", "<script lang=\"ts\">\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { Client } from \"@gradio/client\";\n\timport Render from \"./Render.svelte\";\n\n\texport let rootNode: any;\n\texport let root: any;\n\texport let target: any;\n\texport let theme_mode: any;\n\texport let version: any;\n\texport let autoscroll: boolean;\n\texport let max_file_size: number | null = null;\n\texport let client: Client;\n\n\tconst dispatch = createEventDispatcher<{ mount?: never }>();\n\tonMount(() => {\n\t\tdispatch(\"mount\");\n\t});\n</script>\n\n<Render\n\tnode={rootNode}\n\t{root}\n\t{target}\n\t{theme_mode}\n\t{version}\n\t{autoscroll}\n\t{max_file_size}\n\t{client}\n/>\n", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e\"", "import { type Writable, writable, get } from \"svelte/store\";\n\nexport interface LoadingStatus {\n\teta: number | null;\n\tstatus: \"pending\" | \"error\" | \"complete\" | \"generating\";\n\tqueue: boolean;\n\tqueue_position: number | null;\n\tqueue_size?: number;\n\tfn_index: number;\n\tmessage?: string | null;\n\tscroll_to_output?: boolean;\n\tshow_progress?: \"full\" | \"minimal\" | \"hidden\";\n\tprogress?: {\n\t\tprogress: number | null;\n\t\tindex: number | null;\n\t\tlength: number | null;\n\t\tunit: string | null;\n\t\tdesc: string | null;\n\t}[];\n}\n\nexport type LoadingStatusCollection = Record<number, LoadingStatus>;\n\ninterface LoadingStatusStore {\n\tupdate: (status: LoadingStatus) => void;\n\tsubscribe: Writable<LoadingStatusCollection>[\"subscribe\"];\n\tregister: (index: number, inputs: number[], outputs: number[]) => void;\n\tget_status_for_fn: (i: number) => LoadingStatus[\"status\"];\n\tget_inputs_to_update: () => Map<number, string>;\n}\n\nexport function create_loading_status_store(): LoadingStatusStore {\n\tconst store = writable<LoadingStatusCollection>({});\n\n\tconst fn_inputs: Record<number, number[]> = {};\n\tconst fn_outputs: Record<number, number[]> = {};\n\tconst pending_outputs = new Map<number, number>();\n\tconst pending_inputs = new Map<number, number>();\n\n\tconst inputs_to_update = new Map<number, string>();\n\tconst fn_status: Record<number, LoadingStatus[\"status\"]> = {};\n\n\tfunction update({\n\t\tfn_index,\n\t\tstatus,\n\t\tqueue = true,\n\t\tsize,\n\t\tposition = null,\n\t\teta = null,\n\t\tmessage = null,\n\t\tprogress\n\t}: {\n\t\tfn_index: LoadingStatus[\"fn_index\"];\n\t\tstatus: LoadingStatus[\"status\"];\n\t\tqueue?: LoadingStatus[\"queue\"];\n\t\tsize?: LoadingStatus[\"queue_size\"];\n\t\tposition?: LoadingStatus[\"queue_position\"];\n\t\teta?: LoadingStatus[\"eta\"];\n\t\tmessage?: LoadingStatus[\"message\"];\n\t\tprogress?: LoadingStatus[\"progress\"];\n\t}): void {\n\t\tconst outputs = fn_outputs[fn_index];\n\t\tconst inputs = fn_inputs[fn_index];\n\t\tconst last_status = fn_status[fn_index];\n\n\t\tconst outputs_to_update = outputs.map((id) => {\n\t\t\tlet new_status: LoadingStatus[\"status\"];\n\n\t\t\tconst pending_count = pending_outputs.get(id) || 0;\n\n\t\t\t// from (pending -> error) | complete - decrement pending count\n\t\t\tif (last_status === \"pending\" && status !== \"pending\") {\n\t\t\t\tlet new_count = pending_count - 1;\n\n\t\t\t\tpending_outputs.set(id, new_count < 0 ? 0 : new_count);\n\n\t\t\t\tnew_status = new_count > 0 ? \"pending\" : status;\n\n\t\t\t\t// from pending -> pending - do nothing\n\t\t\t} else if (last_status === \"pending\" && status === \"pending\") {\n\t\t\t\tnew_status = \"pending\";\n\n\t\t\t\t// (error | complete) -> pending - - increment pending count\n\t\t\t} else if (last_status !== \"pending\" && status === \"pending\") {\n\t\t\t\tnew_status = \"pending\";\n\t\t\t\tpending_outputs.set(id, pending_count + 1);\n\t\t\t} else {\n\t\t\t\tnew_status = status;\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tid,\n\t\t\t\tqueue_position: position,\n\t\t\t\tqueue_size: size,\n\t\t\t\teta: eta,\n\t\t\t\tstatus: new_status,\n\t\t\t\tmessage: message,\n\t\t\t\tprogress: progress\n\t\t\t};\n\t\t});\n\n\t\tinputs.forEach((id) => {\n\t\t\tconst pending_count = pending_inputs.get(id) || 0;\n\n\t\t\t// from (pending -> error) | complete - decrement pending count\n\t\t\tif (last_status === \"pending\" && status !== \"pending\") {\n\t\t\t\tlet new_count = pending_count - 1;\n\t\t\t\tpending_inputs.set(id, new_count < 0 ? 0 : new_count);\n\t\t\t\tinputs_to_update.set(id, status);\n\t\t\t} else if (last_status !== \"pending\" && status === \"pending\") {\n\t\t\t\tpending_inputs.set(id, pending_count + 1);\n\t\t\t\tinputs_to_update.set(id, status);\n\t\t\t} else {\n\t\t\t\tinputs_to_update.delete(id);\n\t\t\t}\n\t\t});\n\n\t\tstore.update((outputs: LoadingStatusCollection) => {\n\t\t\toutputs_to_update.forEach(\n\t\t\t\t({\n\t\t\t\t\tid,\n\t\t\t\t\tqueue_position,\n\t\t\t\t\tqueue_size,\n\t\t\t\t\teta,\n\t\t\t\t\tstatus,\n\t\t\t\t\tmessage,\n\t\t\t\t\tprogress\n\t\t\t\t}) => {\n\t\t\t\t\toutputs[id] = {\n\t\t\t\t\t\tqueue: queue,\n\t\t\t\t\t\tqueue_size: queue_size,\n\t\t\t\t\t\tqueue_position: queue_position,\n\t\t\t\t\t\teta: eta,\n\t\t\t\t\t\tmessage: message,\n\t\t\t\t\t\tprogress,\n\t\t\t\t\t\tstatus,\n\t\t\t\t\t\tfn_index\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t);\n\n\t\t\treturn outputs;\n\t\t});\n\t\tfn_status[fn_index] = status;\n\t}\n\n\tfunction register(index: number, inputs: number[], outputs: number[]): void {\n\t\tfn_inputs[index] = inputs;\n\t\tfn_outputs[index] = outputs;\n\t}\n\n\treturn {\n\t\tupdate,\n\t\tregister,\n\t\tsubscribe: store.subscribe,\n\t\tget_status_for_fn(i: number) {\n\t\t\treturn fn_status[i];\n\t\t},\n\t\tget_inputs_to_update() {\n\t\t\treturn inputs_to_update;\n\t\t}\n\t};\n}\n\nexport type LoadingStatusType = ReturnType<typeof create_loading_status_store>;\n", "import { writable, type Writable, get } from \"svelte/store\";\nimport type {\n\tComponentMeta,\n\tDependency,\n\tLayoutNode,\n\tTargetMap,\n\tLoadingComponent\n} from \"./types\";\nimport { load_component } from \"virtual:component-loader\";\nimport type { client_return } from \"@gradio/client\";\nimport { create_loading_status_store } from \"./stores\";\nimport { _ } from \"svelte-i18n\";\n\nexport interface UpdateTransaction {\n\tid: number;\n\tvalue: any;\n\tprop: string;\n}\n\nlet pending_updates: UpdateTransaction[][] = [];\n\n/**\n * Create a store with the layout and a map of targets\n * @returns A store with the layout and a map of targets\n */\nexport function create_components(): {\n\tlayout: Writable<ComponentMeta>;\n\ttargets: Writable<TargetMap>;\n\tupdate_value: (updates: UpdateTransaction[]) => void;\n\tget_data: (id: number) => any | Promise<any>;\n\tloading_status: ReturnType<typeof create_loading_status_store>;\n\tscheduled_updates: Writable<boolean>;\n\tcreate_layout: (args: {\n\t\tapp: client_return;\n\t\tcomponents: ComponentMeta[];\n\t\tlayout: LayoutNode;\n\t\tdependencies: Dependency[];\n\t\troot: string;\n\t\toptions: {\n\t\t\tfill_height: boolean;\n\t\t};\n\t}) => void;\n\trerender_layout: (args: {\n\t\trender_id: number;\n\t\tcomponents: ComponentMeta[];\n\t\tlayout: LayoutNode;\n\t\troot: string;\n\t\tdependencies: Dependency[];\n\t}) => void;\n} {\n\tlet _component_map: Map<number, ComponentMeta>;\n\n\tlet target_map: Writable<TargetMap> = writable({});\n\tlet _target_map: TargetMap = {};\n\tlet inputs: Set<number>;\n\tlet outputs: Set<number>;\n\tlet constructor_map: Map<ComponentMeta[\"type\"], LoadingComponent>;\n\tlet instance_map: { [id: number]: ComponentMeta };\n\tlet loading_status: ReturnType<typeof create_loading_status_store> =\n\t\tcreate_loading_status_store();\n\tconst layout_store: Writable<ComponentMeta> = writable();\n\tlet _components: ComponentMeta[] = [];\n\tlet app: client_return;\n\tlet keyed_component_values: Record<string | number, any> = {};\n\tlet _rootNode: ComponentMeta;\n\n\tfunction create_layout({\n\t\tapp: _app,\n\t\tcomponents,\n\t\tlayout,\n\t\tdependencies,\n\t\troot,\n\t\toptions\n\t}: {\n\t\tapp: client_return;\n\t\tcomponents: ComponentMeta[];\n\t\tlayout: LayoutNode;\n\t\tdependencies: Dependency[];\n\t\troot: string;\n\t\toptions: {\n\t\t\tfill_height: boolean;\n\t\t};\n\t}): void {\n\t\tapp = _app;\n\t\tstore_keyed_values(_components);\n\n\t\t_components = components;\n\t\tinputs = new Set();\n\t\toutputs = new Set();\n\t\tpending_updates = [];\n\t\tconstructor_map = new Map();\n\t\t_component_map = new Map();\n\n\t\tinstance_map = {};\n\n\t\t_rootNode = {\n\t\t\tid: layout.id,\n\t\t\ttype: \"column\",\n\t\t\tprops: { interactive: false, scale: options.fill_height ? 1 : null },\n\t\t\thas_modes: false,\n\t\t\tinstance: null as unknown as ComponentMeta[\"instance\"],\n\t\t\tcomponent: null as unknown as ComponentMeta[\"component\"],\n\t\t\tcomponent_class_id: \"\",\n\t\t\tkey: null\n\t\t};\n\n\t\tcomponents.push(_rootNode);\n\n\t\tdependencies.forEach((dep) => {\n\t\t\tloading_status.register(dep.id, dep.inputs, dep.outputs);\n\t\t\tdep.frontend_fn = process_frontend_fn(\n\t\t\t\tdep.js,\n\t\t\t\t!!dep.backend_fn,\n\t\t\t\tdep.inputs.length,\n\t\t\t\tdep.outputs.length\n\t\t\t);\n\t\t\tcreate_target_meta(dep.targets, dep.id, _target_map);\n\t\t\tget_inputs_outputs(dep, inputs, outputs);\n\t\t});\n\n\t\ttarget_map.set(_target_map);\n\n\t\tconstructor_map = preload_all_components(components, root);\n\n\t\tinstance_map = components.reduce(\n\t\t\t(acc, c) => {\n\t\t\t\tacc[c.id] = c;\n\t\t\t\treturn acc;\n\t\t\t},\n\t\t\t{} as { [id: number]: ComponentMeta }\n\t\t);\n\n\t\twalk_layout(layout, root).then(() => {\n\t\t\tlayout_store.set(_rootNode);\n\t\t});\n\t}\n\n\t/**\n\t * Rerender the layout when the config has been modified to attach new components\n\t */\n\tfunction rerender_layout({\n\t\trender_id,\n\t\tcomponents,\n\t\tlayout,\n\t\troot,\n\t\tdependencies\n\t}: {\n\t\trender_id: number;\n\t\tcomponents: ComponentMeta[];\n\t\tlayout: LayoutNode;\n\t\troot: string;\n\t\tdependencies: Dependency[];\n\t}): void {\n\t\tlet _constructor_map = preload_all_components(components, root);\n\t\t_constructor_map.forEach((v, k) => {\n\t\t\tconstructor_map.set(k, v);\n\t\t});\n\n\t\t_target_map = {};\n\n\t\tdependencies.forEach((dep) => {\n\t\t\tloading_status.register(dep.id, dep.inputs, dep.outputs);\n\t\t\tdep.frontend_fn = process_frontend_fn(\n\t\t\t\tdep.js,\n\t\t\t\t!!dep.backend_fn,\n\t\t\t\tdep.inputs.length,\n\t\t\t\tdep.outputs.length\n\t\t\t);\n\t\t\tcreate_target_meta(dep.targets, dep.id, _target_map);\n\t\t\tget_inputs_outputs(dep, inputs, outputs);\n\t\t});\n\n\t\ttarget_map.set(_target_map);\n\n\t\tlet current_element = instance_map[layout.id];\n\t\tlet all_current_children: ComponentMeta[] = [];\n\t\tconst add_to_current_children = (component: ComponentMeta): void => {\n\t\t\tall_current_children.push(component);\n\t\t\tif (component.children) {\n\t\t\t\tcomponent.children.forEach((child) => {\n\t\t\t\t\tadd_to_current_children(child);\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\t\tadd_to_current_children(current_element);\n\t\tstore_keyed_values(all_current_children);\n\n\t\tObject.entries(instance_map).forEach(([id, component]) => {\n\t\t\tlet _id = Number(id);\n\t\t\tif (component.rendered_in === render_id) {\n\t\t\t\tdelete instance_map[_id];\n\t\t\t\tif (_component_map.has(_id)) {\n\t\t\t\t\t_component_map.delete(_id);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tcomponents.forEach((c) => {\n\t\t\tinstance_map[c.id] = c;\n\t\t\t_component_map.set(c.id, c);\n\t\t});\n\t\tif (current_element.parent) {\n\t\t\tcurrent_element.parent.children![\n\t\t\t\tcurrent_element.parent.children!.indexOf(current_element)\n\t\t\t] = instance_map[layout.id];\n\t\t}\n\n\t\twalk_layout(layout, root, current_element.parent).then(() => {\n\t\t\tlayout_store.set(_rootNode);\n\t\t});\n\t}\n\n\tasync function walk_layout(\n\t\tnode: LayoutNode,\n\t\troot: string,\n\t\tparent?: ComponentMeta\n\t): Promise<ComponentMeta> {\n\t\tconst instance = instance_map[node.id];\n\n\t\tinstance.component = (await constructor_map.get(\n\t\t\tinstance.component_class_id || instance.type\n\t\t))!?.default;\n\t\tinstance.parent = parent;\n\n\t\tif (instance.type === \"dataset\") {\n\t\t\tinstance.props.component_map = get_component(\n\t\t\t\tinstance.type,\n\t\t\t\tinstance.component_class_id,\n\t\t\t\troot,\n\t\t\t\t_components,\n\t\t\t\tinstance.props.components\n\t\t\t).example_components;\n\t\t}\n\n\t\tif (_target_map[instance.id]) {\n\t\t\tinstance.props.attached_events = Object.keys(_target_map[instance.id]);\n\t\t}\n\n\t\tinstance.props.interactive = determine_interactivity(\n\t\t\tinstance.id,\n\t\t\tinstance.props.interactive,\n\t\t\tinstance.props.value,\n\t\t\tinputs,\n\t\t\toutputs\n\t\t);\n\n\t\tinstance.props.server = process_server_fn(\n\t\t\tinstance.id,\n\t\t\tinstance.props.server_fns,\n\t\t\tapp\n\t\t);\n\n\t\tif (\n\t\t\tinstance.key != null &&\n\t\t\tkeyed_component_values[instance.key] !== undefined\n\t\t) {\n\t\t\tinstance.props.value = keyed_component_values[instance.key];\n\t\t}\n\n\t\t_component_map.set(instance.id, instance);\n\n\t\tif (node.children) {\n\t\t\tinstance.children = await Promise.all(\n\t\t\t\tnode.children.map((v) => walk_layout(v, root, instance))\n\t\t\t);\n\t\t}\n\n\t\treturn instance;\n\t}\n\n\tlet update_scheduled = false;\n\tlet update_scheduled_store = writable(false);\n\n\tfunction store_keyed_values(components: ComponentMeta[]): void {\n\t\tcomponents.forEach((c) => {\n\t\t\tif (c.key != null) {\n\t\t\t\tkeyed_component_values[c.key] = c.props.value;\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction flush(): void {\n\t\tlayout_store.update((layout) => {\n\t\t\tfor (let i = 0; i < pending_updates.length; i++) {\n\t\t\t\tfor (let j = 0; j < pending_updates[i].length; j++) {\n\t\t\t\t\tconst update = pending_updates[i][j];\n\t\t\t\t\tif (!update) continue;\n\t\t\t\t\tconst instance = instance_map[update.id];\n\t\t\t\t\tif (!instance) continue;\n\t\t\t\t\tlet new_value;\n\t\t\t\t\tif (update.value instanceof Map) new_value = new Map(update.value);\n\t\t\t\t\telse if (update.value instanceof Set)\n\t\t\t\t\t\tnew_value = new Set(update.value);\n\t\t\t\t\telse if (Array.isArray(update.value)) new_value = [...update.value];\n\t\t\t\t\telse if (update.value === null) new_value = null;\n\t\t\t\t\telse if (typeof update.value === \"object\")\n\t\t\t\t\t\tnew_value = { ...update.value };\n\t\t\t\t\telse new_value = update.value;\n\t\t\t\t\tinstance.props[update.prop] = new_value;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn layout;\n\t\t});\n\n\t\tpending_updates = [];\n\t\tupdate_scheduled = false;\n\t\tupdate_scheduled_store.set(false);\n\t}\n\n\tfunction update_value(updates: UpdateTransaction[] | undefined): void {\n\t\tif (!updates) return;\n\t\tpending_updates.push(updates);\n\n\t\tif (!update_scheduled) {\n\t\t\tupdate_scheduled = true;\n\t\t\tupdate_scheduled_store.set(true);\n\t\t\trequestAnimationFrame(flush);\n\t\t}\n\t}\n\n\tfunction get_data(id: number): any | Promise<any> {\n\t\tconst comp = _component_map.get(id);\n\t\tif (!comp) {\n\t\t\treturn null;\n\t\t}\n\t\tif (comp.instance.get_value) {\n\t\t\treturn comp.instance.get_value() as Promise<any>;\n\t\t}\n\t\treturn comp.props.value;\n\t}\n\n\treturn {\n\t\tlayout: layout_store,\n\t\ttargets: target_map,\n\t\tupdate_value,\n\t\tget_data,\n\t\tloading_status,\n\t\tscheduled_updates: update_scheduled_store,\n\t\tcreate_layout: (...args) =>\n\t\t\trequestAnimationFrame(() => create_layout(...args)),\n\t\trerender_layout\n\t};\n}\n\n/** An async version of 'new Function' */\nexport const AsyncFunction: new (\n\t...args: string[]\n) => (...args: any[]) => Promise<any> = Object.getPrototypeOf(\n\tasync function () {}\n).constructor;\n\n/**\n * Takes a string of source code and returns a function that can be called with arguments\n * @param source the source code\n * @param backend_fn if there is also a backend function\n * @param input_length the number of inputs\n * @param output_length the number of outputs\n * @returns The function, or null if the source code is invalid or missing\n */\nexport function process_frontend_fn(\n\tsource: string | null | undefined | false,\n\tbackend_fn: boolean,\n\tinput_length: number,\n\toutput_length: number\n): ((...args: unknown[]) => Promise<unknown[]>) | null {\n\tif (!source) return null;\n\n\tconst wrap = backend_fn ? input_length === 1 : output_length === 1;\n\ttry {\n\t\treturn new AsyncFunction(\n\t\t\t\"__fn_args\",\n\t\t\t`  let result = await (${source})(...__fn_args);\n  if (typeof result === \"undefined\") return [];\n  return (${wrap} && !Array.isArray(result)) ? [result] : result;`\n\t\t);\n\t} catch (e) {\n\t\tconsole.error(\"Could not parse custom js method.\");\n\t\tconsole.error(e);\n\t\treturn null;\n\t}\n}\n\n/**\n * `Dependency.targets` is an array of `[id, trigger]` pairs with the ids as the `fn_id`.\n * This function take a single list of `Dependency.targets` and add those to the target_map.\n * @param targets the targets array\n * @param fn_id the function index\n * @param target_map the target map\n * @returns the tagert map\n */\nexport function create_target_meta(\n\ttargets: Dependency[\"targets\"],\n\tfn_id: number,\n\ttarget_map: TargetMap\n): TargetMap {\n\ttargets.forEach(([id, trigger]) => {\n\t\tif (!target_map[id]) {\n\t\t\ttarget_map[id] = {};\n\t\t}\n\t\tif (\n\t\t\ttarget_map[id]?.[trigger] &&\n\t\t\t!target_map[id]?.[trigger].includes(fn_id)\n\t\t) {\n\t\t\ttarget_map[id][trigger].push(fn_id);\n\t\t} else {\n\t\t\ttarget_map[id][trigger] = [fn_id];\n\t\t}\n\t});\n\n\treturn target_map;\n}\n\n/**\n * Get all component ids that are an input or output of a dependency\n * @param dep the dependency\n * @param inputs the set of inputs\n * @param outputs the set of outputs\n * @returns a tuple of the inputs and outputs\n */\nexport function get_inputs_outputs(\n\tdep: Dependency,\n\tinputs: Set<number>,\n\toutputs: Set<number>\n): [Set<number>, Set<number>] {\n\tdep.inputs.forEach((input) => inputs.add(input));\n\tdep.outputs.forEach((output) => outputs.add(output));\n\treturn [inputs, outputs];\n}\n\n/**\n * Check if a value is not a default value\n * @param value the value to check\n * @returns default value boolean\n */\nfunction has_no_default_value(value: any): boolean {\n\treturn (\n\t\t(Array.isArray(value) && value.length === 0) ||\n\t\tvalue === \"\" ||\n\t\tvalue === 0 ||\n\t\t!value\n\t);\n}\n\n/**\n * Determines if a component is interactive\n * @param id component id\n * @param interactive_prop value of the interactive prop\n * @param value the main value of the component\n * @param inputs set of ids that are inputs to a dependency\n * @param outputs set of ids that are outputs to a dependency\n * @returns if the component is interactive\n */\nexport function determine_interactivity(\n\tid: number,\n\tinteractive_prop: boolean | undefined,\n\tvalue: any,\n\tinputs: Set<number>,\n\toutputs: Set<number>\n): boolean {\n\tif (interactive_prop === false) {\n\t\treturn false;\n\t} else if (interactive_prop === true) {\n\t\treturn true;\n\t} else if (\n\t\tinputs.has(id) ||\n\t\t(!outputs.has(id) && has_no_default_value(value))\n\t) {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\ntype ServerFunctions = Record<string, (...args: any[]) => Promise<any>>;\n\n/**\n * Process the server function names and return a dictionary of functions\n * @param id the component id\n * @param server_fns the server function names\n * @param app the client instance\n * @returns the actual server functions\n */\nexport function process_server_fn(\n\tid: number,\n\tserver_fns: string[] | undefined,\n\tapp: client_return\n): ServerFunctions {\n\tif (!server_fns) {\n\t\treturn {};\n\t}\n\treturn server_fns.reduce((acc, fn: string) => {\n\t\tacc[fn] = async (...args: any[]) => {\n\t\t\tif (args.length === 1) {\n\t\t\t\targs = args[0];\n\t\t\t}\n\t\t\tconst result = await app.component_server(id, fn, args);\n\t\t\treturn result;\n\t\t};\n\t\treturn acc;\n\t}, {} as ServerFunctions);\n}\n\n/**\n * Get a component from the backend\n * @param type the type of the component\n * @param class_id the class id of the component\n * @param root the root url of the app\n * @param components the list of component metadata\n * @param example_components the list of example components\n * @returns the component and its name\n */\nexport function get_component(\n\ttype: string,\n\tclass_id: string,\n\troot: string,\n\tcomponents: ComponentMeta[],\n\texample_components?: string[]\n): {\n\tcomponent: LoadingComponent;\n\tname: ComponentMeta[\"type\"];\n\texample_components?: Map<ComponentMeta[\"type\"], LoadingComponent>;\n} {\n\tlet example_component_map: Map<ComponentMeta[\"type\"], LoadingComponent> =\n\t\tnew Map();\n\tif (type === \"dataset\" && example_components) {\n\t\t(example_components as string[]).forEach((name: string) => {\n\t\t\tif (example_component_map.has(name)) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet _c;\n\n\t\t\tconst matching_component = components.find((c) => c.type === name);\n\t\t\tif (matching_component) {\n\t\t\t\t_c = load_component({\n\t\t\t\t\tapi_url: root,\n\t\t\t\t\tname,\n\t\t\t\t\tid: matching_component.component_class_id,\n\t\t\t\t\tvariant: \"example\"\n\t\t\t\t});\n\t\t\t\texample_component_map.set(name, _c.component);\n\t\t\t}\n\t\t});\n\t}\n\n\tconst _c = load_component({\n\t\tapi_url: root,\n\t\tname: type,\n\t\tid: class_id,\n\t\tvariant: \"component\"\n\t});\n\n\treturn {\n\t\tcomponent: _c.component,\n\t\tname: _c.name,\n\t\texample_components:\n\t\t\texample_component_map.size > 0 ? example_component_map : undefined\n\t};\n}\n\n/**\n * Preload all components\n * @param components A list of component metadata\n * @param root The root url of the app\n * @returns A map of component ids to their constructors\n */\nexport function preload_all_components(\n\tcomponents: ComponentMeta[],\n\troot: string\n): Map<ComponentMeta[\"type\"], LoadingComponent> {\n\tlet constructor_map: Map<ComponentMeta[\"type\"], LoadingComponent> = new Map();\n\n\tcomponents.forEach((c) => {\n\t\tconst { component, example_components } = get_component(\n\t\t\tc.type,\n\t\t\tc.component_class_id,\n\t\t\troot,\n\t\t\tcomponents\n\t\t);\n\n\t\tconstructor_map.set(c.component_class_id || c.type, component);\n\n\t\tif (example_components) {\n\t\t\tfor (const [name, example_component] of example_components) {\n\t\t\t\tconstructor_map.set(name, example_component);\n\t\t\t}\n\t\t}\n\t});\n\n\treturn constructor_map;\n}\n", "<script lang=\"ts\">\n\timport { tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport { Client } from \"@gradio/client\";\n\n\timport type { LoadingStatus, LoadingStatusCollection } from \"./stores\";\n\n\timport type { ComponentMeta, Dependency, LayoutNode } from \"./types\";\n\timport type { UpdateTransaction } from \"./init\";\n\timport { setupi18n } from \"./i18n\";\n\timport { ApiDocs, ApiRecorder } from \"./api_docs/\";\n\timport type { ThemeMode, Payload } from \"./types\";\n\timport { Toast } from \"@gradio/statustracker\";\n\timport type { ToastMessage } from \"@gradio/statustracker\";\n\timport type { ShareData } from \"@gradio/utils\";\n\timport MountComponents from \"./MountComponents.svelte\";\n\n\timport logo from \"./images/logo.svg\";\n\timport api_logo from \"./api_docs/img/api-logo.svg\";\n\timport { create_components, AsyncFunction } from \"./init\";\n\timport type {\n\t\tLogMessage,\n\t\tRenderMessage,\n\t\tStatusMessage\n\t} from \"@gradio/client\";\n\n\tsetupi18n();\n\n\texport let root: string;\n\texport let components: ComponentMeta[];\n\texport let layout: LayoutNode;\n\texport let dependencies: Dependency[];\n\texport let title = \"Gradio\";\n\texport let target: HTMLElement;\n\texport let autoscroll: boolean;\n\texport let show_api = true;\n\texport let show_footer = true;\n\texport let control_page_title = false;\n\texport let app_mode: boolean;\n\texport let theme_mode: ThemeMode;\n\texport let app: Awaited<ReturnType<typeof Client.connect>>;\n\texport let space_id: string | null;\n\texport let version: string;\n\texport let js: string | null;\n\texport let fill_height = false;\n\texport let ready: boolean;\n\texport let username: string | null;\n\n\tconst {\n\t\tlayout: _layout,\n\t\ttargets,\n\t\tupdate_value,\n\t\tget_data,\n\t\tloading_status,\n\t\tscheduled_updates,\n\t\tcreate_layout,\n\t\trerender_layout\n\t} = create_components();\n\n\t$: create_layout({\n\t\tcomponents,\n\t\tlayout,\n\t\tdependencies,\n\t\troot,\n\t\tapp,\n\t\toptions: {\n\t\t\tfill_height\n\t\t}\n\t});\n\n\t$: {\n\t\tready = !!$_layout;\n\t}\n\n\tlet params = new URLSearchParams(window.location.search);\n\tlet api_docs_visible = params.get(\"view\") === \"api\" && show_api;\n\tlet api_recorder_visible = params.get(\"view\") === \"api-recorder\" && show_api;\n\tfunction set_api_docs_visible(visible: boolean): void {\n\t\tapi_recorder_visible = false;\n\t\tapi_docs_visible = visible;\n\t\tlet params = new URLSearchParams(window.location.search);\n\t\tif (visible) {\n\t\t\tparams.set(\"view\", \"api\");\n\t\t} else {\n\t\t\tparams.delete(\"view\");\n\t\t}\n\t\thistory.replaceState(null, \"\", \"?\" + params.toString());\n\t}\n\tlet api_calls: Payload[] = [];\n\n\texport let render_complete = false;\n\tasync function handle_update(data: any, fn_index: number): Promise<void> {\n\t\tconst outputs = dependencies.find((dep) => dep.id == fn_index)!.outputs;\n\n\t\tconst meta_updates = data?.map((value: any, i: number) => {\n\t\t\treturn {\n\t\t\t\tid: outputs[i],\n\t\t\t\tprop: \"value_is_output\",\n\t\t\t\tvalue: true\n\t\t\t};\n\t\t});\n\n\t\tupdate_value(meta_updates);\n\n\t\tawait tick();\n\n\t\tconst updates: UpdateTransaction[] = [];\n\n\t\tdata?.forEach((value: any, i: number) => {\n\t\t\tif (\n\t\t\t\ttypeof value === \"object\" &&\n\t\t\t\tvalue !== null &&\n\t\t\t\tvalue.__type__ === \"update\"\n\t\t\t) {\n\t\t\t\tfor (const [update_key, update_value] of Object.entries(value)) {\n\t\t\t\t\tif (update_key === \"__type__\") {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tupdates.push({\n\t\t\t\t\t\t\tid: outputs[i],\n\t\t\t\t\t\t\tprop: update_key,\n\t\t\t\t\t\t\tvalue: update_value\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tupdates.push({\n\t\t\t\t\tid: outputs[i],\n\t\t\t\t\tprop: \"value\",\n\t\t\t\t\tvalue\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\tupdate_value(updates);\n\n\t\tawait tick();\n\t}\n\n\tlet submit_map: Map<number, ReturnType<typeof app.submit>> = new Map();\n\n\tlet messages: (ToastMessage & { fn_index: number })[] = [];\n\tfunction new_message(\n\t\tmessage: string,\n\t\tfn_index: number,\n\t\ttype: ToastMessage[\"type\"],\n\t\tduration: number | null = 10,\n\t\tvisible = true\n\t): ToastMessage & { fn_index: number } {\n\t\treturn {\n\t\t\tmessage,\n\t\t\tfn_index,\n\t\t\ttype,\n\t\t\tid: ++_error_id,\n\t\t\tduration,\n\t\t\tvisible\n\t\t};\n\t}\n\n\texport function add_new_message(\n\t\tmessage: string,\n\t\ttype: ToastMessage[\"type\"]\n\t): void {\n\t\tmessages = [new_message(message, -1, type), ...messages];\n\t}\n\n\tlet _error_id = -1;\n\n\tlet user_left_page = false;\n\tdocument.addEventListener(\"visibilitychange\", function () {\n\t\tif (document.visibilityState === \"hidden\") {\n\t\t\tuser_left_page = true;\n\t\t}\n\t});\n\n\tconst MESSAGE_QUOTE_RE = /^'([^]+)'$/;\n\n\tconst DUPLICATE_MESSAGE = $_(\"blocks.long_requests_queue\");\n\tconst MOBILE_QUEUE_WARNING = $_(\"blocks.connection_can_break\");\n\tconst MOBILE_RECONNECT_MESSAGE = $_(\"blocks.lost_connection\");\n\tconst SHOW_DUPLICATE_MESSAGE_ON_ETA = 15;\n\tconst SHOW_MOBILE_QUEUE_WARNING_ON_ETA = 10;\n\tconst is_mobile_device =\n\t\t/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n\t\t\tnavigator.userAgent\n\t\t);\n\tlet showed_duplicate_message = false;\n\tlet showed_mobile_warning = false;\n\n\t// as state updates are not synchronous, we need to ensure updates are flushed before triggering any requests\n\tfunction wait_then_trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): void {\n\t\tlet _unsub = (): void => {};\n\t\tfunction unsub(): void {\n\t\t\t_unsub();\n\t\t}\n\t\tif ($scheduled_updates) {\n\t\t\t_unsub = scheduled_updates.subscribe((updating) => {\n\t\t\t\tif (!updating) {\n\t\t\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t\t\t\tunsub();\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t}\n\t}\n\n\tasync function trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): Promise<void> {\n\t\tlet dep = dependencies.find((dep) => dep.id === dep_index)!;\n\n\t\tconst current_status = loading_status.get_status_for_fn(dep_index);\n\t\tmessages = messages.filter(({ fn_index }) => fn_index !== dep_index);\n\t\tif (current_status === \"pending\" || current_status === \"generating\") {\n\t\t\tdep.pending_request = true;\n\t\t}\n\n\t\tlet payload: Payload = {\n\t\t\tfn_index: dep_index,\n\t\t\tdata: await Promise.all(dep.inputs.map((id) => get_data(id))),\n\t\t\tevent_data: dep.collects_event_data ? event_data : null,\n\t\t\ttrigger_id: trigger_id\n\t\t};\n\n\t\tif (dep.frontend_fn) {\n\t\t\tdep\n\t\t\t\t.frontend_fn(\n\t\t\t\t\tpayload.data.concat(\n\t\t\t\t\t\tawait Promise.all(dep.outputs.map((id) => get_data(id)))\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t\t.then((v: unknown[]) => {\n\t\t\t\t\tif (dep.backend_fn) {\n\t\t\t\t\t\tpayload.data = v;\n\t\t\t\t\t\ttrigger_prediction(dep, payload);\n\t\t\t\t\t} else {\n\t\t\t\t\t\thandle_update(v, dep_index);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t} else if (dep.types.cancel && dep.cancels) {\n\t\t\tawait Promise.all(\n\t\t\t\tdep.cancels.map(async (fn_index) => {\n\t\t\t\t\tconst submission = submit_map.get(fn_index);\n\t\t\t\t\tsubmission?.cancel();\n\t\t\t\t\treturn submission;\n\t\t\t\t})\n\t\t\t);\n\t\t} else {\n\t\t\tif (dep.backend_fn) {\n\t\t\t\ttrigger_prediction(dep, payload);\n\t\t\t}\n\t\t}\n\n\t\tfunction trigger_prediction(dep: Dependency, payload: Payload): void {\n\t\t\tif (dep.trigger_mode === \"once\") {\n\t\t\t\tif (!dep.pending_request) make_prediction(payload);\n\t\t\t} else if (dep.trigger_mode === \"multiple\") {\n\t\t\t\tmake_prediction(payload);\n\t\t\t} else if (dep.trigger_mode === \"always_last\") {\n\t\t\t\tif (!dep.pending_request) {\n\t\t\t\t\tmake_prediction(payload);\n\t\t\t\t} else {\n\t\t\t\t\tdep.final_event = payload;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync function make_prediction(payload: Payload): Promise<void> {\n\t\t\tif (api_recorder_visible) {\n\t\t\t\tapi_calls = [...api_calls, JSON.parse(JSON.stringify(payload))];\n\t\t\t}\n\n\t\t\tlet submission: ReturnType<typeof app.submit>;\n\t\t\ttry {\n\t\t\t\tsubmission = app.submit(\n\t\t\t\t\tpayload.fn_index,\n\t\t\t\t\tpayload.data as unknown[],\n\t\t\t\t\tpayload.event_data,\n\t\t\t\t\tpayload.trigger_id\n\t\t\t\t);\n\t\t\t} catch (e) {\n\t\t\t\tconst fn_index = 0; // Mock value for fn_index\n\t\t\t\tmessages = [new_message(String(e), fn_index, \"error\"), ...messages];\n\t\t\t\tloading_status.update({\n\t\t\t\t\tstatus: \"error\",\n\t\t\t\t\tfn_index,\n\t\t\t\t\teta: 0,\n\t\t\t\t\tqueue: false,\n\t\t\t\t\tqueue_position: null\n\t\t\t\t});\n\t\t\t\tset_status($loading_status);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tsubmit_map.set(dep_index, submission);\n\n\t\t\tfor await (const message of submission) {\n\t\t\t\tif (message.type === \"data\") {\n\t\t\t\t\thandle_data(message);\n\t\t\t\t} else if (message.type === \"render\") {\n\t\t\t\t\thandle_render(message);\n\t\t\t\t} else if (message.type === \"status\") {\n\t\t\t\t\thandle_status_update(message);\n\t\t\t\t} else if (message.type === \"log\") {\n\t\t\t\t\thandle_log(message);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction handle_data(message: Payload): void {\n\t\t\t\tconst { data, fn_index } = message;\n\t\t\t\tif (dep.pending_request && dep.final_event) {\n\t\t\t\t\tdep.pending_request = false;\n\t\t\t\t\tmake_prediction(dep.final_event);\n\t\t\t\t}\n\t\t\t\tdep.pending_request = false;\n\t\t\t\thandle_update(data, fn_index);\n\t\t\t\tset_status($loading_status);\n\t\t\t}\n\n\t\t\tfunction handle_render(message: RenderMessage): void {\n\t\t\t\tconst { data } = message;\n\t\t\t\tlet _components: ComponentMeta[] = data.components;\n\t\t\t\tlet render_layout: LayoutNode = data.layout;\n\t\t\t\tlet _dependencies: Dependency[] = data.dependencies;\n\t\t\t\tlet render_id = data.render_id;\n\n\t\t\t\tlet deps_to_remove: number[] = [];\n\t\t\t\tdependencies.forEach((dep, i) => {\n\t\t\t\t\tif (dep.rendered_in === render_id) {\n\t\t\t\t\t\tdeps_to_remove.push(i);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tdeps_to_remove.reverse().forEach((i) => {\n\t\t\t\t\tdependencies.splice(i, 1);\n\t\t\t\t});\n\t\t\t\t_dependencies.forEach((dep) => {\n\t\t\t\t\tdependencies.push(dep);\n\t\t\t\t});\n\n\t\t\t\trerender_layout({\n\t\t\t\t\tcomponents: _components,\n\t\t\t\t\tlayout: render_layout,\n\t\t\t\t\troot: root,\n\t\t\t\t\tdependencies: dependencies,\n\t\t\t\t\trender_id: render_id\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tfunction handle_log(msg: LogMessage): void {\n\t\t\t\tconst { log, fn_index, level, duration, visible } = msg;\n\t\t\t\tmessages = [\n\t\t\t\t\tnew_message(log, fn_index, level, duration, visible),\n\t\t\t\t\t...messages\n\t\t\t\t];\n\t\t\t}\n\n\t\t\tfunction handle_status_update(message: StatusMessage): void {\n\t\t\t\tconst { fn_index, ...status } = message;\n\t\t\t\t//@ts-ignore\n\t\t\t\tloading_status.update({\n\t\t\t\t\t...status,\n\t\t\t\t\tstatus: status.stage,\n\t\t\t\t\tprogress: status.progress_data,\n\t\t\t\t\tfn_index\n\t\t\t\t});\n\t\t\t\tset_status($loading_status);\n\t\t\t\tif (\n\t\t\t\t\t!showed_duplicate_message &&\n\t\t\t\t\tspace_id !== null &&\n\t\t\t\t\tstatus.position !== undefined &&\n\t\t\t\t\tstatus.position >= 2 &&\n\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\tstatus.eta > SHOW_DUPLICATE_MESSAGE_ON_ETA\n\t\t\t\t) {\n\t\t\t\t\tshowed_duplicate_message = true;\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(DUPLICATE_MESSAGE, fn_index, \"warning\"),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\t\t\t\t}\n\t\t\t\tif (\n\t\t\t\t\t!showed_mobile_warning &&\n\t\t\t\t\tis_mobile_device &&\n\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\tstatus.eta > SHOW_MOBILE_QUEUE_WARNING_ON_ETA\n\t\t\t\t) {\n\t\t\t\t\tshowed_mobile_warning = true;\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(MOBILE_QUEUE_WARNING, fn_index, \"warning\"),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\t\t\t\t}\n\n\t\t\t\tif (status.stage === \"complete\") {\n\t\t\t\t\tstatus.changed_state_ids?.forEach((id) => {\n\t\t\t\t\t\tdependencies\n\t\t\t\t\t\t\t.filter((dep) => dep.targets.some(([_id, _]) => _id === id))\n\t\t\t\t\t\t\t.forEach((dep) => {\n\t\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\tdependencies.forEach(async (dep) => {\n\t\t\t\t\t\tif (dep.trigger_after === fn_index) {\n\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t// submission.destroy();\n\t\t\t\t}\n\t\t\t\tif (status.broken && is_mobile_device && user_left_page) {\n\t\t\t\t\twindow.setTimeout(() => {\n\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\tnew_message(MOBILE_RECONNECT_MESSAGE, fn_index, \"error\"),\n\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t];\n\t\t\t\t\t}, 0);\n\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id, event_data);\n\t\t\t\t\tuser_left_page = false;\n\t\t\t\t} else if (status.stage === \"error\") {\n\t\t\t\t\tif (status.message) {\n\t\t\t\t\t\tconst _message = status.message.replace(\n\t\t\t\t\t\t\tMESSAGE_QUOTE_RE,\n\t\t\t\t\t\t\t(_, b) => b\n\t\t\t\t\t\t);\n\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\tnew_message(\n\t\t\t\t\t\t\t\t_message,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\"error\",\n\t\t\t\t\t\t\t\tstatus.duration,\n\t\t\t\t\t\t\t\tstatus.visible\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t];\n\t\t\t\t\t}\n\t\t\t\t\tdependencies.map(async (dep) => {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tdep.trigger_after === fn_index &&\n\t\t\t\t\t\t\t!dep.trigger_only_on_success\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction trigger_share(title: string | undefined, description: string): void {\n\t\tif (space_id === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst discussion_url = new URL(\n\t\t\t`https://huggingface.co/spaces/${space_id}/discussions/new`\n\t\t);\n\t\tif (title !== undefined && title.length > 0) {\n\t\t\tdiscussion_url.searchParams.set(\"title\", title);\n\t\t}\n\t\tdiscussion_url.searchParams.set(\"description\", description);\n\t\twindow.open(discussion_url.toString(), \"_blank\");\n\t}\n\n\tfunction handle_error_close(e: Event & { detail: number }): void {\n\t\tconst _id = e.detail;\n\t\tmessages = messages.filter((m) => m.id !== _id);\n\t}\n\n\tconst is_external_url = (link: string | null): boolean =>\n\t\t!!(link && new URL(link, location.href).origin !== location.origin);\n\n\tasync function handle_mount(): Promise<void> {\n\t\tif (js) {\n\t\t\tlet blocks_frontend_fn = new AsyncFunction(\n\t\t\t\t`let result = await (${js})();\n\t\t\t\t\treturn (!Array.isArray(result)) ? [result] : result;`\n\t\t\t);\n\t\t\tawait blocks_frontend_fn();\n\t\t}\n\n\t\tawait tick();\n\n\t\tvar a = target.getElementsByTagName(\"a\");\n\n\t\tfor (var i = 0; i < a.length; i++) {\n\t\t\tconst _target = a[i].getAttribute(\"target\");\n\t\t\tconst _link = a[i].getAttribute(\"href\");\n\n\t\t\t// only target anchor tags with external links\n\t\t\tif (is_external_url(_link) && _target !== \"_blank\")\n\t\t\t\ta[i].setAttribute(\"target\", \"_blank\");\n\t\t}\n\n\t\t// handle load triggers\n\t\tdependencies.forEach((dep) => {\n\t\t\tif (dep.targets.some((dep) => dep[1] === \"load\")) {\n\t\t\t\twait_then_trigger_api_call(dep.id);\n\t\t\t}\n\t\t});\n\n\t\tif (render_complete) return;\n\n\t\ttarget.addEventListener(\"prop_change\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\t\t\tconst { id, prop, value } = e.detail;\n\t\t\tupdate_value([{ id, prop, value }]);\n\t\t});\n\t\ttarget.addEventListener(\"gradio\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\n\t\t\tconst { id, event, data } = e.detail;\n\n\t\t\tif (event === \"share\") {\n\t\t\t\tconst { title, description } = data as ShareData;\n\t\t\t\ttrigger_share(title, description);\n\t\t\t} else if (event === \"error\" || event === \"warning\") {\n\t\t\t\tmessages = [new_message(data, -1, event), ...messages];\n\t\t\t} else if (event == \"clear_status\") {\n\t\t\t\tupdate_status(id, \"complete\", data);\n\t\t\t} else {\n\t\t\t\tconst deps = $targets[id]?.[event];\n\n\t\t\t\tdeps?.forEach((dep_id) => {\n\t\t\t\t\trequestAnimationFrame(() => {\n\t\t\t\t\t\twait_then_trigger_api_call(dep_id, id, data);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\trender_complete = true;\n\t}\n\n\t$: set_status($loading_status);\n\n\tfunction update_status(\n\t\tid: number,\n\t\tstatus: \"error\" | \"complete\" | \"pending\",\n\t\tdata: LoadingStatus\n\t): void {\n\t\tdata.status = status;\n\t\tupdate_value([\n\t\t\t{\n\t\t\t\tid,\n\t\t\t\tprop: \"loading_status\",\n\t\t\t\tvalue: data\n\t\t\t}\n\t\t]);\n\t}\n\n\tfunction set_status(statuses: LoadingStatusCollection): void {\n\t\tlet updates: {\n\t\t\tid: number;\n\t\t\tprop: string;\n\t\t\tvalue: LoadingStatus;\n\t\t}[] = [];\n\t\tObject.entries(statuses).forEach(([id, loading_status]) => {\n\t\t\tlet dependency = dependencies.find(\n\t\t\t\t(dep) => dep.id == loading_status.fn_index\n\t\t\t);\n\t\t\tif (dependency === undefined) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tloading_status.scroll_to_output = dependency.scroll_to_output;\n\t\t\tloading_status.show_progress = dependency.show_progress;\n\t\t\tupdates.push({\n\t\t\t\tid: parseInt(id),\n\t\t\t\tprop: \"loading_status\",\n\t\t\t\tvalue: loading_status\n\t\t\t});\n\t\t});\n\n\t\tconst inputs_to_update = loading_status.get_inputs_to_update();\n\t\tconst additional_updates = Array.from(inputs_to_update).map(\n\t\t\t([id, pending_status]) => {\n\t\t\t\treturn {\n\t\t\t\t\tid,\n\t\t\t\t\tprop: \"pending\",\n\t\t\t\t\tvalue: pending_status === \"pending\"\n\t\t\t\t};\n\t\t\t}\n\t\t);\n\n\t\tupdate_value([...updates, ...additional_updates]);\n\t}\n\n\tfunction isCustomEvent(event: Event): event is CustomEvent {\n\t\treturn \"detail\" in event;\n\t}\n</script>\n\n<svelte:head>\n\t{#if control_page_title}\n\t\t<title>{title}</title>\n\t{/if}\n</svelte:head>\n\n<div class=\"wrap\" style:min-height={app_mode ? \"100%\" : \"auto\"}>\n\t<div class=\"contain\" style:flex-grow={app_mode ? \"1\" : \"auto\"}>\n\t\t{#if $_layout && app.config}\n\t\t\t<MountComponents\n\t\t\t\trootNode={$_layout}\n\t\t\t\t{root}\n\t\t\t\t{target}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:mount={handle_mount}\n\t\t\t\t{version}\n\t\t\t\t{autoscroll}\n\t\t\t\tmax_file_size={app.config.max_file_size}\n\t\t\t\tclient={app}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t{#if show_footer}\n\t\t<footer>\n\t\t\t{#if show_api}\n\t\t\t\t<button\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tset_api_docs_visible(!api_docs_visible);\n\t\t\t\t\t}}\n\t\t\t\t\tclass=\"show-api\"\n\t\t\t\t>\n\t\t\t\t\t{$_(\"errors.use_via_api\")}\n\t\t\t\t\t<img src={api_logo} alt={$_(\"common.logo\")} />\n\t\t\t\t</button>\n\t\t\t\t<div>·</div>\n\t\t\t{/if}\n\t\t\t<a\n\t\t\t\thref=\"https://gradio.app\"\n\t\t\t\tclass=\"built-with\"\n\t\t\t\ttarget=\"_blank\"\n\t\t\t\trel=\"noreferrer\"\n\t\t\t>\n\t\t\t\t{$_(\"common.built_with_gradio\")}\n\t\t\t\t<img src={logo} alt={$_(\"common.logo\")} />\n\t\t\t</a>\n\t\t</footer>\n\t{/if}\n</div>\n\n{#if api_recorder_visible}\n\t<!-- TODO: fix -->\n\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t<div\n\t\tid=\"api-recorder-container\"\n\t\ton:click={() => {\n\t\t\tset_api_docs_visible(true);\n\t\t\tapi_recorder_visible = false;\n\t\t}}\n\t>\n\t\t<ApiRecorder {api_calls} {dependencies} />\n\t</div>\n{/if}\n\n{#if api_docs_visible && $_layout}\n\t<div class=\"api-docs\">\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t<div\n\t\t\tclass=\"backdrop\"\n\t\t\ton:click={() => {\n\t\t\t\tset_api_docs_visible(false);\n\t\t\t}}\n\t\t/>\n\t\t<div class=\"api-docs-wrap\">\n\t\t\t<ApiDocs\n\t\t\t\troot_node={$_layout}\n\t\t\t\ton:close={(event) => {\n\t\t\t\t\tset_api_docs_visible(false);\n\t\t\t\t\tapi_calls = [];\n\t\t\t\t\tapi_recorder_visible = event.detail.api_recorder_visible;\n\t\t\t\t}}\n\t\t\t\t{dependencies}\n\t\t\t\t{root}\n\t\t\t\t{app}\n\t\t\t\t{space_id}\n\t\t\t\t{api_calls}\n\t\t\t\t{username}\n\t\t\t/>\n\t\t</div>\n\t</div>\n{/if}\n\n{#if messages}\n\t<Toast {messages} on:close={handle_error_close} />\n{/if}\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t\twidth: var(--size-full);\n\t\tfont-weight: var(--body-text-weight);\n\t\tfont-size: var(--body-text-size);\n\t}\n\n\t.contain {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\tfooter {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-top: var(--size-4);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tfooter > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.show-api {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t.show-api:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.show-api img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-2);\n\t\twidth: var(--size-3);\n\t}\n\n\t.built-with {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.built-with:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.built-with img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-1);\n\t\tmargin-bottom: 1px;\n\t\twidth: var(--size-4);\n\t}\n\n\t.api-docs {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-top);\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\twidth: var(--size-screen);\n\t\theight: var(--size-screen-h);\n\t}\n\n\t.backdrop {\n\t\tflex: 1 1 0%;\n\t\t-webkit-backdrop-filter: blur(4px);\n\t\tbackdrop-filter: blur(4px);\n\t}\n\n\t.api-docs-wrap {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tbackground: var(--background-fill-primary);\n\t\toverflow-x: hidden;\n\t\toverflow-y: auto;\n\t}\n\n\t@media (--screen-md) {\n\t\t.api-docs-wrap {\n\t\t\tborder-top-left-radius: var(--radius-lg);\n\t\t\tborder-bottom-left-radius: var(--radius-lg);\n\t\t\twidth: 950px;\n\t\t}\n\t}\n\n\t@media (--screen-xxl) {\n\t\t.api-docs-wrap {\n\t\t\twidth: 1150px;\n\t\t}\n\t}\n\n\t#api-recorder-container {\n\t\tposition: fixed;\n\t\tleft: 10px;\n\t\tbottom: 10px;\n\t\tz-index: 1000;\n\t}\n</style>\n"], "file": "assets/Blocks-CyfcXtBq.js"}