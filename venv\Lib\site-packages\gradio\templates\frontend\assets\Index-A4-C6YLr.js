import{C as Rn,S as Gn}from"./Index-DB1XLvMK.js";import{u as Ht,c as On}from"./Blocks-CyfcXtBq.js";import{d as Yn}from"./index-CnqicUFC.js";import{S as Jn}from"./ShareButton-DRfMJDgB.js";import{I as Kn}from"./Image-CJc3fwmN.js";import"./index-BQPjLIsY.js";/* empty css                                                   */import"./Index.svelte_svelte_type_style_lang-OwOFPfLe.js";import{M as kt}from"./Example.svelte_svelte_type_style_lang-DXGdiQV3.js";import{C as kn}from"./Check-CZUQOzJl.js";import{C as $n}from"./Copy-B6RcHnoK.js";import{D as Qn}from"./DownloadLink-CHpWw1Ex.js";import{B as Wn}from"./Button-BIUaXfcG.js";import{B as Xn}from"./BlockLabel-BlSr62f_.js";import"./file-url-SIRImsEF.js";import"./svelte/svelte.js";import"./prism-python-BTLCWl-V.js";const{SvelteComponent:xn,append:St,attr:E,detach:el,init:tl,insert:nl,noop:at,safe_not_equal:ll,svg_element:rt}=window.__gradio__svelte__internal;function il(l){let e,t,n;return{c(){e=rt("svg"),t=rt("path"),n=rt("path"),E(t,"fill","currentColor"),E(t,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),E(n,"fill","currentColor"),E(n,"d","M8 10h16v2H8zm0 6h10v2H8z"),E(e,"xmlns","http://www.w3.org/2000/svg"),E(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),E(e,"aria-hidden","true"),E(e,"role","img"),E(e,"class","iconify iconify--carbon"),E(e,"width","100%"),E(e,"height","100%"),E(e,"preserveAspectRatio","xMidYMid meet"),E(e,"viewBox","0 0 32 32")},m(i,s){nl(i,e,s),St(e,t),St(e,n)},p:at,i:at,o:at,d(i){i&&el(e)}}}class ol extends xn{constructor(e){super(),tl(this,e,null,il,ll,{})}}const sl=async l=>(await Promise.all(l.map(async t=>await Promise.all(t.map(async(n,i)=>{if(n===null)return"";let s=i===0?"😃":"🤖",o="";if(typeof n=="string"){const r={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};o=n;for(let[a,f]of Object.entries(r)){let u;for(;(u=f.exec(n))!==null;){const _=u[1]||u[2],h=await Ht(_);o=o.replace(_,h)}}}else{if(!n?.url)return"";const r=await Ht(n.url);n.mime_type?.includes("audio")?o=`<audio controls src="${r}"></audio>`:n.mime_type?.includes("video")?o=r:n.mime_type?.includes("image")&&(o=`<img src="${r}" />`)}return`${s}: ${o}`}))))).map(t=>t.join(t[0]!==""&&t[1]!==""?`
`:"")).join(`
`),Cn=(l,e)=>l.replace('src="/file',`src="${e}file`);function al(l){return l?l.includes("audio")?"audio":l.includes("video")?"video":l.includes("image")?"image":"file":"file"}function yn(l){const e=Array.isArray(l.file)?l.file[0]:l.file;return{component:al(e?.mime_type),value:l.file,alt_text:l.alt_text,constructor_args:{},props:{}}}function rl(l,e){return l===null?l:l.map((t,n)=>typeof t.content=="string"?{role:t.role,metadata:t.metadata,content:Cn(t.content,e),type:"text",index:n}:"file"in t.content?{content:yn(t.content),metadata:t.metadata,role:t.role,type:"component",index:n}:{type:"component",...t})}function ul(l,e){return l===null?l:l.flatMap((n,i)=>n.map((s,o)=>{if(s==null)return null;const r=o==0?"user":"assistant";return typeof s=="string"?{role:r,type:"text",content:Cn(s,e),metadata:{title:null},index:[i,o]}:"file"in s?{content:yn(s),role:r,type:"component",index:[i,o]}:{role:r,content:s,type:"component",index:[i,o]}})).filter(n=>n!=null)}function ke(l){return l.type==="component"}const{SvelteComponent:fl,attr:Fe,detach:_l,element:cl,flush:ml,init:hl,insert:dl,noop:qt,safe_not_equal:gl,set_style:Vt}=window.__gradio__svelte__internal;function bl(l){let e;return{c(){e=cl("div"),e.innerHTML=`<span class="sr-only">Loading content</span> <div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>`,Fe(e,"class","message pending svelte-1gpwetz"),Fe(e,"role","status"),Fe(e,"aria-label","Loading response"),Fe(e,"aria-live","polite"),Vt(e,"border-radius",l[0]==="bubble"?"var(--radius-xxl)":"none")},m(t,n){dl(t,e,n)},p(t,[n]){n&1&&Vt(e,"border-radius",t[0]==="bubble"?"var(--radius-xxl)":"none")},i:qt,o:qt,d(t){t&&_l(e)}}}function pl(l,e,t){let{layout:n="bubble"}=e;return l.$$set=i=>{"layout"in i&&t(0,n=i.layout)},[n]}class wl extends fl{constructor(e){super(),hl(this,e,pl,bl,gl,{layout:0})}get layout(){return this.$$.ctx[0]}set layout(e){this.$$set({layout:e}),ml()}}const{SvelteComponent:vl,append:qe,attr:De,check_outros:kl,create_slot:$l,detach:Hn,element:Ee,flush:Cl,get_all_dirty_from_scope:yl,get_slot_changes:Hl,group_outros:Sl,init:ql,insert:Sn,listen:Vl,safe_not_equal:zl,set_data:jl,set_style:zt,space:jt,text:Al,transition_in:Ge,transition_out:pt,update_slot_base:Ll}=window.__gradio__svelte__internal;function At(l){let e,t;const n=l[4].default,i=$l(n,l,l[3],null);return{c(){e=Ee("div"),i&&i.c(),De(e,"class","content svelte-1e60bn1")},m(s,o){Sn(s,e,o),i&&i.m(e,null),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Ll(i,n,s,s[3],t?Hl(n,s[3],o,null):yl(s[3]),null)},i(s){t||(Ge(i,s),t=!0)},o(s){pt(i,s),t=!1},d(s){s&&Hn(e),i&&i.d(s)}}}function Ml(l){let e,t,n,i,s,o,r,a,f,u,_=l[1]&&At(l);return{c(){e=Ee("button"),t=Ee("div"),n=Ee("span"),i=Al(l[0]),s=jt(),o=Ee("span"),o.textContent="▼",r=jt(),_&&_.c(),De(n,"class","title-text svelte-1e60bn1"),De(o,"class","arrow svelte-1e60bn1"),zt(o,"transform",l[1]?"rotate(0)":"rotate(90deg)"),De(t,"class","title svelte-1e60bn1"),De(e,"class","box svelte-1e60bn1")},m(h,p){Sn(h,e,p),qe(e,t),qe(t,n),qe(n,i),qe(t,s),qe(t,o),qe(e,r),_&&_.m(e,null),a=!0,f||(u=Vl(e,"click",l[2]),f=!0)},p(h,[p]){(!a||p&1)&&jl(i,h[0]),p&2&&zt(o,"transform",h[1]?"rotate(0)":"rotate(90deg)"),h[1]?_?(_.p(h,p),p&2&&Ge(_,1)):(_=At(h),_.c(),Ge(_,1),_.m(e,null)):_&&(Sl(),pt(_,1,1,()=>{_=null}),kl())},i(h){a||(Ge(_),a=!0)},o(h){pt(_),a=!1},d(h){h&&Hn(e),_&&_.d(),f=!1,u()}}}function Bl(l,e,t){let{$$slots:n={},$$scope:i}=e,s=!1,{title:o}=e;function r(){t(1,s=!s)}return l.$$set=a=>{"title"in a&&t(0,o=a.title),"$$scope"in a&&t(3,i=a.$$scope)},[o,s,r,i,n]}class Tl extends vl{constructor(e){super(),ql(this,e,Bl,Ml,zl,{title:0})}get title(){return this.$$.ctx[0]}set title(e){this.$$set({title:e}),Cl()}}const{SvelteComponent:Nl,attr:Zl,bubble:Ve,check_outros:$e,construct_svelte_component:I,create_component:F,destroy_component:R,detach:me,element:Dl,empty:Ce,flush:ne,group_outros:ye,init:El,insert:he,mount_component:G,noop:Pl,safe_not_equal:Ul,transition_in:Z,transition_out:D}=window.__gradio__svelte__internal;function Il(l){let e,t,n;var i=l[1][l[0]];function s(o,r){return{props:{value:o[2],show_label:!1,label:"chatbot-image",show_share_button:!0,i18n:o[6],gradio:{dispatch:Ql}}}}return i&&(e=I(i,s(l)),e.$on("load",l[14])),{c(){e&&F(e.$$.fragment),t=Ce()},m(o,r){e&&G(e,o,r),he(o,t,r),n=!0},p(o,r){if(r&3&&i!==(i=o[1][o[0]])){if(e){ye();const a=e;D(a.$$.fragment,1,0,()=>{R(a,1)}),$e()}i?(e=I(i,s(o)),e.$on("load",o[14]),F(e.$$.fragment),Z(e.$$.fragment,1),G(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=o[2]),r&64&&(a.i18n=o[6]),e.$set(a)}},i(o){n||(e&&Z(e.$$.fragment,o),n=!0)},o(o){e&&D(e.$$.fragment,o),n=!1},d(o){o&&me(t),e&&R(e,o)}}}function Fl(l){let e,t,n;var i=l[1][l[0]];function s(o,r){return{props:{value:o[2],show_label:!1,label:"chatbot-image",show_download_button:!1,i18n:o[6]}}}return i&&(e=I(i,s(l)),e.$on("load",l[13])),{c(){e&&F(e.$$.fragment),t=Ce()},m(o,r){e&&G(e,o,r),he(o,t,r),n=!0},p(o,r){if(r&3&&i!==(i=o[1][o[0]])){if(e){ye();const a=e;D(a.$$.fragment,1,0,()=>{R(a,1)}),$e()}i?(e=I(i,s(o)),e.$on("load",o[13]),F(e.$$.fragment),Z(e.$$.fragment,1),G(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=o[2]),r&64&&(a.i18n=o[6]),e.$set(a)}},i(o){n||(e&&Z(e.$$.fragment,o),n=!0)},o(o){e&&D(e.$$.fragment,o),n=!1},d(o){o&&me(t),e&&R(e,o)}}}function Rl(l){let e,t,n;var i=l[1][l[0]];function s(o,r){return{props:{autoplay:!0,value:o[2].video||o[2],show_label:!1,show_share_button:!0,i18n:o[6],upload:o[7],show_download_button:!1,$$slots:{default:[Jl]},$$scope:{ctx:o}}}}return i&&(e=I(i,s(l)),e.$on("load",l[12])),{c(){e&&F(e.$$.fragment),t=Ce()},m(o,r){e&&G(e,o,r),he(o,t,r),n=!0},p(o,r){if(r&3&&i!==(i=o[1][o[0]])){if(e){ye();const a=e;D(a.$$.fragment,1,0,()=>{R(a,1)}),$e()}i?(e=I(i,s(o)),e.$on("load",o[12]),F(e.$$.fragment),Z(e.$$.fragment,1),G(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=o[2].video||o[2]),r&64&&(a.i18n=o[6]),r&128&&(a.upload=o[7]),r&32768&&(a.$$scope={dirty:r,ctx:o}),e.$set(a)}},i(o){n||(e&&Z(e.$$.fragment,o),n=!0)},o(o){e&&D(e.$$.fragment,o),n=!1},d(o){o&&me(t),e&&R(e,o)}}}function Gl(l){let e,t,n;var i=l[1][l[0]];function s(o,r){return{props:{value:o[2],show_label:!1,show_share_button:!0,i18n:o[6],label:"",waveform_settings:{},waveform_options:{},show_download_button:!1}}}return i&&(e=I(i,s(l)),e.$on("load",l[11])),{c(){e&&F(e.$$.fragment),t=Ce()},m(o,r){e&&G(e,o,r),he(o,t,r),n=!0},p(o,r){if(r&3&&i!==(i=o[1][o[0]])){if(e){ye();const a=e;D(a.$$.fragment,1,0,()=>{R(a,1)}),$e()}i?(e=I(i,s(o)),e.$on("load",o[11]),F(e.$$.fragment),Z(e.$$.fragment,1),G(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=o[2]),r&64&&(a.i18n=o[6]),e.$set(a)}},i(o){n||(e&&Z(e.$$.fragment,o),n=!0)},o(o){e&&D(e.$$.fragment,o),n=!1},d(o){o&&me(t),e&&R(e,o)}}}function Ol(l){let e,t,n;var i=l[1][l[0]];function s(o,r){return{props:{value:o[2],target:o[3],theme_mode:o[4],bokeh_version:o[5].bokeh_version,caption:"",show_actions_button:!0}}}return i&&(e=I(i,s(l)),e.$on("load",l[10])),{c(){e&&F(e.$$.fragment),t=Ce()},m(o,r){e&&G(e,o,r),he(o,t,r),n=!0},p(o,r){if(r&3&&i!==(i=o[1][o[0]])){if(e){ye();const a=e;D(a.$$.fragment,1,0,()=>{R(a,1)}),$e()}i?(e=I(i,s(o)),e.$on("load",o[10]),F(e.$$.fragment),Z(e.$$.fragment,1),G(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=o[2]),r&8&&(a.target=o[3]),r&16&&(a.theme_mode=o[4]),r&32&&(a.bokeh_version=o[5].bokeh_version),e.$set(a)}},i(o){n||(e&&Z(e.$$.fragment,o),n=!0)},o(o){e&&D(e.$$.fragment,o),n=!1},d(o){o&&me(t),e&&R(e,o)}}}function Yl(l){let e,t,n;var i=l[1][l[0]];function s(o,r){return{props:{value:o[2],show_label:!1,i18n:o[6],label:"",_fetch:o[8],allow_preview:!1,interactive:!1,mode:"minimal",fixed_height:1}}}return i&&(e=I(i,s(l)),e.$on("load",l[9])),{c(){e&&F(e.$$.fragment),t=Ce()},m(o,r){e&&G(e,o,r),he(o,t,r),n=!0},p(o,r){if(r&3&&i!==(i=o[1][o[0]])){if(e){ye();const a=e;D(a.$$.fragment,1,0,()=>{R(a,1)}),$e()}i?(e=I(i,s(o)),e.$on("load",o[9]),F(e.$$.fragment),Z(e.$$.fragment,1),G(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=o[2]),r&64&&(a.i18n=o[6]),r&256&&(a._fetch=o[8]),e.$set(a)}},i(o){n||(e&&Z(e.$$.fragment,o),n=!0)},o(o){e&&D(e.$$.fragment,o),n=!1},d(o){o&&me(t),e&&R(e,o)}}}function Jl(l){let e;return{c(){e=Dl("track"),Zl(e,"kind","captions")},m(t,n){he(t,e,n)},p:Pl,d(t){t&&me(e)}}}function Kl(l){let e,t,n,i;const s=[Yl,Ol,Gl,Rl,Fl,Il],o=[];function r(a,f){return a[0]==="gallery"?0:a[0]==="plot"?1:a[0]==="audio"?2:a[0]==="video"?3:a[0]==="image"?4:a[0]==="html"?5:-1}return~(e=r(l))&&(t=o[e]=s[e](l)),{c(){t&&t.c(),n=Ce()},m(a,f){~e&&o[e].m(a,f),he(a,n,f),i=!0},p(a,[f]){let u=e;e=r(a),e===u?~e&&o[e].p(a,f):(t&&(ye(),D(o[u],1,1,()=>{o[u]=null}),$e()),~e?(t=o[e],t?t.p(a,f):(t=o[e]=s[e](a),t.c()),Z(t,1),t.m(n.parentNode,n)):t=null)},i(a){i||(Z(t),i=!0)},o(a){D(t),i=!1},d(a){a&&me(n),~e&&o[e].d(a)}}}const Ql=()=>{};function Wl(l,e,t){let{type:n}=e,{components:i}=e,{value:s}=e,{target:o}=e,{theme_mode:r}=e,{props:a}=e,{i18n:f}=e,{upload:u}=e,{_fetch:_}=e;function h(c){Ve.call(this,l,c)}function p(c){Ve.call(this,l,c)}function w(c){Ve.call(this,l,c)}function q(c){Ve.call(this,l,c)}function m(c){Ve.call(this,l,c)}function b(c){Ve.call(this,l,c)}return l.$$set=c=>{"type"in c&&t(0,n=c.type),"components"in c&&t(1,i=c.components),"value"in c&&t(2,s=c.value),"target"in c&&t(3,o=c.target),"theme_mode"in c&&t(4,r=c.theme_mode),"props"in c&&t(5,a=c.props),"i18n"in c&&t(6,f=c.i18n),"upload"in c&&t(7,u=c.upload),"_fetch"in c&&t(8,_=c._fetch)},[n,i,s,o,r,a,f,u,_,h,p,w,q,m,b]}class Xl extends Nl{constructor(e){super(),El(this,e,Wl,Kl,Ul,{type:0,components:1,value:2,target:3,theme_mode:4,props:5,i18n:6,upload:7,_fetch:8})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),ne()}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),ne()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),ne()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),ne()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),ne()}get props(){return this.$$.ctx[5]}set props(e){this.$$set({props:e}),ne()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),ne()}get upload(){return this.$$.ctx[7]}set upload(e){this.$$set({upload:e}),ne()}get _fetch(){return this.$$.ctx[8]}set _fetch(e){this.$$set({_fetch:e}),ne()}}const{SvelteComponent:xl,append:ei,attr:de,detach:ti,init:ni,insert:li,noop:ut,safe_not_equal:ii,svg_element:Lt}=window.__gradio__svelte__internal;function oi(l){let e,t;return{c(){e=Lt("svg"),t=Lt("path"),de(t,"d","M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z"),de(t,"fill","currentColor"),de(e,"width","16"),de(e,"height","16"),de(e,"viewBox","0 0 12 12"),de(e,"fill","none"),de(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){li(n,e,i),ei(e,t)},p:ut,i:ut,o:ut,d(n){n&&ti(e)}}}class si extends xl{constructor(e){super(),ni(this,e,null,oi,ii,{})}}const{SvelteComponent:ai,append:ri,attr:ge,detach:ui,init:fi,insert:_i,noop:ft,safe_not_equal:ci,svg_element:Mt}=window.__gradio__svelte__internal;function mi(l){let e,t;return{c(){e=Mt("svg"),t=Mt("path"),ge(t,"d","M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z"),ge(t,"fill","currentColor"),ge(e,"width","16"),ge(e,"height","16"),ge(e,"viewBox","0 0 12 12"),ge(e,"fill","none"),ge(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){_i(n,e,i),ri(e,t)},p:ft,i:ft,o:ft,d(n){n&&ui(e)}}}class hi extends ai{constructor(e){super(),fi(this,e,null,mi,ci,{})}}const{SvelteComponent:di,append:gi,attr:be,detach:bi,init:pi,insert:wi,noop:_t,safe_not_equal:vi,svg_element:Bt}=window.__gradio__svelte__internal;function ki(l){let e,t;return{c(){e=Bt("svg"),t=Bt("path"),be(t,"d","M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z"),be(t,"fill","currentColor"),be(e,"width","16"),be(e,"height","16"),be(e,"viewBox","0 0 12 12"),be(e,"fill","none"),be(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){wi(n,e,i),gi(e,t)},p:_t,i:_t,o:_t,d(n){n&&bi(e)}}}class $i extends di{constructor(e){super(),pi(this,e,null,ki,vi,{})}}const{SvelteComponent:Ci,append:yi,attr:pe,detach:Hi,init:Si,insert:qi,noop:ct,safe_not_equal:Vi,svg_element:Tt}=window.__gradio__svelte__internal;function zi(l){let e,t;return{c(){e=Tt("svg"),t=Tt("path"),pe(t,"d","M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z"),pe(t,"fill","currentColor"),pe(e,"width","16"),pe(e,"height","16"),pe(e,"viewBox","0 0 12 12"),pe(e,"fill","none"),pe(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){qi(n,e,i),yi(e,t)},p:ct,i:ct,o:ct,d(n){n&&Hi(e)}}}class ji extends Ci{constructor(e){super(),Si(this,e,null,zi,Vi,{})}}const{SvelteComponent:Ai,attr:we,check_outros:Nt,create_component:We,destroy_component:Xe,detach:mt,element:Zt,flush:ht,group_outros:Dt,init:Li,insert:dt,listen:Et,mount_component:xe,run_all:Mi,safe_not_equal:Bi,space:Ti,toggle_class:se,transition_in:fe,transition_out:_e}=window.__gradio__svelte__internal;function Ni(l){let e,t;return e=new hi({}),{c(){We(e.$$.fragment)},m(n,i){xe(e,n,i),t=!0},i(n){t||(fe(e.$$.fragment,n),t=!0)},o(n){_e(e.$$.fragment,n),t=!1},d(n){Xe(e,n)}}}function Zi(l){let e,t;return e=new si({}),{c(){We(e.$$.fragment)},m(n,i){xe(e,n,i),t=!0},i(n){t||(fe(e.$$.fragment,n),t=!0)},o(n){_e(e.$$.fragment,n),t=!1},d(n){Xe(e,n)}}}function Di(l){let e,t;return e=new ji({}),{c(){We(e.$$.fragment)},m(n,i){xe(e,n,i),t=!0},i(n){t||(fe(e.$$.fragment,n),t=!0)},o(n){_e(e.$$.fragment,n),t=!1},d(n){Xe(e,n)}}}function Ei(l){let e,t;return e=new $i({}),{c(){We(e.$$.fragment)},m(n,i){xe(e,n,i),t=!0},i(n){t||(fe(e.$$.fragment,n),t=!0)},o(n){_e(e.$$.fragment,n),t=!1},d(n){Xe(e,n)}}}function Pi(l){let e,t,n,i,s,o,r,a,f,u,_,h,p;const w=[Zi,Ni],q=[];function m($,z){return $[3]==="dislike"?0:1}t=m(l),n=q[t]=w[t](l);const b=[Ei,Di],c=[];function v($,z){return $[3]==="like"?0:1}return a=v(l),f=c[a]=b[a](l),{c(){e=Zt("button"),n.c(),o=Ti(),r=Zt("button"),f.c(),we(e,"aria-label",i=l[3]==="dislike"?"clicked dislike":"dislike"),we(e,"class",s="dislike-button "+l[2]+" svelte-21g1vy"),se(e,"padded",l[1]),se(e,"selected",l[3]==="dislike"),we(r,"class","like-button svelte-21g1vy"),we(r,"aria-label",u=l[3]==="like"?"clicked like":"like"),se(r,"padded",l[1]),se(r,"selected",l[3]==="like")},m($,z){dt($,e,z),q[t].m(e,null),dt($,o,z),dt($,r,z),c[a].m(r,null),_=!0,h||(p=[Et(e,"click",l[4]),Et(r,"click",l[5])],h=!0)},p($,[z]){let x=t;t=m($),t!==x&&(Dt(),_e(q[x],1,1,()=>{q[x]=null}),Nt(),n=q[t],n||(n=q[t]=w[t]($),n.c()),fe(n,1),n.m(e,null)),(!_||z&8&&i!==(i=$[3]==="dislike"?"clicked dislike":"dislike"))&&we(e,"aria-label",i),(!_||z&4&&s!==(s="dislike-button "+$[2]+" svelte-21g1vy"))&&we(e,"class",s),(!_||z&6)&&se(e,"padded",$[1]),(!_||z&12)&&se(e,"selected",$[3]==="dislike");let ee=a;a=v($),a!==ee&&(Dt(),_e(c[ee],1,1,()=>{c[ee]=null}),Nt(),f=c[a],f||(f=c[a]=b[a]($),f.c()),fe(f,1),f.m(r,null)),(!_||z&8&&u!==(u=$[3]==="like"?"clicked like":"like"))&&we(r,"aria-label",u),(!_||z&2)&&se(r,"padded",$[1]),(!_||z&8)&&se(r,"selected",$[3]==="like")},i($){_||(fe(n),fe(f),_=!0)},o($){_e(n),_e(f),_=!1},d($){$&&(mt(e),mt(o),mt(r)),q[t].d(),c[a].d(),h=!1,Mi(p)}}}function Ui(l,e,t){let{handle_action:n}=e,{padded:i=!1}=e,{position:s="left"}=e,o=null;const r=()=>{t(3,o="dislike"),n(o)},a=()=>{t(3,o="like"),n(o)};return l.$$set=f=>{"handle_action"in f&&t(0,n=f.handle_action),"padded"in f&&t(1,i=f.padded),"position"in f&&t(2,s=f.position)},[n,i,s,o,r,a]}class Ii extends Ai{constructor(e){super(),Li(this,e,Ui,Pi,Bi,{handle_action:0,padded:1,position:2})}get handle_action(){return this.$$.ctx[0]}set handle_action(e){this.$$set({handle_action:e}),ht()}get padded(){return this.$$.ctx[1]}set padded(e){this.$$set({padded:e}),ht()}get position(){return this.$$.ctx[2]}set position(e){this.$$set({position:e}),ht()}}const{SvelteComponent:Fi,append:Ri,attr:Re,check_outros:Pt,create_component:qn,destroy_component:Vn,detach:Gi,element:Oi,flush:Yi,group_outros:Ut,init:Ji,insert:Ki,listen:Qi,mount_component:zn,safe_not_equal:Wi,space:Xi,transition_in:re,transition_out:ze}=window.__gradio__svelte__internal,{onDestroy:xi}=window.__gradio__svelte__internal;function It(l){let e,t;return e=new $n({}),{c(){qn(e.$$.fragment)},m(n,i){zn(e,n,i),t=!0},i(n){t||(re(e.$$.fragment,n),t=!0)},o(n){ze(e.$$.fragment,n),t=!1},d(n){Vn(e,n)}}}function Ft(l){let e,t;return e=new kn({}),{c(){qn(e.$$.fragment)},m(n,i){zn(e,n,i),t=!0},i(n){t||(re(e.$$.fragment,n),t=!0)},o(n){ze(e.$$.fragment,n),t=!1},d(n){Vn(e,n)}}}function eo(l){let e,t,n,i,s,o,r=!l[0]&&It(),a=l[0]&&Ft();return{c(){e=Oi("button"),r&&r.c(),t=Xi(),a&&a.c(),Re(e,"class","action svelte-6u540h"),Re(e,"title","copy"),Re(e,"aria-label",n=l[0]?"Copied message":"Copy message")},m(f,u){Ki(f,e,u),r&&r.m(e,null),Ri(e,t),a&&a.m(e,null),i=!0,s||(o=Qi(e,"click",l[1]),s=!0)},p(f,[u]){f[0]?r&&(Ut(),ze(r,1,1,()=>{r=null}),Pt()):r?u&1&&re(r,1):(r=It(),r.c(),re(r,1),r.m(e,t)),f[0]?a?u&1&&re(a,1):(a=Ft(),a.c(),re(a,1),a.m(e,null)):a&&(Ut(),ze(a,1,1,()=>{a=null}),Pt()),(!i||u&1&&n!==(n=f[0]?"Copied message":"Copy message"))&&Re(e,"aria-label",n)},i(f){i||(re(r),re(a),i=!0)},o(f){ze(r),ze(a),i=!1},d(f){f&&Gi(e),r&&r.d(),a&&a.d(),s=!1,o()}}}function to(l,e,t){let n=!1,{value:i}=e,s;function o(){t(0,n=!0),s&&clearTimeout(s),s=setTimeout(()=>{t(0,n=!1)},2e3)}async function r(){if("clipboard"in navigator)await navigator.clipboard.writeText(i),o();else{const a=document.createElement("textarea");a.value=i,a.style.position="absolute",a.style.left="-999999px",document.body.prepend(a),a.select();try{document.execCommand("copy"),o()}catch(f){console.error(f)}finally{a.remove()}}}return xi(()=>{s&&clearTimeout(s)}),l.$$set=a=>{"value"in a&&t(2,i=a.value)},[n,r,i]}class no extends Fi{constructor(e){super(),Ji(this,e,to,eo,Wi,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),Yi()}}const{SvelteComponent:lo,append:Rt,attr:le,detach:io,init:oo,insert:so,noop:gt,safe_not_equal:ao,svg_element:bt}=window.__gradio__svelte__internal;function ro(l){let e,t,n;return{c(){e=bt("svg"),t=bt("path"),n=bt("path"),le(t,"d","M6.27701 8.253C6.24187 8.29143 6.19912 8.32212 6.15147 8.34311C6.10383 8.36411 6.05233 8.37495 6.00026 8.37495C5.94819 8.37495 5.89669 8.36411 5.84905 8.34311C5.8014 8.32212 5.75865 8.29143 5.72351 8.253L3.72351 6.0655C3.65798 5.99185 3.62408 5.89536 3.62916 5.79691C3.63424 5.69846 3.67788 5.60596 3.75064 5.53945C3.8234 5.47293 3.91943 5.43774 4.01794 5.44149C4.11645 5.44525 4.20952 5.48764 4.27701 5.5595L5.62501 7.0345V1.5C5.62501 1.40054 5.66452 1.30516 5.73485 1.23483C5.80517 1.16451 5.90055 1.125 6.00001 1.125C6.09947 1.125 6.19485 1.16451 6.26517 1.23483C6.3355 1.30516 6.37501 1.40054 6.37501 1.5V7.034L7.72351 5.559C7.79068 5.4856 7.88425 5.44189 7.98364 5.43748C8.08304 5.43308 8.18011 5.46833 8.25351 5.5355C8.32691 5.60267 8.37062 5.69624 8.37503 5.79563C8.37943 5.89503 8.34418 5.9921 8.27701 6.0655L6.27701 8.253Z"),le(t,"fill","currentColor"),le(n,"d","M1.875 7.39258C1.875 7.29312 1.83549 7.19774 1.76517 7.12741C1.69484 7.05709 1.59946 7.01758 1.5 7.01758C1.40054 7.01758 1.30516 7.05709 1.23483 7.12741C1.16451 7.19774 1.125 7.29312 1.125 7.39258V7.42008C1.125 8.10358 1.125 8.65508 1.1835 9.08858C1.2435 9.53858 1.3735 9.91758 1.674 10.2186C1.975 10.5196 2.354 10.6486 2.804 10.7096C3.2375 10.7676 3.789 10.7676 4.4725 10.7676H7.5275C8.211 10.7676 8.7625 10.7676 9.196 10.7096C9.646 10.6486 10.025 10.5196 10.326 10.2186C10.627 9.91758 10.756 9.53858 10.817 9.08858C10.875 8.65508 10.875 8.10358 10.875 7.42008V7.39258C10.875 7.29312 10.8355 7.19774 10.7652 7.12741C10.6948 7.05709 10.5995 7.01758 10.5 7.01758C10.4005 7.01758 10.3052 7.05709 10.2348 7.12741C10.1645 7.19774 10.125 7.29312 10.125 7.39258C10.125 8.11008 10.124 8.61058 10.0735 8.98858C10.024 9.35558 9.9335 9.54958 9.7955 9.68808C9.657 9.82658 9.463 9.91658 9.0955 9.96608C8.718 10.0166 8.2175 10.0176 7.5 10.0176H4.5C3.7825 10.0176 3.2815 10.0166 2.904 9.96608C2.537 9.91658 2.343 9.82608 2.2045 9.68808C2.066 9.54958 1.976 9.35558 1.9265 8.98808C1.876 8.61058 1.875 8.11008 1.875 7.39258Z"),le(n,"fill","currentColor"),le(e,"width","16"),le(e,"height","16"),le(e,"viewBox","0 0 12 12"),le(e,"fill","none"),le(e,"xmlns","http://www.w3.org/2000/svg")},m(i,s){so(i,e,s),Rt(e,t),Rt(e,n)},p:gt,i:gt,o:gt,d(i){i&&io(e)}}}class uo extends lo{constructor(e){super(),oo(this,e,null,ro,ao,{})}}const{SvelteComponent:fo,append:Gt,attr:wt,check_outros:Oe,create_component:et,destroy_component:tt,detach:$t,element:jn,empty:_o,flush:ae,group_outros:Ye,init:co,insert:Ct,mount_component:nt,noop:mo,safe_not_equal:ho,space:Ot,transition_in:L,transition_out:U}=window.__gradio__svelte__internal;function Yt(l){let e,t,n=l[7]&&!Array.isArray(l[2])&&ke(l[2]),i,s,o,r=l[8]&&Jt(l),a=n&&Kt(l),f=l[0]&&Qt(l);return{c(){e=jn("div"),r&&r.c(),t=Ot(),a&&a.c(),i=Ot(),f&&f.c(),wt(e,"class",s="message-buttons-"+l[3]+" "+l[6]+" message-buttons "+(l[4]!==null&&"with-avatar")+" svelte-hs6bgi")},m(u,_){Ct(u,e,_),r&&r.m(e,null),Gt(e,t),a&&a.m(e,null),Gt(e,i),f&&f.m(e,null),o=!0},p(u,_){u[8]?r?(r.p(u,_),_&256&&L(r,1)):(r=Jt(u),r.c(),L(r,1),r.m(e,t)):r&&(Ye(),U(r,1,1,()=>{r=null}),Oe()),_&132&&(n=u[7]&&!Array.isArray(u[2])&&ke(u[2])),n?a?(a.p(u,_),_&132&&L(a,1)):(a=Kt(u),a.c(),L(a,1),a.m(e,i)):a&&(Ye(),U(a,1,1,()=>{a=null}),Oe()),u[0]?f?(f.p(u,_),_&1&&L(f,1)):(f=Qt(u),f.c(),L(f,1),f.m(e,null)):f&&(Ye(),U(f,1,1,()=>{f=null}),Oe()),(!o||_&88&&s!==(s="message-buttons-"+u[3]+" "+u[6]+" message-buttons "+(u[4]!==null&&"with-avatar")+" svelte-hs6bgi"))&&wt(e,"class",s)},i(u){o||(L(r),L(a),L(f),o=!0)},o(u){U(r),U(a),U(f),o=!1},d(u){u&&$t(e),r&&r.d(),a&&a.d(),f&&f.d()}}}function Jt(l){let e,t;return e=new no({props:{value:l[9]}}),{c(){et(e.$$.fragment)},m(n,i){nt(e,n,i),t=!0},p(n,i){const s={};i&512&&(s.value=n[9]),e.$set(s)},i(n){t||(L(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){tt(e,n)}}}function Kt(l){let e,t;return e=new Qn({props:{href:l[2]?.content?.value.url,download:l[2].content.value.orig_name||"image",$$slots:{default:[go]},$$scope:{ctx:l}}}),{c(){et(e.$$.fragment)},m(n,i){nt(e,n,i),t=!0},p(n,i){const s={};i&4&&(s.href=n[2]?.content?.value.url),i&4&&(s.download=n[2].content.value.orig_name||"image"),i&2048&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(L(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){tt(e,n)}}}function go(l){let e,t,n;return t=new uo({}),{c(){e=jn("span"),et(t.$$.fragment),wt(e,"class","icon-wrap svelte-hs6bgi")},m(i,s){Ct(i,e,s),nt(t,e,null),n=!0},p:mo,i(i){n||(L(t.$$.fragment,i),n=!0)},o(i){U(t.$$.fragment,i),n=!1},d(i){i&&$t(e),tt(t)}}}function Qt(l){let e,t;return e=new Ii({props:{handle_action:l[5],padded:l[8]||l[7]}}),{c(){et(e.$$.fragment)},m(n,i){nt(e,n,i),t=!0},p(n,i){const s={};i&32&&(s.handle_action=n[5]),i&384&&(s.padded=n[8]||n[7]),e.$set(s)},i(n){t||(L(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){tt(e,n)}}}function bo(l){let e,t,n=l[1]&&Yt(l);return{c(){n&&n.c(),e=_o()},m(i,s){n&&n.m(i,s),Ct(i,e,s),t=!0},p(i,[s]){i[1]?n?(n.p(i,s),s&2&&L(n,1)):(n=Yt(i),n.c(),L(n,1),n.m(e.parentNode,e)):n&&(Ye(),U(n,1,1,()=>{n=null}),Oe())},i(i){t||(L(n),t=!0)},o(i){U(n),t=!1},d(i){i&&$t(e),n&&n.d(i)}}}function Wt(l){return Array.isArray(l)&&l.every(e=>typeof e.content=="string")||!Array.isArray(l)&&typeof l.content=="string"}function po(l){return Array.isArray(l)?l.map(e=>e.content).join(`
`):l.content}function wo(l,e,t){let n,i,s,{likeable:o}=e,{show_copy_button:r}=e,{show:a}=e,{message:f}=e,{position:u}=e,{avatar:_}=e,{handle_action:h}=e,{layout:p}=e;return l.$$set=w=>{"likeable"in w&&t(0,o=w.likeable),"show_copy_button"in w&&t(10,r=w.show_copy_button),"show"in w&&t(1,a=w.show),"message"in w&&t(2,f=w.message),"position"in w&&t(3,u=w.position),"avatar"in w&&t(4,_=w.avatar),"handle_action"in w&&t(5,h=w.handle_action),"layout"in w&&t(6,p=w.layout)},l.$$.update=()=>{l.$$.dirty&4&&t(9,n=Wt(f)?po(f):""),l.$$.dirty&1028&&t(8,i=r&&f&&Wt(f)),l.$$.dirty&4&&t(7,s=!Array.isArray(f)&&ke(f)&&f.content.value?.url)},[o,a,f,u,_,h,p,s,i,n,r]}class vo extends fo{constructor(e){super(),co(this,e,wo,bo,ho,{likeable:0,show_copy_button:10,show:1,message:2,position:3,avatar:4,handle_action:5,layout:6})}get likeable(){return this.$$.ctx[0]}set likeable(e){this.$$set({likeable:e}),ae()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),ae()}get show(){return this.$$.ctx[1]}set show(e){this.$$set({show:e}),ae()}get message(){return this.$$.ctx[2]}set message(e){this.$$set({message:e}),ae()}get position(){return this.$$.ctx[3]}set position(e){this.$$set({position:e}),ae()}get avatar(){return this.$$.ctx[4]}set avatar(e){this.$$set({avatar:e}),ae()}get handle_action(){return this.$$.ctx[5]}set handle_action(e){this.$$set({handle_action:e}),ae()}get layout(){return this.$$.ctx[6]}set layout(e){this.$$set({layout:e}),ae()}}const{SvelteComponent:ko,attr:Ze,check_outros:$o,create_component:An,destroy_component:Ln,detach:Co,element:yo,flush:Ho,group_outros:So,init:qo,insert:Vo,listen:zo,mount_component:Mn,null_to_empty:Xt,safe_not_equal:jo,transition_in:Je,transition_out:Ke}=window.__gradio__svelte__internal,{onDestroy:Ao}=window.__gradio__svelte__internal;function Lo(l){let e,t;return e=new $n({}),{c(){An(e.$$.fragment)},m(n,i){Mn(e,n,i),t=!0},i(n){t||(Je(e.$$.fragment,n),t=!0)},o(n){Ke(e.$$.fragment,n),t=!1},d(n){Ln(e,n)}}}function Mo(l){let e,t;return e=new kn({}),{c(){An(e.$$.fragment)},m(n,i){Mn(e,n,i),t=!0},i(n){t||(Je(e.$$.fragment,n),t=!0)},o(n){Ke(e.$$.fragment,n),t=!1},d(n){Ln(e,n)}}}function Bo(l){let e,t,n,i,s,o,r,a;const f=[Mo,Lo],u=[];function _(h,p){return h[0]?0:1}return t=_(l),n=u[t]=f[t](l),{c(){e=yo("button"),n.c(),Ze(e,"title","Copy conversation"),Ze(e,"class",i=Xt(l[0]?"copied":"copy-text")+" svelte-12prz41"),Ze(e,"aria-label",s=l[0]?"Copied conversation":"Copy conversation")},m(h,p){Vo(h,e,p),u[t].m(e,null),o=!0,r||(a=zo(e,"click",l[1]),r=!0)},p(h,[p]){let w=t;t=_(h),t!==w&&(So(),Ke(u[w],1,1,()=>{u[w]=null}),$o(),n=u[t],n||(n=u[t]=f[t](h),n.c()),Je(n,1),n.m(e,null)),(!o||p&1&&i!==(i=Xt(h[0]?"copied":"copy-text")+" svelte-12prz41"))&&Ze(e,"class",i),(!o||p&1&&s!==(s=h[0]?"Copied conversation":"Copy conversation"))&&Ze(e,"aria-label",s)},i(h){o||(Je(n),o=!0)},o(h){Ke(n),o=!1},d(h){h&&Co(e),u[t].d(),r=!1,a()}}}function To(l,e,t){let n=!1,{value:i}=e,s;function o(){t(0,n=!0),s&&clearTimeout(s),s=setTimeout(()=>{t(0,n=!1)},1e3)}const r=()=>{if(i){const f=i.map(u=>u.type==="text"?`${u.role}: ${u.content}`:`${u.role}: ${u.content.value.url}`).join(`

`);navigator.clipboard.writeText(f).catch(u=>{console.error("Failed to copy conversation: ",u)})}};async function a(){"clipboard"in navigator&&(r(),o())}return Ao(()=>{s&&clearTimeout(s)}),l.$$set=f=>{"value"in f&&t(2,i=f.value)},[n,a,i]}class No extends ko{constructor(e){super(),qo(this,e,To,Bo,jo,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),Ho()}}const{SvelteComponent:Zo,action_destroyer:Do,append:ie,attr:C,binding_callbacks:Eo,bubble:xt,check_outros:W,create_component:Y,destroy_component:J,destroy_each:Bn,detach:B,element:P,empty:Tn,ensure_array_like:Qe,flush:V,group_outros:X,init:Po,insert:T,listen:vt,mount_component:K,noop:en,null_to_empty:tn,run_all:Uo,safe_not_equal:Io,set_data:Fo,set_style:ve,space:ce,src_url_equal:nn,text:Ro,toggle_class:S,transition_in:k,transition_out:y}=window.__gradio__svelte__internal,{beforeUpdate:Go,afterUpdate:Oo,createEventDispatcher:Yo,tick:Jo,onMount:Ko}=window.__gradio__svelte__internal;function ln(l,e,t){const n=l.slice();n[45]=e[t],n[50]=t;const i=n[45][0].role==="user"?"user":"bot";n[46]=i;const s=n[10][n[46]==="user"?0:1];n[47]=s;const o=n[10][n[46]==="user"?0:1];return n[48]=o,n}function on(l,e,t){const n=l.slice();n[51]=e[t],n[54]=t;const i=n[45][0].type;return n[52]=i,n}function sn(l){let e,t,n;return t=new Jn({props:{i18n:l[16],formatter:sl,value:l[0]}}),t.$on("error",l[35]),t.$on("share",l[36]),{c(){e=P("div"),Y(t.$$.fragment),C(e,"class","share-button svelte-1e1jlin")},m(i,s){T(i,e,s),K(t,e,null),n=!0},p(i,s){const o={};s[0]&65536&&(o.i18n=i[16]),s[0]&1&&(o.value=i[0]),t.$set(o)},i(i){n||(k(t.$$.fragment,i),n=!0)},o(i){y(t.$$.fragment,i),n=!1},d(i){i&&B(e),J(t)}}}function an(l){let e,t;return e=new No({props:{value:l[0]}}),{c(){Y(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const s={};i[0]&1&&(s.value=n[0]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Qo(l){let e,t,n;return t=new kt({props:{message:l[18],latex_delimiters:l[2],root:l[21]}}),{c(){e=P("center"),Y(t.$$.fragment),C(e,"class","svelte-1e1jlin")},m(i,s){T(i,e,s),K(t,e,null),n=!0},p(i,s){const o={};s[0]&262144&&(o.message=i[18]),s[0]&4&&(o.latex_delimiters=i[2]),s[0]&2097152&&(o.root=i[21]),t.$set(o)},i(i){n||(k(t.$$.fragment,i),n=!0)},o(i){y(t.$$.fragment,i),n=!1},d(i){i&&B(e),J(t)}}}function Wo(l){let e,t,n,i=Qe(l[28]),s=[];for(let a=0;a<i.length;a+=1)s[a]=_n(ln(l,i,a));const o=a=>y(s[a],1,1,()=>{s[a]=null});let r=l[3]&&cn(l);return{c(){for(let a=0;a<s.length;a+=1)s[a].c();e=ce(),r&&r.c(),t=Tn()},m(a,f){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(a,f);T(a,e,f),r&&r.m(a,f),T(a,t,f),n=!0},p(a,f){if(f[0]&2130444087|f[1]&1){i=Qe(a[28]);let u;for(u=0;u<i.length;u+=1){const _=ln(a,i,u);s[u]?(s[u].p(_,f),k(s[u],1)):(s[u]=_n(_),s[u].c(),k(s[u],1),s[u].m(e.parentNode,e))}for(X(),u=i.length;u<s.length;u+=1)o(u);W()}a[3]?r?(r.p(a,f),f[0]&8&&k(r,1)):(r=cn(a),r.c(),k(r,1),r.m(t.parentNode,t)):r&&(X(),y(r,1,1,()=>{r=null}),W())},i(a){if(!n){for(let f=0;f<i.length;f+=1)k(s[f]);k(r),n=!0}},o(a){s=s.filter(Boolean);for(let f=0;f<s.length;f+=1)y(s[f]);y(r),n=!1},d(a){a&&(B(e),B(t)),Bn(s,a),r&&r.d(a)}}}function rn(l){let e,t,n,i,s,o,r,a,f;return o=new Rn({}),{c(){e=P("div"),t=P("img"),i=ce(),s=P("button"),Y(o.$$.fragment),nn(t.src,n=l[25])||C(t,"src",n),C(t,"alt",l[26]),C(t,"class","svelte-1e1jlin"),C(s,"class","image-preview-close-button svelte-1e1jlin"),C(e,"class","image-preview svelte-1e1jlin")},m(u,_){T(u,e,_),ie(e,t),ie(e,i),ie(e,s),K(o,s,null),r=!0,a||(f=vt(s,"click",l[37]),a=!0)},p(u,_){(!r||_[0]&33554432&&!nn(t.src,n=u[25]))&&C(t,"src",n),(!r||_[0]&67108864)&&C(t,"alt",u[26])},i(u){r||(k(o.$$.fragment,u),r=!0)},o(u){y(o.$$.fragment,u),r=!1},d(u){u&&B(e),J(o),a=!1,f()}}}function un(l){let e,t,n;return t=new Kn({props:{class:"avatar-image",src:l[47]?.url,alt:l[46]+" avatar"}}),{c(){e=P("div"),Y(t.$$.fragment),C(e,"class","avatar-container svelte-1e1jlin")},m(i,s){T(i,e,s),K(t,e,null),n=!0},p(i,s){const o={};s[0]&268436480&&(o.src=i[47]?.url),s[0]&268435456&&(o.alt=i[46]+" avatar"),t.$set(o)},i(i){n||(k(t.$$.fragment,i),n=!0)},o(i){y(t.$$.fragment,i),n=!1},d(i){i&&B(e),J(t)}}}function Xo(l){let e,t=(l[51].content.value?.orig_name||l[51].content.value?.path.split("/").pop()||"file")+"",n,i,s;return{c(){e=P("a"),n=Ro(t),C(e,"data-testid","chatbot-file"),C(e,"class","file-pil svelte-1e1jlin"),C(e,"href",i=l[51].content.value.url),C(e,"target","_blank"),C(e,"download",s=window.__is_colab__?null:l[51].content.value?.orig_name||l[51].content.value?.path.split("/").pop()||"file")},m(o,r){T(o,e,r),ie(e,n)},p(o,r){r[0]&268435456&&t!==(t=(o[51].content.value?.orig_name||o[51].content.value?.path.split("/").pop()||"file")+"")&&Fo(n,t),r[0]&268435456&&i!==(i=o[51].content.value.url)&&C(e,"href",i),r[0]&268435456&&s!==(s=window.__is_colab__?null:o[51].content.value?.orig_name||o[51].content.value?.path.split("/").pop()||"file")&&C(e,"download",s)},i:en,o:en,d(o){o&&B(e)}}}function xo(l){let e,t;return e=new Xl({props:{target:l[23],theme_mode:l[15],props:l[51].content.props,type:l[51].content.component,components:l[22],value:l[51].content.value,i18n:l[16],upload:l[19],_fetch:l[1]}}),e.$on("load",l[29]),{c(){Y(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const s={};i[0]&8388608&&(s.target=n[23]),i[0]&32768&&(s.theme_mode=n[15]),i[0]&268435456&&(s.props=n[51].content.props),i[0]&268435456&&(s.type=n[51].content.component),i[0]&4194304&&(s.components=n[22]),i[0]&268435456&&(s.value=n[51].content.value),i[0]&65536&&(s.i18n=n[16]),i[0]&524288&&(s.upload=n[19]),i[0]&2&&(s._fetch=n[1]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function es(l){let e,t,n,i;const s=[ns,ts],o=[];function r(a,f){return a[51].metadata.title?0:1}return e=r(l),t=o[e]=s[e](l),{c(){t.c(),n=Tn()},m(a,f){o[e].m(a,f),T(a,n,f),i=!0},p(a,f){let u=e;e=r(a),e===u?o[e].p(a,f):(X(),y(o[u],1,1,()=>{o[u]=null}),W(),t=o[e],t?t.p(a,f):(t=o[e]=s[e](a),t.c()),k(t,1),t.m(n.parentNode,n))},i(a){i||(k(t),i=!0)},o(a){y(t),i=!1},d(a){a&&B(n),o[e].d(a)}}}function ts(l){let e,t;return e=new kt({props:{message:l[51].content,latex_delimiters:l[2],sanitize_html:l[11],render_markdown:l[13],line_breaks:l[14],root:l[21]}}),e.$on("load",l[29]),{c(){Y(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const s={};i[0]&268435456&&(s.message=n[51].content),i[0]&4&&(s.latex_delimiters=n[2]),i[0]&2048&&(s.sanitize_html=n[11]),i[0]&8192&&(s.render_markdown=n[13]),i[0]&16384&&(s.line_breaks=n[14]),i[0]&2097152&&(s.root=n[21]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ns(l){let e,t;return e=new Tl({props:{title:l[51].metadata.title,$$slots:{default:[ls]},$$scope:{ctx:l}}}),{c(){Y(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const s={};i[0]&268435456&&(s.title=n[51].metadata.title),i[0]&270559236|i[1]&16777216&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function ls(l){let e,t;return e=new kt({props:{message:l[51].content,latex_delimiters:l[2],sanitize_html:l[11],render_markdown:l[13],line_breaks:l[14],root:l[21]}}),e.$on("load",l[29]),{c(){Y(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const s={};i[0]&268435456&&(s.message=n[51].content),i[0]&4&&(s.latex_delimiters=n[2]),i[0]&2048&&(s.sanitize_html=n[11]),i[0]&8192&&(s.render_markdown=n[13]),i[0]&16384&&(s.line_breaks=n[14]),i[0]&2097152&&(s.root=n[21]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function fn(l){let e,t,n,i,s,o,r,a,f,u,_,h;const p=[es,xo,Xo],w=[];function q(c,v){return c[51].type==="text"?0:c[51].type==="component"&&c[51].content.component in c[22]?1:c[51].type==="component"&&c[51].content.component==="file"?2:-1}~(n=q(l))&&(i=w[n]=p[n](l));function m(){return l[38](l[50],l[51])}function b(...c){return l[39](l[50],l[51],...c)}return{c(){e=P("div"),t=P("button"),i&&i.c(),a=ce(),C(t,"data-testid",s=l[46]),C(t,"dir",o=l[8]?"rtl":"ltr"),C(t,"aria-label",r=l[46]+"'s message: "+mn(l[51])),C(t,"class","svelte-1e1jlin"),S(t,"latest",l[50]===l[0].length-1),S(t,"message-markdown-disabled",!l[13]),S(t,"selectable",l[4]),ve(t,"user-select","text"),ve(t,"cursor",l[4]?"pointer":"default"),ve(t,"text-align",l[8]?"right":"left"),C(e,"class",f="message "+l[46]+" "+(ke(l[51])?l[51]?.content.component:"")+" svelte-1e1jlin"),S(e,"message-fit",!l[12]),S(e,"panel-full-width",!0),S(e,"message-markdown-disabled",!l[13]),S(e,"component",l[52]==="component"),S(e,"html",ke(l[51])&&l[51].content.component==="html"),S(e,"thought",l[54]>0),ve(e,"text-align",l[8]&&l[46]==="user"?"left":"right")},m(c,v){T(c,e,v),ie(e,t),~n&&w[n].m(t,null),ie(e,a),u=!0,_||(h=[vt(t,"click",m),vt(t,"keydown",b)],_=!0)},p(c,v){l=c;let $=n;n=q(l),n===$?~n&&w[n].p(l,v):(i&&(X(),y(w[$],1,1,()=>{w[$]=null}),W()),~n?(i=w[n],i?i.p(l,v):(i=w[n]=p[n](l),i.c()),k(i,1),i.m(t,null)):i=null),(!u||v[0]&268435456&&s!==(s=l[46]))&&C(t,"data-testid",s),(!u||v[0]&256&&o!==(o=l[8]?"rtl":"ltr"))&&C(t,"dir",o),(!u||v[0]&268435456&&r!==(r=l[46]+"'s message: "+mn(l[51])))&&C(t,"aria-label",r),(!u||v[0]&1)&&S(t,"latest",l[50]===l[0].length-1),(!u||v[0]&8192)&&S(t,"message-markdown-disabled",!l[13]),(!u||v[0]&16)&&S(t,"selectable",l[4]),v[0]&16&&ve(t,"cursor",l[4]?"pointer":"default"),v[0]&256&&ve(t,"text-align",l[8]?"right":"left"),(!u||v[0]&268435456&&f!==(f="message "+l[46]+" "+(ke(l[51])?l[51]?.content.component:"")+" svelte-1e1jlin"))&&C(e,"class",f),(!u||v[0]&268439552)&&S(e,"message-fit",!l[12]),(!u||v[0]&268435456)&&S(e,"panel-full-width",!0),(!u||v[0]&268443648)&&S(e,"message-markdown-disabled",!l[13]),(!u||v[0]&268435456)&&S(e,"component",l[52]==="component"),(!u||v[0]&268435456)&&S(e,"html",ke(l[51])&&l[51].content.component==="html"),(!u||v[0]&268435456)&&S(e,"thought",l[54]>0),v[0]&268435712&&ve(e,"text-align",l[8]&&l[46]==="user"?"left":"right")},i(c){u||(k(i),u=!0)},o(c){y(i),u=!1},d(c){c&&B(e),~n&&w[n].d(),_=!1,Uo(h)}}}function _n(l){let e,t,n,i,s,o,r,a,f,u=l[27]&&rn(l),_=l[47]!==null&&un(l),h=Qe(l[45]),p=[];for(let m=0;m<h.length;m+=1)p[m]=fn(on(l,h,m));const w=m=>y(p[m],1,1,()=>{p[m]=null});function q(...m){return l[40](l[50],l[45],...m)}return a=new vo({props:{show:l[5]||l[9],handle_action:q,likeable:l[5],show_copy_button:l[9],message:l[20]==="tuples"?l[45][0]:l[45],position:l[46]==="user"?"right":"left",avatar:l[47],layout:l[17]}}),{c(){u&&u.c(),e=ce(),t=P("div"),_&&_.c(),n=ce(),i=P("div");for(let m=0;m<p.length;m+=1)p[m].c();r=ce(),Y(a.$$.fragment),C(i,"class",s="flex-wrap "+l[46]+" svelte-1e1jlin"),S(i,"component-wrap",l[45][0].type==="component"),C(t,"class",o="message-row "+l[17]+" "+l[46]+"-row svelte-1e1jlin"),S(t,"with_avatar",l[47]!==null),S(t,"with_opposite_avatar",l[48]!==null)},m(m,b){u&&u.m(m,b),T(m,e,b),T(m,t,b),_&&_.m(t,null),ie(t,n),ie(t,i);for(let c=0;c<p.length;c+=1)p[c]&&p[c].m(i,null);T(m,r,b),K(a,m,b),f=!0},p(m,b){if(l=m,l[27]?u?(u.p(l,b),b[0]&134217728&&k(u,1)):(u=rn(l),u.c(),k(u,1),u.m(e.parentNode,e)):u&&(X(),y(u,1,1,()=>{u=null}),W()),l[47]!==null?_?(_.p(l,b),b[0]&268436480&&k(_,1)):(_=un(l),_.c(),k(_,1),_.m(t,n)):_&&(X(),y(_,1,1,()=>{_=null}),W()),b[0]&1894381847){h=Qe(l[45]);let v;for(v=0;v<h.length;v+=1){const $=on(l,h,v);p[v]?(p[v].p($,b),k(p[v],1)):(p[v]=fn($),p[v].c(),k(p[v],1),p[v].m(i,null))}for(X(),v=h.length;v<p.length;v+=1)w(v);W()}(!f||b[0]&268435456&&s!==(s="flex-wrap "+l[46]+" svelte-1e1jlin"))&&C(i,"class",s),(!f||b[0]&268435456)&&S(i,"component-wrap",l[45][0].type==="component"),(!f||b[0]&268566528&&o!==(o="message-row "+l[17]+" "+l[46]+"-row svelte-1e1jlin"))&&C(t,"class",o),(!f||b[0]&268567552)&&S(t,"with_avatar",l[47]!==null),(!f||b[0]&268567552)&&S(t,"with_opposite_avatar",l[48]!==null);const c={};b[0]&544&&(c.show=l[5]||l[9]),b[0]&268435456&&(c.handle_action=q),b[0]&32&&(c.likeable=l[5]),b[0]&512&&(c.show_copy_button=l[9]),b[0]&269484032&&(c.message=l[20]==="tuples"?l[45][0]:l[45]),b[0]&268435456&&(c.position=l[46]==="user"?"right":"left"),b[0]&268436480&&(c.avatar=l[47]),b[0]&131072&&(c.layout=l[17]),a.$set(c)},i(m){if(!f){k(u),k(_);for(let b=0;b<h.length;b+=1)k(p[b]);k(a.$$.fragment,m),f=!0}},o(m){y(u),y(_),p=p.filter(Boolean);for(let b=0;b<p.length;b+=1)y(p[b]);y(a.$$.fragment,m),f=!1},d(m){m&&(B(e),B(t),B(r)),u&&u.d(m),_&&_.d(),Bn(p,m),J(a,m)}}}function cn(l){let e,t;return e=new wl({props:{layout:l[17]}}),{c(){Y(e.$$.fragment)},m(n,i){K(e,n,i),t=!0},p(n,i){const s={};i[0]&131072&&(s.layout=n[17]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){y(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function is(l){let e,t,n,i,s,o,r,a,f,u,_=l[6]&&l[0]!==null&&l[0].length>0&&sn(l),h=l[7]&&an(l);const p=[Wo,Qo],w=[];function q(m,b){return m[0]!==null&&m[0].length>0&&m[28]!==null?0:m[18]!==null?1:-1}return~(s=q(l))&&(o=w[s]=p[s](l)),{c(){_&&_.c(),e=ce(),h&&h.c(),t=ce(),n=P("div"),i=P("div"),o&&o.c(),C(i,"class","message-wrap svelte-1e1jlin"),C(n,"class",r=tn(l[17]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1e1jlin"),C(n,"role","log"),C(n,"aria-label","chatbot conversation"),C(n,"aria-live","polite"),S(n,"placeholder-container",l[0]===null||l[0].length===0)},m(m,b){_&&_.m(m,b),T(m,e,b),h&&h.m(m,b),T(m,t,b),T(m,n,b),ie(n,i),~s&&w[s].m(i,null),l[41](n),a=!0,f||(u=Do(On.call(null,i)),f=!0)},p(m,b){m[6]&&m[0]!==null&&m[0].length>0?_?(_.p(m,b),b[0]&65&&k(_,1)):(_=sn(m),_.c(),k(_,1),_.m(e.parentNode,e)):_&&(X(),y(_,1,1,()=>{_=null}),W()),m[7]?h?(h.p(m,b),b[0]&128&&k(h,1)):(h=an(m),h.c(),k(h,1),h.m(t.parentNode,t)):h&&(X(),y(h,1,1,()=>{h=null}),W());let c=s;s=q(m),s===c?~s&&w[s].p(m,b):(o&&(X(),y(w[c],1,1,()=>{w[c]=null}),W()),~s?(o=w[s],o?o.p(m,b):(o=w[s]=p[s](m),o.c()),k(o,1),o.m(i,null)):o=null),(!a||b[0]&131072&&r!==(r=tn(m[17]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1e1jlin"))&&C(n,"class",r),(!a||b[0]&131073)&&S(n,"placeholder-container",m[0]===null||m[0].length===0)},i(m){a||(k(_),k(h),k(o),a=!0)},o(m){y(_),y(h),y(o),a=!1},d(m){m&&(B(e),B(t),B(n)),_&&_.d(m),h&&h.d(m),~s&&w[s].d(),l[41](null),f=!1,u()}}}function os(l){if(!l)return[];let e=new Set;return l.forEach(t=>{t.type==="component"&&e.add(t.content.component)}),Array.from(e)}function ss(){let e=getComputedStyle(document.body).getPropertyValue("--body-text-size"),t;switch(e){case"13px":t=14;break;case"14px":t=16;break;case"16px":t=20;break;default:t=14;break}document.body.style.setProperty("--chatbot-body-text-size",t+"px")}function mn(l){return l.type==="text"?l.content:l.type==="component"&&l.content.component==="file"?Array.isArray(l.content.value)?`file of extension type: ${l.content.value[0].orig_name?.split(".").pop()}`:`file of extension type: ${l.content.value?.orig_name?.split(".").pop()}`+(l.content.value?.orig_name??""):`a component of type ${l.content.component??"unknown"}`}function as(l,e,t){let n,{value:i=[]}=e,s=null,{_fetch:o}=e,{load_component:r}=e,a={};async function f(g){let j=[],A=[];g.forEach(N=>{if(a[N]||N==="file")return;const{name:Ne,component:st}=r(N,"base");j.push(Ne),A.push(st)}),(await Promise.all(A)).forEach((N,Ne)=>{t(22,a[j[Ne]]=N.default,a)})}let{latex_delimiters:u}=e,{pending_message:_=!1}=e,{selectable:h=!1}=e,{likeable:p=!1}=e,{show_share_button:w=!1}=e,{show_copy_all_button:q=!1}=e,{rtl:m=!1}=e,{show_copy_button:b=!1}=e,{avatar_images:c=[null,null]}=e,{sanitize_html:v=!0}=e,{bubble_full_width:$=!0}=e,{render_markdown:z=!0}=e,{line_breaks:x=!0}=e,{theme_mode:ee}=e,{i18n:je}=e,{layout:O="bubble"}=e,{placeholder:Ae=null}=e,{upload:Le}=e,{msg_format:oe="tuples"}=e,{root:Me}=e,Be=null;Ko(()=>{t(23,Be=document.querySelector("div.gradio-container")),ss()});let M,He;const Se=Yo();Go(()=>{t(34,He=M&&M.offsetHeight+M.scrollTop>M.scrollHeight-100)});async function Pe(){M&&(await Jo(),requestAnimationFrame(()=>{He&&M?.scrollTo(0,M.scrollHeight)}))}let Ue,Ie,Te=!1;Oo(()=>{M&&M.querySelectorAll("img").forEach(g=>{g.addEventListener("click",j=>{const A=j.target;A&&(t(25,Ue=A.src),t(26,Ie=A.alt),t(27,Te=!0))})})});function d(g,j){Se("select",{index:j.index,value:j.content})}function yt(g,j,A){if(oe==="tuples")Se("like",{index:j.index,value:j.content,liked:A==="like"});else{if(!n)return;const te=n[g],[N,Ne]=[te[0],te[te.length-1]];Se("like",{index:[N.index,Ne.index],value:te.map(st=>st.content),liked:A==="like"})}}function Nn(g){const j=[];let A=[],te=null;for(const N of g)oe==="tuples"&&(te=null),(N.role==="assistant"||N.role==="user")&&(N.role===te?A.push(N):(A.length>0&&j.push(A),A=[N],te=N.role));return A.length>0&&j.push(A),j}function Zn(g){xt.call(this,l,g)}function Dn(g){xt.call(this,l,g)}const En=()=>{t(27,Te=!1)},Pn=(g,j)=>d(g,j),Un=(g,j,A)=>{A.key==="Enter"&&d(g,j)},In=(g,j,A)=>yt(g,j[0],A);function Fn(g){Eo[g?"unshift":"push"](()=>{M=g,t(24,M)})}return l.$$set=g=>{"value"in g&&t(0,i=g.value),"_fetch"in g&&t(1,o=g._fetch),"load_component"in g&&t(32,r=g.load_component),"latex_delimiters"in g&&t(2,u=g.latex_delimiters),"pending_message"in g&&t(3,_=g.pending_message),"selectable"in g&&t(4,h=g.selectable),"likeable"in g&&t(5,p=g.likeable),"show_share_button"in g&&t(6,w=g.show_share_button),"show_copy_all_button"in g&&t(7,q=g.show_copy_all_button),"rtl"in g&&t(8,m=g.rtl),"show_copy_button"in g&&t(9,b=g.show_copy_button),"avatar_images"in g&&t(10,c=g.avatar_images),"sanitize_html"in g&&t(11,v=g.sanitize_html),"bubble_full_width"in g&&t(12,$=g.bubble_full_width),"render_markdown"in g&&t(13,z=g.render_markdown),"line_breaks"in g&&t(14,x=g.line_breaks),"theme_mode"in g&&t(15,ee=g.theme_mode),"i18n"in g&&t(16,je=g.i18n),"layout"in g&&t(17,O=g.layout),"placeholder"in g&&t(18,Ae=g.placeholder),"upload"in g&&t(19,Le=g.upload),"msg_format"in g&&t(20,oe=g.msg_format),"root"in g&&t(21,Me=g.root)},l.$$.update=()=>{l.$$.dirty[0]&1&&f(os(i)),l.$$.dirty[0]&4194305|l.$$.dirty[1]&8&&(i||He||a)&&Pe(),l.$$.dirty[0]&1|l.$$.dirty[1]&4&&(Yn(i,s)||(t(33,s=i),Se("change"))),l.$$.dirty[0]&1&&t(28,n=i&&Nn(i))},[i,o,u,_,h,p,w,q,m,b,c,v,$,z,x,ee,je,O,Ae,Le,oe,Me,a,Be,M,Ue,Ie,Te,n,Pe,d,yt,r,s,He,Zn,Dn,En,Pn,Un,In,Fn]}class rs extends Zo{constructor(e){super(),Po(this,e,as,is,Io,{value:0,_fetch:1,load_component:32,latex_delimiters:2,pending_message:3,selectable:4,likeable:5,show_share_button:6,show_copy_all_button:7,rtl:8,show_copy_button:9,avatar_images:10,sanitize_html:11,bubble_full_width:12,render_markdown:13,line_breaks:14,theme_mode:15,i18n:16,layout:17,placeholder:18,upload:19,msg_format:20,root:21},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),V()}get _fetch(){return this.$$.ctx[1]}set _fetch(e){this.$$set({_fetch:e}),V()}get load_component(){return this.$$.ctx[32]}set load_component(e){this.$$set({load_component:e}),V()}get latex_delimiters(){return this.$$.ctx[2]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),V()}get pending_message(){return this.$$.ctx[3]}set pending_message(e){this.$$set({pending_message:e}),V()}get selectable(){return this.$$.ctx[4]}set selectable(e){this.$$set({selectable:e}),V()}get likeable(){return this.$$.ctx[5]}set likeable(e){this.$$set({likeable:e}),V()}get show_share_button(){return this.$$.ctx[6]}set show_share_button(e){this.$$set({show_share_button:e}),V()}get show_copy_all_button(){return this.$$.ctx[7]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),V()}get rtl(){return this.$$.ctx[8]}set rtl(e){this.$$set({rtl:e}),V()}get show_copy_button(){return this.$$.ctx[9]}set show_copy_button(e){this.$$set({show_copy_button:e}),V()}get avatar_images(){return this.$$.ctx[10]}set avatar_images(e){this.$$set({avatar_images:e}),V()}get sanitize_html(){return this.$$.ctx[11]}set sanitize_html(e){this.$$set({sanitize_html:e}),V()}get bubble_full_width(){return this.$$.ctx[12]}set bubble_full_width(e){this.$$set({bubble_full_width:e}),V()}get render_markdown(){return this.$$.ctx[13]}set render_markdown(e){this.$$set({render_markdown:e}),V()}get line_breaks(){return this.$$.ctx[14]}set line_breaks(e){this.$$set({line_breaks:e}),V()}get theme_mode(){return this.$$.ctx[15]}set theme_mode(e){this.$$set({theme_mode:e}),V()}get i18n(){return this.$$.ctx[16]}set i18n(e){this.$$set({i18n:e}),V()}get layout(){return this.$$.ctx[17]}set layout(e){this.$$set({layout:e}),V()}get placeholder(){return this.$$.ctx[18]}set placeholder(e){this.$$set({placeholder:e}),V()}get upload(){return this.$$.ctx[19]}set upload(e){this.$$set({upload:e}),V()}get msg_format(){return this.$$.ctx[20]}set msg_format(e){this.$$set({msg_format:e}),V()}get root(){return this.$$.ctx[21]}set root(e){this.$$set({root:e}),V()}}const us=rs,{SvelteComponent:fs,append:_s,assign:cs,attr:ms,check_outros:hn,create_component:lt,destroy_component:it,detach:dn,element:hs,flush:H,get_spread_object:ds,get_spread_update:gs,group_outros:gn,init:bs,insert:bn,mount_component:ot,safe_not_equal:ps,space:pn,transition_in:Q,transition_out:ue}=window.__gradio__svelte__internal;function wn(l){let e,t;const n=[{autoscroll:l[21].autoscroll},{i18n:l[21].i18n},l[23],{show_progress:l[23].show_progress==="hidden"?"hidden":"minimal"}];let i={};for(let s=0;s<n.length;s+=1)i=cs(i,n[s]);return e=new Gn({props:i}),e.$on("clear_status",l[29]),{c(){lt(e.$$.fragment)},m(s,o){ot(e,s,o),t=!0},p(s,o){const r=o[0]&10485760?gs(n,[o[0]&2097152&&{autoscroll:s[21].autoscroll},o[0]&2097152&&{i18n:s[21].i18n},o[0]&8388608&&ds(s[23]),o[0]&8388608&&{show_progress:s[23].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(r)},i(s){t||(Q(e.$$.fragment,s),t=!0)},o(s){ue(e.$$.fragment,s),t=!1},d(s){it(e,s)}}}function vn(l){let e,t;return e=new Xn({props:{show_label:l[7],Icon:ol,float:!0,label:l[6]||"Chatbot"}}),{c(){lt(e.$$.fragment)},m(n,i){ot(e,n,i),t=!0},p(n,i){const s={};i[0]&128&&(s.show_label=n[7]),i[0]&64&&(s.label=n[6]||"Chatbot"),e.$set(s)},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){ue(e.$$.fragment,n),t=!1},d(n){it(e,n)}}}function ws(l){let e,t,n,i,s,o=l[23]&&wn(l),r=l[7]&&vn(l);return i=new us({props:{i18n:l[21].i18n,selectable:l[8],likeable:l[9],show_share_button:l[10],show_copy_all_button:l[13],value:l[27],latex_delimiters:l[20],render_markdown:l[18],theme_mode:l[26],pending_message:l[23]?.status==="pending",rtl:l[11],show_copy_button:l[12],avatar_images:l[22],sanitize_html:l[14],bubble_full_width:l[15],line_breaks:l[19],layout:l[16],placeholder:l[25],upload:l[21].client.upload,_fetch:l[21].client.fetch,load_component:l[21].load_component,msg_format:l[17],root:l[21].root}}),i.$on("change",l[30]),i.$on("select",l[31]),i.$on("like",l[32]),i.$on("share",l[33]),i.$on("error",l[34]),{c(){o&&o.c(),e=pn(),t=hs("div"),r&&r.c(),n=pn(),lt(i.$$.fragment),ms(t,"class","wrapper svelte-nab2ao")},m(a,f){o&&o.m(a,f),bn(a,e,f),bn(a,t,f),r&&r.m(t,null),_s(t,n),ot(i,t,null),s=!0},p(a,f){a[23]?o?(o.p(a,f),f[0]&8388608&&Q(o,1)):(o=wn(a),o.c(),Q(o,1),o.m(e.parentNode,e)):o&&(gn(),ue(o,1,1,()=>{o=null}),hn()),a[7]?r?(r.p(a,f),f[0]&128&&Q(r,1)):(r=vn(a),r.c(),Q(r,1),r.m(t,n)):r&&(gn(),ue(r,1,1,()=>{r=null}),hn());const u={};f[0]&2097152&&(u.i18n=a[21].i18n),f[0]&256&&(u.selectable=a[8]),f[0]&512&&(u.likeable=a[9]),f[0]&1024&&(u.show_share_button=a[10]),f[0]&8192&&(u.show_copy_all_button=a[13]),f[0]&134217728&&(u.value=a[27]),f[0]&1048576&&(u.latex_delimiters=a[20]),f[0]&262144&&(u.render_markdown=a[18]),f[0]&67108864&&(u.theme_mode=a[26]),f[0]&8388608&&(u.pending_message=a[23]?.status==="pending"),f[0]&2048&&(u.rtl=a[11]),f[0]&4096&&(u.show_copy_button=a[12]),f[0]&4194304&&(u.avatar_images=a[22]),f[0]&16384&&(u.sanitize_html=a[14]),f[0]&32768&&(u.bubble_full_width=a[15]),f[0]&524288&&(u.line_breaks=a[19]),f[0]&65536&&(u.layout=a[16]),f[0]&33554432&&(u.placeholder=a[25]),f[0]&2097152&&(u.upload=a[21].client.upload),f[0]&2097152&&(u._fetch=a[21].client.fetch),f[0]&2097152&&(u.load_component=a[21].load_component),f[0]&131072&&(u.msg_format=a[17]),f[0]&2097152&&(u.root=a[21].root),i.$set(u)},i(a){s||(Q(o),Q(r),Q(i.$$.fragment,a),s=!0)},o(a){ue(o),ue(r),ue(i.$$.fragment,a),s=!1},d(a){a&&(dn(e),dn(t)),o&&o.d(a),r&&r.d(),it(i)}}}function vs(l){let e,t;return e=new Wn({props:{elem_id:l[0],elem_classes:l[1],visible:l[2],padding:!1,scale:l[4],min_width:l[5],height:l[24],allow_overflow:!1,$$slots:{default:[ws]},$$scope:{ctx:l}}}),{c(){lt(e.$$.fragment)},m(n,i){ot(e,n,i),t=!0},p(n,i){const s={};i[0]&1&&(s.elem_id=n[0]),i[0]&2&&(s.elem_classes=n[1]),i[0]&4&&(s.visible=n[2]),i[0]&16&&(s.scale=n[4]),i[0]&32&&(s.min_width=n[5]),i[0]&16777216&&(s.height=n[24]),i[0]&251658184|i[1]&16&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(Q(e.$$.fragment,n),t=!0)},o(n){ue(e.$$.fragment,n),t=!1},d(n){it(e,n)}}}function ks(l,e,t){let{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:s=!0}=e,{value:o=[]}=e,{scale:r=null}=e,{min_width:a=void 0}=e,{label:f}=e,{show_label:u=!0}=e,{root:_}=e,{_selectable:h=!1}=e,{likeable:p=!1}=e,{show_share_button:w=!1}=e,{rtl:q=!1}=e,{show_copy_button:m=!0}=e,{show_copy_all_button:b=!1}=e,{sanitize_html:c=!0}=e,{bubble_full_width:v=!0}=e,{layout:$="bubble"}=e,{type:z="tuples"}=e,{render_markdown:x=!0}=e,{line_breaks:ee=!0}=e,{latex_delimiters:je}=e,{gradio:O}=e,{avatar_images:Ae=[null,null]}=e,Le=[],{loading_status:oe=void 0}=e,{height:Me=400}=e,{placeholder:Be=null}=e,{theme_mode:M}=e;const He=()=>O.dispatch("clear_status",oe),Se=()=>O.dispatch("change",o),Pe=d=>O.dispatch("select",d.detail),Ue=d=>O.dispatch("like",d.detail),Ie=d=>O.dispatch("share",d.detail),Te=d=>O.dispatch("error",d.detail);return l.$$set=d=>{"elem_id"in d&&t(0,n=d.elem_id),"elem_classes"in d&&t(1,i=d.elem_classes),"visible"in d&&t(2,s=d.visible),"value"in d&&t(3,o=d.value),"scale"in d&&t(4,r=d.scale),"min_width"in d&&t(5,a=d.min_width),"label"in d&&t(6,f=d.label),"show_label"in d&&t(7,u=d.show_label),"root"in d&&t(28,_=d.root),"_selectable"in d&&t(8,h=d._selectable),"likeable"in d&&t(9,p=d.likeable),"show_share_button"in d&&t(10,w=d.show_share_button),"rtl"in d&&t(11,q=d.rtl),"show_copy_button"in d&&t(12,m=d.show_copy_button),"show_copy_all_button"in d&&t(13,b=d.show_copy_all_button),"sanitize_html"in d&&t(14,c=d.sanitize_html),"bubble_full_width"in d&&t(15,v=d.bubble_full_width),"layout"in d&&t(16,$=d.layout),"type"in d&&t(17,z=d.type),"render_markdown"in d&&t(18,x=d.render_markdown),"line_breaks"in d&&t(19,ee=d.line_breaks),"latex_delimiters"in d&&t(20,je=d.latex_delimiters),"gradio"in d&&t(21,O=d.gradio),"avatar_images"in d&&t(22,Ae=d.avatar_images),"loading_status"in d&&t(23,oe=d.loading_status),"height"in d&&t(24,Me=d.height),"placeholder"in d&&t(25,Be=d.placeholder),"theme_mode"in d&&t(26,M=d.theme_mode)},l.$$.update=()=>{l.$$.dirty[0]&268566536&&t(27,Le=z==="tuples"?ul(o,_):rl(o,_))},[n,i,s,o,r,a,f,u,h,p,w,q,m,b,c,v,$,z,x,ee,je,O,Ae,oe,Me,Be,M,Le,_,He,Se,Pe,Ue,Ie,Te]}class Es extends fs{constructor(e){super(),bs(this,e,ks,vs,ps,{elem_id:0,elem_classes:1,visible:2,value:3,scale:4,min_width:5,label:6,show_label:7,root:28,_selectable:8,likeable:9,show_share_button:10,rtl:11,show_copy_button:12,show_copy_all_button:13,sanitize_html:14,bubble_full_width:15,layout:16,type:17,render_markdown:18,line_breaks:19,latex_delimiters:20,gradio:21,avatar_images:22,loading_status:23,height:24,placeholder:25,theme_mode:26},null,[-1,-1])}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),H()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),H()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),H()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),H()}get scale(){return this.$$.ctx[4]}set scale(e){this.$$set({scale:e}),H()}get min_width(){return this.$$.ctx[5]}set min_width(e){this.$$set({min_width:e}),H()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),H()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),H()}get root(){return this.$$.ctx[28]}set root(e){this.$$set({root:e}),H()}get _selectable(){return this.$$.ctx[8]}set _selectable(e){this.$$set({_selectable:e}),H()}get likeable(){return this.$$.ctx[9]}set likeable(e){this.$$set({likeable:e}),H()}get show_share_button(){return this.$$.ctx[10]}set show_share_button(e){this.$$set({show_share_button:e}),H()}get rtl(){return this.$$.ctx[11]}set rtl(e){this.$$set({rtl:e}),H()}get show_copy_button(){return this.$$.ctx[12]}set show_copy_button(e){this.$$set({show_copy_button:e}),H()}get show_copy_all_button(){return this.$$.ctx[13]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),H()}get sanitize_html(){return this.$$.ctx[14]}set sanitize_html(e){this.$$set({sanitize_html:e}),H()}get bubble_full_width(){return this.$$.ctx[15]}set bubble_full_width(e){this.$$set({bubble_full_width:e}),H()}get layout(){return this.$$.ctx[16]}set layout(e){this.$$set({layout:e}),H()}get type(){return this.$$.ctx[17]}set type(e){this.$$set({type:e}),H()}get render_markdown(){return this.$$.ctx[18]}set render_markdown(e){this.$$set({render_markdown:e}),H()}get line_breaks(){return this.$$.ctx[19]}set line_breaks(e){this.$$set({line_breaks:e}),H()}get latex_delimiters(){return this.$$.ctx[20]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),H()}get gradio(){return this.$$.ctx[21]}set gradio(e){this.$$set({gradio:e}),H()}get avatar_images(){return this.$$.ctx[22]}set avatar_images(e){this.$$set({avatar_images:e}),H()}get loading_status(){return this.$$.ctx[23]}set loading_status(e){this.$$set({loading_status:e}),H()}get height(){return this.$$.ctx[24]}set height(e){this.$$set({height:e}),H()}get placeholder(){return this.$$.ctx[25]}set placeholder(e){this.$$set({placeholder:e}),H()}get theme_mode(){return this.$$.ctx[26]}set theme_mode(e){this.$$set({theme_mode:e}),H()}}export{us as BaseChatBot,Es as default};
//# sourceMappingURL=Index-A4-C6YLr.js.map
