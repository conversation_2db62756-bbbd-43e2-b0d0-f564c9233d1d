# Don't change this file. Instead, copy it to .env and change the values there. The default values will work out of the box as long as you provide your license key.
_LANGSMITH_IMAGE_VERSION=0.8.12 # Change to the desired Langsmith image version
LANGSMITH_LICENSE_KEY=your-license-key # Change to your Langsmith license key
AUTH_TYPE=none # Set to oauth if you want to use OAuth2.0 with PKCE. Set to mixed for basic auth or OAuth2.0 with OAuth2.0 client secret
OAUTH_CLIENT_ID=your-client-id # Required if AUTH_TYPE=oauth or mixed with OAuth2.0 with OAuth2.0 client secret
OAUTH_ISSUER_URL=https://your-issuer-url # Required if AUTH_TYPE=oauth or mixed with OAuth2.0 with OAuth2.0 client secret
OAUTH_CLIENT_SECRET=your-client-secret # Required if AUTH_TYPE=mixed with OAuth2.0 with OAuth2.0 client secret
LANGSMITH_URL=http://localhost:1980 # Change to your hosted Langsmith URL. Required if AUTH_TYPE=mixed with OAuth2.0 client secret
API_KEY_SALT=super # Change to your desired API key salt. Can be any random value. Must be set if AUTH_TYPE=oauth
POSTGRES_DATABASE_URI=postgres:postgres@langchain-db:5432/postgres # Change to your database URI if using external postgres. Otherwise, leave it as is
REDIS_DATABASE_URI=redis://langchain-redis:6379 # Change to your Redis URI if using external Redis. Otherwise, leave it as is
LOG_LEVEL=warning # Change to your desired log level
MAX_ASYNC_JOBS_PER_WORKER=10 # Change to your desired maximum async jobs per worker. We recommend 10/suggest spinning up more replicas of the queue worker if you need more throughput
ASYNCPG_POOL_MAX_SIZE=3 # Change the PG pool size based off your pg instance/requirements.
CLICKHOUSE_HOST=langchain-clickhouse # Change to your Clickhouse host if using external Clickhouse. Otherwise, leave it as is
CLICKHOUSE_USER=default # Change to your Clickhouse user if needed
CLICKHOUSE_DB=default # Change to your Clickhouse database if needed
CLICKHOUSE_PORT=8123 # Change to your Clickhouse port if needed
CLICKHOUSE_TLS=false # Change to true if you are using TLS to connect to Clickhouse. Otherwise, leave it as is
CLICKHOUSE_PASSWORD=password # Change to your Clickhouse password if needed
CLICKHOUSE_NATIVE_PORT=9000 # Change to your Clickhouse native port if needed
ORG_CREATION_DISABLED=false # Set to true if you want to disable org creation
WORKSPACE_SCOPE_ORG_INVITES_ENABLED=false # Set to true if you want to disable workspace scope org invites
PERSONAL_ORGS_DISABLED=false # Set to true if you want to disable personal orgs
TTL_ENABLED=true # Set to true if you want to enable TTL for your data
SHORT_LIVED_TTL_SECONDS=1209600 # Set to your desired TTL for short-lived traces. Default is 14 days
LONG_LIVED_TTL_SECONDS=34560000 # Set to your desired TTL for long-lived traces. Default is 400 days
BLOB_STORAGE_ENABLED=false # Set to true if you want to enable blob storage
BLOB_STORAGE_BUCKET_NAME=langsmith-blob-storage # Change to your desired blob storage bucket name
BLOB_STORAGE_API_URL=https://s3.us-west-2.amazonaws.com # Change to your desired blob storage API URL
BLOB_STORAGE_ACCESS_KEY=your-access-key # Change to your desired blob storage access key
BLOB_STORAGE_ACCESS_KEY_SECRET=your-access-key-secret # Change to your desired blob storage access key secret
CH_SEARCH_ENABLED=true # Set to false if you do not want to store tokenized inputs/outputs in clickhouse
BASIC_AUTH_ENABLED=false # Set to true if you want to enable basic auth
BASIC_AUTH_JWT_SECRET=your-jwt-secret # Change to your desired basic auth JWT secret
INITIAL_ORG_ADMIN_EMAIL=your-email # Change to your desired initial org admin email. Only used if BASIC_AUTH_ENABLED=true
INITIAL_ORG_ADMIN_PASSWORD=your-password # Change to your desired initial org admin password. Only used if BASIC_AUTH_ENABLED=true
