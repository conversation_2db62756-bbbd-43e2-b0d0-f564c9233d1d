import{S as ne}from"./Index-DB1XLvMK.js";import{L as te}from"./LineChart-CKh1Fdep.js";import{B as ae}from"./Button-BIUaXfcG.js";import{B as se}from"./BlockLabel-BlSr62f_.js";import{E as ie}from"./Empty-BgF7sXBn.js";import"./index-BQPjLIsY.js";import"./svelte/svelte.js";const{SvelteComponent:re,append:h,attr:d,destroy_each:ce,detach:R,element:B,empty:oe,ensure_array_like:V,flush:H,init:ue,insert:T,listen:fe,noop:W,safe_not_equal:_e,set_data:Q,set_style:I,space:z,text:A,toggle_class:F}=window.__gradio__svelte__internal,{createEventDispatcher:de}=window.__gradio__svelte__internal;function X(n,e,a){const t=n.slice();return t[5]=e[a],t[7]=a,t}function Y(n){let e,a=V(n[0].confidences),t=[];for(let l=0;l<a.length;l+=1)t[l]=Z(X(n,a,l));return{c(){for(let l=0;l<t.length;l+=1)t[l].c();e=oe()},m(l,s){for(let i=0;i<t.length;i+=1)t[i]&&t[i].m(l,s);T(l,e,s)},p(l,s){if(s&13){a=V(l[0].confidences);let i;for(i=0;i<a.length;i+=1){const u=X(l,a,i);t[i]?t[i].p(u,s):(t[i]=Z(u),t[i].c(),t[i].m(e.parentNode,e))}for(;i<t.length;i+=1)t[i].d(1);t.length=a.length}},d(l){l&&R(e),ce(t,l)}}}function Z(n){let e,a,t,l,s,i,u,f,b,o,$=n[5].label+"",m,N,r,_,S,k=Math.round(n[5].confidence*100)+"",q,J,c,O,G,U;function le(){return n[4](n[7],n[5])}return{c(){e=B("button"),a=B("div"),t=B("meter"),f=z(),b=B("dl"),o=B("dt"),m=A($),N=z(),_=B("div"),S=B("dd"),q=A(k),J=A("%"),c=z(),d(t,"aria-labelledby",l=D(`meter-text-${n[5].label}`)),d(t,"aria-label",s=n[5].label),d(t,"aria-valuenow",i=Math.round(n[5].confidence*100)),d(t,"aria-valuemin","0"),d(t,"aria-valuemax","100"),d(t,"class","bar svelte-1l15rn0"),d(t,"min","0"),d(t,"max","1"),t.value=u=n[5].confidence,I(t,"width",n[5].confidence*100+"%"),I(t,"background","var(--stat-background-fill)"),d(o,"id",r=D(`meter-text-${n[5].label}`)),d(o,"class","text svelte-1l15rn0"),d(_,"class","line svelte-1l15rn0"),d(S,"class","confidence svelte-1l15rn0"),d(b,"class","label svelte-1l15rn0"),d(a,"class","inner-wrap svelte-1l15rn0"),d(e,"class","confidence-set group svelte-1l15rn0"),d(e,"data-testid",O=`${n[5].label}-confidence-set`),F(e,"selectable",n[2])},m(E,v){T(E,e,v),h(e,a),h(a,t),h(a,f),h(a,b),h(b,o),h(o,m),h(o,N),h(b,_),h(b,S),h(S,q),h(S,J),h(e,c),G||(U=fe(e,"click",le),G=!0)},p(E,v){n=E,v&1&&l!==(l=D(`meter-text-${n[5].label}`))&&d(t,"aria-labelledby",l),v&1&&s!==(s=n[5].label)&&d(t,"aria-label",s),v&1&&i!==(i=Math.round(n[5].confidence*100))&&d(t,"aria-valuenow",i),v&1&&u!==(u=n[5].confidence)&&(t.value=u),v&1&&I(t,"width",n[5].confidence*100+"%"),v&1&&$!==($=n[5].label+"")&&Q(m,$),v&1&&r!==(r=D(`meter-text-${n[5].label}`))&&d(o,"id",r),v&1&&k!==(k=Math.round(n[5].confidence*100)+"")&&Q(q,k),v&1&&O!==(O=`${n[5].label}-confidence-set`)&&d(e,"data-testid",O),v&4&&F(e,"selectable",n[2])},d(E){E&&R(e),G=!1,U()}}}function be(n){let e,a,t=n[0].label+"",l,s,i=typeof n[0]=="object"&&n[0].confidences&&Y(n);return{c(){e=B("div"),a=B("h2"),l=A(t),s=z(),i&&i.c(),d(a,"class","output-class svelte-1l15rn0"),d(a,"data-testid","label-output-value"),F(a,"no-confidence",!("confidences"in n[0])),I(a,"background-color",n[1]||"transparent"),d(e,"class","container svelte-1l15rn0")},m(u,f){T(u,e,f),h(e,a),h(a,l),h(e,s),i&&i.m(e,null)},p(u,[f]){f&1&&t!==(t=u[0].label+"")&&Q(l,t),f&1&&F(a,"no-confidence",!("confidences"in u[0])),f&2&&I(a,"background-color",u[1]||"transparent"),typeof u[0]=="object"&&u[0].confidences?i?i.p(u,f):(i=Y(u),i.c(),i.m(e,null)):i&&(i.d(1),i=null)},i:W,o:W,d(u){u&&R(e),i&&i.d()}}}function D(n){return n.replace(/\s/g,"-")}function me(n,e,a){let{value:t}=e;const l=de();let{color:s=void 0}=e,{selectable:i=!1}=e;const u=(f,b)=>{l("select",{index:f,value:b.label})};return n.$$set=f=>{"value"in f&&a(0,t=f.value),"color"in f&&a(1,s=f.color),"selectable"in f&&a(2,i=f.selectable)},[t,s,i,l,u]}class he extends re{constructor(e){super(),ue(this,e,me,be,_e,{value:0,color:1,selectable:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),H()}get color(){return this.$$.ctx[1]}set color(e){this.$$set({color:e}),H()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),H()}}const ge=he,{SvelteComponent:ve,assign:ke,check_outros:p,create_component:C,destroy_component:M,detach:K,empty:we,flush:g,get_spread_object:$e,get_spread_update:Se,group_outros:y,init:Le,insert:P,mount_component:j,safe_not_equal:Be,space:x,transition_in:w,transition_out:L}=window.__gradio__svelte__internal;function ee(n){let e,a;return e=new se({props:{Icon:te,label:n[6],disable:n[7]===!1}}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),a=!0},p(t,l){const s={};l&64&&(s.label=t[6]),l&128&&(s.disable=t[7]===!1),e.$set(s)},i(t){a||(w(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){M(e,t)}}}function Ne(n){let e,a;return e=new ie({props:{unpadded_box:!0,$$slots:{default:[Me]},$$scope:{ctx:n}}}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),a=!0},p(t,l){const s={};l&131072&&(s.$$scope={dirty:l,ctx:t}),e.$set(s)},i(t){a||(w(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){M(e,t)}}}function Ce(n){let e,a;return e=new ge({props:{selectable:n[12],value:n[5],color:n[4]}}),e.$on("select",n[16]),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),a=!0},p(t,l){const s={};l&4096&&(s.selectable=t[12]),l&32&&(s.value=t[5]),l&16&&(s.color=t[4]),e.$set(s)},i(t){a||(w(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){M(e,t)}}}function Me(n){let e,a;return e=new te({}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),a=!0},i(t){a||(w(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){M(e,t)}}}function je(n){let e,a,t,l,s,i,u;const f=[{autoscroll:n[0].autoscroll},{i18n:n[0].i18n},n[10]];let b={};for(let r=0;r<f.length;r+=1)b=ke(b,f[r]);e=new ne({props:b}),e.$on("clear_status",n[15]);let o=n[11]&&ee(n);const $=[Ce,Ne],m=[];function N(r,_){return r[13]!==void 0&&r[13]!==null?0:1}return l=N(n),s=m[l]=$[l](n),{c(){C(e.$$.fragment),a=x(),o&&o.c(),t=x(),s.c(),i=we()},m(r,_){j(e,r,_),P(r,a,_),o&&o.m(r,_),P(r,t,_),m[l].m(r,_),P(r,i,_),u=!0},p(r,_){const S=_&1025?Se(f,[_&1&&{autoscroll:r[0].autoscroll},_&1&&{i18n:r[0].i18n},_&1024&&$e(r[10])]):{};e.$set(S),r[11]?o?(o.p(r,_),_&2048&&w(o,1)):(o=ee(r),o.c(),w(o,1),o.m(t.parentNode,t)):o&&(y(),L(o,1,1,()=>{o=null}),p());let k=l;l=N(r),l===k?m[l].p(r,_):(y(),L(m[k],1,1,()=>{m[k]=null}),p(),s=m[l],s?s.p(r,_):(s=m[l]=$[l](r),s.c()),w(s,1),s.m(i.parentNode,i))},i(r){u||(w(e.$$.fragment,r),w(o),w(s),u=!0)},o(r){L(e.$$.fragment,r),L(o),L(s),u=!1},d(r){r&&(K(a),K(t),K(i)),M(e,r),o&&o.d(r),m[l].d(r)}}}function qe(n){let e,a;return e=new ae({props:{test_id:"label",visible:n[3],elem_id:n[1],elem_classes:n[2],container:n[7],scale:n[8],min_width:n[9],padding:!1,$$slots:{default:[je]},$$scope:{ctx:n}}}),{c(){C(e.$$.fragment)},m(t,l){j(e,t,l),a=!0},p(t,[l]){const s={};l&8&&(s.visible=t[3]),l&2&&(s.elem_id=t[1]),l&4&&(s.elem_classes=t[2]),l&128&&(s.container=t[7]),l&256&&(s.scale=t[8]),l&512&&(s.min_width=t[9]),l&146673&&(s.$$scope={dirty:l,ctx:t}),e.$set(s)},i(t){a||(w(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){M(e,t)}}}function Ee(n,e,a){let t,{gradio:l}=e,{elem_id:s=""}=e,{elem_classes:i=[]}=e,{visible:u=!0}=e,{color:f=void 0}=e,{value:b={}}=e,o=null,{label:$=l.i18n("label.label")}=e,{container:m=!0}=e,{scale:N=null}=e,{min_width:r=void 0}=e,{loading_status:_}=e,{show_label:S=!0}=e,{_selectable:k=!1}=e;const q=()=>l.dispatch("clear_status",_),J=({detail:c})=>l.dispatch("select",c);return n.$$set=c=>{"gradio"in c&&a(0,l=c.gradio),"elem_id"in c&&a(1,s=c.elem_id),"elem_classes"in c&&a(2,i=c.elem_classes),"visible"in c&&a(3,u=c.visible),"color"in c&&a(4,f=c.color),"value"in c&&a(5,b=c.value),"label"in c&&a(6,$=c.label),"container"in c&&a(7,m=c.container),"scale"in c&&a(8,N=c.scale),"min_width"in c&&a(9,r=c.min_width),"loading_status"in c&&a(10,_=c.loading_status),"show_label"in c&&a(11,S=c.show_label),"_selectable"in c&&a(12,k=c._selectable)},n.$$.update=()=>{n.$$.dirty&16417&&JSON.stringify(b)!==JSON.stringify(o)&&(a(14,o=b),l.dispatch("change")),n.$$.dirty&32&&a(13,t=b.label)},[l,s,i,u,f,b,$,m,N,r,_,S,k,t,o,q,J]}class Ge extends ve{constructor(e){super(),Le(this,e,Ee,qe,Be,{gradio:0,elem_id:1,elem_classes:2,visible:3,color:4,value:5,label:6,container:7,scale:8,min_width:9,loading_status:10,show_label:11,_selectable:12})}get gradio(){return this.$$.ctx[0]}set gradio(e){this.$$set({gradio:e}),g()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),g()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),g()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),g()}get color(){return this.$$.ctx[4]}set color(e){this.$$set({color:e}),g()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),g()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),g()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),g()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),g()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),g()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),g()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),g()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),g()}}export{ge as BaseLabel,Ge as default};
//# sourceMappingURL=Index-mT8gSmoJ.js.map
