#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour le Enhanced News Chatbot
Ce script teste les fonctionnalités principales du chatbot
"""

import os
import sys
import json
import time
import logging
from datetime import datetime

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_imports():
    """Teste l'importation des modules nécessaires"""
    print("🔍 Test des imports...")
    
    try:
        import feedparser
        print("✅ feedparser")
    except ImportError as e:
        print(f"❌ feedparser: {e}")
        return False
    
    try:
        import requests
        print("✅ requests")
    except ImportError as e:
        print(f"❌ requests: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ beautifulsoup4")
    except ImportError as e:
        print(f"❌ beautifulsoup4: {e}")
        return False
    
    try:
        import nltk
        print("✅ nltk")
    except ImportError as e:
        print(f"❌ nltk: {e}")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ sentence-transformers")
    except ImportError as e:
        print(f"❌ sentence-transformers: {e}")
        return False
    
    try:
        import faiss
        print("✅ faiss-cpu")
    except ImportError as e:
        print(f"❌ faiss-cpu: {e}")
        return False
    
    try:
        import gradio as gr
        print("✅ gradio")
    except ImportError as e:
        print(f"❌ gradio: {e}")
        return False
    
    try:
        import schedule
        print("✅ schedule")
    except ImportError as e:
        print(f"❌ schedule: {e}")
        return False
    
    try:
        import threading
        print("✅ threading (module intégré)")
    except ImportError as e:
        print(f"❌ threading: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Teste les fonctionnalités de base"""
    print("\n🧪 Test des fonctionnalités de base...")
    
    # Test de collecte RSS simple
    try:
        import feedparser
        print("📡 Test de collecte RSS...")
        
        # Test avec BBC News
        feed_url = "http://feeds.bbci.co.uk/news/rss.xml"
        feed = feedparser.parse(feed_url)
        
        if feed.entries:
            print(f"✅ RSS collecté: {len(feed.entries)} articles de BBC")
            print(f"   Premier article: {feed.entries[0].title[:50]}...")
        else:
            print("⚠️ Aucun article collecté de BBC")
        
    except Exception as e:
        print(f"❌ Erreur collecte RSS: {e}")
    
    # Test de nettoyage HTML
    try:
        from bs4 import BeautifulSoup
        print("🧹 Test de nettoyage HTML...")
        
        html_text = "<p>Ceci est un <strong>test</strong> de nettoyage HTML.</p>"
        soup = BeautifulSoup(html_text, 'html.parser')
        clean_text = soup.get_text()
        
        if clean_text.strip() == "Ceci est un test de nettoyage HTML.":
            print("✅ Nettoyage HTML fonctionnel")
        else:
            print(f"⚠️ Nettoyage HTML inattendu: {clean_text}")
        
    except Exception as e:
        print(f"❌ Erreur nettoyage HTML: {e}")
    
    # Test de tokenisation NLTK
    try:
        import nltk
        print("📝 Test de tokenisation NLTK...")
        
        # Télécharger les données nécessaires
        try:
            nltk.download('punkt', quiet=True)
        except:
            pass
        
        from nltk.tokenize import sent_tokenize
        
        text = "Ceci est une première phrase. Voici une seconde phrase."
        sentences = sent_tokenize(text)
        
        if len(sentences) == 2:
            print("✅ Tokenisation NLTK fonctionnelle")
        else:
            print(f"⚠️ Tokenisation inattendue: {len(sentences)} phrases")
        
    except Exception as e:
        print(f"❌ Erreur tokenisation NLTK: {e}")

def test_embedding_model():
    """Teste le modèle d'embeddings"""
    print("\n🧠 Test du modèle d'embeddings...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        print("📥 Chargement du modèle all-MiniLM-L6-v2...")
        model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Test d'embedding simple
        test_texts = [
            "Ceci est un test d'embedding.",
            "Voici un autre texte de test."
        ]
        
        print("🔢 Génération des embeddings...")
        embeddings = model.encode(test_texts)
        
        if embeddings.shape[0] == 2 and embeddings.shape[1] == 384:
            print(f"✅ Embeddings générés: {embeddings.shape}")
        else:
            print(f"⚠️ Forme d'embeddings inattendue: {embeddings.shape}")
        
    except Exception as e:
        print(f"❌ Erreur modèle d'embeddings: {e}")

def test_faiss_index():
    """Teste la création d'index FAISS"""
    print("\n🔍 Test de l'index FAISS...")
    
    try:
        import faiss
        import numpy as np
        
        print("📊 Création d'un index FAISS de test...")
        
        # Créer des vecteurs de test
        dimension = 384  # Dimension de all-MiniLM-L6-v2
        nb_vectors = 10
        vectors = np.random.random((nb_vectors, dimension)).astype('float32')
        
        # Créer l'index
        index = faiss.IndexFlatL2(dimension)
        index.add(vectors)
        
        # Test de recherche
        query_vector = np.random.random((1, dimension)).astype('float32')
        distances, indices = index.search(query_vector, k=3)
        
        if len(indices[0]) == 3:
            print(f"✅ Index FAISS fonctionnel: {index.ntotal} vecteurs indexés")
        else:
            print(f"⚠️ Recherche FAISS inattendue: {len(indices[0])} résultats")
        
    except Exception as e:
        print(f"❌ Erreur index FAISS: {e}")

def test_gradio_interface():
    """Teste l'interface Gradio"""
    print("\n🎨 Test de l'interface Gradio...")
    
    try:
        import gradio as gr
        
        def simple_chat(message, history):
            return f"Echo: {message}"
        
        print("🖥️ Création d'une interface Gradio de test...")
        
        # Créer une interface simple
        interface = gr.ChatInterface(
            simple_chat,
            title="Test Chatbot",
            description="Interface de test"
        )
        
        print("✅ Interface Gradio créée avec succès")
        print("   (Interface non lancée en mode test)")
        
    except Exception as e:
        print(f"❌ Erreur interface Gradio: {e}")

def main():
    """Fonction principale de test"""
    print("🤖 Enhanced News Chatbot - Tests de fonctionnalité")
    print("=" * 60)
    
    # Test des imports
    if not test_imports():
        print("\n❌ Échec des tests d'import. Installez les dépendances manquantes.")
        return False
    
    # Tests des fonctionnalités
    test_basic_functionality()
    test_embedding_model()
    test_faiss_index()
    test_gradio_interface()
    
    print("\n" + "=" * 60)
    print("🎉 Tests terminés !")
    print("\n📋 Prochaines étapes:")
    print("1. Si tous les tests sont ✅, le chatbot devrait fonctionner")
    print("2. Ouvrez Enhanced_News_Chatbot.ipynb dans Jupyter")
    print("3. Exécutez toutes les cellules")
    print("4. Utilisez l'interface Gradio qui s'ouvrira")
    
    return True

if __name__ == "__main__":
    main()
