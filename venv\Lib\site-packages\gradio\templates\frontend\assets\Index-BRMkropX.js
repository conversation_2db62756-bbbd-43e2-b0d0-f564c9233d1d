import{B as Le}from"./Button-BIUaXfcG.js";import{B as Ee}from"./BlockTitle-CXNngU7y.js";import{S as Te}from"./Index-DB1XLvMK.js";import{E as Ve}from"./Empty-BgF7sXBn.js";import{L as je}from"./LineChart-CKh1Fdep.js";import{e as Be}from"./vega-embed.module-B-qtr4Vf.js";import"./index-BQPjLIsY.js";import"./svelte/svelte.js";import"./Info-CrBVEpWV.js";import"./dsv-DB8NKgIY.js";const{SvelteComponent:Ie,append:Ne,assign:qe,attr:ke,binding_callbacks:De,check_outros:ge,create_component:I,destroy_component:N,detach:W,element:we,empty:ve,flush:u,get_spread_object:Me,get_spread_update:He,group_outros:he,init:Re,insert:O,mount_component:q,noop:be,safe_not_equal:Ue,set_data:Fe,space:te,text:ze,transition_in:k,transition_out:F}=window.__gradio__svelte__internal,{onMount:Xe}=window.__gradio__svelte__internal;function ye(r){let e,t;const i=[{autoscroll:r[2].autoscroll},{i18n:r[2].i18n},r[10]];let n={};for(let s=0;s<i.length;s+=1)n=qe(n,i[s]);return e=new Te({props:n}),e.$on("clear_status",r[38]),{c(){I(e.$$.fragment)},m(s,c){q(e,s,c),t=!0},p(s,c){const m=c[0]&1028?He(i,[c[0]&4&&{autoscroll:s[2].autoscroll},c[0]&4&&{i18n:s[2].i18n},c[0]&1024&&Me(s[10])]):{};e.$set(m)},i(s){t||(k(e.$$.fragment,s),t=!0)},o(s){F(e.$$.fragment,s),t=!1},d(s){N(e,s)}}}function Ye(r){let e;return{c(){e=ze(r[3])},m(t,i){O(t,e,i)},p(t,i){i[0]&8&&Fe(e,t[3])},d(t){t&&W(e)}}}function Ge(r){let e,t;return e=new Ve({props:{unpadded_box:!0,$$slots:{default:[Ke]},$$scope:{ctx:r}}}),{c(){I(e.$$.fragment)},m(i,n){q(e,i,n),t=!0},p(i,n){const s={};n[1]&4194304&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){t||(k(e.$$.fragment,i),t=!0)},o(i){F(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Je(r){let e,t,i,n=r[1]&&xe(r);return{c(){e=we("div"),t=te(),n&&n.c(),i=ve(),ke(e,"class","svelte-10k9m4v")},m(s,c){O(s,e,c),r[39](e),O(s,t,c),n&&n.m(s,c),O(s,i,c)},p(s,c){s[1]?n?n.p(s,c):(n=xe(s),n.c(),n.m(i.parentNode,i)):n&&(n.d(1),n=null)},i:be,o:be,d(s){s&&(W(e),W(t),W(i)),r[39](null),n&&n.d(s)}}}function Ke(r){let e,t;return e=new je({}),{c(){I(e.$$.fragment)},m(i,n){q(e,i,n),t=!0},i(i){t||(k(e.$$.fragment,i),t=!0)},o(i){F(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function xe(r){let e,t;return{c(){e=we("p"),t=ze(r[1]),ke(e,"class","caption svelte-10k9m4v")},m(i,n){O(i,e,n),Ne(e,t)},p(i,n){n[0]&2&&Fe(t,i[1])},d(i){i&&W(e)}}}function Qe(r){let e,t,i,n,s,c,m,a=r[10]&&ye(r);t=new Ee({props:{show_label:r[7],info:void 0,$$slots:{default:[Ye]},$$scope:{ctx:r}}});const g=[Je,Ge],f=[];function _(o,d){return o[0]&&o[13]?0:1}return n=_(r),s=f[n]=g[n](r),{c(){a&&a.c(),e=te(),I(t.$$.fragment),i=te(),s.c(),c=ve()},m(o,d){a&&a.m(o,d),O(o,e,d),q(t,o,d),O(o,i,d),f[n].m(o,d),O(o,c,d),m=!0},p(o,d){o[10]?a?(a.p(o,d),d[0]&1024&&k(a,1)):(a=ye(o),a.c(),k(a,1),a.m(e.parentNode,e)):a&&(he(),F(a,1,1,()=>{a=null}),ge());const z={};d[0]&128&&(z.show_label=o[7]),d[0]&8|d[1]&4194304&&(z.$$scope={dirty:d,ctx:o}),t.$set(z);let A=n;n=_(o),n===A?f[n].p(o,d):(he(),F(f[A],1,1,()=>{f[A]=null}),ge(),s=f[n],s?s.p(o,d):(s=f[n]=g[n](o),s.c()),k(s,1),s.m(c.parentNode,c))},i(o){m||(k(a),k(t.$$.fragment,o),k(s),m=!0)},o(o){F(a),F(t.$$.fragment,o),F(s),m=!1},d(o){o&&(W(e),W(i),W(c)),a&&a.d(o),N(t,o),f[n].d(o)}}}function Ze(r){let e,t;return e=new Le({props:{visible:r[6],elem_id:r[4],elem_classes:r[5],scale:r[8],min_width:r[9],allow_overflow:!1,padding:!0,height:r[11],$$slots:{default:[Qe]},$$scope:{ctx:r}}}),{c(){I(e.$$.fragment)},m(i,n){q(e,i,n),t=!0},p(i,n){const s={};n[0]&64&&(s.visible=i[6]),n[0]&16&&(s.elem_id=i[4]),n[0]&32&&(s.elem_classes=i[5]),n[0]&256&&(s.scale=i[8]),n[0]&512&&(s.min_width=i[9]),n[0]&2048&&(s.height=i[11]),n[0]&5263|n[1]&4194304&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){t||(k(e.$$.fragment,i),t=!0)},o(i){F(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function pe(r,e,t){let i,n,s,c,m,{value:a}=e,{x:g}=e,{y:f}=e,{color:_=null}=e,{title:o=null}=e,{x_title:d=null}=e,{y_title:z=null}=e,{color_title:A=null}=e,{x_bin:w=null}=e,{y_aggregate:j=void 0}=e,{color_map:D=null}=e,{x_lim:E=null}=e,{y_lim:M=null}=e,{x_label_angle:H=null}=e,{y_label_angle:R=null}=e,{caption:le=null}=e,{sort:K=null}=e;function Se(l){if(l==="x")return"ascending";if(l==="-x")return"descending";if(l==="y")return{field:f,order:"ascending"};if(l==="-y")return{field:f,order:"descending"};if(l===null)return;if(Array.isArray(l))return l}let{_selectable:U=!1}=e,{target:X}=e,Y,{gradio:T}=e,S,Q=!1;const Pe={s:1,m:60,h:60*60,d:24*60*60};let B,V;function Ce(l){let h=l.columns.indexOf(g),C=l.columns.indexOf(f),x=_?l.columns.indexOf(_):null;return l.data.map(v=>{const b={[g]:v[h],[f]:v[C]};return _&&x!==null&&(b[_]=v[x]),b})}const ie=typeof window<"u";let y,P,Z=!1,ne,G;function se(){if(P&&P.finalize(),!a||!y)return;ne=y.offsetWidth;const l=We();l&&(G=new ResizeObserver(h=>{!h[0].target||!(h[0].target instanceof HTMLElement)||(ne===0&&y.offsetWidth!==0&&a.datatypes[g]==="nominal"?se():P.signal("width",h[0].target.offsetWidth).run())}),Be(y,l,{actions:!1}).then(function(h){P=h.view,G.observe(y);var C;P.addEventListener("dblclick",()=>{T.dispatch("double_click")}),y.addEventListener("mousedown",function(x){x.detail>1&&x.preventDefault()},!1),U&&P.addSignalListener("brush",function(x,v){if(Object.keys(v).length===0)return;clearTimeout(C);let b=v[Object.keys(v)[0]];s&&(b=[b[0]/1e3,b[1]/1e3]),Q?J=()=>{T.dispatch("select",{value:b,index:b,selected:!0})}:C=setTimeout(function(){T.dispatch("select",{value:b,index:b,selected:!0})},250)})}))}let J=null;Xe(()=>(t(35,Z=!0),y.addEventListener("mousedown",()=>{Q=!0}),y.addEventListener("mouseup",()=>{Q=!1,J&&(J(),J=null)}),()=>{t(35,Z=!1),P&&P.finalize(),G&&G.disconnect()}));function We(){if(!a||!m)return null;let l=m.getPropertyValue("--color-accent"),h=m.getPropertyValue("--body-text-color"),C=m.getPropertyValue("--border-color-primary"),x=m.fontFamily,v=m.getPropertyValue("--block-title-text-weight");const b=L=>L.endsWith("px")?parseFloat(L.slice(0,-2)):12;let me=b(m.getPropertyValue("--text-md")),$=b(m.getPropertyValue("--text-sm"));return{$schema:"https://vega.github.io/schema/vega-lite/v5.17.0.json",background:"transparent",config:{autosize:{type:"fit",contains:"padding"},axis:{labelFont:x,labelColor:h,titleFont:x,titleColor:h,titlePadding:8,tickColor:C,labelFontSize:$,gridColor:C,titleFontWeight:"normal",titleFontSize:$,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:h,labelFont:x,titleColor:h,titleFont:x,titleFontWeight:"normal",titleFontSize:$,labelFontWeight:"normal",offset:2},title:{color:h,font:x,fontSize:me,fontWeight:v,anchor:"middle"},view:{stroke:C},mark:{stroke:a.mark!=="bar"?l:void 0,fill:a.mark==="bar"?l:void 0,cursor:"crosshair"}},data:{name:"data"},datasets:{data:Y},layer:["plot",...a.mark==="line"?["hover"]:[]].map(L=>({encoding:{size:a.mark==="line"?L=="plot"?{condition:{empty:!1,param:"hoverPlot",value:3},value:2}:{condition:{empty:!1,param:"hover",value:100},value:0}:void 0,opacity:L==="plot"?void 0:{condition:{empty:!1,param:"hover",value:1},value:0},x:{axis:H?{labelAngle:H}:{},field:g,title:d||g,type:a.datatypes[g],scale:c?{domain:c}:void 0,bin:S?{step:S}:void 0,sort:n},y:{axis:R?{labelAngle:R}:{},field:f,title:z||f,type:a.datatypes[f],scale:M?{domain:M}:void 0,aggregate:V?B:void 0},color:_?{field:_,legend:{orient:"bottom",title:A},scale:a.datatypes[_]==="nominal"?{domain:i,range:D?i.map(ee=>D[ee]):void 0}:{range:[100,200,300,400,500,600,700,800,900].map(ee=>m.getPropertyValue("--primary-"+ee)),interpolate:"hsl"},type:a.datatypes[_]}:void 0,tooltip:[{field:f,type:a.datatypes[f],aggregate:V?B:void 0,title:z||f},{field:g,type:a.datatypes[g],title:d||g,format:s?"%Y-%m-%d %H:%M:%S":void 0,bin:S?{step:S}:void 0},..._?[{field:_,type:a.datatypes[_]}]:[]]},strokeDash:{},mark:{clip:!0,type:L==="hover"?"point":a.mark},name:L})),params:[...a.mark==="line"?[{name:"hoverPlot",select:{clear:"mouseout",fields:_?[_]:[],nearest:!0,on:"mouseover",type:"point"},views:["hover"]},{name:"hover",select:{clear:"mouseout",nearest:!0,on:"mouseover",type:"point"},views:["hover"]}]:[],...U?[{name:"brush",select:{encodings:["x"],mark:{fill:"gray",fillOpacity:.3,stroke:"none"},type:"interval"},views:["plot"]}]:[]],width:y.offsetWidth,title:o||void 0}}let{label:re="Textbox"}=e,{elem_id:ae=""}=e,{elem_classes:oe=[]}=e,{visible:ue=!0}=e,{show_label:ce}=e,{scale:fe=null}=e,{min_width:_e=void 0}=e,{loading_status:p=void 0}=e,{height:de=void 0}=e;const Oe=()=>T.dispatch("clear_status",p);function Ae(l){De[l?"unshift":"push"](()=>{y=l,t(12,y)})}return r.$$set=l=>{"value"in l&&t(0,a=l.value),"x"in l&&t(14,g=l.x),"y"in l&&t(15,f=l.y),"color"in l&&t(16,_=l.color),"title"in l&&t(17,o=l.title),"x_title"in l&&t(18,d=l.x_title),"y_title"in l&&t(19,z=l.y_title),"color_title"in l&&t(20,A=l.color_title),"x_bin"in l&&t(21,w=l.x_bin),"y_aggregate"in l&&t(22,j=l.y_aggregate),"color_map"in l&&t(23,D=l.color_map),"x_lim"in l&&t(24,E=l.x_lim),"y_lim"in l&&t(25,M=l.y_lim),"x_label_angle"in l&&t(26,H=l.x_label_angle),"y_label_angle"in l&&t(27,R=l.y_label_angle),"caption"in l&&t(1,le=l.caption),"sort"in l&&t(28,K=l.sort),"_selectable"in l&&t(29,U=l._selectable),"target"in l&&t(30,X=l.target),"gradio"in l&&t(2,T=l.gradio),"label"in l&&t(3,re=l.label),"elem_id"in l&&t(4,ae=l.elem_id),"elem_classes"in l&&t(5,oe=l.elem_classes),"visible"in l&&t(6,ue=l.visible),"show_label"in l&&t(7,ce=l.show_label),"scale"in l&&t(8,fe=l.scale),"min_width"in l&&t(9,_e=l.min_width),"loading_status"in l&&t(10,p=l.loading_status),"height"in l&&t(11,de=l.height)},r.$$.update=()=>{r.$$.dirty[0]&1&&t(31,Y=a?Ce(a):[]),r.$$.dirty[0]&65537|r.$$.dirty[1]&1&&(i=_&&a&&a.datatypes[_]==="nominal"?Array.from(new Set(Y.map(l=>l[_]))):[]),r.$$.dirty[0]&268435456&&(n=Se(K)),r.$$.dirty[0]&16385&&t(36,s=a&&a.datatypes[g]==="temporal"),r.$$.dirty[0]&16777216|r.$$.dirty[1]&32&&(c=E&&s?[E[0]*1e3,E[1]*1e3]:E),r.$$.dirty[0]&2097152&&t(32,S=w?typeof w=="string"?1e3*parseInt(w.substring(0,w.length-1))*Pe[w[w.length-1]]:w:void 0),r.$$.dirty[0]&4210689|r.$$.dirty[1]&10&&a&&(a.mark==="point"?(t(34,V=S!==void 0),t(33,B=j||V?"sum":void 0)):(t(34,V=S!==void 0||a.datatypes[g]==="nominal"),t(33,B=j||"sum"))),r.$$.dirty[0]&1073741824&&t(37,m=X&&ie?window.getComputedStyle(X):null),r.$$.dirty[0]&331337731|r.$$.dirty[1]&84&&m&&requestAnimationFrame(se)},[a,le,T,re,ae,oe,ue,ce,fe,_e,p,de,y,ie,g,f,_,o,d,z,A,w,j,D,E,M,H,R,K,U,X,Y,S,B,V,Z,s,m,Oe,Ae]}class ut extends Ie{constructor(e){super(),Re(this,e,pe,Ze,Ue,{value:0,x:14,y:15,color:16,title:17,x_title:18,y_title:19,color_title:20,x_bin:21,y_aggregate:22,color_map:23,x_lim:24,y_lim:25,x_label_angle:26,y_label_angle:27,caption:1,sort:28,_selectable:29,target:30,gradio:2,label:3,elem_id:4,elem_classes:5,visible:6,show_label:7,scale:8,min_width:9,loading_status:10,height:11},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get x(){return this.$$.ctx[14]}set x(e){this.$$set({x:e}),u()}get y(){return this.$$.ctx[15]}set y(e){this.$$set({y:e}),u()}get color(){return this.$$.ctx[16]}set color(e){this.$$set({color:e}),u()}get title(){return this.$$.ctx[17]}set title(e){this.$$set({title:e}),u()}get x_title(){return this.$$.ctx[18]}set x_title(e){this.$$set({x_title:e}),u()}get y_title(){return this.$$.ctx[19]}set y_title(e){this.$$set({y_title:e}),u()}get color_title(){return this.$$.ctx[20]}set color_title(e){this.$$set({color_title:e}),u()}get x_bin(){return this.$$.ctx[21]}set x_bin(e){this.$$set({x_bin:e}),u()}get y_aggregate(){return this.$$.ctx[22]}set y_aggregate(e){this.$$set({y_aggregate:e}),u()}get color_map(){return this.$$.ctx[23]}set color_map(e){this.$$set({color_map:e}),u()}get x_lim(){return this.$$.ctx[24]}set x_lim(e){this.$$set({x_lim:e}),u()}get y_lim(){return this.$$.ctx[25]}set y_lim(e){this.$$set({y_lim:e}),u()}get x_label_angle(){return this.$$.ctx[26]}set x_label_angle(e){this.$$set({x_label_angle:e}),u()}get y_label_angle(){return this.$$.ctx[27]}set y_label_angle(e){this.$$set({y_label_angle:e}),u()}get caption(){return this.$$.ctx[1]}set caption(e){this.$$set({caption:e}),u()}get sort(){return this.$$.ctx[28]}set sort(e){this.$$set({sort:e}),u()}get _selectable(){return this.$$.ctx[29]}set _selectable(e){this.$$set({_selectable:e}),u()}get target(){return this.$$.ctx[30]}set target(e){this.$$set({target:e}),u()}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),u()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),u()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),u()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),u()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),u()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),u()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),u()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),u()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),u()}get height(){return this.$$.ctx[11]}set height(e){this.$$set({height:e}),u()}}export{ut as default};
//# sourceMappingURL=Index-BRMkropX.js.map
